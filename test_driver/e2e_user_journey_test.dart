import 'package:flutter/material.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:integration_test/integration_test.dart';
import 'package:supabase/supabase.dart';
import 'package:fitgo_app/main.dart' as app;

// Global variables for test data
late String testInstructorId;
late String testStudentId;
late String testInstructorEmail;
late String testStudentEmail;

void main() {
  IntegrationTestWidgetsFlutterBinding.ensureInitialized();

  group('🚀 COMPLETE E2E USER JOURNEY TESTS', () {
    late SupabaseClient supabase;

    setUpAll(() async {
      // Initialize Supabase for database validation
      supabase = SupabaseClient(
        'https://wrevdlggsevlckprjrwm.supabase.co',
        'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6IndyZXZkbGdnc2V2bGNrcHJqcndtIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDEwMjk3MTcsImV4cCI6MjA1NjYwNTcxN30.KRdBVWPAqDSCsyvYBE3ntrPqpH09KzUmnzbYONAFtzY',
      );

      // Generate unique test identifiers
      final timestamp = DateTime.now().millisecondsSinceEpoch;
      testInstructorId = 'test-instructor-$timestamp';
      testStudentId = 'test-student-$timestamp';
      testInstructorEmail = 'instructor-$<EMAIL>';
      testStudentEmail = 'student-$<EMAIL>';

      debugPrint('🧪 E2E Test Setup:');
      debugPrint('📧 Instructor Email: $testInstructorEmail');
      debugPrint('📧 Student Email: $testStudentEmail');
    });

    tearDownAll(() async {
      // Cleanup test data
      try {
        debugPrint('🧹 Cleaning up test data...');
        await _cleanupTestData(supabase, testInstructorId, testStudentId, testInstructorEmail, testStudentEmail);
        debugPrint('✅ Cleanup completed');
      } catch (e) {
        debugPrint('⚠️ Cleanup error (non-critical): $e');
      }
    });

    testWidgets('🎯 COMPLETE E2E USER JOURNEY - INSTRUCTOR & STUDENT FLOW', (WidgetTester tester) async {
      debugPrint('\n🚀 Starting Complete E2E User Journey Test...\n');

      // ==========================================
      // PHASE 1: INSTRUCTOR REGISTRATION
      // ==========================================
      debugPrint('📋 PHASE 1: Instructor Registration');
      
      await _testInstructorRegistration(tester, testInstructorEmail);
      
      // Verify instructor in database (should be incomplete/not approved)
      await _verifyInstructorInDatabase(supabase, testInstructorEmail, expectedStatus: 'not_submitted');

      // ==========================================
      // PHASE 2: STUDENT REGISTRATION  
      // ==========================================
      debugPrint('\n📋 PHASE 2: Student Registration');
      
      await _testStudentRegistration(tester, testStudentEmail);
      
      // Verify student in database
      await _verifyStudentInDatabase(supabase, testStudentEmail);

      // ==========================================
      // PHASE 3: VERIFY INSTRUCTOR NOT VISIBLE
      // ==========================================
      debugPrint('\n📋 PHASE 3: Verify Instructor Not Visible in Featured List');
      
      await _testInstructorNotVisible(tester, testInstructorEmail);

      // ==========================================
      // PHASE 4: COMPLETE INSTRUCTOR PROFILE
      // ==========================================
      debugPrint('\n📋 PHASE 4: Complete Instructor Profile');
      
      await _testCompleteInstructorProfile(tester, testInstructorEmail);
      
      // Verify profile completion in database
      await _verifyInstructorProfileComplete(supabase, testInstructorEmail);

      // ==========================================
      // PHASE 5: INSTRUCTOR APPROVAL WORKFLOW
      // ==========================================
      debugPrint('\n📋 PHASE 5: Instructor Approval Workflow');
      
      // Manually reject instructor in database
      await _rejectInstructorInDatabase(supabase, testInstructorEmail);
      
      // Test resubmission
      await _testInstructorResubmission(tester, testInstructorEmail);
      
      // Manually approve instructor in database
      await _approveInstructorInDatabase(supabase, testInstructorEmail);

      // ==========================================
      // PHASE 6: VERIFY INSTRUCTOR NOW VISIBLE
      // ==========================================
      debugPrint('\n📋 PHASE 6: Verify Approved Instructor Visible');
      
      await _testInstructorNowVisible(tester, testInstructorEmail);

      // ==========================================
      // PHASE 7: STUDENT ENROLLMENT
      // ==========================================
      debugPrint('\n📋 PHASE 7: Student Enrollment Process');
      
      await _testStudentEnrollment(tester, testStudentEmail, testInstructorEmail);
      
      // Verify enrollment in database
      await _verifyEnrollmentInDatabase(supabase, testStudentEmail, testInstructorEmail);

      // ==========================================
      // PHASE 8: SELF-INTRODUCTION FORM
      // ==========================================
      debugPrint('\n📋 PHASE 8: Self-Introduction Form');
      
      await _testSelfIntroductionForm(tester);
      
      // Verify user profile data in database
      await _verifyUserProfileInDatabase(supabase, testStudentEmail);

      // ==========================================
      // PHASE 9: WAITING SCREEN & STATUS CHECK
      // ==========================================
      debugPrint('\n📋 PHASE 9: Waiting Screen & Status Check');
      
      await _testWaitingScreen(tester);

      // ==========================================
      // PHASE 10: INSTRUCTOR CREATES TEMPLATES
      // ==========================================
      debugPrint('\n📋 PHASE 10: Instructor Creates Templates');
      
      await _testInstructorCreatesTemplates(tester, testInstructorEmail);
      
      // Verify templates in database
      await _verifyTemplatesInDatabase(supabase, testInstructorEmail);

      // ==========================================
      // PHASE 11: ASSIGN TEMPLATES TO STUDENT
      // ==========================================
      debugPrint('\n📋 PHASE 11: Assign Templates to Student');
      
      await _testAssignTemplates(tester, testInstructorEmail, testStudentEmail);
      
      // Verify assignments in database
      await _verifyAssignmentsInDatabase(supabase, testStudentEmail);

      // ==========================================
      // PHASE 12: STUDENT DASHBOARD ACCESS
      // ==========================================
      debugPrint('\n📋 PHASE 12: Student Dashboard Access');
      
      await _testStudentDashboardAccess(tester, testStudentEmail);

      // ==========================================
      // PHASE 13: VERIFY ASSIGNED PLANS
      // ==========================================
      debugPrint('\n📋 PHASE 13: Verify Assigned Plans in Dashboard');

      await _testVerifyAssignedPlans(tester);

      // ==========================================
      // PHASE 14: COMPLETE WORKOUT SESSION FLOW
      // ==========================================
      debugPrint('\n📋 PHASE 14: Complete Workout Session Flow');

      await _testCompleteWorkoutSession(tester);

      // ==========================================
      // PHASE 15: NUTRITION LOGGING FLOW
      // ==========================================
      debugPrint('\n📋 PHASE 15: Nutrition Logging Flow');

      await _testNutritionLoggingFlow(tester);

      // ==========================================
      // PHASE 16: PROGRESS TRACKING & PHOTOS
      // ==========================================
      debugPrint('\n📋 PHASE 16: Progress Tracking & Photo Upload');

      await _testProgressTrackingAndPhotos(tester);

      // ==========================================
      // PHASE 17: EXPLORE TAB FEATURES
      // ==========================================
      debugPrint('\n📋 PHASE 17: Explore Tab Features');

      await _testExploreTabFeatures(tester);

      // ==========================================
      // PHASE 18: INSTRUCTOR DASHBOARD FEATURES
      // ==========================================
      debugPrint('\n📋 PHASE 18: Instructor Dashboard Features');

      await _testInstructorDashboardFeatures(tester, testInstructorEmail, testStudentEmail);

      // ==========================================
      // PHASE 19: FINAL VERIFICATION
      // ==========================================
      debugPrint('\n📋 PHASE 19: Final System Verification');

      await _testFinalSystemVerification(tester, testStudentEmail, testInstructorEmail);

      debugPrint('\n🎉 COMPLETE E2E USER JOURNEY TEST COMPLETED SUCCESSFULLY! 🎉\n');
    });
  });
}

// ==========================================
// HELPER FUNCTIONS
// ==========================================

/// Phase 1: Test instructor registration
Future<void> _testInstructorRegistration(WidgetTester tester, String email) async {
  debugPrint('👨‍🏫 Testing instructor registration...');
  
  // Launch app
  app.main();
  await tester.pumpAndSettle(const Duration(seconds: 3));
  
  try {
    // Look for role selection or direct auth screen
    if (find.text('Instructor').evaluate().isNotEmpty) {
      await tester.tap(find.text('Instructor'));
      await tester.pumpAndSettle();
    }
    
    // Navigate to registration
    if (find.text('Sign Up').evaluate().isNotEmpty) {
      await tester.tap(find.text('Sign Up'));
      await tester.pumpAndSettle();
    }
    
    // Fill instructor registration form
    await _fillRegistrationForm(tester, 
      name: 'Test',
      surname: 'Instructor', 
      email: email,
      phone: '+905551234567',
      password: 'InstructorPass123!',
      role: 'instructor'
    );
    
    debugPrint('✅ Instructor registration completed');
  } catch (e) {
    debugPrint('❌ Instructor registration failed: $e');
    rethrow;
  }
}

/// Phase 2: Test student registration
Future<void> _testStudentRegistration(WidgetTester tester, String email) async {
  debugPrint('👨‍🎓 Testing student registration...');
  
  try {
    // Restart app for fresh session
    app.main();
    await tester.pumpAndSettle(const Duration(seconds: 3));
    
    // Look for role selection
    if (find.text('Student').evaluate().isNotEmpty) {
      await tester.tap(find.text('Student'));
      await tester.pumpAndSettle();
    }
    
    // Navigate to registration
    if (find.text('Sign Up').evaluate().isNotEmpty) {
      await tester.tap(find.text('Sign Up'));
      await tester.pumpAndSettle();
    }
    
    // Fill student registration form
    await _fillRegistrationForm(tester,
      name: 'Test',
      surname: 'Student',
      email: email,
      phone: '+905551234568',
      password: 'StudentPass123!',
      role: 'student'
    );
    
    debugPrint('✅ Student registration completed');
  } catch (e) {
    debugPrint('❌ Student registration failed: $e');
    rethrow;
  }
}

/// Helper: Fill registration form
Future<void> _fillRegistrationForm(WidgetTester tester, {
  required String name,
  required String surname,
  required String email,
  required String phone,
  required String password,
  required String role,
}) async {
  // Wait for form to load
  await tester.pumpAndSettle();
  
  // Fill form fields with error handling
  await _enterTextSafely(tester, 'name_field', name);
  await _enterTextSafely(tester, 'surname_field', surname);
  await _enterTextSafely(tester, 'email_field', email);
  await _enterTextSafely(tester, 'phone_field', phone);
  await _enterTextSafely(tester, 'password_field', password);
  
  // Select role if available
  if (find.text(role.capitalize()).evaluate().isNotEmpty) {
    await tester.tap(find.text(role.capitalize()));
    await tester.pump();
  }
  
  // Submit form
  final submitButton = find.text('Create Account').first;
  await tester.tap(submitButton);
  await tester.pumpAndSettle(const Duration(seconds: 5));
}

/// Helper: Safely enter text in field
Future<void> _enterTextSafely(WidgetTester tester, String key, String text) async {
  final field = find.byKey(Key(key));
  if (field.evaluate().isNotEmpty) {
    await tester.enterText(field, text);
    await tester.pump();
  }
}

/// Extension for string capitalization
extension StringExtension on String {
  String capitalize() {
    return "${this[0].toUpperCase()}${substring(1)}";
  }
}

/// Phase 3: Test instructor not visible in featured list
Future<void> _testInstructorNotVisible(WidgetTester tester, String instructorEmail) async {
  debugPrint('🔍 Testing instructor not visible in featured list...');

  try {
    // Navigate to featured instructors
    await _navigateToFeaturedInstructors(tester);

    // Wait for list to load
    await tester.pumpAndSettle(const Duration(seconds: 3));

    // Verify instructor is not visible (should not find instructor name/email)
    expect(find.textContaining('Test Instructor'), findsNothing);

    debugPrint('✅ Instructor correctly not visible in featured list');
  } catch (e) {
    debugPrint('❌ Failed to verify instructor visibility: $e');
    rethrow;
  }
}

/// Phase 4: Complete instructor profile
Future<void> _testCompleteInstructorProfile(WidgetTester tester, String instructorEmail) async {
  debugPrint('📝 Testing instructor profile completion...');

  try {
    // Login as instructor
    await _loginUser(tester, instructorEmail, 'InstructorPass123!');

    // Navigate to profile completion
    await _navigateToProfileCompletion(tester);

    // Fill basic info
    await _fillInstructorBasicInfo(tester);

    // Add work history
    await _addWorkHistory(tester);

    // Add certifications
    await _addCertifications(tester);

    // Set pricing
    await _setPricing(tester);

    // Submit for review
    await _submitForReview(tester);

    debugPrint('✅ Instructor profile completion successful');
  } catch (e) {
    debugPrint('❌ Instructor profile completion failed: $e');
    rethrow;
  }
}

/// Phase 7: Test student enrollment
Future<void> _testStudentEnrollment(WidgetTester tester, String studentEmail, String instructorEmail) async {
  debugPrint('📚 Testing student enrollment...');

  try {
    // Login as student
    await _loginUser(tester, studentEmail, 'StudentPass123!');

    // Navigate to featured instructors
    await _navigateToFeaturedInstructors(tester);

    // Find and select the approved instructor
    await _selectInstructor(tester, 'Test Instructor');

    // Select plan and enroll
    await _selectPlanAndEnroll(tester);

    // Complete payment
    await _completePayment(tester);

    debugPrint('✅ Student enrollment successful');
  } catch (e) {
    debugPrint('❌ Student enrollment failed: $e');
    rethrow;
  }
}

/// Phase 8: Test self-introduction form
Future<void> _testSelfIntroductionForm(WidgetTester tester) async {
  debugPrint('📋 Testing self-introduction form...');

  try {
    // Should be redirected to self-introduction after enrollment
    await tester.pumpAndSettle(const Duration(seconds: 3));

    // Verify self-introduction form is shown
    expect(find.text('Complete Your Profile'), findsOneWidget);

    // Fill form
    await _fillSelfIntroductionForm(tester);

    // Submit form
    await _submitSelfIntroductionForm(tester);

    debugPrint('✅ Self-introduction form completed');
  } catch (e) {
    debugPrint('❌ Self-introduction form failed: $e');
    rethrow;
  }
}

/// Phase 9: Test waiting screen
Future<void> _testWaitingScreen(WidgetTester tester) async {
  debugPrint('⏳ Testing waiting screen...');

  try {
    // Should be on waiting screen after profile completion
    await tester.pumpAndSettle();

    // Verify waiting screen elements
    expect(find.text('Waiting for Workout Plan'), findsOneWidget);
    expect(find.text('Check Status'), findsOneWidget);

    // Test hot restart simulation
    app.main();
    await tester.pumpAndSettle(const Duration(seconds: 3));

    // Should still be on waiting screen
    expect(find.text('Waiting for Workout Plan'), findsOneWidget);

    // Test check status button
    await tester.tap(find.text('Check Status'));
    await tester.pumpAndSettle();

    // Should still be waiting
    expect(find.text('Still waiting'), findsOneWidget);

    debugPrint('✅ Waiting screen behavior verified');
  } catch (e) {
    debugPrint('❌ Waiting screen test failed: $e');
    rethrow;
  }
}

/// Phase 10: Test instructor creates templates
Future<void> _testInstructorCreatesTemplates(WidgetTester tester, String instructorEmail) async {
  debugPrint('📝 Testing instructor template creation...');

  try {
    // Login as instructor
    await _loginUser(tester, instructorEmail, 'InstructorPass123!');

    // Navigate to template creation
    await _navigateToTemplateCreation(tester);

    // Create workout template
    await _createWorkoutTemplate(tester);

    // Create nutrition template
    await _createNutritionTemplate(tester);

    debugPrint('✅ Instructor template creation successful');
  } catch (e) {
    debugPrint('❌ Instructor template creation failed: $e');
    rethrow;
  }
}

/// Phase 11: Test assign templates
Future<void> _testAssignTemplates(WidgetTester tester, String instructorEmail, String studentEmail) async {
  debugPrint('📋 Testing template assignment...');

  try {
    // Should already be logged in as instructor

    // Navigate to student management
    await _navigateToStudentManagement(tester);

    // Find the test student
    await _findAndSelectStudent(tester, 'Test Student');

    // Test three assignment scenarios
    await _testAssignFromExistingTemplate(tester);
    await _testCreateNewTemplateDuringAssignment(tester);
    await _testAssignNewlyCreatedInlineTemplate(tester);

    debugPrint('✅ Template assignment successful');
  } catch (e) {
    debugPrint('❌ Template assignment failed: $e');
    rethrow;
  }
}

/// Phase 12: Test student dashboard access
Future<void> _testStudentDashboardAccess(WidgetTester tester, String studentEmail) async {
  debugPrint('🏠 Testing student dashboard access...');

  try {
    // Login as student
    await _loginUser(tester, studentEmail, 'StudentPass123!');

    // Should go to waiting screen first
    await tester.pumpAndSettle();

    // Check status - should now have access to dashboard
    await tester.tap(find.text('Check Status'));
    await tester.pumpAndSettle();

    // Should now be on dashboard
    expect(find.byType(BottomNavigationBar), findsOneWidget);
    expect(find.text('Exercise'), findsOneWidget);
    expect(find.text('Nutrition'), findsOneWidget);

    debugPrint('✅ Student dashboard access successful');
  } catch (e) {
    debugPrint('❌ Student dashboard access failed: $e');
    rethrow;
  }
}

/// Phase 13: Test verify assigned plans
Future<void> _testVerifyAssignedPlans(WidgetTester tester) async {
  debugPrint('✅ Testing assigned plans verification...');

  try {
    // Test workout tab
    await tester.tap(find.text('Exercise'));
    await tester.pumpAndSettle();

    // Verify workout plan is assigned
    expect(find.text('Test Workout Plan'), findsOneWidget);
    expect(find.text('Start Workout'), findsOneWidget);

    // Test nutrition tab
    await tester.tap(find.text('Nutrition'));
    await tester.pumpAndSettle();

    // Verify nutrition plan is assigned
    expect(find.text('Test Nutrition Plan'), findsOneWidget);
    expect(find.text('View Plan'), findsOneWidget);

    debugPrint('✅ Assigned plans verification successful');
  } catch (e) {
    debugPrint('❌ Assigned plans verification failed: $e');
    rethrow;
  }
}

// ==========================================
// NAVIGATION HELPER FUNCTIONS
// ==========================================

/// Navigate to featured instructors
Future<void> _navigateToFeaturedInstructors(WidgetTester tester) async {
  // Look for featured instructors or course list
  if (find.text('Featured Instructors').evaluate().isNotEmpty) {
    await tester.tap(find.text('Featured Instructors'));
  } else if (find.text('Choose Your Instructor').evaluate().isNotEmpty) {
    // Already on the right screen
  } else {
    // Try to find navigation to course selection
    if (find.byIcon(Icons.search).evaluate().isNotEmpty) {
      await tester.tap(find.byIcon(Icons.search));
    }
  }
  await tester.pumpAndSettle();
}

/// Login user with email and password
Future<void> _loginUser(WidgetTester tester, String email, String password) async {
  // Restart app for fresh session
  app.main();
  await tester.pumpAndSettle(const Duration(seconds: 3));

  // Navigate to login if needed
  if (find.text('Login').evaluate().isNotEmpty) {
    await tester.tap(find.text('Login'));
    await tester.pumpAndSettle();
  }

  // Fill login form
  await _enterTextSafely(tester, 'email_field', email);
  await _enterTextSafely(tester, 'password_field', password);

  // Submit login
  await tester.tap(find.text('Login').first);
  await tester.pumpAndSettle(const Duration(seconds: 5));
}

/// Navigate to profile completion
Future<void> _navigateToProfileCompletion(WidgetTester tester) async {
  // Look for profile completion prompts
  if (find.text('Complete Profile').evaluate().isNotEmpty) {
    await tester.tap(find.text('Complete Profile'));
  } else if (find.text('Profile').evaluate().isNotEmpty) {
    await tester.tap(find.text('Profile'));
  }
  await tester.pumpAndSettle();
}

/// Fill instructor basic info
Future<void> _fillInstructorBasicInfo(WidgetTester tester) async {
  await _enterTextSafely(tester, 'title_field', 'Personal Trainer');
  await _enterTextSafely(tester, 'experience_field', '5');
  await _enterTextSafely(tester, 'bio_field', 'Experienced fitness trainer');

  // Select specializations
  if (find.text('Strength Training').evaluate().isNotEmpty) {
    await tester.tap(find.text('Strength Training'));
    await tester.pump();
  }

  // Continue to next step
  if (find.text('Continue').evaluate().isNotEmpty) {
    await tester.tap(find.text('Continue'));
    await tester.pumpAndSettle();
  }
}

/// Add work history
Future<void> _addWorkHistory(WidgetTester tester) async {
  await _enterTextSafely(tester, 'company_field', 'Elite Fitness');
  await _enterTextSafely(tester, 'position_field', 'Senior Trainer');
  await _enterTextSafely(tester, 'start_year_field', '2020');

  if (find.text('Add Experience').evaluate().isNotEmpty) {
    await tester.tap(find.text('Add Experience'));
    await tester.pump();
  }

  if (find.text('Continue').evaluate().isNotEmpty) {
    await tester.tap(find.text('Continue'));
    await tester.pumpAndSettle();
  }
}

/// Add certifications
Future<void> _addCertifications(WidgetTester tester) async {
  await _enterTextSafely(tester, 'cert_name_field', 'NASM-CPT');
  await _enterTextSafely(tester, 'cert_org_field', 'NASM');
  await _enterTextSafely(tester, 'cert_year_field', '2020');

  if (find.text('Add Certification').evaluate().isNotEmpty) {
    await tester.tap(find.text('Add Certification'));
    await tester.pump();
  }

  if (find.text('Continue').evaluate().isNotEmpty) {
    await tester.tap(find.text('Continue'));
    await tester.pumpAndSettle();
  }
}

/// Set pricing
Future<void> _setPricing(WidgetTester tester) async {
  await _enterTextSafely(tester, 'basic_monthly_field', '100');
  await _enterTextSafely(tester, 'premium_monthly_field', '150');

  if (find.text('Save Pricing').evaluate().isNotEmpty) {
    await tester.tap(find.text('Save Pricing'));
    await tester.pumpAndSettle();
  }
}

/// Submit for review
Future<void> _submitForReview(WidgetTester tester) async {
  if (find.text('Submit for Review').evaluate().isNotEmpty) {
    await tester.tap(find.text('Submit for Review'));
    await tester.pumpAndSettle(const Duration(seconds: 3));
  }
}

/// Select instructor from list
Future<void> _selectInstructor(WidgetTester tester, String instructorName) async {
  // Wait for list to load
  await tester.pumpAndSettle(const Duration(seconds: 3));

  // Find instructor card or name
  if (find.textContaining(instructorName).evaluate().isNotEmpty) {
    await tester.tap(find.textContaining(instructorName).first);
  } else if (find.byType(Card).evaluate().isNotEmpty) {
    await tester.tap(find.byType(Card).first);
  }
  await tester.pumpAndSettle();
}

/// Select plan and enroll
Future<void> _selectPlanAndEnroll(WidgetTester tester) async {
  // Select basic plan
  if (find.text('Basic Plan').evaluate().isNotEmpty) {
    await tester.tap(find.text('Basic Plan'));
    await tester.pump();
  }

  // Select monthly duration
  if (find.text('Monthly').evaluate().isNotEmpty) {
    await tester.tap(find.text('Monthly'));
    await tester.pump();
  }

  // Enroll
  if (find.text('Enroll Now').evaluate().isNotEmpty) {
    await tester.tap(find.text('Enroll Now'));
    await tester.pumpAndSettle();
  }
}

/// Complete payment
Future<void> _completePayment(WidgetTester tester) async {
  // Fill payment form with test data
  await _enterTextSafely(tester, 'card_number_field', '****************');
  await _enterTextSafely(tester, 'expiry_field', '12/25');
  await _enterTextSafely(tester, 'cvv_field', '123');
  await _enterTextSafely(tester, 'cardholder_field', 'Test User');

  // Process payment
  if (find.text('Pay Now').evaluate().isNotEmpty) {
    await tester.tap(find.text('Pay Now'));
    await tester.pumpAndSettle(const Duration(seconds: 5));
  }
}

/// Fill self-introduction form
Future<void> _fillSelfIntroductionForm(WidgetTester tester) async {
  await _enterTextSafely(tester, 'age_field', '25');
  await _enterTextSafely(tester, 'height_field', '175');
  await _enterTextSafely(tester, 'weight_field', '70');
  await _enterTextSafely(tester, 'goal_field', 'Weight Loss');

  // Select fitness level
  if (find.text('Beginner').evaluate().isNotEmpty) {
    await tester.tap(find.text('Beginner'));
    await tester.pump();
  }
}

/// Submit self-introduction form
Future<void> _submitSelfIntroductionForm(WidgetTester tester) async {
  if (find.text('Complete Profile').evaluate().isNotEmpty) {
    await tester.tap(find.text('Complete Profile'));
    await tester.pumpAndSettle(const Duration(seconds: 3));
  }
}

/// Navigate to template creation
Future<void> _navigateToTemplateCreation(WidgetTester tester) async {
  // Look for template or plan creation options
  if (find.text('Create Template').evaluate().isNotEmpty) {
    await tester.tap(find.text('Create Template'));
  } else if (find.text('Templates').evaluate().isNotEmpty) {
    await tester.tap(find.text('Templates'));
  }
  await tester.pumpAndSettle();
}

/// Create workout template
Future<void> _createWorkoutTemplate(WidgetTester tester) async {
  // Navigate to workout template creation
  if (find.text('Workout Template').evaluate().isNotEmpty) {
    await tester.tap(find.text('Workout Template'));
    await tester.pumpAndSettle();
  }

  // Fill template details
  await _enterTextSafely(tester, 'template_name_field', 'Test Workout Plan');
  await _enterTextSafely(tester, 'template_description_field', 'Test workout description');

  // Add exercises
  if (find.text('Add Exercise').evaluate().isNotEmpty) {
    await tester.tap(find.text('Add Exercise'));
    await tester.pumpAndSettle();

    // Fill exercise details
    await _enterTextSafely(tester, 'exercise_name_field', 'Push-ups');
    await _enterTextSafely(tester, 'sets_field', '3');
    await _enterTextSafely(tester, 'reps_field', '10');

    if (find.text('Save Exercise').evaluate().isNotEmpty) {
      await tester.tap(find.text('Save Exercise'));
      await tester.pump();
    }
  }

  // Save template
  if (find.text('Save Template').evaluate().isNotEmpty) {
    await tester.tap(find.text('Save Template'));
    await tester.pumpAndSettle();
  }
}

/// Create nutrition template
Future<void> _createNutritionTemplate(WidgetTester tester) async {
  // Navigate to nutrition template creation
  if (find.text('Nutrition Template').evaluate().isNotEmpty) {
    await tester.tap(find.text('Nutrition Template'));
    await tester.pumpAndSettle();
  }

  // Fill template details
  await _enterTextSafely(tester, 'nutrition_name_field', 'Test Nutrition Plan');
  await _enterTextSafely(tester, 'nutrition_description_field', 'Test nutrition description');

  // Set macro targets
  await _enterTextSafely(tester, 'calories_field', '2000');
  await _enterTextSafely(tester, 'protein_field', '150');
  await _enterTextSafely(tester, 'carbs_field', '200');
  await _enterTextSafely(tester, 'fat_field', '70');

  // Save template
  if (find.text('Save Template').evaluate().isNotEmpty) {
    await tester.tap(find.text('Save Template'));
    await tester.pumpAndSettle();
  }
}

/// Navigate to student management
Future<void> _navigateToStudentManagement(WidgetTester tester) async {
  if (find.text('Students').evaluate().isNotEmpty) {
    await tester.tap(find.text('Students'));
  } else if (find.text('My Students').evaluate().isNotEmpty) {
    await tester.tap(find.text('My Students'));
  }
  await tester.pumpAndSettle();
}

/// Find and select student
Future<void> _findAndSelectStudent(WidgetTester tester, String studentName) async {
  // Wait for student list to load
  await tester.pumpAndSettle(const Duration(seconds: 3));

  // Find student
  if (find.textContaining(studentName).evaluate().isNotEmpty) {
    await tester.tap(find.textContaining(studentName).first);
  } else if (find.byType(Card).evaluate().isNotEmpty) {
    await tester.tap(find.byType(Card).first);
  }
  await tester.pumpAndSettle();
}

/// Test assign from existing template
Future<void> _testAssignFromExistingTemplate(WidgetTester tester) async {
  if (find.text('Assign Template').evaluate().isNotEmpty) {
    await tester.tap(find.text('Assign Template'));
    await tester.pumpAndSettle();

    // Select existing template
    if (find.text('Test Workout Plan').evaluate().isNotEmpty) {
      await tester.tap(find.text('Test Workout Plan'));
      await tester.pump();
    }

    if (find.text('Assign').evaluate().isNotEmpty) {
      await tester.tap(find.text('Assign'));
      await tester.pumpAndSettle();
    }
  }
}

/// Test create new template during assignment
Future<void> _testCreateNewTemplateDuringAssignment(WidgetTester tester) async {
  if (find.text('Create New').evaluate().isNotEmpty) {
    await tester.tap(find.text('Create New'));
    await tester.pumpAndSettle();

    // Create new template inline
    await _createWorkoutTemplate(tester);
  }
}

/// Test assign newly created inline template
Future<void> _testAssignNewlyCreatedInlineTemplate(WidgetTester tester) async {
  // Should automatically assign the newly created template
  // Verify assignment was successful
  expect(find.text('Template Assigned'), findsOneWidget);
}

// ==========================================
// ADDITIONAL FEATURE TESTS
// ==========================================

/// Phase 14: Test complete workout session flow
Future<void> _testCompleteWorkoutSession(WidgetTester tester) async {
  debugPrint('🏋️ Testing complete workout session flow...');

  try {
    // Should already be on dashboard, go to exercise tab
    await tester.tap(find.text('Exercise'));
    await tester.pumpAndSettle();

    // Start workout session
    if (find.text('Start Workout').evaluate().isNotEmpty) {
      await tester.tap(find.text('Start Workout'));
      await tester.pumpAndSettle(const Duration(seconds: 3));
    }

    // Verify workout session screen
    expect(find.text('Workout Session'), findsOneWidget);
    expect(find.text('Exercise 1'), findsOneWidget);

    // Complete first set
    if (find.text('Complete Set').evaluate().isNotEmpty) {
      await tester.tap(find.text('Complete Set'));
      await tester.pump();
    }

    // Verify rest timer appears
    expect(find.textContaining('Rest'), findsOneWidget);

    // Skip rest or wait
    if (find.text('Skip Rest').evaluate().isNotEmpty) {
      await tester.tap(find.text('Skip Rest'));
      await tester.pump();
    }

    // Complete remaining sets
    for (int i = 0; i < 2; i++) {
      if (find.text('Complete Set').evaluate().isNotEmpty) {
        await tester.tap(find.text('Complete Set'));
        await tester.pump();

        if (find.text('Skip Rest').evaluate().isNotEmpty) {
          await tester.tap(find.text('Skip Rest'));
          await tester.pump();
        }
      }
    }

    // Move to next exercise or finish workout
    if (find.text('Next Exercise').evaluate().isNotEmpty) {
      await tester.tap(find.text('Next Exercise'));
      await tester.pumpAndSettle();
    } else if (find.text('Finish Workout').evaluate().isNotEmpty) {
      await tester.tap(find.text('Finish Workout'));
      await tester.pumpAndSettle();
    }

    // Verify workout completion
    expect(find.text('Workout Completed'), findsOneWidget);
    expect(find.text('Great job!'), findsOneWidget);

    // View workout summary
    if (find.text('View Summary').evaluate().isNotEmpty) {
      await tester.tap(find.text('View Summary'));
      await tester.pumpAndSettle();
    }

    debugPrint('✅ Complete workout session flow successful');
  } catch (e) {
    debugPrint('❌ Complete workout session flow failed: $e');
    rethrow;
  }
}

/// Phase 15: Test nutrition logging flow
Future<void> _testNutritionLoggingFlow(WidgetTester tester) async {
  debugPrint('🥗 Testing nutrition logging flow...');

  try {
    // Navigate to nutrition tab
    await tester.tap(find.text('Nutrition'));
    await tester.pumpAndSettle();

    // Verify nutrition dashboard
    expect(find.text('Today\'s Nutrition'), findsOneWidget);
    expect(find.text('Calories'), findsOneWidget);
    expect(find.text('Protein'), findsOneWidget);

    // Add breakfast
    if (find.text('Add Meal').evaluate().isNotEmpty) {
      await tester.tap(find.text('Add Meal'));
      await tester.pumpAndSettle();
    }

    // Search for food
    if (find.byKey(const Key('food_search_field')).evaluate().isNotEmpty) {
      await tester.enterText(find.byKey(const Key('food_search_field')), 'Apple');
      await tester.pump();
    }

    // Select food from search results
    if (find.textContaining('Apple').evaluate().isNotEmpty) {
      await tester.tap(find.textContaining('Apple').first);
      await tester.pump();
    }

    // Set portion size
    if (find.byKey(const Key('portion_field')).evaluate().isNotEmpty) {
      await tester.enterText(find.byKey(const Key('portion_field')), '1');
      await tester.pump();
    }

    // Add to log
    if (find.text('Add to Log').evaluate().isNotEmpty) {
      await tester.tap(find.text('Add to Log'));
      await tester.pumpAndSettle();
    }

    // Verify meal was added
    expect(find.textContaining('Apple'), findsOneWidget);

    // Check macro updates
    expect(find.textContaining('kcal'), findsOneWidget);

    // Add water intake
    if (find.byIcon(Icons.local_drink).evaluate().isNotEmpty) {
      await tester.tap(find.byIcon(Icons.local_drink));
      await tester.pump();
    }

    // Log another meal (lunch)
    if (find.text('Add Meal').evaluate().isNotEmpty) {
      await tester.tap(find.text('Add Meal'));
      await tester.pumpAndSettle();

      // Quick add from recent or favorites
      if (find.text('Recent Foods').evaluate().isNotEmpty) {
        await tester.tap(find.text('Recent Foods'));
        await tester.pump();

        if (find.byType(Card).evaluate().isNotEmpty) {
          await tester.tap(find.byType(Card).first);
          await tester.pump();

          if (find.text('Add to Log').evaluate().isNotEmpty) {
            await tester.tap(find.text('Add to Log'));
            await tester.pumpAndSettle();
          }
        }
      }
    }

    // View nutrition insights
    if (find.text('View Insights').evaluate().isNotEmpty) {
      await tester.tap(find.text('View Insights'));
      await tester.pumpAndSettle();

      // Verify insights screen
      expect(find.text('Nutrition Insights'), findsOneWidget);
    }

    debugPrint('✅ Nutrition logging flow successful');
  } catch (e) {
    debugPrint('❌ Nutrition logging flow failed: $e');
    rethrow;
  }
}

/// Phase 16: Test progress tracking and photos
Future<void> _testProgressTrackingAndPhotos(WidgetTester tester) async {
  debugPrint('📸 Testing progress tracking and photo upload...');

  try {
    // Navigate to progress tab
    await tester.tap(find.text('Progress'));
    await tester.pumpAndSettle();

    // Verify progress dashboard
    expect(find.text('Your Progress'), findsOneWidget);
    expect(find.text('Weight Progress'), findsOneWidget);

    // Add weight measurement
    if (find.text('Add Weight').evaluate().isNotEmpty) {
      await tester.tap(find.text('Add Weight'));
      await tester.pumpAndSettle();

      // Enter weight
      if (find.byKey(const Key('weight_input')).evaluate().isNotEmpty) {
        await tester.enterText(find.byKey(const Key('weight_input')), '70.5');
        await tester.pump();
      }

      // Save weight
      if (find.text('Save').evaluate().isNotEmpty) {
        await tester.tap(find.text('Save'));
        await tester.pumpAndSettle();
      }
    }

    // Add body measurements
    if (find.text('Add Measurements').evaluate().isNotEmpty) {
      await tester.tap(find.text('Add Measurements'));
      await tester.pumpAndSettle();

      // Fill measurements
      await _enterTextSafely(tester, 'chest_field', '95');
      await _enterTextSafely(tester, 'waist_field', '80');
      await _enterTextSafely(tester, 'hips_field', '90');

      if (find.text('Save Measurements').evaluate().isNotEmpty) {
        await tester.tap(find.text('Save Measurements'));
        await tester.pumpAndSettle();
      }
    }

    // Add progress photo
    if (find.text('Add Photo').evaluate().isNotEmpty) {
      await tester.tap(find.text('Add Photo'));
      await tester.pumpAndSettle();

      // Simulate photo selection (mock)
      if (find.text('Camera').evaluate().isNotEmpty) {
        await tester.tap(find.text('Camera'));
        await tester.pumpAndSettle();

        // Mock photo capture
        if (find.text('Capture').evaluate().isNotEmpty) {
          await tester.tap(find.text('Capture'));
          await tester.pumpAndSettle();
        }

        // Confirm photo
        if (find.text('Use Photo').evaluate().isNotEmpty) {
          await tester.tap(find.text('Use Photo'));
          await tester.pumpAndSettle();
        }
      }
    }

    // View progress charts
    if (find.text('View Charts').evaluate().isNotEmpty) {
      await tester.tap(find.text('View Charts'));
      await tester.pumpAndSettle();

      // Verify charts are displayed
      expect(find.text('Weight Chart'), findsOneWidget);
      expect(find.text('Measurements Chart'), findsOneWidget);
    }

    // Check workout streak
    expect(find.textContaining('Workout Streak'), findsOneWidget);
    expect(find.textContaining('days'), findsOneWidget);

    debugPrint('✅ Progress tracking and photos successful');
  } catch (e) {
    debugPrint('❌ Progress tracking and photos failed: $e');
    rethrow;
  }
}

/// Phase 17: Test explore tab features
Future<void> _testExploreTabFeatures(WidgetTester tester) async {
  debugPrint('🌟 Testing explore tab features...');

  try {
    // Navigate to explore tab
    await tester.tap(find.text('Explore'));
    await tester.pumpAndSettle();

    // Verify explore content
    expect(find.text('Discover'), findsOneWidget);

    // Check featured content sections
    expect(find.text('Featured Workouts'), findsOneWidget);
    expect(find.text('Nutrition Guides'), findsOneWidget);
    expect(find.text('Success Stories'), findsOneWidget);

    // Browse featured workouts
    if (find.text('Featured Workouts').evaluate().isNotEmpty) {
      await tester.tap(find.text('Featured Workouts'));
      await tester.pumpAndSettle();

      // Verify workout list
      expect(find.byType(Card), findsAtLeast(1));

      // View workout details
      if (find.byType(Card).evaluate().isNotEmpty) {
        await tester.tap(find.byType(Card).first);
        await tester.pumpAndSettle();

        // Verify workout detail screen
        expect(find.text('Workout Details'), findsOneWidget);
        expect(find.text('Exercises'), findsOneWidget);

        // Go back
        if (find.byIcon(Icons.arrow_back).evaluate().isNotEmpty) {
          await tester.tap(find.byIcon(Icons.arrow_back));
          await tester.pumpAndSettle();
        }
      }
    }

    // Browse nutrition guides
    if (find.text('Nutrition Guides').evaluate().isNotEmpty) {
      await tester.tap(find.text('Nutrition Guides'));
      await tester.pumpAndSettle();

      // Verify nutrition guide list
      expect(find.byType(Card), findsAtLeast(1));

      // View guide details
      if (find.byType(Card).evaluate().isNotEmpty) {
        await tester.tap(find.byType(Card).first);
        await tester.pumpAndSettle();

        // Verify guide detail screen
        expect(find.text('Nutrition Guide'), findsOneWidget);

        // Go back
        if (find.byIcon(Icons.arrow_back).evaluate().isNotEmpty) {
          await tester.tap(find.byIcon(Icons.arrow_back));
          await tester.pumpAndSettle();
        }
      }
    }

    // Search functionality
    if (find.byIcon(Icons.search).evaluate().isNotEmpty) {
      await tester.tap(find.byIcon(Icons.search));
      await tester.pumpAndSettle();

      // Enter search term
      if (find.byKey(const Key('search_field')).evaluate().isNotEmpty) {
        await tester.enterText(find.byKey(const Key('search_field')), 'cardio');
        await tester.pump();

        // Verify search results
        expect(find.textContaining('cardio'), findsAtLeast(1));
      }
    }

    debugPrint('✅ Explore tab features successful');
  } catch (e) {
    debugPrint('❌ Explore tab features failed: $e');
    rethrow;
  }
}

/// Phase 18: Test instructor dashboard features
Future<void> _testInstructorDashboardFeatures(WidgetTester tester, String instructorEmail, String studentEmail) async {
  debugPrint('👨‍🏫 Testing instructor dashboard features...');

  try {
    // Login as instructor
    await _loginUser(tester, instructorEmail, 'InstructorPass123!');

    // Verify instructor dashboard
    expect(find.text('Instructor Dashboard'), findsOneWidget);
    expect(find.text('My Students'), findsOneWidget);
    expect(find.text('Templates'), findsOneWidget);

    // Check student list
    if (find.text('My Students').evaluate().isNotEmpty) {
      await tester.tap(find.text('My Students'));
      await tester.pumpAndSettle();

      // Verify student appears in list
      expect(find.textContaining('Test Student'), findsOneWidget);

      // View student details
      if (find.textContaining('Test Student').evaluate().isNotEmpty) {
        await tester.tap(find.textContaining('Test Student').first);
        await tester.pumpAndSettle();

        // Verify student detail screen
        expect(find.text('Student Profile'), findsOneWidget);
        expect(find.text('Progress'), findsOneWidget);
        expect(find.text('Workout History'), findsOneWidget);

        // Check student progress
        if (find.text('View Progress').evaluate().isNotEmpty) {
          await tester.tap(find.text('View Progress'));
          await tester.pumpAndSettle();

          // Verify progress data
          expect(find.text('Weight Progress'), findsOneWidget);
          expect(find.text('Workout Completion'), findsOneWidget);
        }

        // Send message to student
        if (find.text('Send Message').evaluate().isNotEmpty) {
          await tester.tap(find.text('Send Message'));
          await tester.pumpAndSettle();

          // Type message
          if (find.byKey(const Key('message_field')).evaluate().isNotEmpty) {
            await tester.enterText(find.byKey(const Key('message_field')), 'Great progress! Keep it up!');
            await tester.pump();
          }

          // Send message
          if (find.text('Send').evaluate().isNotEmpty) {
            await tester.tap(find.text('Send'));
            await tester.pumpAndSettle();
          }
        }

        // Go back to student list
        if (find.byIcon(Icons.arrow_back).evaluate().isNotEmpty) {
          await tester.tap(find.byIcon(Icons.arrow_back));
          await tester.pumpAndSettle();
        }
      }
    }

    // Check analytics
    if (find.text('Analytics').evaluate().isNotEmpty) {
      await tester.tap(find.text('Analytics'));
      await tester.pumpAndSettle();

      // Verify analytics dashboard
      expect(find.text('Student Analytics'), findsOneWidget);
      expect(find.text('Engagement Rate'), findsOneWidget);
      expect(find.text('Completion Rate'), findsOneWidget);
    }

    // Manage templates
    if (find.text('Templates').evaluate().isNotEmpty) {
      await tester.tap(find.text('Templates'));
      await tester.pumpAndSettle();

      // Verify template list
      expect(find.text('My Templates'), findsOneWidget);
      expect(find.text('Test Workout Plan'), findsOneWidget);

      // Edit template
      if (find.byIcon(Icons.edit).evaluate().isNotEmpty) {
        await tester.tap(find.byIcon(Icons.edit).first);
        await tester.pumpAndSettle();

        // Modify template
        if (find.byKey(const Key('template_name_field')).evaluate().isNotEmpty) {
          await tester.enterText(find.byKey(const Key('template_name_field')), 'Updated Workout Plan');
          await tester.pump();
        }

        // Save changes
        if (find.text('Save Changes').evaluate().isNotEmpty) {
          await tester.tap(find.text('Save Changes'));
          await tester.pumpAndSettle();
        }
      }
    }

    debugPrint('✅ Instructor dashboard features successful');
  } catch (e) {
    debugPrint('❌ Instructor dashboard features failed: $e');
    rethrow;
  }
}

/// Phase 19: Final system verification
Future<void> _testFinalSystemVerification(WidgetTester tester, String studentEmail, String instructorEmail) async {
  debugPrint('🔍 Testing final system verification...');

  try {
    // Login as student for final verification
    await _loginUser(tester, studentEmail, 'StudentPass123!');

    // Verify all tabs are functional
    final tabs = ['Exercise', 'Nutrition', 'Progress', 'Explore'];

    for (final tab in tabs) {
      await tester.tap(find.text(tab));
      await tester.pumpAndSettle();

      // Verify tab content loads
      expect(find.byType(CircularProgressIndicator), findsNothing);

      debugPrint('✅ $tab tab verified');
    }

    // Test app state persistence (hot restart simulation)
    debugPrint('🔄 Testing app state persistence...');
    app.main();
    await tester.pumpAndSettle(const Duration(seconds: 5));

    // Should maintain login state and go to dashboard
    expect(find.byType(BottomNavigationBar), findsOneWidget);

    // Test logout and login cycle
    if (find.byIcon(Icons.logout).evaluate().isNotEmpty) {
      await tester.tap(find.byIcon(Icons.logout));
      await tester.pumpAndSettle();

      // Should go to auth screen
      expect(find.text('Login'), findsOneWidget);

      // Login again
      await _loginUser(tester, studentEmail, 'StudentPass123!');

      // Should go back to dashboard
      expect(find.byType(BottomNavigationBar), findsOneWidget);
    }

    // Test error handling
    debugPrint('🚨 Testing error handling...');

    // Simulate network error scenario
    if (find.text('Refresh').evaluate().isNotEmpty) {
      await tester.tap(find.text('Refresh'));
      await tester.pumpAndSettle();

      // Should handle gracefully
      expect(find.text('Something went wrong'), findsNothing);
    }

    // Final database integrity check
    debugPrint('🔍 Final database integrity check...');

    // Verify all test data exists and is consistent
    await _verifyFinalDatabaseState(studentEmail, instructorEmail);

    debugPrint('✅ Final system verification successful');
  } catch (e) {
    debugPrint('❌ Final system verification failed: $e');
    rethrow;
  }
}

// ==========================================
// DATABASE VALIDATION FUNCTIONS
// ==========================================

/// Verify instructor in database
Future<void> _verifyInstructorInDatabase(SupabaseClient supabase, String email, {required String expectedStatus}) async {
  debugPrint('🔍 Verifying instructor in database...');

  try {
    // Get instructor profile
    final profile = await supabase
        .from('profiles')
        .select('id, email, name, surname, role')
        .eq('email', email)
        .maybeSingle();

    expect(profile, isNotNull, reason: 'Instructor profile should exist');
    expect(profile!['role'], 'instructor');

    // Get instructor record
    final instructor = await supabase
        .from('instructors')
        .select('id, profile_id, approval_status, is_public, is_active')
        .eq('profile_id', profile['id'])
        .maybeSingle();

    if (expectedStatus == 'not_submitted') {
      // Instructor might not have instructor record yet
      if (instructor != null) {
        expect(instructor['approval_status'], expectedStatus);
      }
    } else {
      expect(instructor, isNotNull, reason: 'Instructor record should exist');
      expect(instructor!['approval_status'], expectedStatus);
    }

    debugPrint('✅ Instructor database verification successful');
  } catch (e) {
    debugPrint('❌ Instructor database verification failed: $e');
    rethrow;
  }
}

/// Verify student in database
Future<void> _verifyStudentInDatabase(SupabaseClient supabase, String email) async {
  debugPrint('🔍 Verifying student in database...');

  try {
    final profile = await supabase
        .from('profiles')
        .select('id, email, name, surname, role')
        .eq('email', email)
        .single();

    expect(profile['role'], 'student');
    expect(profile['email'], email);

    debugPrint('✅ Student database verification successful');
  } catch (e) {
    debugPrint('❌ Student database verification failed: $e');
    rethrow;
  }
}

/// Verify instructor profile complete
Future<void> _verifyInstructorProfileComplete(SupabaseClient supabase, String email) async {
  debugPrint('🔍 Verifying instructor profile completion...');

  try {
    final profile = await supabase
        .from('profiles')
        .select('id')
        .eq('email', email)
        .single();

    // Check instructor record
    final instructor = await supabase
        .from('instructors')
        .select('*')
        .eq('profile_id', profile['id'])
        .single();

    expect(instructor['title'], isNotNull);
    expect(instructor['experience_years'], greaterThan(0));

    // Check work history
    final workHistory = await supabase
        .from('instructor_work_history')
        .select('*')
        .eq('instructor_id', instructor['id']);

    expect(workHistory, isNotEmpty, reason: 'Work history should exist');

    // Check certifications
    final certifications = await supabase
        .from('instructor_certifications')
        .select('*')
        .eq('instructor_id', instructor['id']);

    expect(certifications, isNotEmpty, reason: 'Certifications should exist');

    // Check pricing config
    final pricing = await supabase
        .from('instructor_subscription_configs')
        .select('*')
        .eq('instructor_id', instructor['id'])
        .maybeSingle();

    expect(pricing, isNotNull, reason: 'Pricing config should exist');

    debugPrint('✅ Instructor profile completion verification successful');
  } catch (e) {
    debugPrint('❌ Instructor profile completion verification failed: $e');
    rethrow;
  }
}

/// Reject instructor in database
Future<void> _rejectInstructorInDatabase(SupabaseClient supabase, String email) async {
  debugPrint('❌ Rejecting instructor in database...');

  try {
    final profile = await supabase
        .from('profiles')
        .select('id')
        .eq('email', email)
        .single();

    await supabase
        .from('instructors')
        .update({
          'approval_status': 'rejected',
          'is_public': false,
          'rejected_at': DateTime.now().toIso8601String(),
        })
        .eq('profile_id', profile['id']);

    debugPrint('✅ Instructor rejected in database');
  } catch (e) {
    debugPrint('❌ Failed to reject instructor: $e');
    rethrow;
  }
}

/// Approve instructor in database
Future<void> _approveInstructorInDatabase(SupabaseClient supabase, String email) async {
  debugPrint('✅ Approving instructor in database...');

  try {
    final profile = await supabase
        .from('profiles')
        .select('id')
        .eq('email', email)
        .single();

    await supabase
        .from('instructors')
        .update({
          'approval_status': 'approved',
          'is_public': true,
          'is_active': true,
          'approved_at': DateTime.now().toIso8601String(),
        })
        .eq('profile_id', profile['id']);

    debugPrint('✅ Instructor approved in database');
  } catch (e) {
    debugPrint('❌ Failed to approve instructor: $e');
    rethrow;
  }
}

/// Verify enrollment in database
Future<void> _verifyEnrollmentInDatabase(SupabaseClient supabase, String studentEmail, String instructorEmail) async {
  debugPrint('🔍 Verifying enrollment in database...');

  try {
    final studentProfile = await supabase
        .from('profiles')
        .select('id')
        .eq('email', studentEmail)
        .single();

    final instructorProfile = await supabase
        .from('profiles')
        .select('id')
        .eq('email', instructorEmail)
        .single();

    final instructor = await supabase
        .from('instructors')
        .select('id')
        .eq('profile_id', instructorProfile['id'])
        .single();

    final enrollment = await supabase
        .from('enrollments')
        .select('*')
        .eq('student_id', studentProfile['id'])
        .eq('instructor_id', instructor['id'])
        .single();

    expect(enrollment['is_active'], true);
    expect(enrollment['amount_paid'], greaterThan(0));

    debugPrint('✅ Enrollment database verification successful');
  } catch (e) {
    debugPrint('❌ Enrollment database verification failed: $e');
    rethrow;
  }
}

/// Verify user profile in database
Future<void> _verifyUserProfileInDatabase(SupabaseClient supabase, String email) async {
  debugPrint('🔍 Verifying user profile in database...');

  try {
    final profile = await supabase
        .from('profiles')
        .select('id')
        .eq('email', email)
        .single();

    final userProfile = await supabase
        .from('user_profiles')
        .select('*')
        .eq('id', profile['id'])
        .single();

    expect(userProfile['height'], greaterThan(0));
    expect(userProfile['weight'], greaterThan(0));

    debugPrint('✅ User profile database verification successful');
  } catch (e) {
    debugPrint('❌ User profile database verification failed: $e');
    rethrow;
  }
}

/// Verify templates in database
Future<void> _verifyTemplatesInDatabase(SupabaseClient supabase, String instructorEmail) async {
  debugPrint('🔍 Verifying templates in database...');

  try {
    final profile = await supabase
        .from('profiles')
        .select('id')
        .eq('email', instructorEmail)
        .single();

    final instructor = await supabase
        .from('instructors')
        .select('id')
        .eq('profile_id', profile['id'])
        .single();

    // Check workout templates
    final workoutTemplates = await supabase
        .from('workout_plan_templates')
        .select('*')
        .eq('instructor_id', instructor['id']);

    expect(workoutTemplates, isNotEmpty, reason: 'Workout templates should exist');

    // Check nutrition templates (if table exists)
    try {
      final nutritionTemplates = await supabase
          .from('nutrition_plan_templates')
          .select('*')
          .eq('instructor_id', instructor['id']);

      expect(nutritionTemplates, isNotEmpty, reason: 'Nutrition templates should exist');
    } catch (e) {
      debugPrint('⚠️ Nutrition templates table might not exist: $e');
    }

    debugPrint('✅ Templates database verification successful');
  } catch (e) {
    debugPrint('❌ Templates database verification failed: $e');
    rethrow;
  }
}

/// Verify assignments in database
Future<void> _verifyAssignmentsInDatabase(SupabaseClient supabase, String studentEmail) async {
  debugPrint('🔍 Verifying assignments in database...');

  try {
    final profile = await supabase
        .from('profiles')
        .select('id')
        .eq('email', studentEmail)
        .single();

    // Check workout plan assignments
    final workoutAssignments = await supabase
        .from('student_workout_plans')
        .select('*')
        .eq('student_id', profile['id']);

    expect(workoutAssignments, isNotEmpty, reason: 'Workout assignments should exist');

    // Check nutrition plan assignments (if table exists)
    try {
      final nutritionAssignments = await supabase
          .from('student_nutrition_plans')
          .select('*')
          .eq('student_id', profile['id']);

      expect(nutritionAssignments, isNotEmpty, reason: 'Nutrition assignments should exist');
    } catch (e) {
      debugPrint('⚠️ Nutrition assignments table might not exist: $e');
    }

    debugPrint('✅ Assignments database verification successful');
  } catch (e) {
    debugPrint('❌ Assignments database verification failed: $e');
    rethrow;
  }
}

// ==========================================
// ADDITIONAL HELPER FUNCTIONS
// ==========================================

/// Test instructor resubmission
Future<void> _testInstructorResubmission(WidgetTester tester, String instructorEmail) async {
  debugPrint('🔄 Testing instructor resubmission...');

  try {
    // Login as instructor
    await _loginUser(tester, instructorEmail, 'InstructorPass123!');

    // Should see rejection message
    expect(find.textContaining('rejected'), findsOneWidget);

    // Resubmit
    if (find.text('Resubmit').evaluate().isNotEmpty) {
      await tester.tap(find.text('Resubmit'));
      await tester.pumpAndSettle();
    }

    debugPrint('✅ Instructor resubmission successful');
  } catch (e) {
    debugPrint('❌ Instructor resubmission failed: $e');
    rethrow;
  }
}

/// Test instructor now visible
Future<void> _testInstructorNowVisible(WidgetTester tester, String instructorEmail) async {
  debugPrint('👁️ Testing instructor now visible...');

  try {
    // Get student email from global scope
    final studentEmail = testStudentEmail;

    // Login as student to check visibility
    await _loginUser(tester, studentEmail, 'StudentPass123!');

    // Navigate to featured instructors
    await _navigateToFeaturedInstructors(tester);

    // Wait for list to load
    await tester.pumpAndSettle(const Duration(seconds: 3));

    // Verify instructor is now visible
    expect(find.textContaining('Test Instructor'), findsOneWidget);

    debugPrint('✅ Instructor visibility verification successful');
  } catch (e) {
    debugPrint('❌ Instructor visibility verification failed: $e');
    rethrow;
  }
}

/// Verify final database state
Future<void> _verifyFinalDatabaseState(String studentEmail, String instructorEmail) async {
  debugPrint('🔍 Verifying final database state...');

  try {
    // Get global supabase instance
    final supabase = SupabaseClient(
      'https://wrevdlggsevlckprjrwm.supabase.co',
      'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6IndyZXZkbGdnc2V2bGNrcHJqcndtIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDEwMjk3MTcsImV4cCI6MjA1NjYwNTcxN30.KRdBVWPAqDSCsyvYBE3ntrPqpH09KzUmnzbYONAFtzY',
    );

    // Verify student profile completeness
    final studentProfile = await supabase
        .from('profiles')
        .select('id')
        .eq('email', studentEmail)
        .single();

    final userProfile = await supabase
        .from('user_profiles')
        .select('*')
        .eq('id', studentProfile['id'])
        .single();

    expect(userProfile['height'], greaterThan(0));
    expect(userProfile['weight'], greaterThan(0));

    // Verify instructor profile completeness
    final instructorProfile = await supabase
        .from('profiles')
        .select('id')
        .eq('email', instructorEmail)
        .single();

    final instructor = await supabase
        .from('instructors')
        .select('*')
        .eq('profile_id', instructorProfile['id'])
        .single();

    expect(instructor['approval_status'], 'approved');
    expect(instructor['is_public'], true);

    // Verify enrollment relationship
    final enrollment = await supabase
        .from('enrollments')
        .select('*')
        .eq('student_id', studentProfile['id'])
        .eq('instructor_id', instructor['id'])
        .single();

    expect(enrollment['is_active'], true);

    // Verify template assignments
    final workoutAssignments = await supabase
        .from('student_workout_plans')
        .select('*')
        .eq('student_id', studentProfile['id']);

    expect(workoutAssignments, isNotEmpty);

    // Verify workout session data (if any)
    final workoutSessions = await supabase
        .from('workout_sessions')
        .select('*')
        .eq('student_id', studentProfile['id']);

    // Should have at least one session from the workout test
    expect(workoutSessions, isNotEmpty);

    // Verify nutrition logs (if any)
    final nutritionLogs = await supabase
        .from('nutrition_logs')
        .select('*')
        .eq('user_id', studentProfile['id']);

    // Should have nutrition entries from the nutrition test
    expect(nutritionLogs, isNotEmpty);

    // Verify progress data
    final progressPhotos = await supabase
        .from('progress_photos')
        .select('*')
        .eq('user_id', studentProfile['id']);

    // Should have progress photos from the progress test
    expect(progressPhotos, isNotEmpty);

    debugPrint('✅ Final database state verification successful');
  } catch (e) {
    debugPrint('❌ Final database state verification failed: $e');
    rethrow;
  }
}

/// Cleanup test data
Future<void> _cleanupTestData(SupabaseClient supabase, String instructorId, String studentId, String instructorEmail, String studentEmail) async {
  try {
    // Get profile IDs
    final instructorProfile = await supabase
        .from('profiles')
        .select('id')
        .eq('email', instructorEmail)
        .maybeSingle();

    final studentProfile = await supabase
        .from('profiles')
        .select('id')
        .eq('email', studentEmail)
        .maybeSingle();

    if (instructorProfile != null) {
      // Get instructor record
      final instructor = await supabase
          .from('instructors')
          .select('id')
          .eq('profile_id', instructorProfile['id'])
          .maybeSingle();

      if (instructor != null) {
        // Delete instructor-related data
        await supabase.from('instructor_subscription_configs').delete().eq('instructor_id', instructor['id']);
        await supabase.from('instructor_certifications').delete().eq('instructor_id', instructor['id']);
        await supabase.from('instructor_work_history').delete().eq('instructor_id', instructor['id']);
        await supabase.from('workout_plan_templates').delete().eq('instructor_id', instructor['id']);

        // Delete enrollments
        if (studentProfile != null) {
          await supabase.from('enrollments')
              .delete()
              .eq('student_id', studentProfile['id'])
              .eq('instructor_id', instructor['id']);
        }

        // Delete instructor record
        await supabase.from('instructors').delete().eq('id', instructor['id']);
      }

      // Delete instructor profile
      await supabase.from('profiles').delete().eq('id', instructorProfile['id']);
    }

    if (studentProfile != null) {
      // Delete student-related data
      await supabase.from('user_profiles').delete().eq('id', studentProfile['id']);
      await supabase.from('student_workout_plans').delete().eq('student_id', studentProfile['id']);

      // Delete student profile
      await supabase.from('profiles').delete().eq('id', studentProfile['id']);
    }

    debugPrint('✅ Test data cleanup completed');
  } catch (e) {
    debugPrint('⚠️ Cleanup error (non-critical): $e');
  }
}
