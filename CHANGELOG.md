
# Changelog

All notable changes to the FitGo app will be documented in this file.

The format is based on [Keep a Changelog](https://keepachangelog.com/en/1.0.0/),
and this project adheres to [Semantic Versioning](https://semver.org/spec/v2.0.0.html).

## [Unreleased]

### Added
- **Course Visibility Management**: Instructors can now hide their courses from new students while continuing to serve existing enrolled students
- Database functions for safe course hiding and reactivation with student enrollment validation
- Enhanced RLS policies to support course visibility for enrolled students only
- UI components for course status management in subscription plan screen
- Comprehensive course status tracking (active, hidden, suspended)

### Enhanced
- Instructor subscription management with course visibility controls
- Student course list filtering to respect instructor course visibility settings
- Database schema with course status fields and proper indexing

### Fixed
- Course list filtering now properly handles hidden courses while maintaining access for enrolled students
- Instructor profile repository methods updated for course visibility management

## [1.2.1] - 2025-07-19

### 🐛 Bug Fixes
- **Registration Role Selection**: Fixed critical bug where user role selection was not working correctly in register form
  - Fixed color logic in register bottom sheet for Athlete/Trainer selection
  - Removed duplicate instructor record creation in auth repository
  - Supabase triggers now handle instructor record creation automatically
  - Users selecting "Athlete" are now correctly registered as students

### 🔧 Code Improvements
- **Auth Repository Cleanup**: Removed redundant `_createInstructorRecord` function
- **Import Optimization**: Removed unused UserType import from auth repository
- **Database Consistency**: Fixed role assignment logic to prevent incorrect instructor registrations

### Added
- **Student Profile Navigation & Display**: Enhanced instructor-student interaction system
  - **Navigation Integration**: Home tab "View" button now properly navigates to student profile detail screen
  - **Profile Form Data Display**: Real student data from user_profiles table (weight, height, fitness goals, medical conditions)
  - **Body Photos Display**: Instructor can view student's initial body photos (front, side, back) with proper photo gallery
  - **Profile Form Input Validation**: Enhanced weight/height input formatting (XX for weight, XXX for height with decimal support)
- **Feedback System Implementation**: Complete student-instructor feedback system with photo uploads and status management
  - **Database Schema**: Created `feedback` and `feedback_photos` tables with proper RLS policies
    - Student-instructor feedback relationship with enrollment tracking
    - Status management: waiting → reviewed → closed workflow
    - One-time instructor response system with automatic status updates
    - Photo upload support (front, side, back body photos) with organized storage
    - Comprehensive RLS policies for data security and access control
  - **Storage Integration**: Added `feedback_photos` bucket with proper access policies
    - Students can upload/view/delete their own feedback photos (only for waiting feedback)
    - Instructors can view student feedback photos for their enrolled students
    - Organized storage path: `{userId}/feedback/{feedbackId}/{photoType}.jpg`
  - **Flutter Implementation**: Complete feedback feature with modern UI/UX
    - **Domain Models**: Comprehensive feedback models with Freezed integration
      - FeedbackStatus enum (waiting, reviewed, closed) with Turkish localization
      - FeedbackPhoto model with upload progress tracking
      - Form models for creation and response with validation
    - **Repository Layer**: Full CRUD operations with photo upload integration
      - Photo upload with progress tracking and error handling
      - Feedback creation with enrollment validation
      - Student feedback history with pagination
      - Instructor feedback queue with priority sorting
      - One-time response submission with automatic status updates
    - **Provider Layer**: Riverpod state management with reactive updates
      - Student feedback history provider with infinite scroll
      - Instructor feedback queue provider with waiting/reviewed separation
      - Feedback detail provider with response form management
      - Feedback form provider with photo upload state management
    - **Student UI**: Modern feedback interface with photo upload capabilities
      - Feedback creation screen with instructor selection and photo uploads
      - Feedback history screen with status indicators and search
      - Feedback detail screen with photo gallery and instructor responses
      - Photo upload with camera/gallery selection and progress indicators
    - **Instructor UI**: Professional feedback management interface
      - Feedback queue screen with priority indicators and waiting time tracking
      - Instructor feedback detail screen with response form
      - Photo viewing capabilities with full-screen dialog
      - One-time response system with automatic feedback closure
  - **Business Logic**: Comprehensive feedback workflow management
    - One feedback per day per instructor limitation
    - Enrollment validation for feedback creation
    - Automatic status updates when instructor responds
    - Photo upload validation (5MB limit, image formats only)
    - Waiting time tracking with color-coded urgency indicators
- **Pricing Structure Refactor (Migration 002)**: Complete overhaul of instructor pricing system
  - **instructor_subscription_configs**: Complete JSON-to-columns refactor
    - **Removed**: `basic_plan_pricing`, `premium_plan_pricing` JSON fields (no longer used)
    - **Added**: Individual pricing columns for multi-duration plans
      - `basic_plan_monthly_price`: Base monthly price calculated from desired earnings
      - `basic_plan_6month_price`: 6-month plan with 5% discount
      - `basic_plan_yearly_price`: Yearly plan with 10% discount
      - `premium_plan_*_price`: Premium plans at 1.5x basic plan prices
    - `desired_monthly_earnings`: Instructor's target income (before platform commission)
    - `basic_plan_features/premium_plan_features`: Array-based feature lists
    - `basic_plan_description/premium_plan_description`: Plan descriptions
  - **admin_settings**: Commission-based pricing configuration
    - Commission rates: `platform_commission_rate` (15%), `instructor_commission_rate` (85%)
    - Pricing rules: `basic_plan_6month_discount` (5%), `basic_plan_yearly_discount` (10%)
    - Premium multiplier: `premium_plan_multiplier` (1.5x basic plan)
    - Dynamic configuration without requiring code deployment
  - **Automated Price Calculation**: `calculate_plan_prices_from_earnings()` function
    - Input: Instructor's desired monthly earnings (e.g., 1000 TL)
    - Output: All plan prices calculated with commission (e.g., 1176.47 TL monthly)
    - Commission-based: Instructor gets 85%, platform gets 15%
    - Multi-duration: Automatic discounts for longer commitments
  - **Flutter Code Updates**: Updated subscription_plan_repository to use new column structure
    - Removed JSON parsing logic for `basic_plan_pricing`/`premium_plan_pricing`
    - Updated to read from individual pricing columns
    - Integrated with `calculate_plan_prices_from_earnings()` database function
  - **Database Cleanup**: Removed unnecessary `audit_log` table and unused JSON columns
  - **Data Migration**: Automated migration from `base_monthly_price` to new structure
- **Major Database Schema Restructuring**: Comprehensive normalization of instructor-related data
  - Created `instructor_capacity` table for dedicated student capacity management
  - Created `instructor_subscription_configs` table for pricing and subscription plans
  - Created `instructor_work_history` table for professional employment tracking
  - Created `instructor_certifications` table for qualifications and certifications
  - Created `instructor_faqs` table for instructor-specific frequently asked questions
  - Enhanced data organization and separation of concerns across specialized tables
- **Flutter Codebase Migration**: Complete update to align with new database schema
  - Migrated domain models from TrainerDetail/TrainerListItem to InstructorDetail/InstructorListItem
  - Updated all application providers and repositories to use new table structure
  - Updated presentation layer views with backward compatibility maintained
  - Enhanced enrollment system to work with instructor_capacity table
  - Improved capacity validation and student enrollment tracking
- **Backward Compatibility**: Maintained API compatibility during migration
  - Added type aliases for TrainerDetail, TrainerListItem, etc.
  - Preserved existing parameter names (trainerId) while using new schema internally
  - Ensured smooth transition without breaking existing functionality
- **Featured Instructors Pricing Fix**: Resolved mock pricing data in student course list
  - Fixed featured instructors showing hardcoded prices instead of real Supabase data
  - Enhanced subscription config system to ensure all approved instructors have proper pricing
  - Updated fallback pricing from 50 TL to 200 TL for better user experience
  - Eliminated mock data, now fully database-driven pricing display
- **Instructor Profile Data Corrections**: Fixed multiple data display issues in course list view
  - Changed gym field to display instructor's professional title (e.g., "Fitness Trainer")
  - Specialty field now shows instructor's current workplace from work history
  - Fixed availableSpots/maxCapacity to show real enrollment counts from database
  - Added basic/premium plan pricing display in trainer detail view
  - Capacity calculations now use actual active enrollments instead of cached values
- **Instructor FAQ System**: Complete FAQ management for instructor profiles
  - Created `instructor_faqs` table with RLS policies and triggers
  - FAQ add/edit/delete functionality with loading states
  - Three-dot menu interface for FAQ management
  - Expandable FAQ cards with proper UI/UX
- **Enhanced Profile UI**: Improved instructor profile interface
  - Three-dot menus for Work History, Certifications, and FAQ items
  - Current/Verified badges repositioned to top-right corners
  - Responsive width for all profile cards
  - Removed "Add certification" placeholder text from badges
- **Database Table Consistency**: Fixed critical table reference issues
  - Renamed `trainer_reviews` to `instructor_reviews` with proper column updates
  - Consolidated duplicate tables: `trainer_certifications` → `instructor_certifications`, `trainer_work_history` → `instructor_work_history`
  - Standardized all columns to use `instructor_id` (UUID) instead of `trainer_id`
  - Added foreign key constraints for data integrity
- Student plan waiting screen with purchase history access
- Bottom navigation icons standardized with AColor.fitgoGreen
- Plan status checking functionality for students
- **Instructor Settings Screen**: Comprehensive settings with logout functionality
- **Settings Navigation**: Accessible from Instructor Explore tab
- **Logout Confirmation**: Matching student drawer logout with confirmation dialog
- **Profile Completion Validation System**: Comprehensive validation for instructor profiles before subscription setup
- **Profile Completion UI Widget**: Todo-style checklist showing missing profile items with user-friendly guidance
- **Real Subscription Data Integration**: Database queries for actual subscription status instead of mock data

### Changed
- Form approval waiting screen redesigned for better student experience
- Removed navigation back button from plan waiting screen
- Updated bottom navigation colors for both student and instructor views

### Fixed
- **FAQ Table Missing Error**: Resolved 404 PostgrestException when adding FAQs
  - Created missing `instructor_faqs` table with proper schema
  - Fixed RLS policies to use `role` instead of `user_type`
  - Updated repository functions to work with new table structure
- **Profile Badge Text**: Removed "Add certification" placeholder from instructor badges
- **UI Consistency**: Fixed positioning and responsive design issues
  - Work History and Certification cards now full-width responsive
  - Current/Verified badges properly positioned in top-right corners
  - Three-dot menus consistently positioned across all profile sections
- **Critical Database Issues**: Resolved "table does not exist" errors by updating all code references to new table names
- **Sample Data Cleanup**: Removed James Wilson and other test data causing authentication confusion
- **Code-Database Consistency**: Updated all repository and provider files to match renamed database tables
- Student workflow now prevents premature navigation to main screens
- **ValueNotifier dispose error** in registration flow
- **Navigator assertion error** during registration
- Registration form lifecycle management issues
- **Subscription Management Mock Data**: Removed hardcoded subscription data and implemented real database queries
- **Profile Completion Validation**: Added validation to prevent subscription submission without complete profile
- **Error Message Localization**: Technical errors now logged for developers while user-friendly Turkish messages shown to users

### Fixed in Latest Update
- **Instructor Profile Photo Display**: Fixed profile photo not showing in instructor homepage
  - **Database Synchronization**: Fixed inconsistency between `instructors.photo_url` and `profiles.avatar_url` fields
  - **Homepage Avatar Fix**: Updated instructor homepage to display profile photos correctly with cache-busting
  - **Dual Table Updates**: Photo upload now updates both database tables for consistency
  - **Dashboard Refresh**: Added automatic dashboard refresh after photo upload
  - **Enhanced Error Handling**: Improved image loading with fallback states and debug logging
- **UI Cleanup**: Removed "Certified Trainer" badge from instructor profile header
  - Made `primaryCertification` field nullable in InstructorBasicInfo model
  - Removed certification badge from instructor profile badges display
  - Cleaned up profile header to show only experience years and student count
- **UI Layout Improvements**: Enhanced positioning and alignment in profile sections
  - **Work History**: Moved "Current" badge next to company title, edit icon to top-right corner
  - **Certifications**: Moved "Verified" badge next to certification title, consistent edit icon positioning
  - **FAQ Section**: Perfect alignment of dropdown and edit icons in same row, right-center positioned
- **Dialog Size Enhancement**: Maximized width of all profile edit dialogs to full screen width
  - **Width**: True full screen width with zero horizontal padding (insetPadding: horizontal: 0)
  - **Height**: Maintained at 85% of screen height for comfortable viewing
  - **Maximum Width**: Dialogs now use complete screen width edge-to-edge
  - **Technical Improvement**: Removed all horizontal margins using insetPadding override
  - **All Dialogs Updated**: Basic Info, Work Experience, Certifications, and FAQ edit dialogs
  - **Ultimate Mobile Experience**: Complete screen width utilization for maximum content area
- **Capacity Adjustment**: Reduced default student capacity from 50 to 5 for free tier
  - **Database Update**: Updated Supabase instructors table default max_students from 50 to 5
  - **Existing Data**: Updated all current instructors' capacity from 50 to 5 students
  - **Application Code**: Updated all hardcoded capacity defaults throughout the codebase
  - **Free Tier**: New instructors now get 5 student capacity as free tier offering
  - **Upgrade Path**: Capacity upgrade packages remain available for expansion beyond 5 students
- **Layout Fix**: Resolved bottom overflow error in FitGo Market nutrition & supplements section
  - **Product Cards**: Replaced Expanded widgets with fixed height containers (70px + 50px)
  - **Column Layout**: Added mainAxisSize.min to prevent overflow in constrained height
  - **Stable Layout**: Product cards now have predictable 120px total height without overflow
  - **Better UX**: Eliminated "bottom overflowed by 5.0" error in explore tab market section
- **Form Validation Enhancement**: Added comprehensive validation to work history and certification forms
  - **Year Fields**: Numeric keyboard, digits-only input, 4-character limit, range validation (1950-current year)
  - **Required Fields**: Company name, role, certification name, issuing organization validation
  - **Smart Validation**: End year must be after start year, URL format validation for links
  - **Input Formatters**: FilteringTextInputFormatter.digitsOnly for year fields
  - **Error Handling**: Red border styling for validation errors with clear error messages
  - **Form State**: GlobalKey<FormState> integration with proper validation checks before submission
- **Dialog Background Fix**: Resolved black screen issue when opening edit/add dialogs
  - **Root Cause**: Double dialog structure (Dialog + AlertDialog) causing transparent background issues
  - **Solution**: Simplified to single AlertDialog with proper background color and padding
  - **Improved UX**: Clean dialog appearance without black overlay background
  - **Consistent Styling**: Unified insetPadding, contentPadding, and actionsPadding across all dialogs
- **Performance Optimization**: Dramatically improved form submission and deletion speeds
  - **State Management**: Replaced full profile refresh with targeted state updates
  - **Work Experience**: Add/Edit/Delete operations now update state directly without API reload
  - **Certifications**: Optimized CRUD operations with instant UI updates
  - **Loading States**: Enhanced loading state management with proper error handling
  - **Riverpod Optimization**: Leveraged Riverpod's reactive state for faster UI updates
  - **User Experience**: Eliminated long waiting times and circular progress indicator freezing
- **Discount Code System Fix**: Resolved database integration and enhanced UI for discount code management
  - **Database Integration**: Fixed repository to use JSONB field in instructor_subscription_configs table
  - **CRUD Operations**: Implemented proper add/delete operations using JSONB array manipulation
  - **Enhanced Dialog UI**: Completely redesigned discount code creation dialog with modern styling
  - **Form Validation**: Added comprehensive validation for code, percentage, usage limit, and expiry date
  - **Plan Selection**: Added checkbox selection for applicable plans (Basic/Premium)
  - **Visual Improvements**: Dark theme styling, proper input formatters, and loading states
  - **Error Handling**: User-friendly error messages and proper form validation feedback
  - **Theme Consistency**: Aligned dialog styling with application-wide theme and color scheme
  - **Typography**: Consistent use of ATextStyle throughout the dialog for unified appearance
  - **Form Styling**: Matching border colors, focus states, and input field styling with app theme
  - **Color Harmony**: Proper use of app's primary colors (fitgoGreen, accent yellow) and background colors
- **Code Structure Fix**: Resolved duplicate build method and syntax errors in discount code dialog
  - **Build Method**: Fixed duplicate @override Widget build() declarations
  - **Return Statement**: Corrected duplicate return AlertDialog() statements
  - **Code Analysis**: All syntax errors resolved, clean code analysis passed
  - **Widget Structure**: Proper HookWidget implementation with single build method
- **Database Constraint Fix**: Resolved duplicate key constraint violation in discount code creation
  - **Root Cause**: instructor_subscription_configs table has unique constraint on instructor_id
  - **Issue**: upsert() operation was causing duplicate key violations
  - **Solution**: Implemented proper check-then-update/insert logic instead of upsert
  - **Error Handling**: Added user-friendly error messages for constraint violations
  - **Database Operations**: Separate UPDATE for existing records, INSERT for new records
  - **Constraint Safety**: Prevents duplicate instructor_subscription_configs entries
- **Subscription Plan Display Fix**: Resolved empty monthly/yearly plan pricing display
  - **Root Cause**: Database records with empty basic_plan_pricing and premium_plan_pricing JSONB fields
  - **Solution**: Added fallback to generate default pricing when database fields are empty
  - **Default Generation**: Automatic calculation of plan pricing for all durations when missing
  - **Base Price Integration**: Uses base_monthly_price from database or defaults to $49.99
  - **Plan Coverage**: Ensures all PlanDuration values (monthly, 6-month, yearly) have pricing
- **Dialog Width Consistency**: Aligned discount code dialog width with profile edit dialogs
  - **Padding Standardization**: Applied same insetPadding (16px horizontal, 24px vertical)
  - **Content Padding**: Consistent contentPadding and actionsPadding with app dialogs
  - **Visual Harmony**: Uniform dialog sizing across the application
  - **Dialog Structure Enhancement**: Converted AlertDialog to Dialog for better width control
  - **Container Constraints**: Added maxWidth constraint for responsive dialog sizing
  - **Layout Optimization**: Proper Column structure with Flexible content area for scrolling
- **Authentication System Unified**: Consistent AuthRepositoryImpl for both login and register
- **User-Friendly Error Messages**: Turkish error messages with technical details hidden
- **ErrorMessageHelper**: New utility to convert technical errors to user-friendly messages
- **AuthRetryableFetchException Handling**: Technical exceptions no longer exposed to users

## Subscription Plan Management Redesign
- **Trainer Earnings Focus**: Changed from plan pricing to trainer earnings input
  - **Input Field**: Trainers now enter desired monthly earnings instead of plan prices
  - **Automatic Calculation**: System calculates Basic and Premium plan prices automatically
  - **Commission Transparency**: Clear breakdown showing trainer earnings vs app commission
  - **30% Commission Rate**: Standardized app commission rate across all plans
- **UI/UX Improvements**: Enhanced subscription plan management interface
  - **Earnings Input**: Dedicated section for trainer earnings with helpful explanations
  - **Commission Breakdown**: Visual breakdown showing earnings distribution per plan
  - **Removed Edit Icons**: Basic and Premium plans no longer manually editable
  - **Clean Plan Headers**: Removed "Otomatik" badges for cleaner plan presentation
  - **Transparent Pricing**: Clear display of trainer earnings, app commission, and total price
- **Pricing Logic Updates**: Revised plan calculation methodology
  - **Base Price = Basic Plan Earnings**: Input represents trainer's net earnings from basic plan
  - **Commission Applied to All Plans**: Both basic and premium plans include 30% app commission
  - **Premium Multiplier**: Premium plans earn 1.5x trainer's basic plan earnings
  - **Commission Calculation**: Total price = trainer earnings / (1 - commission rate)
  - **Duration Discounts**: Applied after commission calculation for accurate pricing
  - **Example**: 15₺ input → Basic: 21₺ total (15₺ trainer + 6₺ commission), Premium: 32₺ total (23₺ trainer + 9₺ commission)

## Currency System Implementation
- **Default Currency Change**: Switched from USD ($) to Turkish Lira (₺) as default currency
  - **Currency Constants**: Created centralized currency management system
  - **Supported Currencies**: TL (₺), USD ($), EUR (€) with localization support
  - **Currency Formatting**: Smart formatting with proper decimal handling for TL
  - **Extension Methods**: Added convenient currency formatting extensions
- **Application-wide Updates**: Updated all pricing displays to use new currency system
  - **Subscription Plans**: All plan pricing now displays in Turkish Lira
  - **Payment Models**: Default currency changed to ₺ with fallback support
  - **Pricing Displays**: Enrollment and payment screens updated
  - **Commission Breakdown**: Trainer earnings and commission displays in ₺
- **Future Localization Ready**: Prepared for multi-currency support based on app localization
  - **Currency Selection**: Framework ready for user/region-based currency selection
  - **Dynamic Formatting**: Currency symbols and formatting adapt to selected currency
  - **Backward Compatibility**: Existing USD/EUR support maintained for future use

## Instructor Approval System Implementation
- **Database Schema Updates**: Comprehensive instructor approval workflow system
  - **instructor_approval_applications**: New table for tracking approval applications
  - **instructor_course_profiles**: Separate course profiles with approval system
  - **Foreign Key Relationships**: Added profile_id to instructors linking to profiles table
  - **Approval Status Columns**: Added approval_status, approved_by, approved_at to instructors
  - **Default Values**: Changed is_public default to false (approval required)
- **Automated Workflow**: Trigger-based approval application creation
  - **Profile Completion Trigger**: Automatically creates approval application when profile completed
  - **Application Tracking**: JSONB storage for application data and metadata
  - **Status Management**: Pending → Under Review → Approved/Rejected workflow
  - **Timestamp Tracking**: Complete audit trail of application lifecycle
- **Admin Panel Integration**: Database functions and views for web admin management
  - **Approval Functions**: approve_instructor_application() and reject_instructor_application()
  - **Admin Views**: admin_instructor_applications view with complete instructor data
  - **Work History & Certifications**: Integrated counts and verification data
  - **Priority System**: Application priority levels (low, normal, high, urgent)
- **Student Visibility Control**: Only approved instructors visible to students
  - **Public Approved View**: public_approved_instructors view for student access
  - **RLS Policies**: Row-level security for instructor and admin access
  - **Automatic Filtering**: Students only see approved, active, public instructors
  - **Profile Completion Requirement**: Instructors must complete profile before approval
- **Security & Access Control**: Comprehensive permission system
  - **Instructor Permissions**: Can view/edit own applications and course profiles
  - **Admin Permissions**: Full access to all applications and approval functions
  - **Student Permissions**: Read-only access to approved instructor data
  - **Data Isolation**: Proper separation between pending and approved instructor data

## UI Enhancement - Atatürk Quote Addition
- **Explore Tab Enhancement**: Added inspirational Atatürk quote to both student and instructor explore screens
  - **Quote Content**: "Ben sporcunun zeki, çevik ve aynı zamanda ahlaklısını severim." - Mustafa Kemal Atatürk
  - **Visual Design**: Beautiful gradient container with Turkish flag red colors (#E30A17)
  - **Color Scheme**: Turkish flag red gradient with proper alpha transparency
  - **Placement**: Positioned after FitGo Market section in explore tabs
  - **Responsive Layout**: Proper spacing and typography for mobile screens
- **Student Explore Tab**: Enhanced with FitGo Market section and Atatürk quote
  - **Market Preview**: Added coming soon placeholder for FitGo Market
  - **Quote Section**: Inspirational message about athletes and character
  - **Cultural Touch**: Celebrates Turkish heritage and values in fitness
- **Instructor Explore Tab**: Added matching Atatürk quote section
  - **Consistent Design**: Same visual treatment as student version
  - **Professional Appearance**: Maintains instructor interface aesthetics
  - **Motivational Content**: Encourages high standards for fitness professionals

## FAQ Dialog State Management Fix
- **Loading State Issue**: Fixed circular progress indicator and black background problem in FAQ add/edit dialog
  - **Form Validation**: Replaced manual validation with proper form validation using GlobalKey<FormState>
  - **State Management**: Improved loading state handling consistent with work history and certification dialogs
  - **Error Handling**: Enhanced error handling with proper context checking and debug logging
  - **User Experience**: Eliminated black background issue during FAQ submission
- **Consistent Pattern**: Aligned FAQ dialog behavior with other profile edit dialogs
  - **Validation Logic**: Uses formKey.currentState!.validate() instead of manual field checking
  - **Loading Protection**: Proper isLoading state management with context.mounted checks
  - **Error Display**: User-friendly error messages with technical details in debug console
  - **Button State**: Disabled button during loading with circular progress indicator

## FAQ Dialog UI Improvements
- **Dialog Width Enhancement**: Expanded FAQ dialog width to match other profile edit dialogs
  - **Full Width**: Added SizedBox with double.maxFinite width for better form field visibility
  - **Responsive Design**: Dialog now properly utilizes available screen space
  - **Consistent Sizing**: Matches WorkExperienceEditDialog and CertificationEditDialog dimensions
- **FAQ Items Spacing**: Added proper spacing between FAQ items in the list
  - **Visual Separation**: 12px spacing between FAQ cards for better readability
  - **Clean Layout**: FAQ items no longer appear stuck together
  - **Improved UX**: Easier to distinguish between different FAQ entries
- **Form Field Improvements**: Enhanced text input handling for long content
  - **Scrollable Content**: SingleChildScrollView prevents dialog overflow
  - **Multi-line Support**: Answer field properly handles long text input
  - **Form Validation**: Proper TextFormField validation with visual feedback

## Subscription Plan Submission Fix
- **RLS Policy Fix**: Fixed Row Level Security policy for instructor_approval_applications table
  - **INSERT Policy**: Added proper WITH CHECK clause to allow instructors to create their own applications
  - **Permission Error**: Resolved "new row violates row-level security policy" error
  - **Policy Update**: Changed from basic INSERT to INSERT WITH CHECK (profile_id = auth.uid())
- **Approval Workflow Integration**: Updated subscription plan submission to use new approval system
  - **Removed Direct Public Status**: No longer sets is_public = true directly on submission
  - **Approval Application**: Creates instructor_approval_applications record instead
  - **Admin Review Required**: Instructors must wait for admin approval before becoming public
  - **Existing Application Handling**: Updates existing applications instead of creating duplicates
- **Profile ID Management**: Enhanced profile_id linking between instructors and profiles tables
  - **Registration Fix**: New instructors automatically get profile_id set during registration
  - **Existing Data**: Updated existing instructors to have proper profile_id relationships
  - **Foreign Key Integrity**: Ensures all instructors have valid profile_id references

## Student-Side Trainer Visibility Fix
- **Approval System Integration**: Updated student-side trainer list to respect new approval system
  - **Query Enhancement**: Added approval_status = 'approved' filter to trainer list queries
  - **Public Status Check**: Combined is_public = true AND approval_status = 'approved' checks
  - **Active Status**: Added is_active = true filter for additional safety
  - **Count Query**: Updated pagination count query to match filtering criteria
- **Currency Display Fix**: Fixed dollar sign display in Featured Instructors section
  - **Currency Formatting**: Replaced hardcoded '$' with proper TL currency formatting
  - **Extension Usage**: Used trainer.price.toDouble().asCurrency for consistent formatting
  - **Import Addition**: Added currency_constants import for proper formatting
  - **Consistency**: Now matches currency display throughout the application

## Trainer Pricing Display Enhancement
- **Plan-Based Pricing**: Updated trainer cards to show both Basic and Premium plan prices
  - **Data Enhancement**: Extended TrainerListItem model with basicPrice and premiumPrice fields
  - **Subscription Config Integration**: Fetch basic_plan_pricing and premium_plan_pricing from database
  - **Price Calculation**: Extract monthly prices from plan pricing JSON data
  - **Fallback Logic**: Default to base_monthly_price * 1.5 for premium if data missing
- **UI Improvements**: Enhanced trainer card layout to display both plan types
  - **Basic Plan**: Shows basic monthly price with "Basic" label
  - **Premium Plan**: Shows premium monthly price with "Premium" label and gold color
  - **Currency Formatting**: Both prices use proper TL currency formatting
  - **Visual Hierarchy**: Clear distinction between plan types with different styling
- **Mock Data Cleanup**: Removed test/mock FAQ entries from database
  - **Database Cleanup**: Deleted inappropriate test FAQ entries
  - **Professional Content**: Kept only legitimate FAQ entries for approved instructors
  - **Data Integrity**: Maintained proper FAQ structure for real instructor profiles

## Null Safety Fix for Trainer Pricing
- **Type Safety Enhancement**: Fixed null safety issues in trainer pricing display
  - **Nullable Fields**: Changed basicPrice and premiumPrice to nullable double fields
  - **Constructor Update**: Made pricing fields optional in TrainerListItem constructor
  - **Null Checks**: Added proper null coalescing operators in UI components
  - **Fallback Values**: Default to 50.0 TL for basic price, 1.5x multiplier for premium
- **Error Prevention**: Resolved "_TypeError (type 'Null' is not a subtype of type 'double')"
  - **Safe Access**: Used null-aware operators (??) for safe property access
  - **Graceful Degradation**: App continues to work even when pricing data is missing
  - **Default Pricing**: Sensible fallback values ensure UI always displays prices

## Student-Side Trainer List Improvements
- **Pagination Fix**: Fixed pagination issue where refresh would clear all trainers
  - **Page Logic**: Corrected loadTrainers to use proper page numbers instead of always page 0
  - **State Management**: Fixed trainer list state to append new trainers instead of replacing
  - **Refresh Handling**: Proper refresh logic that resets to page 0 and clears existing data
- **Subscription Pricing Integration**: Updated trainer pricing to show actual subscription plan prices
  - **Config Fetching**: Added _fetchSubscriptionConfigs method to get instructor pricing
  - **Base Monthly Price**: Trainers now show base_monthly_price instead of hardcoded price_per_session
  - **Fallback Pricing**: Falls back to price_per_session if subscription config not available
  - **Batch Fetching**: Efficiently fetches multiple instructor configs in single query
- **FAQ Display in Trainer Detail**: Added FAQ section to student-side trainer detail view
  - **FAQ Fetching**: Added FAQ fetching to TrainerDetailRepository
  - **Model Updates**: Updated TrainerDetail model to include FAQ list
  - **UI Component**: Added expandable FAQ cards with proper styling
  - **Data Integration**: FAQs are fetched and displayed alongside work history and certifications
  - **Empty State**: FAQ section hidden when no FAQs available

### Added
- Comprehensive README.md with full project documentation
- Enhanced Makefile with FitGo-specific development commands
- Complete project structure documentation
- CHANGELOG.md with detailed version history

### Changed
- Updated app display name from "fitgo_app" to "Fitgo"
- Enhanced development workflow documentation

## [1.0.0] - 2025-01-26

### Added
- **Authentication System**
  - Email/password authentication with Supabase
  - Google, Facebook, and Apple Sign-In integration
  - Secure password reset with domain-based links
  - Wix website integration for password reset handling
  - Deep linking support for mobile app navigation

- **Onboarding & User Management**
  - Persistent onboarding flow with Hive local storage
  - User type selection (Student/Instructor)
  - Multi-step onboarding process
  - Landing page with user type selection
  - Splash screen with intelligent navigation logic

- **Course Management System**
  - Course listing with advanced filtering (rating, price, capacity)
  - Search functionality with real-time filtering
  - Instructor-student enrollment system
  - Course capacity management for instructors
  - Detailed course information views

- **User Interface & Experience**
  - Custom form components with proper validation
  - Country code selection for phone inputs (Turkey +90 default)
  - Date input formatting (DD/MM/YYYY) with automatic slash insertion
  - Responsive design with context extensions
  - Right-side navigation drawer with logout confirmation
  - Dark/Light theme support
  - Multi-language support (English/Turkish)

- **State Management & Architecture**
  - Domain-Driven Design (DDD) with Clean Architecture
  - Riverpod state management with StateNotifier pattern
  - Repository pattern for data access
  - Comprehensive error handling and logging
  - Provider-based dependency injection

- **Database & Backend**
  - Supabase integration with Row Level Security (RLS)
  - Unified instructor table (merged teachers/trainers)
  - User profiles with fitness goals and photos
  - Enrollment tracking and management
  - Secure data access with proper authentication

- **Development Tools**
  - Comprehensive testing setup
  - Code generation with build_runner
  - Localization support with flutter_localizations
  - Static analysis with very_good_analysis
  - Automated formatting and linting

### Fixed
- **Navigation Issues**
  - Fixed Navigator assertion errors during registration
  - Resolved sequential navigation conflicts with proper delays
  - Fixed logout navigation to correct auth route

- **UI/UX Improvements**
  - Fixed keyboard overflow issues in form screens
  - Resolved phone input field border radius issues
  - Fixed password visibility toggle functionality
  - Improved search and filter state management

- **Authentication & Storage**
  - Fixed Supabase connection errors in splash screen
  - Resolved StateNotifier usage errors across multiple files
  - Fixed onboarding data persistence and clearing on logout
  - Corrected AuthNotifier constructor parameter mismatch

- **Database & Data Management**
  - Created missing user_profiles table
  - Fixed freezed model syntax issues
  - Resolved repository pattern implementation
  - Fixed enrollment data access and error handling

### Changed
- **Architecture Improvements**
  - Migrated from GetIt to Riverpod dependency injection
  - Enhanced provider hierarchy with proper scoping
  - Improved error handling at all architectural layers
  - Standardized on "instructor" terminology throughout app

- **UI Component Enhancements**
  - Converted TextFormField to CustomTextFormField usage
  - Implemented SingleChildScrollView-Column structure for forms
  - Enhanced onboarding screens with consistent UI patterns
  - Improved drawer positioning and functionality

- **Development Workflow**
  - Updated to use Puro for Flutter version management
  - Enhanced Makefile with comprehensive development commands
  - Improved code organization following DDD principles
  - Standardized error handling and logging patterns

### Security
- **Authentication Security**
  - Implemented secure password reset with domain validation
  - Added Row Level Security (RLS) policies for all database tables
  - Secured API endpoints with proper authentication checks
  - Protected sensitive user data with encryption

- **Data Protection**
  - Implemented proper user data isolation
  - Added secure file upload for profile photos
  - Protected against unauthorized data access
  - Ensured GDPR compliance for user data handling

### Technical Debt
- **Code Quality**
  - Removed unused/empty files and duplicate components
  - Standardized naming conventions across the codebase
  - Improved type safety with proper model definitions
  - Enhanced test coverage for critical functionality

- **Performance**
  - Optimized state management with efficient providers
  - Improved image caching and loading performance
  - Enhanced navigation performance with proper routing
  - Reduced app startup time with optimized initialization

## [0.1.0] - Initial Development

### Added
- Basic Flutter project structure
- Initial Supabase integration
- Basic authentication flow
- Simple navigation setup
- Core UI components

---

## Release Notes

### Version 1.0.0 Highlights

This major release represents a complete transformation of the FitGo app into a professional, production-ready fitness platform. Key achievements include:

1. **Complete Architecture Overhaul**: Migrated to DDD with Clean Architecture principles
2. **Enhanced User Experience**: Comprehensive onboarding and intuitive navigation
3. **Robust Authentication**: Multi-platform sign-in with secure password management
4. **Advanced Course Management**: Full-featured course system with filtering and search
5. **Production-Ready Infrastructure**: Comprehensive testing, documentation, and deployment setup

### Breaking Changes

- **State Management**: Complete migration from GetIt to Riverpod (requires code updates)
- **Database Schema**: Unified instructor table (teachers/trainers merged)
- **Navigation**: Updated routing structure with GoRouter
- **Authentication**: Enhanced security requirements for password reset

### Migration Guide

For developers updating from earlier versions:

1. Update state management calls to use Riverpod providers
2. Update database queries to use unified instructor table
3. Update navigation calls to use new GoRouter structure
4. Update authentication flows to handle new security requirements

### Known Issues

- Firebase integration temporarily disabled (will be re-enabled in future release)
- Some UI text still references "trainer" instead of "instructor" (will be fixed in patch)
- iOS build requires manual configuration for Apple Sign-In

### Upcoming Features (v1.1.0)

- AI-powered workout recommendations
- Advanced progress analytics with charts
- Social features and leaderboards
- In-app purchase integration
- Push notification system
- Video streaming with caching

<!-- ### General Guidelines:
- **(Note)**: note...
- **SQL**: Use ```sql for SQL-related code.
- **Code (other languages)**: Use language-specific identifiers like ```dart, ```typescript, ```bash, etc.
- **Logs/Errors/General Text**: Use ```text or just regular markdown text formatting for descriptions and messages. -->
