# GitHub Dependabot configuration for FitGo Flutter app
# This file configures automatic dependency updates for the project

version: 2
updates:
  # Flutter/Dart dependencies (pubspec.yaml)
  - package-ecosystem: "pub"
    directory: "/"
    schedule:
      interval: "weekly"
      day: "monday"
      time: "09:00"
      timezone: "Europe/Istanbul"
    open-pull-requests-limit: 5
    reviewers:
      - "alialperenderici"
    assignees:
      - "alialperenderici"
    commit-message:
      prefix: "deps"
      prefix-development: "deps-dev"
      include: "scope"
    labels:
      - "dependencies"
      - "flutter"
    # Group minor and patch updates together
    groups:
      flutter-minor-patch:
        patterns:
          - "*"
        update-types:
          - "minor"
          - "patch"
    # Allow specific major updates
    allow:
      - dependency-type: "direct"
      - dependency-type: "indirect"
    # Ignore specific packages if needed
    ignore:
      # Example: ignore flutter SDK updates (managed manually)
      - dependency-name: "flutter"
        update-types: ["version-update:semver-major"]

  # GitHub Actions dependencies
  - package-ecosystem: "github-actions"
    directory: "/"
    schedule:
      interval: "weekly"
      day: "monday"
      time: "09:00"
      timezone: "Europe/Istanbul"
    open-pull-requests-limit: 3
    reviewers:
      - "alialperenderici"
    assignees:
      - "alialperenderici"
    commit-message:
      prefix: "ci"
      include: "scope"
    labels:
      - "dependencies"
      - "github-actions"
      - "ci/cd"

  # Docker dependencies (if using Docker)
  # - package-ecosystem: "docker"
  #   directory: "/"
  #   schedule:
  #     interval: "weekly"
  #     day: "monday"
  #     time: "09:00"
  #     timezone: "Europe/Istanbul"
  #   open-pull-requests-limit: 2
  #   reviewers:
  #     - "alialperenderici"
  #   assignees:
  #     - "alialperenderici"
  #   commit-message:
  #     prefix: "docker"
  #     include: "scope"
  #   labels:
  #     - "dependencies"
  #     - "docker"
