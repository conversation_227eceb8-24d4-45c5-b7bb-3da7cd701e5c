name: Mobile App Tests

on:
  push:
    branches: [ main, develop, development ]
  pull_request:
    branches: [ main, develop, development ]
  workflow_dispatch:

jobs:
  unit-tests:
    name: Unit Tests
    runs-on: ubuntu-latest
    timeout-minutes: 15
    
    steps:
      - name: Checkout code
        uses: actions/checkout@v4

      - name: Setup Flutter
        uses: subosito/flutter-action@v2
        with:
          flutter-version: '3.32.7'
          channel: 'stable'
          cache: true

      - name: Get dependencies
        run: flutter pub get

      - name: Generate code
        run: dart run build_runner build --delete-conflicting-outputs

      - name: Verify Flutter installation
        run: flutter doctor -v

      - name: Run unit tests
        run: flutter test test/unit/ --coverage --reporter=expanded

      - name: Upload coverage to Codecov
        uses: codecov/codecov-action@v4
        with:
          file: coverage/lcov.info
          flags: unittests
          name: codecov-umbrella
          fail_ci_if_error: false

  integration-tests:
    name: Integration Tests (Supabase)
    runs-on: ubuntu-latest
    timeout-minutes: 30
    needs: unit-tests

    steps:
      - name: Checkout code
        uses: actions/checkout@v4

      - name: Setup Flutter
        uses: subosito/flutter-action@v2
        with:
          flutter-version: '3.32.7'
          channel: 'stable'
          cache: true

      - name: Get dependencies
        run: flutter pub get

      - name: Generate code
        run: dart run build_runner build --delete-conflicting-outputs

      - name: Run Supabase integration tests
        run: flutter test test/integration/ --reporter=expanded
        env:
          SUPABASE_URL: ${{ secrets.SUPABASE_URL }}
          SUPABASE_ANON_KEY: ${{ secrets.SUPABASE_ANON_KEY }}
          SUPABASE_SERVICE_ROLE_KEY: ${{ secrets.SUPABASE_SERVICE_ROLE_KEY }}

      - name: Generate integration test report
        if: always()
        run: |
          echo "## 🔗 Integration Test Results" >> $GITHUB_STEP_SUMMARY
          echo "- Database connection tests" >> $GITHUB_STEP_SUMMARY
          echo "- Instructor registration workflow" >> $GITHUB_STEP_SUMMARY
          echo "- Student enrollment process" >> $GITHUB_STEP_SUMMARY
          echo "- Authentication flow" >> $GITHUB_STEP_SUMMARY
          echo "- Real Supabase database operations" >> $GITHUB_STEP_SUMMARY

  code-quality:
    name: Code Quality
    runs-on: ubuntu-latest
    timeout-minutes: 10
    
    steps:
      - name: Checkout code
        uses: actions/checkout@v4

      - name: Setup Flutter
        uses: subosito/flutter-action@v2
        with:
          flutter-version: '3.32.7'
          channel: 'stable'
          cache: true

      - name: Get dependencies
        run: flutter pub get

      - name: Generate code
        run: dart run build_runner build --delete-conflicting-outputs

      - name: Analyze code (warnings only)
        run: flutter analyze --no-fatal-infos --no-fatal-warnings || echo "Analysis completed with warnings"

      - name: Check formatting (non-blocking)
        run: dart format --set-exit-if-changed . || echo "Formatting issues found but not blocking"

      - name: Run custom lints (non-blocking)
        run: dart run custom_lint || echo "Custom lint completed with warnings"

  build-test:
    name: Build Test
    runs-on: ubuntu-latest
    timeout-minutes: 20
    needs: [unit-tests]
    
    strategy:
      matrix:
        platform: [android]
    
    steps:
      - name: Checkout code
        uses: actions/checkout@v4

      - name: Setup Flutter
        uses: subosito/flutter-action@v2
        with:
          flutter-version: '3.32.7'
          channel: 'stable'
          cache: true

      - name: Setup Android SDK (Android only)
        if: matrix.platform == 'android'
        uses: android-actions/setup-android@v3

      - name: Get dependencies
        run: flutter pub get

      - name: Generate code
        run: dart run build_runner build --delete-conflicting-outputs

      - name: Build for Android
        run: flutter build apk --debug --dart-define envConfig=development

  test-summary:
    name: Test Summary
    runs-on: ubuntu-latest
    needs: [unit-tests, integration-tests, code-quality, build-test]
    if: always()
    
    steps:
      - name: Test Results Summary
        run: |
          echo "## 🧪 Test Results Summary" >> $GITHUB_STEP_SUMMARY
          echo "" >> $GITHUB_STEP_SUMMARY
          echo "| Test Type | Status |" >> $GITHUB_STEP_SUMMARY
          echo "|-----------|--------|" >> $GITHUB_STEP_SUMMARY
          echo "| Unit Tests | ${{ needs.unit-tests.result == 'success' && '✅ Passed' || '❌ Failed' }} |" >> $GITHUB_STEP_SUMMARY
          echo "| Integration Tests | ${{ needs.integration-tests.result == 'success' && '✅ Passed' || '❌ Failed' }} |" >> $GITHUB_STEP_SUMMARY
          echo "| Code Quality | ${{ needs.code-quality.result == 'success' && '✅ Passed' || needs.code-quality.result == 'failure' && '⚠️ Warnings' || '❌ Failed' }} |" >> $GITHUB_STEP_SUMMARY
          echo "| Build Test | ${{ needs.build-test.result == 'success' && '✅ Passed' || '❌ Failed' }} |" >> $GITHUB_STEP_SUMMARY
          echo "" >> $GITHUB_STEP_SUMMARY
          
          if [[ "${{ needs.unit-tests.result }}" == "success" && "${{ needs.integration-tests.result }}" == "success" && "${{ needs.build-test.result }}" == "success" ]]; then
            echo "🎉 **Critical tests passed!** Ready for deployment." >> $GITHUB_STEP_SUMMARY
            echo "📝 Code Quality: ${{ needs.code-quality.result == 'success' && 'Clean' || 'Has warnings (non-blocking)' }}" >> $GITHUB_STEP_SUMMARY
          else
            echo "⚠️ **Critical tests failed.** Please review and fix issues before merging." >> $GITHUB_STEP_SUMMARY
          fi

# Nightly comprehensive tests
  nightly-tests:
    name: Nightly Comprehensive Tests
    runs-on: ubuntu-latest
    timeout-minutes: 60
    if: github.event_name == 'schedule' || github.event_name == 'workflow_dispatch'
    
    steps:
      - name: Checkout code
        uses: actions/checkout@v4

      - name: Setup Flutter
        uses: subosito/flutter-action@v2
        with:
          flutter-version: '3.7.0'
          channel: 'stable'
          cache: true

      - name: Get dependencies
        run: flutter pub get

      - name: Run all tests with coverage
        run: |
          flutter test --coverage --reporter=expanded
          
      - name: Generate coverage report
        run: |
          sudo apt-get update
          sudo apt-get install -y lcov
          genhtml coverage/lcov.info -o coverage/html

      - name: Upload coverage artifacts
        uses: actions/upload-artifact@v4
        with:
          name: coverage-report
          path: coverage/html/

      - name: Performance tests
        run: |
          echo "Running performance tests..."
          # Add performance testing commands here
          
      - name: Security scan
        run: |
          echo "Running security scan..."
          # Add security scanning commands here
