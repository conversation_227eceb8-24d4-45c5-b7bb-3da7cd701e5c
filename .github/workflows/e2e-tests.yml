name: 🚀 E2E Integration Tests

on:
  schedule:
    # Run every night at 2 AM UTC
    - cron: '0 2 * * *'
  workflow_dispatch:
    # Allow manual trigger
    inputs:
      test_environment:
        description: 'Test Environment'
        required: false
        default: 'staging'
        type: choice
        options:
          - staging
          - production
      device_type:
        description: 'Device Type'
        required: false
        default: 'android'
        type: choice
        options:
          - android
          - web
          - both
  push:
    branches: [ main, development ]
    paths:
      - 'integration_test/**'
      - 'lib/**'
      - '.github/workflows/e2e-tests.yml'
  pull_request:
    branches: [ main ]
    paths:
      - 'integration_test/**'
      - 'lib/**'

env:
  FLUTTER_VERSION: '3.32.7'
  JAVA_VERSION: '17'

jobs:
  # Job 1: Android E2E Tests
  android-e2e:
    name: 🤖 Android E2E Tests
    runs-on: ubuntu-latest
    if: ${{ github.event.inputs.device_type != 'web' }}
    timeout-minutes: 45
    
    strategy:
      fail-fast: false
      matrix:
        api-level: [30, 33]
        target: [google_apis]
    
    steps:
      - name: 📥 Checkout code
        uses: actions/checkout@v4

      - name: ☕ Setup Java
        uses: actions/setup-java@v4
        with:
          distribution: 'zulu'
          java-version: ${{ env.JAVA_VERSION }}

      - name: 🐦 Setup Flutter
        uses: subosito/flutter-action@v2
        with:
          flutter-version: ${{ env.FLUTTER_VERSION }}
          channel: 'stable'
          cache: true

      - name: 📦 Get dependencies
        run: flutter pub get

      - name: 🏗️ Generate code
        run: dart run build_runner build --delete-conflicting-outputs

      - name: 🔧 Verify Flutter installation
        run: flutter doctor -v

      - name: 🤖 Setup Android SDK
        uses: android-actions/setup-android@v3

      - name: 📱 Enable KVM group perms
        run: |
          echo 'KERNEL=="kvm", GROUP="kvm", MODE="0666", OPTIONS+="static_node=kvm"' | sudo tee /etc/udev/rules.d/99-kvm4all.rules
          sudo udevadm control --reload-rules
          sudo udevadm trigger --name-match=kvm

      - name: 📱 Create and Start Android Emulator
        uses: reactivecircus/android-emulator-runner@v2
        with:
          api-level: ${{ matrix.api-level }}
          target: ${{ matrix.target }}
          arch: x86_64
          profile: Nexus 6
          cores: 2
          ram-size: 4096M
          heap-size: 1024M
          sdcard-path-or-size: 1000M
          avd-name: test_avd_${{ matrix.api-level }}
          force-avd-creation: false
          emulator-options: -no-snapshot-save -no-window -gpu swiftshader_indirect -noaudio -no-boot-anim -camera-back none
          disable-animations: true
          script: |
            echo "🚀 Starting E2E Tests on Android API ${{ matrix.api-level }}"
            flutter test integration_test/e2e_user_journey_test.dart --verbose
        env:
          SUPABASE_URL: ${{ secrets.SUPABASE_URL }}
          SUPABASE_ANON_KEY: ${{ secrets.SUPABASE_ANON_KEY }}
          SUPABASE_SERVICE_ROLE_KEY: ${{ secrets.SUPABASE_SERVICE_ROLE_KEY }}
          FLUTTER_TEST_ENV: github_actions

      - name: 📊 Upload Test Results
        if: always()
        uses: actions/upload-artifact@v4
        with:
          name: android-e2e-results-api${{ matrix.api-level }}
          path: |
            test-results/
            screenshots/
          retention-days: 7

  # Job 2: Web E2E Tests
  web-e2e:
    name: 🌐 Web E2E Tests
    runs-on: ubuntu-latest
    if: ${{ github.event.inputs.device_type != 'android' }}
    timeout-minutes: 30
    
    strategy:
      fail-fast: false
      matrix:
        browser: [chrome, firefox]
    
    steps:
      - name: 📥 Checkout code
        uses: actions/checkout@v4

      - name: 🐦 Setup Flutter
        uses: subosito/flutter-action@v2
        with:
          flutter-version: ${{ env.FLUTTER_VERSION }}
          channel: 'stable'
          cache: true

      - name: 📦 Get dependencies
        run: flutter pub get

      - name: 🏗️ Generate code
        run: dart run build_runner build --delete-conflicting-outputs

      - name: 🌐 Setup Browser
        if: matrix.browser == 'firefox'
        run: |
          sudo apt-get update
          sudo apt-get install -y firefox

      - name: 🧪 Run Web E2E Tests
        run: |
          echo "🚀 Starting E2E Tests on ${{ matrix.browser }}"
          flutter test integration_test/e2e_user_journey_test.dart -d ${{ matrix.browser }} --verbose
        env:
          SUPABASE_URL: ${{ secrets.SUPABASE_URL }}
          SUPABASE_ANON_KEY: ${{ secrets.SUPABASE_ANON_KEY }}
          SUPABASE_SERVICE_ROLE_KEY: ${{ secrets.SUPABASE_SERVICE_ROLE_KEY }}
          FLUTTER_TEST_ENV: github_actions

      - name: 📊 Upload Test Results
        if: always()
        uses: actions/upload-artifact@v4
        with:
          name: web-e2e-results-${{ matrix.browser }}
          path: |
            test-results/
            screenshots/
          retention-days: 7

  # Job 3: Test Report Generation
  test-report:
    name: 📊 Generate Test Report
    runs-on: ubuntu-latest
    needs: [android-e2e, web-e2e]
    if: always()
    
    steps:
      - name: 📥 Checkout code
        uses: actions/checkout@v4

      - name: 📊 Download Test Results
        uses: actions/download-artifact@v4
        with:
          path: test-results/

      - name: 📋 Generate Test Report
        run: |
          mkdir -p reports
          cat > reports/e2e-test-report.md << 'EOF'
          # 🧪 E2E Test Report
          
          **Date:** $(date)
          **Trigger:** ${{ github.event_name }}
          **Branch:** ${{ github.ref_name }}
          **Commit:** ${{ github.sha }}
          
          ## 📊 Test Results Summary
          
          ### Android Tests
          - **API 30**: ${{ needs.android-e2e.result }}
          - **API 33**: ${{ needs.android-e2e.result }}
          
          ### Web Tests  
          - **Chrome**: ${{ needs.web-e2e.result }}
          - **Firefox**: ${{ needs.web-e2e.result }}
          
          ## 🎯 Test Coverage
          
          This E2E test validates the complete user journey:
          
          ### 👨‍🏫 Instructor Flow (19 Phases)
          1. Registration & Profile Setup
          2. Work History & Certifications
          3. Pricing Configuration
          4. Approval Workflow (Reject → Resubmit → Approve)
          5. Template Creation (Workout & Nutrition)
          6. Student Management
          
          ### 👨‍🎓 Student Flow (15 Phases)
          1. Registration & Profile Setup
          2. Instructor Selection & Enrollment
          3. Plan Assignment & Usage
          4. Workout Session Completion
          5. Nutrition Logging
          6. Progress Tracking
          
          ### 🔍 Validations
          - **Database Integrity**: User data, relationships, status transitions
          - **UI/UX Flows**: Widget interactions, navigation, error handling
          - **Business Logic**: Approval workflows, plan assignments, calculations
          
          ## 🚀 Production Readiness
          
          ✅ **Passing E2E tests guarantee:**
          - Complete user registration flows work
          - Instructor approval workflow functions correctly  
          - Student enrollment process is seamless
          - Template creation and assignment work properly
          - Database integrity is maintained
          - UI/UX flows are consistent and error-free
          
          EOF

      - name: 📤 Upload Test Report
        uses: actions/upload-artifact@v4
        with:
          name: e2e-test-report
          path: reports/
          retention-days: 30

      - name: 💬 Comment PR with Results
        if: github.event_name == 'pull_request'
        uses: actions/github-script@v7
        with:
          script: |
            const fs = require('fs');
            const report = fs.readFileSync('reports/e2e-test-report.md', 'utf8');
            
            github.rest.issues.createComment({
              issue_number: context.issue.number,
              owner: context.repo.owner,
              repo: context.repo.repo,
              body: report
            });

# Required GitHub Secrets:
# - SUPABASE_URL: Your Supabase project URL
# - SUPABASE_ANON_KEY: Your Supabase anonymous key  
# - SUPABASE_SERVICE_ROLE_KEY: Your Supabase service role key (for E2E tests)
