import 'package:fitgo_app/src/shared/constants/app_path.dart';
import 'package:fitgo_app/src/shared/extensions/build_context/screen_util_ext.dart';
import 'package:fitgo_app/src/shared/widgets/image/asset_image.dart';
import 'package:fitgo_app/src/theme/colors.dart';
import 'package:fitgo_app/src/app_provider.dart';
import 'package:fitgo_app/src/shared/enums/user_type.dart';
import 'package:flutter/material.dart';
import 'package:hooks_riverpod/hooks_riverpod.dart';
import 'package:go_router/go_router.dart';

import 'package:fitgo_app/src/dashboard/application/dashboard_provider.dart';
import 'package:fitgo_app/src/enrollment/application/enrollment_provider.dart';
import 'package:supabase_flutter/supabase_flutter.dart';
import 'package:fitgo_app/src/shared/utils/app_logger.dart';

class SplashView extends HookConsumerWidget {
  const SplashView({super.key});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    // Listen to onboarding completion only
    // Note: Auth state navigation is handled by auth_page_view.dart to avoid conflicts

    ref.listen<bool>(onboardingCompletedProvider, (previous, next) {
      if (next && !ref.read(isAuthenticatedProvider)) {
        // Onboarding completed but not authenticated, go to auth
        context.go('/auth');
      }
    });

    // Auto-navigate after splash delay
    Future.delayed(const Duration(seconds: 2), () async {
      if (context.mounted) {
        final isAuthenticated = ref.read(isAuthenticatedProvider);
        final onboardingCompleted = ref.read(onboardingCompletedProvider);
        final userType = ref.read(currentUserTypeProvider);

        if (isAuthenticated) {
          final currentUser = Supabase.instance.client.auth.currentUser;

          if (currentUser != null) {
            // FIRST: Check user role from database to determine correct flow
            bool isInstructor = false;
            try {
              final enrollmentRepository = ref.read(
                enrollmentRepositoryProvider,
              );
              isInstructor = await enrollmentRepository.isUserInstructor(
                currentUser.id,
              );

              // Update the user type provider with the correct role
              if (isInstructor) {
                await ref
                    .read(currentUserTypeProvider.notifier)
                    .setUserType(UserType.instructor);
                AppLogger.navigation('SPLASH', 'INSTRUCTOR_MAIN', tag: 'USER_ROLE');
              } else {
                await ref
                    .read(currentUserTypeProvider.notifier)
                    .setUserType(UserType.student);
                AppLogger.navigation('SPLASH', 'STUDENT_FLOW', tag: 'USER_ROLE');
              }
            } catch (e) {
              AppLogger.error('❌ Error checking user role', tag: 'USER_ROLE', error: e);
              // Default to student if error
              isInstructor = false;
            }

            if (!context.mounted) return;

            // Navigate based on actual user role
            if (isInstructor) {
              // User is instructor, go to instructor homepage
              AppLogger.navigation('SPLASH', '/instructor-main', tag: 'NAVIGATION');
              context.go('/instructor-main');
              return;
            } else {
              // User is student, check enrollment and onboarding status

              // Check if user has any active enrollments
              final enrollmentRepository = ref.read(
                enrollmentRepositoryProvider,
              );
              final enrollmentResult = await enrollmentRepository
                  .checkUserEnrollment(currentUser.id);

              if (!context.mounted) return;

              if (enrollmentResult.error != null) {
                AppLogger.error(
                  '❌ Error checking enrollment: ${enrollmentResult.error}',
                  tag: 'ENROLLMENT',
                );
                // On error, redirect to course list as fallback
                context.go('/course-list');
                return;
              }

              if (!enrollmentResult.hasActiveEnrollment) {
                // No active enrollment found, redirect to course list
                context.go('/course-list');
                return;
              }

              // User has active enrollment, check onboarding completion status
              final dashboardRepository = ref.read(dashboardRepositoryProvider);
              final onboardingStatus =
                  await dashboardRepository.getOnboardingStatus();

              if (!context.mounted) return;

              AppLogger.info('🔍 Student onboarding status check:', tag: 'ONBOARDING');
              AppLogger.info(
                '  - hasCompletedPayment: ${onboardingStatus.hasCompletedPayment}',
                tag: 'ONBOARDING',
              );
              AppLogger.info(
                '  - hasCompletedProfileForm: ${onboardingStatus.hasCompletedProfileForm}',
                tag: 'ONBOARDING',
              );
              AppLogger.info(
                '  - hasAssignedPlan: ${onboardingStatus.hasAssignedPlan}',
                tag: 'ONBOARDING',
              );
              AppLogger.info(
                '  - shouldShowDashboard: ${onboardingStatus.shouldShowDashboard}',
                tag: 'ONBOARDING',
              );
              AppLogger.info(
                '  - shouldShowPlanWaiting: ${onboardingStatus.shouldShowPlanWaiting}',
                tag: 'ONBOARDING',
              );
              AppLogger.info(
                '  - shouldShowProfileForm: ${onboardingStatus.shouldShowProfileForm}',
                tag: 'ONBOARDING',
              );

              if (onboardingStatus.shouldShowDashboard) {
                // Both payment and profile form completed, and plan assigned, show dashboard
                AppLogger.navigation('ONBOARDING', '/dashboard', tag: 'NAVIGATION');
                context.go('/dashboard');
                return;
              }

              if (onboardingStatus.shouldShowPlanWaiting) {
                // Payment and profile form completed, but no plan assigned yet, show waiting screen
                AppLogger.navigation('ONBOARDING', '/form-approval-waiting', tag: 'NAVIGATION');
                context.go('/form-approval-waiting');
                return;
              }

              if (onboardingStatus.shouldShowProfileForm) {
                // Payment completed but profile form not submitted, show profile form
                AppLogger.navigation('ONBOARDING', '/profile-form', tag: 'NAVIGATION');
                context.go('/profile-form');
                return;
              }

              // Fallback for students
              AppLogger.warning('📚 Fallback: Navigating to course list', tag: 'NAVIGATION');
              context.go('/course-list');
              return;
            }
          }

          // Fallback navigation based on stored user type (should not reach here normally)
          if (userType == UserType.instructor) {
            context.go('/instructor-main');
          } else {
            context.go('/course-list');
          }
        } else {
          // Check if user has seen landing page
          final hasSeenLanding = ref.read(landingPageSeenProvider);

          if (onboardingCompleted) {
            context.go('/auth');
          } else if (userType != null) {
            // User has selected type but hasn't completed onboarding
            context.go('/onboarding');
          } else if (hasSeenLanding) {
            // User has seen landing but hasn't selected type, go to landing again
            context.go('/landing');
          } else {
            // First time user, show landing page
            context.go('/landing');
          }
        }
      }
    });

    return Container(
      decoration: const BoxDecoration(color: AColor.black),
      child: Center(
        child: AImage(imgPath: APath.appLogo, width: context.width / 2),
      ),
    );
  }
}
