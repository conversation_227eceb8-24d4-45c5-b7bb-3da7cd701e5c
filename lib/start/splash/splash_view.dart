import 'package:fitgo_app/src/shared/constants/app_path.dart';
import 'package:fitgo_app/src/shared/extensions/build_context/screen_util_ext.dart';
import 'package:fitgo_app/src/shared/widgets/image/asset_image.dart';
import 'package:fitgo_app/src/theme/colors.dart';
import 'package:fitgo_app/src/app_provider.dart';
import 'package:fitgo_app/src/shared/enums/user_type.dart';
import 'package:flutter/material.dart';
import 'package:hooks_riverpod/hooks_riverpod.dart';
import 'package:go_router/go_router.dart';

import 'package:fitgo_app/src/enrollment/application/enrollment_provider.dart';
import 'package:supabase_flutter/supabase_flutter.dart';
import 'package:fitgo_app/src/shared/utils/app_logger.dart';
import 'package:fitgo_app/src/student/application/student_workflow_service.dart';
import 'package:fitgo_app/src/student/application/workflow_providers.dart';

class SplashView extends HookConsumerWidget {
  const SplashView({super.key});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    // Listen to onboarding completion only
    // Note: Auth state navigation is handled by auth_page_view.dart to avoid conflicts

    ref.listen<bool>(onboardingCompletedProvider, (previous, next) {
      if (next && !ref.read(isAuthenticatedProvider)) {
        // Onboarding completed but not authenticated, go to auth
        context.go('/auth');
      }
    });

    // Auto-navigate after splash delay
    Future.delayed(const Duration(seconds: 2), () async {
      if (context.mounted) {
        final isAuthenticated = ref.read(isAuthenticatedProvider);
        final onboardingCompleted = ref.read(onboardingCompletedProvider);
        final userType = ref.read(currentUserTypeProvider);

        if (isAuthenticated) {
          final currentUser = Supabase.instance.client.auth.currentUser;

          if (currentUser != null) {
            // FIRST: Check user role from database to determine correct flow
            bool isInstructor = false;
            try {
              final enrollmentRepository = ref.read(
                enrollmentRepositoryProvider,
              );
              isInstructor = await enrollmentRepository.isUserInstructor(
                currentUser.id,
              );

              // Update the user type provider with the correct role
              if (isInstructor) {
                await ref
                    .read(currentUserTypeProvider.notifier)
                    .setUserType(UserType.instructor);
                AppLogger.navigation('SPLASH', 'INSTRUCTOR_MAIN',
                    tag: 'USER_ROLE');
              } else {
                await ref
                    .read(currentUserTypeProvider.notifier)
                    .setUserType(UserType.student);
                AppLogger.navigation('SPLASH', 'STUDENT_FLOW',
                    tag: 'USER_ROLE');
              }
            } catch (e) {
              AppLogger.error('❌ Error checking user role',
                  tag: 'USER_ROLE', error: e);
              // Default to student if error
              isInstructor = false;
            }

            if (!context.mounted) return;

            // Navigate based on actual user role
            if (isInstructor) {
              // User is instructor, go to instructor homepage
              AppLogger.navigation('SPLASH', '/instructor-main',
                  tag: 'NAVIGATION');
              context.go('/instructor-main');
              return;
            } else {
              // User is student, use workflow service to determine navigation
              try {
                final workflowService =
                    ref.read(studentWorkflowServiceProvider);
                final workflowState =
                    await workflowService.getCurrentWorkflowState();

                if (!context.mounted) return;

                AppLogger.info('🔍 Student workflow state: $workflowState',
                    tag: 'STUDENT_WORKFLOW');

                // Navigate based on workflow state
                switch (workflowState) {
                  case StudentWorkflowState.needsCourseSelection:
                    AppLogger.navigation('STUDENT_WORKFLOW', '/course-list',
                        tag: 'NAVIGATION');
                    context.go('/course-list');
                    break;
                  case StudentWorkflowState.needsProfileForm:
                    AppLogger.navigation('STUDENT_WORKFLOW', '/profile-form',
                        tag: 'NAVIGATION');
                    context.go('/profile-form');
                    break;
                  case StudentWorkflowState.waitingForPlan:
                    AppLogger.navigation(
                        'STUDENT_WORKFLOW', '/form-approval-waiting',
                        tag: 'NAVIGATION');
                    context.go('/form-approval-waiting');
                    break;
                  case StudentWorkflowState.hasActivePlan:
                    AppLogger.navigation('STUDENT_WORKFLOW', '/dashboard',
                        tag: 'NAVIGATION');
                    context.go('/dashboard');
                    break;
                }
                return;
              } catch (e) {
                AppLogger.error('❌ Error checking student workflow state: $e',
                    tag: 'STUDENT_WORKFLOW');
                // Fallback to course list on error
                context.go('/course-list');
                return;
              }
            }
          }

          // Fallback navigation based on stored user type (should not reach here normally)
          if (userType == UserType.instructor) {
            context.go('/instructor-main');
          } else {
            context.go('/course-list');
          }
        } else {
          // Check if user has seen landing page
          final hasSeenLanding = ref.read(landingPageSeenProvider);

          if (onboardingCompleted) {
            context.go('/auth');
          } else if (userType != null) {
            // User has selected type but hasn't completed onboarding
            context.go('/onboarding');
          } else if (hasSeenLanding) {
            // User has seen landing but hasn't selected type, go to landing again
            context.go('/landing');
          } else {
            // First time user, show landing page
            context.go('/landing');
          }
        }
      }
    });

    return Container(
      decoration: const BoxDecoration(color: AColor.black),
      child: Center(
        child: AImage(imgPath: APath.appLogo, width: context.width / 2),
      ),
    );
  }
}
