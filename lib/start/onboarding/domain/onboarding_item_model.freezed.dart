// dart format width=80
// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'onboarding_item_model.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

// dart format off
T _$identity<T>(T value) => value;

/// @nodoc
mixin _$OnboardingItemModel {
  String get imgPath;
  String get title;
  String get desc;

  /// Create a copy of OnboardingItemModel
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @pragma('vm:prefer-inline')
  $OnboardingItemModelCopyWith<OnboardingItemModel> get copyWith =>
      _$OnboardingItemModelCopyWithImpl<OnboardingItemModel>(
          this as OnboardingItemModel, _$identity);

  /// Serializes this OnboardingItemModel to a JSON map.
  Map<String, dynamic> toJson();

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is OnboardingItemModel &&
            (identical(other.imgPath, imgPath) || other.imgPath == imgPath) &&
            (identical(other.title, title) || other.title == title) &&
            (identical(other.desc, desc) || other.desc == desc));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hash(runtimeType, imgPath, title, desc);

  @override
  String toString() {
    return 'OnboardingItemModel(imgPath: $imgPath, title: $title, desc: $desc)';
  }
}

/// @nodoc
abstract mixin class $OnboardingItemModelCopyWith<$Res> {
  factory $OnboardingItemModelCopyWith(
          OnboardingItemModel value, $Res Function(OnboardingItemModel) _then) =
      _$OnboardingItemModelCopyWithImpl;
  @useResult
  $Res call({String imgPath, String title, String desc});
}

/// @nodoc
class _$OnboardingItemModelCopyWithImpl<$Res>
    implements $OnboardingItemModelCopyWith<$Res> {
  _$OnboardingItemModelCopyWithImpl(this._self, this._then);

  final OnboardingItemModel _self;
  final $Res Function(OnboardingItemModel) _then;

  /// Create a copy of OnboardingItemModel
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? imgPath = null,
    Object? title = null,
    Object? desc = null,
  }) {
    return _then(_self.copyWith(
      imgPath: null == imgPath
          ? _self.imgPath
          : imgPath // ignore: cast_nullable_to_non_nullable
              as String,
      title: null == title
          ? _self.title
          : title // ignore: cast_nullable_to_non_nullable
              as String,
      desc: null == desc
          ? _self.desc
          : desc // ignore: cast_nullable_to_non_nullable
              as String,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _OnboardingItemModel implements OnboardingItemModel {
  const _OnboardingItemModel(
      {required this.imgPath, required this.title, required this.desc});
  factory _OnboardingItemModel.fromJson(Map<String, dynamic> json) =>
      _$OnboardingItemModelFromJson(json);

  @override
  final String imgPath;
  @override
  final String title;
  @override
  final String desc;

  /// Create a copy of OnboardingItemModel
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  @pragma('vm:prefer-inline')
  _$OnboardingItemModelCopyWith<_OnboardingItemModel> get copyWith =>
      __$OnboardingItemModelCopyWithImpl<_OnboardingItemModel>(
          this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$OnboardingItemModelToJson(
      this,
    );
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _OnboardingItemModel &&
            (identical(other.imgPath, imgPath) || other.imgPath == imgPath) &&
            (identical(other.title, title) || other.title == title) &&
            (identical(other.desc, desc) || other.desc == desc));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hash(runtimeType, imgPath, title, desc);

  @override
  String toString() {
    return 'OnboardingItemModel(imgPath: $imgPath, title: $title, desc: $desc)';
  }
}

/// @nodoc
abstract mixin class _$OnboardingItemModelCopyWith<$Res>
    implements $OnboardingItemModelCopyWith<$Res> {
  factory _$OnboardingItemModelCopyWith(_OnboardingItemModel value,
          $Res Function(_OnboardingItemModel) _then) =
      __$OnboardingItemModelCopyWithImpl;
  @override
  @useResult
  $Res call({String imgPath, String title, String desc});
}

/// @nodoc
class __$OnboardingItemModelCopyWithImpl<$Res>
    implements _$OnboardingItemModelCopyWith<$Res> {
  __$OnboardingItemModelCopyWithImpl(this._self, this._then);

  final _OnboardingItemModel _self;
  final $Res Function(_OnboardingItemModel) _then;

  /// Create a copy of OnboardingItemModel
  /// with the given fields replaced by the non-null parameter values.
  @override
  @pragma('vm:prefer-inline')
  $Res call({
    Object? imgPath = null,
    Object? title = null,
    Object? desc = null,
  }) {
    return _then(_OnboardingItemModel(
      imgPath: null == imgPath
          ? _self.imgPath
          : imgPath // ignore: cast_nullable_to_non_nullable
              as String,
      title: null == title
          ? _self.title
          : title // ignore: cast_nullable_to_non_nullable
              as String,
      desc: null == desc
          ? _self.desc
          : desc // ignore: cast_nullable_to_non_nullable
              as String,
    ));
  }
}

// dart format on
