import 'package:fitgo_app/src/shared/extensions/build_context/screen_util_ext.dart';
import 'package:fitgo_app/src/shared/widgets/text_widget/text_widget.dart';
import 'package:flutter/material.dart';
import 'package:hooks_riverpod/hooks_riverpod.dart';
import 'package:go_router/go_router.dart';

import 'package:fitgo_app/src/app_provider.dart';
import 'package:fitgo_app/src/shared/constants/app_path.dart';
import 'package:fitgo_app/src/shared/constants/app_text_style.dart';
import 'package:fitgo_app/src/shared/enums/user_type.dart';
import 'package:fitgo_app/src/shared/extensions/string_hardcoded.dart';
import 'package:fitgo_app/src/shared/widgets/buttons/language_button.dart';
import 'package:fitgo_app/src/shared/widgets/buttons/raised_button_widget.dart';
import 'package:fitgo_app/src/shared/widgets/image/asset_image.dart';
import 'package:fitgo_app/src/shared/widgets/scaffold/onboarding_scaffold.dart';
import 'package:fitgo_app/src/theme/colors.dart';

class LandingView extends ConsumerWidget {
  const LandingView({super.key});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    return OnboardingScaffold(
      topBar: Align(alignment: Alignment.topRight, child: LanguageButton()),
      bottomBar: Column(
        children: [
          RaisedButtonWidget(
            width: double.infinity,
            text: 'Continue as an Athlete'.hardcoded,
            borderRadius: 15,
            fontColor: AColor.buttonTextColor,
            bgColor: AColor.buttonColor,
            fontStyle: ATextStyle.buttonText,
            borderSide: BorderSide(color: AColor.textSecondaryColor, width: 2),
            onPressed: () async {
              await ref
                  .read(currentUserTypeProvider.notifier)
                  .setUserType(UserType.student);
              await ref.read(landingPageSeenProvider.notifier).markSeen();
              if (context.mounted) {
                context.go('/onboarding');
              }
            },
          ),
          const SizedBox(height: 16),
          RaisedButtonWidget(
            width: double.infinity,
            text: 'Continue as a Trainer'.hardcoded,
            borderRadius: 15,
            fontColor: AColor.buttonTextColor,
            bgColor: AColor.buttonColor,
            fontStyle: ATextStyle.buttonText,
            borderSide: BorderSide(color: AColor.textSecondaryColor, width: 2),
            onPressed: () async {
              await ref
                  .read(currentUserTypeProvider.notifier)
                  .setUserType(UserType.instructor);
              await ref.read(landingPageSeenProvider.notifier).markSeen();
              if (context.mounted) {
                context.go('/onboarding');
              }
            },
          ),
          const SizedBox(height: 40),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.center,
        children: [
          const SizedBox(height: 100),
          AImage(imgPath: APath.appLogo, width: context.width / 2),
          const SizedBox(height: 60),
          TextWidget('Welcome to FitGo!'.hardcoded, style: ATextStyle.title),
          const SizedBox(height: 16),
          TextWidget(
            'Do you want to achieve your goals as an athlete, or do you want to manage your own students as a trainer?'
                .hardcoded,
            textAlign: TextAlign.center,
            style: ATextStyle.description,
          ),
        ],
      ),
    );
  }
}
