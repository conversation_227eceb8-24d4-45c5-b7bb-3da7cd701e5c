// ignore: unused_import
import 'package:intl/intl.dart' as intl;
import 'app_localizations.dart';

// ignore_for_file: type=lint

/// The translations for Turkish (`tr`).
class AppLocalizationsTr extends AppLocalizations {
  AppLocalizationsTr([String locale = 'tr']) : super(locale);

  @override
  String get appTitle => 'Fitgo Mobil';

  @override
  String get start => 'Başla';

  @override
  String get termsOfServiceAndPrivacyPolicy =>
      '<link href=\"termsOfService\">Hizmet Koşulları</link> ve <link href=\"privacyPolicy\">Gizlilik Sözleşmesi</link>';

  @override
  String get registerAgreement =>
      '<link>Üyelik Sözleşmesini</link>, <link>Veri Koruma ve İşletme Politikasını</link>,\n<link>Müşteri Aydınlatma Metnini</link>, <link>Gizlilik ve Çerez Politikasını</link>\nokuduğumu ve kabul ettiğimi onaylıyorum.';

  @override
  String minCharacterError(int charLength) {
    return 'Karakter sayısı en az $charLength olabilir';
  }

  @override
  String get dashboard => 'Panel';
}
