// ignore: unused_import
import 'package:intl/intl.dart' as intl;
import 'app_localizations.dart';

// ignore_for_file: type=lint

/// The translations for English (`en`).
class AppLocalizationsEn extends AppLocalizations {
  AppLocalizationsEn([String locale = 'en']) : super(locale);

  @override
  String get appTitle => 'Fitgo app';

  @override
  String get start => 'Get Started';

  @override
  String get termsOfServiceAndPrivacyPolicy =>
      '<link href=\"termsOfService\">Terms of Service</link> and <link href=\"privacyPolicy\">Privacy Policy</link>';

  @override
  String get registerAgreement =>
      'I confirm that I have read and accepted the <link>Membership Agreement</link>, <link>Data Protection and Business Policy</link>, <link>Customer Clarification Text</link>, <link>Privacy and Cookie Policy</link>.';

  @override
  String minCharacterError(int charLength) {
    return 'The number of characters can be at least $charLength';
  }

  @override
  String get dashboard => 'Dashboard';
}
