import 'package:fitgo_app/src/shared/enums/user_type.dart';
import 'package:fitgo_app/src/shared/enums/gender.dart';

/// User profile model
class Profile {
  const Profile({
    required this.id,
    required this.email,
    this.name,
    this.surname,
    this.phone,
    this.avatarUrl,
    this.dateOfBirth,
    this.gender,
    this.bio,
    this.role = UserType.student,
    this.firebaseToken,
    this.createdAt,
    this.updatedAt,
  });

  final String id;
  final String email;
  final String? name;
  final String? surname;
  final String? phone;
  final String? avatarUrl;
  final DateTime? dateOfBirth;
  final Gender? gender;
  final String? bio;
  final UserType role;
  final String? firebaseToken;
  final DateTime? createdAt;
  final DateTime? updatedAt;

  /// Create a copy of Profile with updated fields
  Profile copyWith({
    String? id,
    String? email,
    String? name,
    String? surname,
    String? phone,
    String? avatarUrl,
    DateTime? dateOfBirth,
    Gender? gender,
    String? bio,
    UserType? role,
    String? firebaseToken,
    DateTime? createdAt,
    DateTime? updatedAt,
  }) {
    return Profile(
      id: id ?? this.id,
      email: email ?? this.email,
      name: name ?? this.name,
      surname: surname ?? this.surname,
      phone: phone ?? this.phone,
      avatarUrl: avatarUrl ?? this.avatarUrl,
      dateOfBirth: dateOfBirth ?? this.dateOfBirth,
      gender: gender ?? this.gender,
      bio: bio ?? this.bio,
      role: role ?? this.role,
      firebaseToken: firebaseToken ?? this.firebaseToken,
      createdAt: createdAt ?? this.createdAt,
      updatedAt: updatedAt ?? this.updatedAt,
    );
  }

  /// Convert to JSON
  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'email': email,
      'name': name,
      'surname': surname,
      'phone': phone,
      'avatar_url': avatarUrl,
      'date_of_birth': dateOfBirth?.toIso8601String(),
      'gender': gender?.databaseValue,
      'bio': bio,
      'role': role.name, // Convert enum to string
      'firebase_token': firebaseToken,
      'created_at': createdAt?.toIso8601String(),
      'updated_at': updatedAt?.toIso8601String(),
    };
  }

  /// Create from JSON
  factory Profile.fromJson(Map<String, dynamic> json) {
    return Profile(
      id: json['id'] as String,
      email: json['email'] as String,
      name: json['name'] as String?,
      surname: json['surname'] as String?,
      phone: json['phone'] as String?,
      avatarUrl: json['avatar_url'] as String?,
      dateOfBirth:
          json['date_of_birth'] != null
              ? DateTime.parse(json['date_of_birth'] as String)
              : null,
      gender: Gender.fromDatabaseValue(json['gender'] as String?),
      bio: json['bio'] as String?,
      role: _parseUserType(json['role'] as String?),
      firebaseToken: json['firebase_token'] as String?,
      createdAt:
          json['created_at'] != null
              ? DateTime.parse(json['created_at'] as String)
              : null,
      updatedAt:
          json['updated_at'] != null
              ? DateTime.parse(json['updated_at'] as String)
              : null,
    );
  }

  /// Helper method to parse UserType from string
  static UserType _parseUserType(String? roleString) {
    if (roleString == null) return UserType.student;
    switch (roleString.toLowerCase()) {
      case 'instructor':
        return UserType.instructor;
      case 'student':
      default:
        return UserType.student;
    }
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is Profile &&
        other.id == id &&
        other.email == email &&
        other.name == name &&
        other.surname == surname &&
        other.phone == phone &&
        other.avatarUrl == avatarUrl &&
        other.dateOfBirth == dateOfBirth &&
        other.gender == gender &&
        other.bio == bio &&
        other.role == role &&
        other.firebaseToken == firebaseToken &&
        other.createdAt == createdAt &&
        other.updatedAt == updatedAt;
  }

  @override
  int get hashCode {
    return Object.hash(
      id,
      email,
      name,
      surname,
      phone,
      avatarUrl,
      dateOfBirth,
      gender,
      bio,
      role,
      firebaseToken,
      createdAt,
      updatedAt,
    );
  }

  @override
  String toString() {
    return 'Profile(id: $id, email: $email, name: $name, surname: $surname, phone: $phone, avatarUrl: $avatarUrl, dateOfBirth: $dateOfBirth, gender: $gender, bio: $bio, role: $role, firebaseToken: $firebaseToken, createdAt: $createdAt, updatedAt: $updatedAt)';
  }
}

/// Extension to add utility methods to Profile
extension ProfileX on Profile {
  /// Get full name by combining name and surname
  String get fullName {
    if (name == null && surname == null) return '';
    if (name == null) return surname!;
    if (surname == null) return name!;
    return '$name $surname';
  }

  /// Check if profile has complete basic information
  bool get hasBasicInfo {
    return name != null && surname != null && email.isNotEmpty;
  }

  /// Get display name (full name or email if name is not available)
  String get displayName {
    final full = fullName;
    return full.isNotEmpty ? full : email;
  }
}

// import 'package:fitgo_app/src/auth/domain/pasa_enum.dart';
// import 'package:fitgo_app/src/shared/enums/user_type.dart';
// import 'package:freezed_annotation/freezed_annotation.dart';

// part 'profile.freezed.dart';
// part 'profile.g.dart';

// @freezed
// abstract class Profile with _$Profile {
//   @JsonSerializable(fieldRename: FieldRename.snake)
//   const factory Profile({
//     required String id,
//     required String email,
//     @Default(PasaEnum.student) UserType role,
//     String? name,
//     String? surname,
//     String? phone,
//     String? avatarUrl,
//     DateTime? dateOfBirth,
//     String? gender,
//     String? bio,
//     String? firebaseToken,
//     DateTime? createdAt,
//     DateTime? updatedAt,
//   }) = _Profile;

//   factory Profile.fromJson(Map<String, dynamic> json) =>
//       _$ProfileFromJson(json);
// }
