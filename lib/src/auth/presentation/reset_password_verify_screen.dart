import 'package:fitgo_app/src/shared/extensions/string_hardcoded.dart';
import 'package:flutter/material.dart';
import 'package:hooks_riverpod/hooks_riverpod.dart';
import 'package:flutter_hooks/flutter_hooks.dart';
import 'package:go_router/go_router.dart';
import 'package:supabase_flutter/supabase_flutter.dart';
import 'package:fitgo_app/src/shared/constants/app_text_style.dart';
import 'package:fitgo_app/src/shared/constants/app_path.dart';
import 'package:fitgo_app/src/shared/widgets/scaffold/onboarding_scaffold.dart';
import 'package:fitgo_app/src/shared/widgets/buttons/language_button.dart';
import 'package:fitgo_app/src/shared/widgets/buttons/app_bar_back_button.dart';
import 'package:fitgo_app/src/shared/widgets/buttons/raised_button_widget.dart';
import 'package:fitgo_app/src/shared/widgets/buttons/flat_button_widget.dart';
import 'package:fitgo_app/src/shared/widgets/text_widget/text_widget.dart';
import 'package:fitgo_app/src/shared/widgets/image/asset_image.dart';
import 'package:fitgo_app/src/theme/colors.dart';

class ResetPasswordVerifyScreen extends HookConsumerWidget {
  const ResetPasswordVerifyScreen({super.key, required this.token, this.email});

  final String token;
  final String? email;

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final isLoading = useState(true);
    final errorMessage = useState<String?>(null);

    Future<void> verifyToken() async {
      try {
        isLoading.value = true;
        errorMessage.value = null;

        // Verify the token with Supabase
        final response = await Supabase.instance.client.auth.verifyOTP(
          token: token,
          type: OtpType.recovery,
          email: email,
        );

        if (response.session != null) {
          // Token verified successfully, navigate to reset password
          final accessToken = response.session!.accessToken;
          final refreshToken = response.session!.refreshToken;

          if (context.mounted) {
            context.go(
              '/reset-password?access_token=$accessToken&refresh_token=$refreshToken&email=${email ?? ''}'
                  .hardcoded,
            );
          }
        } else {
          errorMessage.value = 'Token verification failed'.hardcoded;
        }
      } catch (e) {
        print('Token verification error: $e'.hardcoded);
        errorMessage.value =
            'Şifre sıfırlama bağlantısının süresi dolmuş veya geçersiz.'
                .hardcoded;
      } finally {
        isLoading.value = false;
      }
    }

    useEffect(() {
      // Verify token and exchange for session
      WidgetsBinding.instance.addPostFrameCallback((_) {
        verifyToken();
      });
      return null;
    }, []);

    return OnboardingScaffold(
      topBar: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          AppBarBackButton(onTap: () => context.go('/auth')),
          const LanguageButton(),
        ],
      ),
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          // Logo or Icon
          Container(
            width: 80,
            height: 80,
            decoration: BoxDecoration(
              color: AColor.fitgoGreen,
              borderRadius: BorderRadius.circular(40),
            ),
            child: const Icon(Icons.lock_reset, color: Colors.white, size: 40),
          ),
          const SizedBox(height: 32),

          // Title
          TextWidget(
            'Şifre Sıfırlama'.hardcoded,
            style: ATextStyle.title,
            textAlign: TextAlign.center,
          ),
          const SizedBox(height: 16),

          if (isLoading.value) ...[
            // Loading state
            const CircularProgressIndicator(
              valueColor: AlwaysStoppedAnimation<Color>(AColor.fitgoGreen),
            ),
            const SizedBox(height: 16),
            TextWidget(
              'Bağlantı doğrulanıyor...'.hardcoded,
              style: ATextStyle.description,
              textAlign: TextAlign.center,
            ),
          ] else if (errorMessage.value != null) ...[
            // Error state
            const Icon(Icons.error_outline, color: Colors.red, size: 48),
            const SizedBox(height: 16),
            TextWidget(
              errorMessage.value!,
              style: ATextStyle.description.copyWith(color: Colors.red),
              textAlign: TextAlign.center,
            ),
            const SizedBox(height: 24),

            // Retry button
            RaisedButtonWidget(
              width: double.infinity,
              text: 'Yeni Bağlantı İste'.hardcoded,
              borderRadius: 15,
              fontColor: AColor.buttonTextColor,
              bgColor: AColor.buttonColor,
              fontStyle: ATextStyle.buttonText,
              borderSide: BorderSide(
                color: AColor.textSecondaryColor,
                width: 2,
              ),
              onPressed: () => context.go('/forgot-password'),
            ),
            const SizedBox(height: 16),

            // Back to login
            FlatButtonWidget(
              text: 'Giriş Sayfasına Dön'.hardcoded,
              fontColor: AColor.fitgoGreen,
              onPressed: () => context.go('/auth'),
            ),
          ],
        ],
      ),
    );
  }
}
