import 'package:fitgo_app/src/auth/application/auth_provider.dart';
import 'package:fitgo_app/src/auth/domain/profile.dart';
import 'package:fitgo_app/src/app_provider.dart';
import 'package:fitgo_app/src/shared/providers/auth_provider.dart';
import 'package:fitgo_app/src/shared/providers/supabase_provider.dart';
import 'package:fitgo_app/src/student/application/workflow_providers.dart';
import 'package:fitgo_app/src/student/application/student_workflow_service.dart';
import 'package:fitgo_app/src/shared/constants/app_fonts.dart';
import 'package:fitgo_app/src/shared/constants/app_text_style.dart';
import 'package:fitgo_app/src/shared/enums/regex_type.dart';
import 'package:fitgo_app/src/shared/enums/user_type.dart';
import 'package:fitgo_app/src/shared/enums/gender.dart';

import 'package:fitgo_app/src/shared/extensions/build_context/screen_util_ext.dart';
import 'package:fitgo_app/src/shared/extensions/string_hardcoded.dart';
import 'package:fitgo_app/src/shared/extensions/string_extensions.dart';
import 'package:fitgo_app/src/shared/utils/dt_util/dt_util.dart';
import 'package:fitgo_app/src/shared/utils/error_message_helper.dart';
import 'package:fitgo_app/src/shared/widgets/buttons/app_bar_back_button.dart';
import 'package:fitgo_app/src/shared/widgets/buttons/raised_button_widget.dart';
import 'package:fitgo_app/src/shared/widgets/text_form_field/custom_text_form_field.dart';
import 'package:fitgo_app/src/shared/widgets/phone_input_field/phone_input_field.dart';
import 'package:fitgo_app/src/shared/widgets/gender_dropdown_widget.dart';
import 'package:fitgo_app/src/shared/widgets/text_widget/text_widget.dart';
import 'package:flutter/material.dart';
import 'package:flutter_hooks/flutter_hooks.dart';
import 'package:hooks_riverpod/hooks_riverpod.dart';
import 'package:go_router/go_router.dart';
import 'package:country_code_picker/country_code_picker.dart';
import 'package:url_launcher/url_launcher.dart';

import 'package:fitgo_app/src/shared/constants/app_path.dart';
import 'package:fitgo_app/src/shared/widgets/buttons/language_button.dart';
import 'package:fitgo_app/src/shared/widgets/image/asset_image.dart';
import 'package:fitgo_app/src/shared/widgets/scaffold/onboarding_scaffold.dart';
import 'package:fitgo_app/src/theme/colors.dart';
import 'package:styled_text/styled_text.dart';

part 'components/login_bottom_sheet.dart';
part 'components/register_bottom_sheet.dart';
part 'components/login_form.dart';

class AuthPageView extends HookConsumerWidget {
  const AuthPageView({super.key});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    // Auth state listener removed - navigation is now handled directly in login forms

    return OnboardingScaffold(
      topBar: Align(alignment: Alignment.topRight, child: LanguageButton()),
      child: SingleChildScrollView(
        child: ConstrainedBox(
          constraints: BoxConstraints(
            minHeight:
                context.height - context.paddingTop - context.paddingBottom,
          ),
          child: IntrinsicHeight(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.center,
              children: [
                SizedBox(height: context.height * 0.05),
                AImage(imgPath: APath.appLogo, width: context.width / 2.5),
                SizedBox(height: context.height * 0.05),
                Expanded(
                  child: Container(
                    width: double.infinity,
                    padding: const EdgeInsets.symmetric(
                      horizontal: 24,
                      vertical: 32,
                    ),
                    decoration: BoxDecoration(
                      color: AColor.bottomSheetBackgroundColor,
                      borderRadius: const BorderRadius.only(
                        topLeft: Radius.circular(30),
                        topRight: Radius.circular(30),
                      ),
                      boxShadow: [
                        BoxShadow(
                          color: Colors.black.withValues(alpha: 0.1),
                          blurRadius: 10,
                          offset: const Offset(0, -5),
                        ),
                      ],
                    ),
                    child: const LoginForm(),
                  ),
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }
}
