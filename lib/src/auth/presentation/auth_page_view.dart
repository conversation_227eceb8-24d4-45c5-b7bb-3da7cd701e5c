import 'package:fitgo_app/src/auth/application/auth_provider.dart';
import 'package:fitgo_app/src/auth/domain/profile.dart';
import 'package:fitgo_app/src/app_provider.dart';
import 'package:fitgo_app/src/shared/providers/auth_provider.dart';
import 'package:fitgo_app/src/shared/providers/supabase_provider.dart';
import 'package:fitgo_app/src/student/application/workflow_providers.dart';
import 'package:fitgo_app/src/student/application/student_workflow_service.dart';
import 'package:fitgo_app/src/shared/constants/app_fonts.dart';
import 'package:fitgo_app/src/shared/constants/app_text_style.dart';
import 'package:fitgo_app/src/shared/enums/regex_type.dart';
import 'package:fitgo_app/src/shared/enums/user_type.dart';
import 'package:fitgo_app/src/shared/enums/gender.dart';

import 'package:fitgo_app/src/shared/extensions/build_context/screen_util_ext.dart';
import 'package:fitgo_app/src/shared/extensions/string_hardcoded.dart';
import 'package:fitgo_app/src/shared/extensions/string_extensions.dart';
import 'package:fitgo_app/src/shared/utils/dt_util/dt_util.dart';
import 'package:fitgo_app/src/shared/utils/error_message_helper.dart';
import 'package:fitgo_app/src/shared/widgets/buttons/app_bar_back_button.dart';
import 'package:fitgo_app/src/shared/widgets/buttons/raised_button_widget.dart';
import 'package:fitgo_app/src/shared/widgets/text_form_field/custom_text_form_field.dart';
import 'package:fitgo_app/src/shared/widgets/phone_input_field/phone_input_field.dart';
import 'package:fitgo_app/src/shared/widgets/gender_dropdown_widget.dart';
import 'package:fitgo_app/src/shared/widgets/text_widget/text_widget.dart';
import 'package:flutter/material.dart';
import 'package:flutter_hooks/flutter_hooks.dart';
import 'package:hooks_riverpod/hooks_riverpod.dart';
import 'package:go_router/go_router.dart';
import 'package:country_code_picker/country_code_picker.dart';
import 'package:url_launcher/url_launcher.dart';

import 'package:fitgo_app/src/shared/constants/app_path.dart';
import 'package:fitgo_app/src/shared/widgets/buttons/language_button.dart';
import 'package:fitgo_app/src/shared/widgets/image/asset_image.dart';
import 'package:fitgo_app/src/shared/widgets/scaffold/onboarding_scaffold.dart';
import 'package:fitgo_app/src/theme/colors.dart';
import 'package:styled_text/styled_text.dart';

part 'components/login_bottom_sheet.dart';
part 'components/register_bottom_sheet.dart';
part 'components/login_form.dart';

class AuthPageView extends HookConsumerWidget {
  const AuthPageView({super.key});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    // Listen to auth state changes
    ref.listen<bool>(isAuthenticatedProvider, (previous, next) {
      if (next && context.mounted) {
        debugPrint('🔄 Auth state changed - user is now authenticated');

        // Use post frame callback to ensure navigation happens after current frame
        WidgetsBinding.instance.addPostFrameCallback((_) {
          if (!context.mounted) return;

          // Navigate based on user type
          final userType = ref.read(currentUserTypeProvider);
          debugPrint('👤 Current user type: $userType');

          if (userType == UserType.instructor) {
            debugPrint('🚀 Navigating instructor to: /instructor-main');
            context.go('/instructor-main');
          } else {
            // For students, get workflow state and navigate
            debugPrint('🎓 Getting student workflow state...');

            Future.microtask(() async {
              try {
                final service = ref.read(studentWorkflowServiceProvider);
                final workflowState = await service.getCurrentWorkflowState();

                debugPrint('🎓 Student workflow state: $workflowState');

                if (context.mounted) {
                  switch (workflowState) {
                    case StudentWorkflowState.needsCourseSelection:
                      debugPrint('🚀 Navigating student to: /course-list');
                      context.go('/course-list');
                      break;
                    case StudentWorkflowState.needsProfileForm:
                      debugPrint('🚀 Navigating student to: /profile-form');
                      context.go('/profile-form');
                      break;
                    case StudentWorkflowState.waitingForPlan:
                      debugPrint(
                        '🚀 Navigating student to: /form-approval-waiting',
                      );
                      context.go('/form-approval-waiting');
                      break;
                    case StudentWorkflowState.hasActivePlan:
                      debugPrint('🚀 Navigating student to: /dashboard');
                      context.go('/dashboard');
                      break;
                  }
                }
              } catch (e) {
                debugPrint('❌ Error getting student workflow state: $e');
                if (context.mounted) {
                  debugPrint(
                    '🚀 Error fallback: Navigating student to: /course-list',
                  );
                  context.go('/course-list');
                }
              }
            });
          }
        });
      }
    });

    return OnboardingScaffold(
      topBar: Align(alignment: Alignment.topRight, child: LanguageButton()),
      child: SingleChildScrollView(
        child: ConstrainedBox(
          constraints: BoxConstraints(
            minHeight:
                context.height - context.paddingTop - context.paddingBottom,
          ),
          child: IntrinsicHeight(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.center,
              children: [
                SizedBox(height: context.height * 0.05),
                AImage(imgPath: APath.appLogo, width: context.width / 2.5),
                SizedBox(height: context.height * 0.05),
                Expanded(
                  child: Container(
                    width: double.infinity,
                    padding: const EdgeInsets.symmetric(
                      horizontal: 24,
                      vertical: 32,
                    ),
                    decoration: BoxDecoration(
                      color: AColor.bottomSheetBackgroundColor,
                      borderRadius: const BorderRadius.only(
                        topLeft: Radius.circular(30),
                        topRight: Radius.circular(30),
                      ),
                      boxShadow: [
                        BoxShadow(
                          color: Colors.black.withValues(alpha: 0.1),
                          blurRadius: 10,
                          offset: const Offset(0, -5),
                        ),
                      ],
                    ),
                    child: const LoginForm(),
                  ),
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }
}
