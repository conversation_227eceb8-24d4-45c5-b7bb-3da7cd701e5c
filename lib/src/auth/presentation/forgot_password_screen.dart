import 'package:fitgo_app/src/shared/extensions/string_hardcoded.dart';
import 'package:flutter/material.dart';
import 'package:hooks_riverpod/hooks_riverpod.dart';
import 'package:flutter_hooks/flutter_hooks.dart';
import 'package:go_router/go_router.dart';
import 'package:fitgo_app/src/auth/application/auth_provider.dart';
import 'package:fitgo_app/src/shared/constants/app_text_style.dart';
import 'package:fitgo_app/src/shared/constants/app_path.dart';
import 'package:fitgo_app/src/shared/widgets/scaffold/onboarding_scaffold.dart';
import 'package:fitgo_app/src/shared/widgets/buttons/language_button.dart';
import 'package:fitgo_app/src/shared/widgets/buttons/app_bar_back_button.dart';
import 'package:fitgo_app/src/shared/widgets/buttons/raised_button_widget.dart';
import 'package:fitgo_app/src/shared/widgets/text_widget/text_widget.dart';
import 'package:fitgo_app/src/shared/widgets/image/asset_image.dart';
import 'package:fitgo_app/src/shared/widgets/text_form_field/custom_text_form_field.dart';
import 'package:fitgo_app/src/shared/enums/regex_type.dart';
import 'package:fitgo_app/src/shared/extensions/build_context/screen_util_ext.dart';
import 'package:fitgo_app/src/theme/colors.dart';

class ForgotPasswordScreen extends HookConsumerWidget {
  const ForgotPasswordScreen({super.key});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final emailController = useTextEditingController();
    final formKey = useMemoized(() => GlobalKey<FormState>());
    final authState = ref.watch(authNotifierProvider);
    final authNotifier = ref.read(authNotifierProvider.notifier);

    // Listen to auth state changes
    ref.listen<AuthState>(authNotifierProvider, (previous, next) {
      if (next.isError) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text(next.errorMessage ?? 'An error occurred'.hardcoded),
            backgroundColor: Colors.red,
          ),
        );
      } else if (next.isInitial && (previous?.isLoading ?? false)) {
        // Password reset email sent successfully
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Password reset link sent to your email'.hardcoded),
            backgroundColor: Colors.green,
          ),
        );
        // Navigate back to login
        context.pop();
      }
    });

    return OnboardingScaffold(
      topBar: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          AppBarBackButton(onTap: () => context.pop()),
          const LanguageButton(),
        ],
      ),
      child: SingleChildScrollView(
        padding: EdgeInsets.only(bottom: context.viewInsets.bottom + 20),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.center,
          children: [
            const SizedBox(height: 60),

            // FitGo Logo
            AImage(imgPath: APath.appLogo, width: 120),

            const SizedBox(height: 60),

            // Title
            TextWidget(
              'Forgot Password'.hardcoded,
              style: ATextStyle.title,
              textAlign: TextAlign.center,
            ),

            const SizedBox(height: 16),

            // Description
            TextWidget(
              'You can continue your process with the password reset link that will come to your registered e-mail address.'
                  .hardcoded,
              style: ATextStyle.description,
              textAlign: TextAlign.center,
            ),

            const SizedBox(height: 40),

            // Form
            Form(
              key: formKey,
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  // Email input with CustomTextFormField
                  CustomTextFormField(
                    controller: emailController,
                    headerText: 'E-mail'.hardcoded,
                    hintText: '<EMAIL>'.hardcoded,
                    keyboardType: TextInputType.emailAddress,
                    textInputAction: TextInputAction.done,
                    regexType: RegexType.eMail,
                    headerTextStyle: ATextStyle.medium.copyWith(
                      color: Colors.white,
                      fontWeight: FontWeight.w500,
                    ),
                    validator: (value) {
                      if (value == null || value.isEmpty) {
                        return 'Please enter your email'.hardcoded;
                      }
                      // Simple email validation
                      if (!RegExp(
                        r'^[\w-\.]+@([\w-]+\.)+[\w-]{2,4}$',
                      ).hasMatch(value)) {
                        return 'Please enter a valid email'.hardcoded;
                      }
                      return null;
                    },
                  ),

                  const SizedBox(height: 40),

                  // Send Reset Link button
                  RaisedButtonWidget(
                    width: double.infinity,
                    text:
                        authState.isLoading ? '' : 'Send Reset Link'.hardcoded,
                    borderRadius: 15,
                    fontColor: AColor.buttonTextColor,
                    bgColor: AColor.buttonColor,
                    fontStyle: ATextStyle.buttonText,
                    borderSide: BorderSide(
                      color: AColor.textSecondaryColor,
                      width: 2,
                    ),
                    onPressed:
                        authState.isLoading
                            ? null
                            : () {
                              if (formKey.currentState!.validate()) {
                                authNotifier.resetPassword(
                                  emailController.text.trim(),
                                );
                              }
                            },
                    child:
                        authState.isLoading
                            ? const SizedBox(
                              height: 20,
                              width: 20,
                              child: CircularProgressIndicator(
                                strokeWidth: 2,
                                valueColor: AlwaysStoppedAnimation<Color>(
                                  Colors.white,
                                ),
                              ),
                            )
                            : null,
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }
}
