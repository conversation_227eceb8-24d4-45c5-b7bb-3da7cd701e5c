part of '../auth_page_view.dart';

class LoginForm extends HookConsumerWidget {
  const LoginForm({super.key});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final emailCtrl = useTextEditingController();
    final passwordCtrl = useTextEditingController();
    final isLoading = useState(false);
    final isPasswordVisible = useState(false);
    final formKey = useMemoized(() => GlobalKey<FormState>());

    void openRegister() {
      ref.read(isRegisterOpenProvider.notifier).state = true;
      showModalBottomSheet(
        context: context,
        isScrollControlled: true,
        backgroundColor: AColor.transparent,
        builder: (_) => const RegisterBottomSheet(),
      ).whenComplete(() {
        ref.read(isRegisterOpenProvider.notifier).state = false;
      });
    }

    Future<void> submit() async {
      if (!(formKey.currentState?.validate() ?? false)) return;

      // Check if widget is still mounted before setting loading state
      if (!context.mounted) return;
      isLoading.value = true;

      try {
        debugPrint('🔐 Starting login process with NEW architecture...');
        debugPrint('📧 Email: ${emailCtrl.text.trim()}');

        // Use consistent auth repository (same as register)
        final authRepo = ref.read(authRepositoryProvider);
        debugPrint('🔗 Auth repository obtained');

        final profile = await authRepo.signInWithEmail(
          email: emailCtrl.text.trim(),
          password: passwordCtrl.text.trim(),
        );

        debugPrint('✅ Supabase login successful!');
        debugPrint('👤 User: ${profile.email}');
        debugPrint('✅ Profile data: ${profile.toJson()}');

        // Update the global user type provider with the actual database role
        await ref
            .read(currentUserTypeProvider.notifier)
            .setUserType(profile.role);
        debugPrint('🔄 Updated global user type to: ${profile.role}');

        // Check if widget is still mounted before continuing
        if (!context.mounted) return;

        debugPrint('✅ Login successful, navigating...');

        // Navigate based on user type
        if (profile.role == UserType.instructor) {
          debugPrint('🚀 Navigating instructor to: /instructor-main');
          context.go('/instructor-main');
        } else {
          debugPrint('🔍 Checking student workflow state...');

          // Get student workflow service
          final workflowService = ref.read(studentWorkflowServiceProvider);

          try {
            final state = await workflowService.getCurrentWorkflowState();
            debugPrint('📊 Student workflow state: $state');

            if (context.mounted) {
              switch (state) {
                case StudentWorkflowState.needsProfileForm:
                  debugPrint('🚀 Navigating student to: /profile-form');
                  context.go('/profile-form');
                  break;
                case StudentWorkflowState.waitingForPlan:
                  debugPrint(
                    '🚀 Navigating student to: /form-approval-waiting',
                  );
                  context.go('/form-approval-waiting');
                  break;
                case StudentWorkflowState.hasActivePlan:
                  debugPrint('🚀 Navigating student to: /dashboard');
                  context.go('/dashboard');
                  break;
                case StudentWorkflowState.needsCourseSelection:
                  debugPrint('🚀 Navigating student to: /course-list');
                  context.go('/course-list');
                  break;
              }
            }
          } catch (e) {
            debugPrint('❌ Error getting student workflow state: $e');
            if (context.mounted) {
              debugPrint(
                '🚀 Error fallback: Navigating student to: /course-list',
              );
              context.go('/course-list');
            }
          }
        }
      } catch (err) {
        debugPrint('❌ Login error: $err');
        debugPrint('❌ Error type: ${err.runtimeType}');

        if (!context.mounted) return;

        final userFriendlyMessage = ErrorMessageHelper.getLoginErrorMessage(
          err,
        );
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text(userFriendlyMessage),
            backgroundColor: Colors.red,
            duration: const Duration(seconds: 4),
          ),
        );
      } finally {
        if (context.mounted) {
          isLoading.value = false;
        }
      }
    }

    return Column(
      crossAxisAlignment: CrossAxisAlignment.stretch,
      children: [
        // Title
        Text(
          'Welcome Back!'.hardcoded,
          style: ATextStyle.title.copyWith(
            color: AColor.textColor,
            fontSize: 28,
            fontWeight: FontWeight.bold,
          ),
          textAlign: TextAlign.center,
        ),
        const SizedBox(height: 8),
        Text(
          'Sign in to continue your fitness journey'.hardcoded,
          style: ATextStyle.medium.copyWith(
            color: AColor.descriptionColor,
            fontSize: 16,
          ),
          textAlign: TextAlign.center,
        ),
        const SizedBox(height: 32),

        // Form
        Form(
          key: formKey,
          child: Column(
            spacing: 20,
            children: [
              CustomTextFormField(
                controller: emailCtrl,
                headerText: 'E-mail'.hardcoded,
                keyboardType: TextInputType.emailAddress,
                textInputAction: TextInputAction.next,
                validator: (value) => value.isValidMail(context),
                regexType: RegexType.eMail,
              ),
              Column(
                crossAxisAlignment: CrossAxisAlignment.end,
                children: [
                  CustomTextFormField(
                    controller: passwordCtrl,
                    headerText: 'Password'.hardcoded,
                    keyboardType: TextInputType.visiblePassword,
                    textInputAction: TextInputAction.done,
                    validator: (value) => value.isValidPassword(context),
                    regexType: RegexType.password,
                    obscureText: !isPasswordVisible.value,
                    suffixIcon: IconButton(
                      onPressed: () {
                        isPasswordVisible.value = !isPasswordVisible.value;
                      },
                      icon: Icon(
                        isPasswordVisible.value
                            ? Icons.visibility
                            : Icons.visibility_off,
                        color: AColor.grey,
                      ),
                    ),
                  ),
                  const SizedBox(height: 8),
                  InkWell(
                    onTap: () {
                      // Navigate to forgot password screen
                      context.push('/forgot-password');
                    },
                    child: TextWidget(
                      'Forgot Password?'.hardcoded,
                      style: ATextStyle.medium.copyWith(
                        color: AColor.fitgoGreen,
                        decoration: TextDecoration.underline,
                        decorationColor: AColor.fitgoGreen,
                      ),
                    ),
                  ),
                ],
              ),
            ],
          ),
        ),

        const SizedBox(height: 32),

        // Login Button
        RaisedButtonWidget(
          width: double.infinity,
          text:
              isLoading.value ? 'Signing In...'.hardcoded : 'Sign In'.hardcoded,
          borderRadius: 15,
          fontColor: AColor.buttonTextColor,
          bgColor: AColor.buttonColor,
          fontStyle: ATextStyle.buttonText,
          onPressed: isLoading.value ? null : submit,
        ),

        const SizedBox(height: 24),

        // Register Link
        Row(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            TextWidget(
              "Don't have an account? ".hardcoded,
              style: ATextStyle.medium.copyWith(color: AColor.descriptionColor),
            ),
            InkWell(
              onTap: openRegister,
              child: TextWidget(
                'Sign Up'.hardcoded,
                style: ATextStyle.medium.copyWith(
                  color: AColor.fitgoGreen,
                  fontWeight: FontWeight.bold,
                  decoration: TextDecoration.underline,
                  decorationColor: AColor.fitgoGreen,
                ),
              ),
            ),
          ],
        ),

        const SizedBox(height: 16),
      ],
    );
  }
}
