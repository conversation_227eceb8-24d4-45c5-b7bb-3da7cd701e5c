import 'package:hooks_riverpod/hooks_riverpod.dart';
import 'package:fitgo_app/src/auth/repository/auth_repository_impl.dart';
import 'package:fitgo_app/src/auth/domain/profile.dart';
import 'package:fitgo_app/src/shared/enums/gender.dart';
import 'package:fitgo_app/src/shared/providers/supabase_provider.dart';
import 'package:fitgo_app/core/services/onboarding_service.dart';

/// UI state provider for register form
final isRegisterOpenProvider = StateProvider<bool>((ref) => false);

/// Auth repository provider
final authRepositoryProvider = Provider<AuthRepository>((ref) {
  final supabaseService = ref.watch(supabaseServiceProvider);
  return AuthRepositoryImpl(supabaseService.client);
});

/// Auth state notifier provider
final authNotifierProvider = StateNotifierProvider<AuthNotifier, AuthState>((
  ref,
) {
  final authRepository = ref.watch(authRepositoryProvider);
  final onboardingService = ref.watch(onboardingServiceProvider);
  return AuthNotifier(authRepository, onboardingService);
});

/// Auth state notifier
class AuthNotifier extends StateNotifier<AuthState> {
  AuthNotifier(this._authRepository, this._onboardingService)
    : super(const AuthState.initial());

  final AuthRepository _authRepository;
  final OnboardingService _onboardingService;

  /// Sign in with email and password
  Future<void> signInWithEmail({
    required String email,
    required String password,
  }) async {
    state = const AuthState.loading();
    try {
      await _authRepository.signInWithEmail(email: email, password: password);
      state = const AuthState.authenticated();
    } catch (error) {
      state = AuthState.error(error.toString());
    }
  }

  /// Sign up with email and password
  Future<void> signUpWithEmail({
    required String email,
    required String password,
    String? name,
    String? surname,
    String? phone,
    Gender? gender,
  }) async {
    state = const AuthState.loading();
    try {
      final profile = Profile(
        id: '', // Will be set by the repository
        email: email,
        name: name,
        surname: surname,
        phone: phone,
        gender: gender,
      );

      await _authRepository.registerUser(user: profile, password: password);
      state = const AuthState.authenticated();
    } catch (error) {
      state = AuthState.error(error.toString());
    }
  }

  /// Sign out current user
  Future<void> signOut() async {
    state = const AuthState.loading();
    try {
      await _authRepository.signOut();
      // Clear onboarding data on logout
      await _onboardingService.clearOnboardingData();
      state = const AuthState.unauthenticated();
    } catch (error) {
      state = AuthState.error(error.toString());
    }
  }

  /// Reset password
  Future<void> resetPassword(String email) async {
    state = const AuthState.loading();
    try {
      await _authRepository.sendPasswordResetEmail(email);
      state = const AuthState.initial();
    } catch (error) {
      state = AuthState.error(error.toString());
    }
  }

  /// Update password with reset token
  Future<void> updatePassword(
    String newPassword,
    String? accessToken,
    String? refreshToken,
  ) async {
    state = const AuthState.loading();
    try {
      await _authRepository.updatePasswordWithToken(
        newPassword,
        accessToken,
        refreshToken,
      );
      state = const AuthState.initial();
    } catch (error) {
      state = AuthState.error(error.toString());
    }
  }
}

/// Auth state sealed class
sealed class AuthState {
  const AuthState();

  const factory AuthState.initial() = _Initial;
  const factory AuthState.loading() = _Loading;
  const factory AuthState.authenticated() = _Authenticated;
  const factory AuthState.unauthenticated() = _Unauthenticated;
  const factory AuthState.error(String message) = _Error;
}

class _Initial extends AuthState {
  const _Initial();
}

class _Loading extends AuthState {
  const _Loading();
}

class _Authenticated extends AuthState {
  const _Authenticated();
}

class _Unauthenticated extends AuthState {
  const _Unauthenticated();
}

class _Error extends AuthState {
  const _Error(this.message);
  final String message;
}

/// Extension for convenient state checking
extension AuthStateExtension on AuthState {
  bool get isInitial => this is _Initial;
  bool get isLoading => this is _Loading;
  bool get isAuthenticated => this is _Authenticated;
  bool get isUnauthenticated => this is _Unauthenticated;
  bool get isError => this is _Error;

  String? get errorMessage => this is _Error ? (this as _Error).message : null;
}
