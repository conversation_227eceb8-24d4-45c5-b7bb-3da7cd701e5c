// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'enrollment_models.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

_UserEnrollment _$UserEnrollmentFromJson(Map<String, dynamic> json) =>
    _UserEnrollment(
      id: json['id'] as String,
      userId: json['userId'] as String,
      instructorId: json['instructorId'] as String,
      isActive: json['isActive'] as bool,
      enrolledAt: DateTime.parse(json['enrolledAt'] as String),
      expiresAt: json['expiresAt'] == null
          ? null
          : DateTime.parse(json['expiresAt'] as String),
      progressPercentage:
          (json['progressPercentage'] as num?)?.toDouble() ?? 0.0,
      createdAt: json['createdAt'] == null
          ? null
          : DateTime.parse(json['createdAt'] as String),
      updatedAt: json['updatedAt'] == null
          ? null
          : DateTime.parse(json['updatedAt'] as String),
    );

Map<String, dynamic> _$UserEnrollmentToJson(_UserEnrollment instance) =>
    <String, dynamic>{
      'id': instance.id,
      'userId': instance.userId,
      'instructorId': instance.instructorId,
      'isActive': instance.isActive,
      'enrolledAt': instance.enrolledAt.toIso8601String(),
      'expiresAt': instance.expiresAt?.toIso8601String(),
      'progressPercentage': instance.progressPercentage,
      'createdAt': instance.createdAt?.toIso8601String(),
      'updatedAt': instance.updatedAt?.toIso8601String(),
    };

_EnrollmentCheckResult _$EnrollmentCheckResultFromJson(
        Map<String, dynamic> json) =>
    _EnrollmentCheckResult(
      hasActiveEnrollment: json['hasActiveEnrollment'] as bool,
      enrollment: json['enrollment'] == null
          ? null
          : UserEnrollment.fromJson(json['enrollment'] as Map<String, dynamic>),
      error: json['error'] as String?,
    );

Map<String, dynamic> _$EnrollmentCheckResultToJson(
        _EnrollmentCheckResult instance) =>
    <String, dynamic>{
      'hasActiveEnrollment': instance.hasActiveEnrollment,
      'enrollment': instance.enrollment,
      'error': instance.error,
    };

_UserRoleInfo _$UserRoleInfoFromJson(Map<String, dynamic> json) =>
    _UserRoleInfo(
      userId: json['userId'] as String,
      role: json['role'] as String,
      name: json['name'] as String?,
      email: json['email'] as String?,
    );

Map<String, dynamic> _$UserRoleInfoToJson(_UserRoleInfo instance) =>
    <String, dynamic>{
      'userId': instance.userId,
      'role': instance.role,
      'name': instance.name,
      'email': instance.email,
    };
