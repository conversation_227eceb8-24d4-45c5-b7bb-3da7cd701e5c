// dart format width=80
// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'enrollment_models.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

// dart format off
T _$identity<T>(T value) => value;

/// @nodoc
mixin _$UserEnrollment {
  String get id;
  String get userId;
  String get instructorId;
  bool get isActive;
  DateTime get enrolledAt;
  DateTime? get expiresAt;
  double get progressPercentage;
  DateTime? get createdAt;
  DateTime? get updatedAt;

  /// Create a copy of UserEnrollment
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @pragma('vm:prefer-inline')
  $UserEnrollmentCopyWith<UserEnrollment> get copyWith =>
      _$UserEnrollmentCopyWithImpl<UserEnrollment>(
          this as UserEnrollment, _$identity);

  /// Serializes this UserEnrollment to a JSON map.
  Map<String, dynamic> toJson();

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is UserEnrollment &&
            (identical(other.id, id) || other.id == id) &&
            (identical(other.userId, userId) || other.userId == userId) &&
            (identical(other.instructorId, instructorId) ||
                other.instructorId == instructorId) &&
            (identical(other.isActive, isActive) ||
                other.isActive == isActive) &&
            (identical(other.enrolledAt, enrolledAt) ||
                other.enrolledAt == enrolledAt) &&
            (identical(other.expiresAt, expiresAt) ||
                other.expiresAt == expiresAt) &&
            (identical(other.progressPercentage, progressPercentage) ||
                other.progressPercentage == progressPercentage) &&
            (identical(other.createdAt, createdAt) ||
                other.createdAt == createdAt) &&
            (identical(other.updatedAt, updatedAt) ||
                other.updatedAt == updatedAt));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hash(
      runtimeType,
      id,
      userId,
      instructorId,
      isActive,
      enrolledAt,
      expiresAt,
      progressPercentage,
      createdAt,
      updatedAt);

  @override
  String toString() {
    return 'UserEnrollment(id: $id, userId: $userId, instructorId: $instructorId, isActive: $isActive, enrolledAt: $enrolledAt, expiresAt: $expiresAt, progressPercentage: $progressPercentage, createdAt: $createdAt, updatedAt: $updatedAt)';
  }
}

/// @nodoc
abstract mixin class $UserEnrollmentCopyWith<$Res> {
  factory $UserEnrollmentCopyWith(
          UserEnrollment value, $Res Function(UserEnrollment) _then) =
      _$UserEnrollmentCopyWithImpl;
  @useResult
  $Res call(
      {String id,
      String userId,
      String instructorId,
      bool isActive,
      DateTime enrolledAt,
      DateTime? expiresAt,
      double progressPercentage,
      DateTime? createdAt,
      DateTime? updatedAt});
}

/// @nodoc
class _$UserEnrollmentCopyWithImpl<$Res>
    implements $UserEnrollmentCopyWith<$Res> {
  _$UserEnrollmentCopyWithImpl(this._self, this._then);

  final UserEnrollment _self;
  final $Res Function(UserEnrollment) _then;

  /// Create a copy of UserEnrollment
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? id = null,
    Object? userId = null,
    Object? instructorId = null,
    Object? isActive = null,
    Object? enrolledAt = null,
    Object? expiresAt = freezed,
    Object? progressPercentage = null,
    Object? createdAt = freezed,
    Object? updatedAt = freezed,
  }) {
    return _then(_self.copyWith(
      id: null == id
          ? _self.id
          : id // ignore: cast_nullable_to_non_nullable
              as String,
      userId: null == userId
          ? _self.userId
          : userId // ignore: cast_nullable_to_non_nullable
              as String,
      instructorId: null == instructorId
          ? _self.instructorId
          : instructorId // ignore: cast_nullable_to_non_nullable
              as String,
      isActive: null == isActive
          ? _self.isActive
          : isActive // ignore: cast_nullable_to_non_nullable
              as bool,
      enrolledAt: null == enrolledAt
          ? _self.enrolledAt
          : enrolledAt // ignore: cast_nullable_to_non_nullable
              as DateTime,
      expiresAt: freezed == expiresAt
          ? _self.expiresAt
          : expiresAt // ignore: cast_nullable_to_non_nullable
              as DateTime?,
      progressPercentage: null == progressPercentage
          ? _self.progressPercentage
          : progressPercentage // ignore: cast_nullable_to_non_nullable
              as double,
      createdAt: freezed == createdAt
          ? _self.createdAt
          : createdAt // ignore: cast_nullable_to_non_nullable
              as DateTime?,
      updatedAt: freezed == updatedAt
          ? _self.updatedAt
          : updatedAt // ignore: cast_nullable_to_non_nullable
              as DateTime?,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _UserEnrollment implements UserEnrollment {
  const _UserEnrollment(
      {required this.id,
      required this.userId,
      required this.instructorId,
      required this.isActive,
      required this.enrolledAt,
      this.expiresAt,
      this.progressPercentage = 0.0,
      this.createdAt,
      this.updatedAt});
  factory _UserEnrollment.fromJson(Map<String, dynamic> json) =>
      _$UserEnrollmentFromJson(json);

  @override
  final String id;
  @override
  final String userId;
  @override
  final String instructorId;
  @override
  final bool isActive;
  @override
  final DateTime enrolledAt;
  @override
  final DateTime? expiresAt;
  @override
  @JsonKey()
  final double progressPercentage;
  @override
  final DateTime? createdAt;
  @override
  final DateTime? updatedAt;

  /// Create a copy of UserEnrollment
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  @pragma('vm:prefer-inline')
  _$UserEnrollmentCopyWith<_UserEnrollment> get copyWith =>
      __$UserEnrollmentCopyWithImpl<_UserEnrollment>(this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$UserEnrollmentToJson(
      this,
    );
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _UserEnrollment &&
            (identical(other.id, id) || other.id == id) &&
            (identical(other.userId, userId) || other.userId == userId) &&
            (identical(other.instructorId, instructorId) ||
                other.instructorId == instructorId) &&
            (identical(other.isActive, isActive) ||
                other.isActive == isActive) &&
            (identical(other.enrolledAt, enrolledAt) ||
                other.enrolledAt == enrolledAt) &&
            (identical(other.expiresAt, expiresAt) ||
                other.expiresAt == expiresAt) &&
            (identical(other.progressPercentage, progressPercentage) ||
                other.progressPercentage == progressPercentage) &&
            (identical(other.createdAt, createdAt) ||
                other.createdAt == createdAt) &&
            (identical(other.updatedAt, updatedAt) ||
                other.updatedAt == updatedAt));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hash(
      runtimeType,
      id,
      userId,
      instructorId,
      isActive,
      enrolledAt,
      expiresAt,
      progressPercentage,
      createdAt,
      updatedAt);

  @override
  String toString() {
    return 'UserEnrollment(id: $id, userId: $userId, instructorId: $instructorId, isActive: $isActive, enrolledAt: $enrolledAt, expiresAt: $expiresAt, progressPercentage: $progressPercentage, createdAt: $createdAt, updatedAt: $updatedAt)';
  }
}

/// @nodoc
abstract mixin class _$UserEnrollmentCopyWith<$Res>
    implements $UserEnrollmentCopyWith<$Res> {
  factory _$UserEnrollmentCopyWith(
          _UserEnrollment value, $Res Function(_UserEnrollment) _then) =
      __$UserEnrollmentCopyWithImpl;
  @override
  @useResult
  $Res call(
      {String id,
      String userId,
      String instructorId,
      bool isActive,
      DateTime enrolledAt,
      DateTime? expiresAt,
      double progressPercentage,
      DateTime? createdAt,
      DateTime? updatedAt});
}

/// @nodoc
class __$UserEnrollmentCopyWithImpl<$Res>
    implements _$UserEnrollmentCopyWith<$Res> {
  __$UserEnrollmentCopyWithImpl(this._self, this._then);

  final _UserEnrollment _self;
  final $Res Function(_UserEnrollment) _then;

  /// Create a copy of UserEnrollment
  /// with the given fields replaced by the non-null parameter values.
  @override
  @pragma('vm:prefer-inline')
  $Res call({
    Object? id = null,
    Object? userId = null,
    Object? instructorId = null,
    Object? isActive = null,
    Object? enrolledAt = null,
    Object? expiresAt = freezed,
    Object? progressPercentage = null,
    Object? createdAt = freezed,
    Object? updatedAt = freezed,
  }) {
    return _then(_UserEnrollment(
      id: null == id
          ? _self.id
          : id // ignore: cast_nullable_to_non_nullable
              as String,
      userId: null == userId
          ? _self.userId
          : userId // ignore: cast_nullable_to_non_nullable
              as String,
      instructorId: null == instructorId
          ? _self.instructorId
          : instructorId // ignore: cast_nullable_to_non_nullable
              as String,
      isActive: null == isActive
          ? _self.isActive
          : isActive // ignore: cast_nullable_to_non_nullable
              as bool,
      enrolledAt: null == enrolledAt
          ? _self.enrolledAt
          : enrolledAt // ignore: cast_nullable_to_non_nullable
              as DateTime,
      expiresAt: freezed == expiresAt
          ? _self.expiresAt
          : expiresAt // ignore: cast_nullable_to_non_nullable
              as DateTime?,
      progressPercentage: null == progressPercentage
          ? _self.progressPercentage
          : progressPercentage // ignore: cast_nullable_to_non_nullable
              as double,
      createdAt: freezed == createdAt
          ? _self.createdAt
          : createdAt // ignore: cast_nullable_to_non_nullable
              as DateTime?,
      updatedAt: freezed == updatedAt
          ? _self.updatedAt
          : updatedAt // ignore: cast_nullable_to_non_nullable
              as DateTime?,
    ));
  }
}

/// @nodoc
mixin _$EnrollmentCheckResult {
  bool get hasActiveEnrollment;
  UserEnrollment? get enrollment;
  String? get error;

  /// Create a copy of EnrollmentCheckResult
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @pragma('vm:prefer-inline')
  $EnrollmentCheckResultCopyWith<EnrollmentCheckResult> get copyWith =>
      _$EnrollmentCheckResultCopyWithImpl<EnrollmentCheckResult>(
          this as EnrollmentCheckResult, _$identity);

  /// Serializes this EnrollmentCheckResult to a JSON map.
  Map<String, dynamic> toJson();

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is EnrollmentCheckResult &&
            (identical(other.hasActiveEnrollment, hasActiveEnrollment) ||
                other.hasActiveEnrollment == hasActiveEnrollment) &&
            (identical(other.enrollment, enrollment) ||
                other.enrollment == enrollment) &&
            (identical(other.error, error) || other.error == error));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode =>
      Object.hash(runtimeType, hasActiveEnrollment, enrollment, error);

  @override
  String toString() {
    return 'EnrollmentCheckResult(hasActiveEnrollment: $hasActiveEnrollment, enrollment: $enrollment, error: $error)';
  }
}

/// @nodoc
abstract mixin class $EnrollmentCheckResultCopyWith<$Res> {
  factory $EnrollmentCheckResultCopyWith(EnrollmentCheckResult value,
          $Res Function(EnrollmentCheckResult) _then) =
      _$EnrollmentCheckResultCopyWithImpl;
  @useResult
  $Res call(
      {bool hasActiveEnrollment, UserEnrollment? enrollment, String? error});

  $UserEnrollmentCopyWith<$Res>? get enrollment;
}

/// @nodoc
class _$EnrollmentCheckResultCopyWithImpl<$Res>
    implements $EnrollmentCheckResultCopyWith<$Res> {
  _$EnrollmentCheckResultCopyWithImpl(this._self, this._then);

  final EnrollmentCheckResult _self;
  final $Res Function(EnrollmentCheckResult) _then;

  /// Create a copy of EnrollmentCheckResult
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? hasActiveEnrollment = null,
    Object? enrollment = freezed,
    Object? error = freezed,
  }) {
    return _then(_self.copyWith(
      hasActiveEnrollment: null == hasActiveEnrollment
          ? _self.hasActiveEnrollment
          : hasActiveEnrollment // ignore: cast_nullable_to_non_nullable
              as bool,
      enrollment: freezed == enrollment
          ? _self.enrollment
          : enrollment // ignore: cast_nullable_to_non_nullable
              as UserEnrollment?,
      error: freezed == error
          ? _self.error
          : error // ignore: cast_nullable_to_non_nullable
              as String?,
    ));
  }

  /// Create a copy of EnrollmentCheckResult
  /// with the given fields replaced by the non-null parameter values.
  @override
  @pragma('vm:prefer-inline')
  $UserEnrollmentCopyWith<$Res>? get enrollment {
    if (_self.enrollment == null) {
      return null;
    }

    return $UserEnrollmentCopyWith<$Res>(_self.enrollment!, (value) {
      return _then(_self.copyWith(enrollment: value));
    });
  }
}

/// @nodoc
@JsonSerializable()
class _EnrollmentCheckResult implements EnrollmentCheckResult {
  const _EnrollmentCheckResult(
      {required this.hasActiveEnrollment, this.enrollment, this.error});
  factory _EnrollmentCheckResult.fromJson(Map<String, dynamic> json) =>
      _$EnrollmentCheckResultFromJson(json);

  @override
  final bool hasActiveEnrollment;
  @override
  final UserEnrollment? enrollment;
  @override
  final String? error;

  /// Create a copy of EnrollmentCheckResult
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  @pragma('vm:prefer-inline')
  _$EnrollmentCheckResultCopyWith<_EnrollmentCheckResult> get copyWith =>
      __$EnrollmentCheckResultCopyWithImpl<_EnrollmentCheckResult>(
          this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$EnrollmentCheckResultToJson(
      this,
    );
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _EnrollmentCheckResult &&
            (identical(other.hasActiveEnrollment, hasActiveEnrollment) ||
                other.hasActiveEnrollment == hasActiveEnrollment) &&
            (identical(other.enrollment, enrollment) ||
                other.enrollment == enrollment) &&
            (identical(other.error, error) || other.error == error));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode =>
      Object.hash(runtimeType, hasActiveEnrollment, enrollment, error);

  @override
  String toString() {
    return 'EnrollmentCheckResult(hasActiveEnrollment: $hasActiveEnrollment, enrollment: $enrollment, error: $error)';
  }
}

/// @nodoc
abstract mixin class _$EnrollmentCheckResultCopyWith<$Res>
    implements $EnrollmentCheckResultCopyWith<$Res> {
  factory _$EnrollmentCheckResultCopyWith(_EnrollmentCheckResult value,
          $Res Function(_EnrollmentCheckResult) _then) =
      __$EnrollmentCheckResultCopyWithImpl;
  @override
  @useResult
  $Res call(
      {bool hasActiveEnrollment, UserEnrollment? enrollment, String? error});

  @override
  $UserEnrollmentCopyWith<$Res>? get enrollment;
}

/// @nodoc
class __$EnrollmentCheckResultCopyWithImpl<$Res>
    implements _$EnrollmentCheckResultCopyWith<$Res> {
  __$EnrollmentCheckResultCopyWithImpl(this._self, this._then);

  final _EnrollmentCheckResult _self;
  final $Res Function(_EnrollmentCheckResult) _then;

  /// Create a copy of EnrollmentCheckResult
  /// with the given fields replaced by the non-null parameter values.
  @override
  @pragma('vm:prefer-inline')
  $Res call({
    Object? hasActiveEnrollment = null,
    Object? enrollment = freezed,
    Object? error = freezed,
  }) {
    return _then(_EnrollmentCheckResult(
      hasActiveEnrollment: null == hasActiveEnrollment
          ? _self.hasActiveEnrollment
          : hasActiveEnrollment // ignore: cast_nullable_to_non_nullable
              as bool,
      enrollment: freezed == enrollment
          ? _self.enrollment
          : enrollment // ignore: cast_nullable_to_non_nullable
              as UserEnrollment?,
      error: freezed == error
          ? _self.error
          : error // ignore: cast_nullable_to_non_nullable
              as String?,
    ));
  }

  /// Create a copy of EnrollmentCheckResult
  /// with the given fields replaced by the non-null parameter values.
  @override
  @pragma('vm:prefer-inline')
  $UserEnrollmentCopyWith<$Res>? get enrollment {
    if (_self.enrollment == null) {
      return null;
    }

    return $UserEnrollmentCopyWith<$Res>(_self.enrollment!, (value) {
      return _then(_self.copyWith(enrollment: value));
    });
  }
}

/// @nodoc
mixin _$UserRoleInfo {
  String get userId;
  String get role;
  String? get name;
  String? get email;

  /// Create a copy of UserRoleInfo
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @pragma('vm:prefer-inline')
  $UserRoleInfoCopyWith<UserRoleInfo> get copyWith =>
      _$UserRoleInfoCopyWithImpl<UserRoleInfo>(
          this as UserRoleInfo, _$identity);

  /// Serializes this UserRoleInfo to a JSON map.
  Map<String, dynamic> toJson();

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is UserRoleInfo &&
            (identical(other.userId, userId) || other.userId == userId) &&
            (identical(other.role, role) || other.role == role) &&
            (identical(other.name, name) || other.name == name) &&
            (identical(other.email, email) || other.email == email));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hash(runtimeType, userId, role, name, email);

  @override
  String toString() {
    return 'UserRoleInfo(userId: $userId, role: $role, name: $name, email: $email)';
  }
}

/// @nodoc
abstract mixin class $UserRoleInfoCopyWith<$Res> {
  factory $UserRoleInfoCopyWith(
          UserRoleInfo value, $Res Function(UserRoleInfo) _then) =
      _$UserRoleInfoCopyWithImpl;
  @useResult
  $Res call({String userId, String role, String? name, String? email});
}

/// @nodoc
class _$UserRoleInfoCopyWithImpl<$Res> implements $UserRoleInfoCopyWith<$Res> {
  _$UserRoleInfoCopyWithImpl(this._self, this._then);

  final UserRoleInfo _self;
  final $Res Function(UserRoleInfo) _then;

  /// Create a copy of UserRoleInfo
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? userId = null,
    Object? role = null,
    Object? name = freezed,
    Object? email = freezed,
  }) {
    return _then(_self.copyWith(
      userId: null == userId
          ? _self.userId
          : userId // ignore: cast_nullable_to_non_nullable
              as String,
      role: null == role
          ? _self.role
          : role // ignore: cast_nullable_to_non_nullable
              as String,
      name: freezed == name
          ? _self.name
          : name // ignore: cast_nullable_to_non_nullable
              as String?,
      email: freezed == email
          ? _self.email
          : email // ignore: cast_nullable_to_non_nullable
              as String?,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _UserRoleInfo implements UserRoleInfo {
  const _UserRoleInfo(
      {required this.userId, required this.role, this.name, this.email});
  factory _UserRoleInfo.fromJson(Map<String, dynamic> json) =>
      _$UserRoleInfoFromJson(json);

  @override
  final String userId;
  @override
  final String role;
  @override
  final String? name;
  @override
  final String? email;

  /// Create a copy of UserRoleInfo
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  @pragma('vm:prefer-inline')
  _$UserRoleInfoCopyWith<_UserRoleInfo> get copyWith =>
      __$UserRoleInfoCopyWithImpl<_UserRoleInfo>(this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$UserRoleInfoToJson(
      this,
    );
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _UserRoleInfo &&
            (identical(other.userId, userId) || other.userId == userId) &&
            (identical(other.role, role) || other.role == role) &&
            (identical(other.name, name) || other.name == name) &&
            (identical(other.email, email) || other.email == email));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hash(runtimeType, userId, role, name, email);

  @override
  String toString() {
    return 'UserRoleInfo(userId: $userId, role: $role, name: $name, email: $email)';
  }
}

/// @nodoc
abstract mixin class _$UserRoleInfoCopyWith<$Res>
    implements $UserRoleInfoCopyWith<$Res> {
  factory _$UserRoleInfoCopyWith(
          _UserRoleInfo value, $Res Function(_UserRoleInfo) _then) =
      __$UserRoleInfoCopyWithImpl;
  @override
  @useResult
  $Res call({String userId, String role, String? name, String? email});
}

/// @nodoc
class __$UserRoleInfoCopyWithImpl<$Res>
    implements _$UserRoleInfoCopyWith<$Res> {
  __$UserRoleInfoCopyWithImpl(this._self, this._then);

  final _UserRoleInfo _self;
  final $Res Function(_UserRoleInfo) _then;

  /// Create a copy of UserRoleInfo
  /// with the given fields replaced by the non-null parameter values.
  @override
  @pragma('vm:prefer-inline')
  $Res call({
    Object? userId = null,
    Object? role = null,
    Object? name = freezed,
    Object? email = freezed,
  }) {
    return _then(_UserRoleInfo(
      userId: null == userId
          ? _self.userId
          : userId // ignore: cast_nullable_to_non_nullable
              as String,
      role: null == role
          ? _self.role
          : role // ignore: cast_nullable_to_non_nullable
              as String,
      name: freezed == name
          ? _self.name
          : name // ignore: cast_nullable_to_non_nullable
              as String?,
      email: freezed == email
          ? _self.email
          : email // ignore: cast_nullable_to_non_nullable
              as String?,
    ));
  }
}

// dart format on
