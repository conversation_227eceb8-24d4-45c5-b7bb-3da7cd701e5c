import 'package:freezed_annotation/freezed_annotation.dart';

part 'enrollment_models.freezed.dart';
part 'enrollment_models.g.dart';

@freezed
abstract class UserEnrollment with _$UserEnrollment {
  const factory UserEnrollment({
    required String id,
    required String userId,
    required String instructorId,
    required bool isActive,
    required DateTime enrolledAt,
    DateTime? expiresAt,
    @Default(0.0) double progressPercentage,
    DateTime? createdAt,
    DateTime? updatedAt,
  }) = _UserEnrollment;

  factory UserEnrollment.fromJson(Map<String, dynamic> json) =>
      _$UserEnrollmentFromJson(json);
}

@freezed
abstract class EnrollmentCheckResult with _$EnrollmentCheckResult {
  const factory EnrollmentCheckResult({
    required bool hasActiveEnrollment,
    UserEnrollment? enrollment,
    String? error,
  }) = _EnrollmentCheckResult;

  factory EnrollmentCheckResult.fromJson(Map<String, dynamic> json) =>
      _$EnrollmentCheckResultFromJson(json);
}

@freezed
abstract class UserRoleInfo with _$UserRoleInfo {
  const factory UserRoleInfo({
    required String userId,
    required String role,
    String? name,
    String? email,
  }) = _UserRoleInfo;

  factory UserRoleInfo.fromJson(Map<String, dynamic> json) =>
      _$UserRoleInfoFromJson(json);
}
