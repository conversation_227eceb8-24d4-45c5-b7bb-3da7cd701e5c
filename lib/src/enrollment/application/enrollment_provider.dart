import 'package:hooks_riverpod/hooks_riverpod.dart';
import 'package:supabase_flutter/supabase_flutter.dart';
import '../repository/enrollment_repository.dart';
import '../domain/enrollment_models.dart';

/// Provider for enrollment repository
final enrollmentRepositoryProvider = Provider<IEnrollmentRepository>((ref) {
  final supabase = Supabase.instance.client;
  return EnrollmentRepository(supabase);
});

/// Provider for checking user enrollment status
final userEnrollmentProvider = FutureProvider.family<EnrollmentCheckResult, String>((ref, userId) async {
  final repository = ref.read(enrollmentRepositoryProvider);
  return repository.checkUserEnrollment(userId);
});

/// Provider for getting user role information
final userRoleProvider = FutureProvider.family<UserRoleInfo?, String>((ref, userId) async {
  final repository = ref.read(enrollmentRepositoryProvider);
  return repository.getUserRole(userId);
});

/// Provider for checking if user is instructor
final isUserInstructorProvider = FutureProvider.family<bool, String>((ref, userId) async {
  final repository = ref.read(enrollmentRepositoryProvider);
  return repository.isUserInstructor(userId);
});

/// Provider for current user enrollment status
final currentUserEnrollmentProvider = FutureProvider<EnrollmentCheckResult?>((ref) async {
  final currentUser = Supabase.instance.client.auth.currentUser;
  if (currentUser == null) return null;
  
  final repository = ref.read(enrollmentRepositoryProvider);
  return repository.checkUserEnrollment(currentUser.id);
});

/// Provider for current user role
final currentUserRoleProvider = FutureProvider<UserRoleInfo?>((ref) async {
  final currentUser = Supabase.instance.client.auth.currentUser;
  if (currentUser == null) return null;
  
  final repository = ref.read(enrollmentRepositoryProvider);
  return repository.getUserRole(currentUser.id);
});

/// Provider for checking if current user is instructor
final isCurrentUserInstructorProvider = FutureProvider<bool>((ref) async {
  final currentUser = Supabase.instance.client.auth.currentUser;
  if (currentUser == null) return false;
  
  final repository = ref.read(enrollmentRepositoryProvider);
  return repository.isUserInstructor(currentUser.id);
});
