import 'package:supabase_flutter/supabase_flutter.dart';
import 'package:flutter/foundation.dart';
import '../domain/enrollment_models.dart';
import '../../shared/utils/app_logger.dart';

/// Abstract interface for enrollment repository
abstract interface class IEnrollmentRepository {
  Future<EnrollmentCheckResult> checkUserEnrollment(String userId);
  Future<UserRoleInfo?> getUserRole(String userId);
  Future<bool> isUserInstructor(String userId);
}

/// Concrete implementation of enrollment repository using Supabase
class EnrollmentRepository implements IEnrollmentRepository {
  EnrollmentRepository(this._supabase);

  final SupabaseClient _supabase;

  @override
  Future<EnrollmentCheckResult> checkUserEnrollment(String userId) async {
    try {
      AppLogger.info('🔍 Checking enrollment for user: $userId',
          tag: 'ENROLLMENT');
      // Check enrollment in clean architecture
      var response = await _supabase
          .from('enrollments')
          .select(
              'id, student_id, instructor_id, is_active, enrolled_at, expires_at, created_at')
          .eq('student_id', userId)
          .eq('is_active', true)
          .maybeSingle();

      if (response == null) {
        AppLogger.warning('❌ No active enrollment found for user: $userId',
            tag: 'ENROLLMENT');
        return const EnrollmentCheckResult(
          hasActiveEnrollment: false,
        );
      }

      final enrollment = UserEnrollment(
        id: response['id'] as String,
        userId: response['student_id']
            as String, // Clean architecture uses student_id
        instructorId: response['instructor_id'] as String,
        isActive: response['is_active'] as bool,
        enrolledAt: _parseDateTime(response['enrolled_at'])!,
        expiresAt: response['expires_at'] != null
            ? _parseDateTime(response['expires_at'])
            : null,
        progressPercentage:
            0.0, // Default value since completion_percentage is not used yet
        createdAt: response['created_at'] != null
            ? _parseDateTime(response['created_at'])
            : null,
        updatedAt: null, // Will be added later if needed
      );

      AppLogger.success('✅ Active enrollment found for user: $userId',
          tag: 'ENROLLMENT');
      return EnrollmentCheckResult(
        hasActiveEnrollment: true,
        enrollment: enrollment,
      );
    } catch (e) {
      AppLogger.error('❌ Error checking enrollment for user $userId',
          tag: 'ENROLLMENT', error: e);
      return EnrollmentCheckResult(
        hasActiveEnrollment: false,
        error: e.toString(),
      );
    }
  }

  @override
  Future<UserRoleInfo?> getUserRole(String userId) async {
    try {
      AppLogger.info('🔍 Checking user role for: $userId', tag: 'USER_ROLE');

      // Direct query to profiles table
      final profileResponse = await _supabase
          .from('profiles')
          .select('id, role, name, email')
          .eq('id', userId)
          .maybeSingle();

      if (profileResponse != null) {
        AppLogger.success(
            '✅ Found user role in profiles: ${profileResponse['role']}',
            tag: 'USER_ROLE');
        return UserRoleInfo.fromJson({
          'userId': profileResponse['id'],
          'role': profileResponse['role'],
          'name': profileResponse['name'],
          'email': profileResponse['email'],
        });
      } else {
        AppLogger.warning('❌ No user role found in profiles for: $userId',
            tag: 'USER_ROLE');
        return null;
      }
    } catch (e) {
      AppLogger.error('❌ Error getting user role for $userId: $e',
          tag: 'USER_ROLE', error: e);
      return null;
    }
  }

  @override
  Future<bool> isUserInstructor(String userId) async {
    try {
      AppLogger.info('🔍 Checking if user is instructor: $userId',
          tag: 'USER_ROLE');

      // DIRECT CHECK: Query profiles table directly to bypass RLS issues
      try {
        final profileResponse = await _supabase
            .from('profiles')
            .select('role')
            .eq('id', userId)
            .maybeSingle();

        if (profileResponse != null) {
          final role = profileResponse['role'] as String;
          final isInstructor = role == 'instructor';
          AppLogger.success(
              '✅ Direct profile check - Role: $role, IsInstructor: $isInstructor',
              tag: 'USER_ROLE');
          return isInstructor;
        } else {
          AppLogger.warning('❌ No profile found for user: $userId',
              tag: 'USER_ROLE');
          return false;
        }
      } catch (e) {
        AppLogger.error('❌ Error checking profile role: $e',
            tag: 'USER_ROLE');

        // Fallback: check instructors table directly
        final instructorResponse = await _supabase
            .from('instructors')
            .select('id')
            .eq('id', userId)
            .maybeSingle();

        final isInstructor = instructorResponse != null;
        AppLogger.success(
            '✅ Fallback instructor table check: ${isInstructor ? 'INSTRUCTOR' : 'STUDENT'}',
            tag: 'USER_ROLE');
        return isInstructor;
      }
    } catch (e) {
      AppLogger.error('❌ Error checking instructor status for $userId',
          tag: 'USER_ROLE', error: e);
      return false;
    }
  }

  /// Helper method to parse DateTime from Supabase response
  DateTime? _parseDateTime(dynamic value) {
    if (value == null) return null;
    if (value is DateTime) return value;
    if (value is String) return DateTime.parse(value);
    return null;
  }
}
