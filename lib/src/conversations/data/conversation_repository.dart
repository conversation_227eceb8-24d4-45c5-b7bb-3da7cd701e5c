import 'package:supabase_flutter/supabase_flutter.dart';
import '../domain/conversation.dart';
import '../domain/question_tag.dart';
import '../../shared/utils/app_logger.dart';

class ConversationRepository {
  final SupabaseClient _supabase;

  ConversationRepository({SupabaseClient? supabase})
      : _supabase = supabase ?? Supabase.instance.client;

  // Get all available question tags
  Future<List<QuestionTag>> getQuestionTags() async {
    try {
      AppLogger.userAction('🏷️ Fetching question tags', tag: 'CONVERSATION_REPO');
      
      final response = await _supabase
          .from('question_tags')
          .select()
          .eq('is_active', true)
          .order('sort_order');

      final tags = (response as List)
          .map((json) => QuestionTag.fromJson(_mapTagFromDb(json)))
          .toList();

      AppLogger.userAction('✅ Fetched ${tags.length} question tags', tag: 'CONVERSATION_REPO');
      return tags;
    } catch (e) {
      AppLogger.userAction('❌ Error fetching question tags: $e', tag: 'CONVERSATION_REPO');
      rethrow;
    }
  }

  // Check if student has an open question with instructor
  Future<Conversation?> getOpenConversation(String studentId, String instructorId) async {
    try {
      AppLogger.userAction('🔍 Checking for open conversation: student=$studentId, instructor=$instructorId',
          tag: 'CONVERSATION_REPO');

      final response = await _supabase
          .from('conversations')
          .select('''
            *,
            question_tags!inner(*)
          ''')
          .eq('student_id', studentId)
          .eq('instructor_id', instructorId)
          .eq('status', 'pending')
          .maybeSingle();

      if (response == null) {
        AppLogger.userAction('📭 No open conversation found', tag: 'CONVERSATION_REPO');
        return null;
      }

      final conversation = Conversation.fromJson(_mapConversationFromDb(response));
      AppLogger.userAction('✅ Found open conversation: ${conversation.id}', tag: 'CONVERSATION_REPO');
      return conversation;
    } catch (e) {
      AppLogger.userAction('❌ Error checking open conversation: $e', tag: 'CONVERSATION_REPO');
      rethrow;
    }
  }

  // Send a new question
  Future<Conversation> sendQuestion({
    required String studentId,
    required String instructorId,
    required String tagId,
    required String questionText,
  }) async {
    try {
      AppLogger.userAction('📤 Sending question: student=$studentId, instructor=$instructorId, tag=$tagId',
          tag: 'CONVERSATION_REPO');

      final response = await _supabase
          .from('conversations')
          .insert({
            'student_id': studentId,
            'instructor_id': instructorId,
            'tag_id': tagId,
            'question_text': questionText,
            'status': 'pending',
          })
          .select('''
            *,
            question_tags!inner(*)
          ''')
          .single();

      final conversation = Conversation.fromJson(_mapConversationFromDb(response));
      AppLogger.userAction('✅ Question sent successfully: ${conversation.id}', tag: 'CONVERSATION_REPO');
      return conversation;
    } catch (e) {
      AppLogger.userAction('❌ Error sending question: $e', tag: 'CONVERSATION_REPO');
      rethrow;
    }
  }

  // Get pending questions for instructor
  Future<List<Conversation>> getPendingQuestions(String instructorId) async {
    try {
      AppLogger.userAction('📥 Fetching pending questions for instructor: $instructorId',
          tag: 'CONVERSATION_REPO');

      final response = await _supabase
          .from('conversations')
          .select('''
            *,
            question_tags!inner(*),
            profiles!conversations_student_id_fkey(name)
          ''')
          .eq('instructor_id', instructorId)
          .eq('status', 'pending')
          .order('question_sent_at', ascending: false);

      final conversations = (response as List)
          .map((json) => Conversation.fromJson(_mapConversationFromDb(json)))
          .toList();

      AppLogger.userAction('✅ Fetched ${conversations.length} pending questions', tag: 'CONVERSATION_REPO');
      return conversations;
    } catch (e) {
      AppLogger.userAction('❌ Error fetching pending questions: $e', tag: 'CONVERSATION_REPO');
      rethrow;
    }
  }

  // Send response to a question
  Future<Conversation> sendResponse({
    required String conversationId,
    required String responseText,
  }) async {
    try {
      AppLogger.userAction('📤 Sending response to conversation: $conversationId',
          tag: 'CONVERSATION_REPO');

      final response = await _supabase
          .from('conversations')
          .update({
            'response_text': responseText,
            'status': 'answered',
            'response_sent_at': DateTime.now().toIso8601String(),
          })
          .eq('id', conversationId)
          .select('''
            *,
            question_tags!inner(*)
          ''')
          .single();

      final conversation = Conversation.fromJson(_mapConversationFromDb(response));
      AppLogger.userAction('✅ Response sent successfully: ${conversation.id}', tag: 'CONVERSATION_REPO');
      return conversation;
    } catch (e) {
      AppLogger.userAction('❌ Error sending response: $e', tag: 'CONVERSATION_REPO');
      rethrow;
    }
  }

  // Get conversation history for student
  Future<List<Conversation>> getStudentConversations(String studentId) async {
    try {
      AppLogger.userAction('📚 Fetching conversation history for student: $studentId',
          tag: 'CONVERSATION_REPO');

      final response = await _supabase
          .from('conversations')
          .select('''
            *,
            question_tags!inner(*),
            profiles!conversations_instructor_id_fkey(name)
          ''')
          .eq('student_id', studentId)
          .order('question_sent_at', ascending: false);

      final conversations = (response as List)
          .map((json) => Conversation.fromJson(_mapConversationFromDb(json)))
          .toList();

      AppLogger.userAction('✅ Fetched ${conversations.length} conversations', tag: 'CONVERSATION_REPO');
      return conversations;
    } catch (e) {
      AppLogger.userAction('❌ Error fetching student conversations: $e', tag: 'CONVERSATION_REPO');
      rethrow;
    }
  }

  // Helper method to map tag data from database
  Map<String, dynamic> _mapTagFromDb(Map<String, dynamic> json) {
    return {
      'id': json['id'],
      'name': json['name'],
      'displayName': json['display_name'],
      'description': json['description'],
      'isActive': json['is_active'],
      'sortOrder': json['sort_order'],
      'createdAt': json['created_at'],
      'updatedAt': json['updated_at'],
    };
  }

  // Helper method to map conversation data from database
  Map<String, dynamic> _mapConversationFromDb(Map<String, dynamic> json) {
    final result = {
      'id': json['id'],
      'studentId': json['student_id'],
      'instructorId': json['instructor_id'],
      'tagId': json['tag_id'],
      'questionText': json['question_text'],
      'responseText': json['response_text'],
      'status': json['status'],
      'questionSentAt': json['question_sent_at'],
      'responseSentAt': json['response_sent_at'],
      'createdAt': json['created_at'],
      'updatedAt': json['updated_at'],
    };

    // Add tag information if available
    if (json['question_tags'] != null) {
      result['tag'] = _mapTagFromDb(json['question_tags']);
    }

    // Add student/instructor information if available
    if (json['profiles'] != null && json['profiles'] is Map) {
      final profile = json['profiles'] as Map<String, dynamic>;
      // This could be either student or instructor profile depending on the query
      result['studentName'] = profile['name'];
      result['instructorName'] = profile['name'];
      result['studentAvatar'] = null; // Will be implemented later
      result['instructorAvatar'] = null; // Will be implemented later
    }

    return result;
  }
}
