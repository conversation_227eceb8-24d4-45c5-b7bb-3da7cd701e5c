import 'package:flutter/material.dart';
import 'package:flutter_hooks/flutter_hooks.dart';
import 'package:hooks_riverpod/hooks_riverpod.dart';
import 'package:go_router/go_router.dart';
import '../../shared/widgets/text_widget/text_widget.dart';
import '../../shared/constants/app_text_style.dart';
import '../../shared/extensions/string_hardcoded.dart';
import '../../theme/colors.dart';
import '../../shared/utils/app_logger.dart';
import '../../shared/providers/auth_provider.dart';
import '../../enrollment/application/enrollment_provider.dart';
import '../application/conversation_providers.dart';
import '../domain/conversation.dart';
import '../domain/question_tag.dart';

class StudentQuestionsScreen extends HookConsumerWidget {
  const StudentQuestionsScreen({super.key});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final currentUserAsync = ref.watch(currentUserProvider);
    
    return Scaffold(
      backgroundColor: const Color(0xFF0F172A),
      appBar: AppBar(
        backgroundColor: const Color(0xFF0F172A),
        elevation: 0,
        leading: IconButton(
          icon: const Icon(Icons.arrow_back, color: Colors.white),
          onPressed: () => context.pop(),
        ),
        title: TextWidget(
          'My Questions'.hardcoded,
          style: ATextStyle.title.copyWith(color: Colors.white),
        ),
        actions: [
          IconButton(
            icon: const Icon(Icons.add, color: AColor.fitgoGreen),
            onPressed: () => _showAskQuestionModal(context, ref),
          ),
        ],
      ),
      body: currentUserAsync.when(
        loading: () => const Center(child: CircularProgressIndicator()),
        error: (error, stack) => Center(
          child: TextWidget(
            'Error: $error',
            style: ATextStyle.medium.copyWith(color: Colors.red),
          ),
        ),
        data: (currentUser) {
          if (currentUser == null) {
            return Center(
              child: TextWidget(
                'Please log in to view your questions',
                style: ATextStyle.medium.copyWith(color: Colors.white),
              ),
            );
          }
          
          final conversationsAsync = ref.watch(studentConversationsProvider(currentUser.id));
          
          return conversationsAsync.when(
            loading: () => const Center(child: CircularProgressIndicator()),
            error: (error, stack) => Center(
              child: TextWidget(
                'Error loading conversations: $error',
                style: ATextStyle.medium.copyWith(color: Colors.red),
              ),
            ),
            data: (conversations) {
              if (conversations.isEmpty) {
                return _buildEmptyState(context, ref);
              }
              return _buildConversationsList(conversations);
            },
          );
        },
      ),
    );
  }

  Widget _buildEmptyState(BuildContext context, WidgetRef ref) {
    return Center(
      child: Padding(
        padding: const EdgeInsets.all(32),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              Icons.question_answer_outlined,
              size: 80,
              color: Colors.grey.withOpacity(0.5),
            ),
            const SizedBox(height: 24),
            TextWidget(
              'No questions yet'.hardcoded,
              style: ATextStyle.title.copyWith(color: Colors.white),
              textAlign: TextAlign.center,
            ),
            const SizedBox(height: 12),
            TextWidget(
              'Ask your instructor anything about your fitness journey'.hardcoded,
              style: ATextStyle.medium.copyWith(color: Colors.grey),
              textAlign: TextAlign.center,
            ),
            const SizedBox(height: 24),
            ElevatedButton.icon(
              onPressed: () => _showAskQuestionModal(context, ref),
              icon: const Icon(Icons.add),
              label: TextWidget(
                'Ask Question'.hardcoded,
                style: ATextStyle.medium.copyWith(color: Colors.white),
              ),
              style: ElevatedButton.styleFrom(
                backgroundColor: AColor.fitgoGreen,
                padding: const EdgeInsets.symmetric(horizontal: 24, vertical: 12),
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(8),
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildConversationsList(List<Conversation> conversations) {
    return ListView.builder(
      padding: const EdgeInsets.all(16),
      itemCount: conversations.length,
      itemBuilder: (context, index) {
        final conversation = conversations[index];
        return _buildConversationCard(context, conversation);
      },
    );
  }

  Widget _buildConversationCard(BuildContext context, Conversation conversation) {
    return Container(
      margin: const EdgeInsets.only(bottom: 16),
      decoration: BoxDecoration(
        color: const Color(0xFF1E293B),
        borderRadius: BorderRadius.circular(12),
        border: Border.all(
          color: conversation.isPending
              ? AColor.fitgoGreen.withOpacity(0.3)
              : Colors.grey.withOpacity(0.2),
        ),
      ),
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Header with tag and status
            Row(
              children: [
                Container(
                  padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                  decoration: BoxDecoration(
                    color: AColor.fitgoGreen.withOpacity(0.2),
                    borderRadius: BorderRadius.circular(12),
                  ),
                  child: TextWidget(
                    conversation.tag?.displayName ?? 'General',
                    style: ATextStyle.small.copyWith(
                      color: AColor.fitgoGreen,
                      fontWeight: FontWeight.w500,
                    ),
                  ),
                ),
                const Spacer(),
                Container(
                  padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                  decoration: BoxDecoration(
                    color: conversation.isPending 
                        ? Colors.orange.withOpacity(0.2)
                        : Colors.green.withOpacity(0.2),
                    borderRadius: BorderRadius.circular(12),
                  ),
                  child: TextWidget(
                    conversation.statusDisplayName,
                    style: ATextStyle.small.copyWith(
                      color: conversation.isPending ? Colors.orange : Colors.green,
                      fontWeight: FontWeight.w500,
                    ),
                  ),
                ),
              ],
            ),
            const SizedBox(height: 12),
            
            // Question
            TextWidget(
              conversation.questionText,
              style: ATextStyle.medium.copyWith(color: Colors.white),
              maxLines: 2,
              overflow: TextOverflow.ellipsis,
            ),
            const SizedBox(height: 8),
            TextWidget(
              conversation.timeAgo,
              style: ATextStyle.small.copyWith(color: Colors.grey),
            ),
            
            // Response indicator
            if (conversation.hasResponse) ...[
              const SizedBox(height: 8),
              Row(
                children: [
                  Icon(
                    Icons.check_circle,
                    color: Colors.green,
                    size: 16,
                  ),
                  const SizedBox(width: 4),
                  TextWidget(
                    'Answered',
                    style: ATextStyle.small.copyWith(
                      color: Colors.green,
                      fontWeight: FontWeight.w500,
                    ),
                  ),
                ],
              ),
            ],
          ],
        ),
      ),
    );
  }

  void _showAskQuestionModal(BuildContext context, WidgetRef ref) {
    // Reset providers before showing modal
    ref.read(selectedTagProvider.notifier).state = null;
    ref.read(questionTextProvider.notifier).state = '';

    showModalBottomSheet(
      context: context,
      isScrollControlled: true,
      backgroundColor: Colors.transparent,
      builder: (context) => const AskQuestionModal(),
    );
  }

  void _sendQuestion(BuildContext context, WidgetRef ref, QuestionTag tag, String questionText) {
    final currentUserAsync = ref.read(currentUserProvider);

    currentUserAsync.when(
      loading: () {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(content: Text('Loading user data...')),
        );
      },
      error: (error, stack) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Error: $error'),
            backgroundColor: Colors.red,
          ),
        );
      },
      data: (currentUser) {
        if (currentUser == null) {
          ScaffoldMessenger.of(context).showSnackBar(
            const SnackBar(
              content: Text('User not authenticated'),
              backgroundColor: Colors.red,
            ),
          );
          return;
        }

        // Get instructor ID from enrollment
        final enrollmentAsync = ref.read(currentUserEnrollmentProvider);
        enrollmentAsync.when(
          loading: () {
            ScaffoldMessenger.of(context).showSnackBar(
              const SnackBar(content: Text('Loading enrollment data...')),
            );
          },
          error: (error, stack) {
            ScaffoldMessenger.of(context).showSnackBar(
              SnackBar(
                content: Text('Enrollment error: $error'),
                backgroundColor: Colors.red,
              ),
            );
          },
          data: (enrollmentResult) {
            if (enrollmentResult?.enrollment?.instructorId == null) {
              ScaffoldMessenger.of(context).showSnackBar(
                const SnackBar(
                  content: Text('No instructor found. Please enroll with an instructor first.'),
                  backgroundColor: Colors.red,
                ),
              );
              return;
            }

            // Send question
            ref.read(questionNotifierProvider.notifier).sendQuestion(
              studentId: currentUser.id,
              instructorId: enrollmentResult!.enrollment!.instructorId,
              tagId: tag.id,
              questionText: questionText,
            );

            // Refresh conversations list
            ref.invalidate(studentConversationsProvider(currentUser.id));

            // Close modal and show success
            context.pop();
            ScaffoldMessenger.of(context).showSnackBar(
              const SnackBar(
                content: Text('Question sent successfully!'),
                backgroundColor: Colors.green,
              ),
            );
          },
        );
      },
    );
  }
}

class AskQuestionModal extends HookConsumerWidget {
  const AskQuestionModal({super.key});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final questionController = useTextEditingController();
    final selectedTag = ref.watch(selectedTagProvider);
    final questionText = ref.watch(questionTextProvider);
    final canSend = ref.watch(canSendQuestionProvider);
    final questionState = ref.watch(questionNotifierProvider);
    final tagsAsync = ref.watch(questionTagsProvider);

    // Listen to question text changes
    useEffect(() {
      void listener() {
        ref.read(questionTextProvider.notifier).state = questionController.text;
      }
      questionController.addListener(listener);
      return () => questionController.removeListener(listener);
    }, [questionController]);

    // Listen to question state changes
    ref.listen<AsyncValue<Conversation?>>(questionNotifierProvider, (previous, next) {
      next.when(
        loading: () {},
        data: (conversation) {
          if (conversation != null) {
            AppLogger.userAction('✅ Question sent successfully', tag: 'STUDENT_QUESTIONS');
            ScaffoldMessenger.of(context).showSnackBar(
              const SnackBar(
                content: Text('Question sent successfully!'),
                backgroundColor: Colors.green,
              ),
            );
          }
        },
        error: (error, stack) {
          AppLogger.userAction('❌ Error sending question: $error', tag: 'STUDENT_QUESTIONS');
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Text('Error sending question: $error'),
              backgroundColor: Colors.red,
            ),
          );
        },
      );
    });

    return Container(
      height: MediaQuery.of(context).size.height * 0.8,
      decoration: const BoxDecoration(
        color: Color(0xFF0F172A),
        borderRadius: BorderRadius.only(
          topLeft: Radius.circular(20),
          topRight: Radius.circular(20),
        ),
      ),
      child: Column(
        children: [
          // Header
          Container(
            padding: const EdgeInsets.all(20),
            decoration: const BoxDecoration(
              color: Color(0xFF1E293B),
              borderRadius: BorderRadius.only(
                topLeft: Radius.circular(20),
                topRight: Radius.circular(20),
              ),
            ),
            child: Row(
              children: [
                TextWidget(
                  'Ask a Question'.hardcoded,
                  style: ATextStyle.title.copyWith(color: Colors.white),
                ),
                const Spacer(),
                IconButton(
                  onPressed: () => context.pop(),
                  icon: const Icon(Icons.close, color: Colors.white),
                ),
              ],
            ),
          ),

          // Content
          Expanded(
            child: SingleChildScrollView(
              padding: const EdgeInsets.all(20),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  // Category selection
                  TextWidget(
                    'Select Category'.hardcoded,
                    style: ATextStyle.medium.copyWith(
                      color: Colors.white,
                      fontWeight: FontWeight.w600,
                    ),
                  ),
                  const SizedBox(height: 12),
                  tagsAsync.when(
                    loading: () => const CircularProgressIndicator(),
                    error: (error, stack) => TextWidget(
                      'Error loading categories: $error',
                      style: ATextStyle.small.copyWith(color: Colors.red),
                    ),
                    data: (tags) => _buildTagSelector(ref, tags),
                  ),

                  const SizedBox(height: 24),

                  // Question input
                  TextWidget(
                    'Your Question'.hardcoded,
                    style: ATextStyle.medium.copyWith(
                      color: Colors.white,
                      fontWeight: FontWeight.w500,
                    ),
                  ),
                  const SizedBox(height: 8),
                  TextField(
                    controller: questionController,
                    maxLines: 6,
                    style: ATextStyle.medium.copyWith(color: Colors.white),
                    onChanged: (value) {
                      ref.read(questionTextProvider.notifier).state = value;
                    },
                    decoration: InputDecoration(
                      hintText: 'Type your question here...'.hardcoded,
                      hintStyle: ATextStyle.medium.copyWith(color: Colors.grey),
                      filled: true,
                      fillColor: const Color(0xFF334155),
                      border: OutlineInputBorder(
                        borderRadius: BorderRadius.circular(8),
                        borderSide: BorderSide.none,
                      ),
                      focusedBorder: OutlineInputBorder(
                        borderRadius: BorderRadius.circular(8),
                        borderSide: const BorderSide(color: AColor.fitgoGreen),
                      ),
                    ),
                  ),

                  const SizedBox(height: 24),

                  // Send button
                  SizedBox(
                    width: double.infinity,
                    child: ElevatedButton(
                      onPressed: canSend && !questionState.isLoading
                          ? () => _sendQuestion(context, ref, selectedTag!, questionText)
                          : null,
                      style: ElevatedButton.styleFrom(
                        backgroundColor: canSend ? AColor.fitgoGreen : Colors.grey,
                        padding: const EdgeInsets.symmetric(vertical: 16),
                        shape: RoundedRectangleBorder(
                          borderRadius: BorderRadius.circular(8),
                        ),
                      ),
                      child: questionState.isLoading
                          ? const SizedBox(
                              height: 20,
                              width: 20,
                              child: CircularProgressIndicator(
                                color: Colors.white,
                                strokeWidth: 2,
                              ),
                            )
                          : TextWidget(
                              'Send Question'.hardcoded,
                              style: ATextStyle.medium.copyWith(
                                color: Colors.white,
                                fontWeight: FontWeight.w600,
                              ),
                            ),
                    ),
                  ),
                ],
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildTagSelector(WidgetRef ref, List<QuestionTag> tags) {
    final selectedTag = ref.watch(selectedTagProvider);

    return Wrap(
      spacing: 8,
      runSpacing: 8,
      children: tags.map((tag) {
        final isSelected = selectedTag?.id == tag.id;
        return GestureDetector(
          onTap: () => ref.read(selectedTagProvider.notifier).state = tag,
          child: Container(
            padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
            decoration: BoxDecoration(
              color: isSelected ? AColor.fitgoGreen : const Color(0xFF334155),
              borderRadius: BorderRadius.circular(20),
              border: Border.all(
                color: isSelected ? AColor.fitgoGreen : Colors.grey.withOpacity(0.3),
              ),
            ),
            child: TextWidget(
              tag.displayName,
              style: ATextStyle.small.copyWith(
                color: isSelected ? Colors.white : Colors.grey,
                fontWeight: isSelected ? FontWeight.w600 : FontWeight.normal,
              ),
            ),
          ),
        );
      }).toList(),
    );
  }

  void _sendQuestion(BuildContext context, WidgetRef ref, QuestionTag tag, String questionText) {
    final currentUserAsync = ref.read(currentUserProvider);

    currentUserAsync.when(
      loading: () {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(content: Text('Loading user data...')),
        );
      },
      error: (error, stack) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Error: $error'),
            backgroundColor: Colors.red,
          ),
        );
      },
      data: (currentUser) {
        if (currentUser == null) {
          ScaffoldMessenger.of(context).showSnackBar(
            const SnackBar(
              content: Text('User not authenticated'),
              backgroundColor: Colors.red,
            ),
          );
          return;
        }

        // Get instructor ID from enrollment
        final enrollmentAsync = ref.read(currentUserEnrollmentProvider);
        enrollmentAsync.when(
          loading: () {
            ScaffoldMessenger.of(context).showSnackBar(
              const SnackBar(content: Text('Loading enrollment data...')),
            );
          },
          error: (error, stack) {
            ScaffoldMessenger.of(context).showSnackBar(
              SnackBar(
                content: Text('Enrollment error: $error'),
                backgroundColor: Colors.red,
              ),
            );
          },
          data: (enrollmentResult) {
            if (enrollmentResult?.enrollment?.instructorId == null) {
              ScaffoldMessenger.of(context).showSnackBar(
                const SnackBar(
                  content: Text('No instructor found. Please enroll with an instructor first.'),
                  backgroundColor: Colors.red,
                ),
              );
              return;
            }

            // Send question
            ref.read(questionNotifierProvider.notifier).sendQuestion(
              studentId: currentUser.id,
              instructorId: enrollmentResult!.enrollment!.instructorId,
              tagId: tag.id,
              questionText: questionText,
            );

            // Refresh conversations list
            ref.invalidate(studentConversationsProvider(currentUser.id));

            // Close modal and show success
            context.pop();
            ScaffoldMessenger.of(context).showSnackBar(
              const SnackBar(
                content: Text('Question sent successfully!'),
                backgroundColor: Colors.green,
              ),
            );
          },
        );
      },
    );
  }
}
