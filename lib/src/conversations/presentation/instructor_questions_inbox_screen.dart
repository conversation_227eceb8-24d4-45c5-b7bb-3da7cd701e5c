import 'package:flutter/material.dart';
import 'package:hooks_riverpod/hooks_riverpod.dart';
import 'package:go_router/go_router.dart';
import '../../shared/widgets/text_widget/text_widget.dart';
import '../../shared/constants/app_text_style.dart';
import '../../shared/extensions/string_hardcoded.dart';
import '../../theme/colors.dart';
import '../../shared/utils/app_logger.dart';
import '../../shared/providers/auth_provider.dart';
import '../application/conversation_providers.dart';
import '../domain/conversation.dart';
import 'instructor_question_reply_screen.dart';

class InstructorQuestionsInboxScreen extends HookConsumerWidget {
  const InstructorQuestionsInboxScreen({super.key});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final currentUserAsync = ref.watch(currentUserProvider);

    return currentUserAsync.when(
      loading: () => const Scaffold(
        body: Center(child: CircularProgressIndicator()),
      ),
      error: (error, stack) => Scaffold(
        body: Center(
          child: Text('Error: $error'),
        ),
      ),
      data: (currentUser) {
        if (currentUser == null) {
          return const Scaffold(
            body: Center(child: CircularProgressIndicator()),
          );
        }

        final pendingQuestionsAsync = ref.watch(pendingQuestionsProvider(currentUser.id));

    return Scaffold(
      backgroundColor: const Color(0xFF0F172A),
      appBar: AppBar(
        backgroundColor: const Color(0xFF0F172A),
        elevation: 0,
        leading: IconButton(
          icon: const Icon(Icons.arrow_back, color: Colors.white),
          onPressed: () => context.pop(),
        ),
        title: TextWidget(
          'Student Questions'.hardcoded,
          style: ATextStyle.title.copyWith(color: Colors.white),
        ),
        actions: [
          IconButton(
            icon: const Icon(Icons.refresh, color: AColor.fitgoGreen),
            onPressed: () => ref.refresh(pendingQuestionsProvider(currentUser.id)),
          ),
        ],
      ),
      body: pendingQuestionsAsync.when(
        loading: () => const Center(
          child: CircularProgressIndicator(color: AColor.fitgoGreen),
        ),
        error: (error, stack) => Center(
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              Icon(Icons.error_outline, color: Colors.red, size: 48),
              const SizedBox(height: 16),
              TextWidget(
                'Error loading questions'.hardcoded,
                style: ATextStyle.medium.copyWith(color: Colors.white),
              ),
              const SizedBox(height: 8),
              TextWidget(
                error.toString(),
                style: ATextStyle.small.copyWith(color: Colors.grey),
                textAlign: TextAlign.center,
              ),
            ],
          ),
        ),
        data: (questions) => _buildQuestionsList(context, ref, questions),
      ),
    );
      },
    );
  }

  Widget _buildQuestionsList(BuildContext context, WidgetRef ref, List<Conversation> questions) {
    if (questions.isEmpty) {
      return Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(Icons.inbox_outlined, color: Colors.grey, size: 64),
            const SizedBox(height: 16),
            TextWidget(
              'No pending questions'.hardcoded,
              style: ATextStyle.large.copyWith(color: Colors.white),
            ),
            const SizedBox(height: 8),
            TextWidget(
              'Students haven\'t asked any questions yet'.hardcoded,
              style: ATextStyle.medium.copyWith(color: Colors.grey),
              textAlign: TextAlign.center,
            ),
          ],
        ),
      );
    }

    return ListView.builder(
      padding: const EdgeInsets.all(16),
      itemCount: questions.length,
      itemBuilder: (context, index) {
        final question = questions[index];
        return _buildQuestionCard(context, question);
      },
    );
  }

  Widget _buildQuestionCard(BuildContext context, Conversation question) {
    return Container(
      margin: const EdgeInsets.only(bottom: 16),
      decoration: BoxDecoration(
        color: const Color(0xFF1E293B),
        borderRadius: BorderRadius.circular(12),
        border: Border.all(
          color: AColor.fitgoGreen.withOpacity(0.3),
        ),
      ),
      child: InkWell(
        onTap: () => _navigateToReply(context, question),
        borderRadius: BorderRadius.circular(12),
        child: Padding(
          padding: const EdgeInsets.all(16),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // Header with student info and tag
              Row(
                children: [
                  // Student avatar
                  CircleAvatar(
                    radius: 20,
                    backgroundColor: AColor.fitgoGreen,
                    backgroundImage: question.studentAvatar != null
                        ? NetworkImage(question.studentAvatar!)
                        : null,
                    child: question.studentAvatar == null
                        ? Text(
                            question.studentName?.substring(0, 1).toUpperCase() ?? 'S',
                            style: ATextStyle.medium.copyWith(
                              color: Colors.white,
                              fontWeight: FontWeight.bold,
                            ),
                          )
                        : null,
                  ),
                  const SizedBox(width: 12),
                  
                  // Student name and time
                  Expanded(
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        TextWidget(
                          question.studentName ?? 'Unknown Student',
                          style: ATextStyle.medium.copyWith(
                            color: Colors.white,
                            fontWeight: FontWeight.w600,
                          ),
                        ),
                        TextWidget(
                          question.timeAgo,
                          style: ATextStyle.small.copyWith(color: Colors.grey),
                        ),
                      ],
                    ),
                  ),
                  
                  // Tag
                  Container(
                    padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                    decoration: BoxDecoration(
                      color: AColor.fitgoGreen.withOpacity(0.2),
                      borderRadius: BorderRadius.circular(12),
                    ),
                    child: TextWidget(
                      question.tag?.displayName ?? 'General',
                      style: ATextStyle.small.copyWith(
                        color: AColor.fitgoGreen,
                        fontWeight: FontWeight.w500,
                      ),
                    ),
                  ),
                ],
              ),
              
              const SizedBox(height: 12),
              
              // Question preview
              Container(
                width: double.infinity,
                padding: const EdgeInsets.all(12),
                decoration: BoxDecoration(
                  color: const Color(0xFF334155),
                  borderRadius: BorderRadius.circular(8),
                ),
                child: TextWidget(
                  question.questionText,
                  style: ATextStyle.medium.copyWith(color: Colors.white),
                  maxLines: 3,
                  overflow: TextOverflow.ellipsis,
                ),
              ),
              
              const SizedBox(height: 12),
              
              // Reply button
              Row(
                children: [
                  const Spacer(),
                  Container(
                    padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
                    decoration: BoxDecoration(
                      color: AColor.fitgoGreen.withOpacity(0.1),
                      borderRadius: BorderRadius.circular(16),
                      border: Border.all(
                        color: AColor.fitgoGreen.withOpacity(0.3),
                      ),
                    ),
                    child: Row(
                      mainAxisSize: MainAxisSize.min,
                      children: [
                        Icon(
                          Icons.reply,
                          size: 16,
                          color: AColor.fitgoGreen,
                        ),
                        const SizedBox(width: 4),
                        TextWidget(
                          'Reply'.hardcoded,
                          style: ATextStyle.small.copyWith(
                            color: AColor.fitgoGreen,
                            fontWeight: FontWeight.w500,
                          ),
                        ),
                      ],
                    ),
                  ),
                ],
              ),
            ],
          ),
        ),
      ),
    );
  }

  void _navigateToReply(BuildContext context, Conversation question) {
    AppLogger.userAction('📝 Opening question reply: ${question.id}', tag: 'INSTRUCTOR_INBOX');
    
    Navigator.of(context).push(
      MaterialPageRoute(
        builder: (context) => InstructorQuestionReplyScreen(
          conversation: question,
        ),
      ),
    );
  }
}
