import 'package:flutter/material.dart';
import 'package:flutter_hooks/flutter_hooks.dart';
import 'package:hooks_riverpod/hooks_riverpod.dart';
import 'package:go_router/go_router.dart';
import '../../shared/widgets/text_widget/text_widget.dart';
import '../../shared/constants/app_text_style.dart';
import '../../shared/extensions/string_hardcoded.dart';
import '../../theme/colors.dart';
import '../../shared/utils/app_logger.dart';
import '../application/conversation_providers.dart';
import '../domain/conversation.dart';

class InstructorQuestionReplyScreen extends HookConsumerWidget {
  final Conversation conversation;

  const InstructorQuestionReplyScreen({
    super.key,
    required this.conversation,
  });

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final responseController = useTextEditingController();
    final responseText = ref.watch(responseTextProvider);
    final canSend = ref.watch(canSendResponseProvider);
    final responseState = ref.watch(responseNotifierProvider);

    // Listen to response text changes
    useEffect(() {
      void listener() {
        ref.read(responseTextProvider.notifier).state = responseController.text;
      }
      responseController.addListener(listener);
      return () => responseController.removeListener(listener);
    }, [responseController]);

    // Handle successful response sending
    ref.listen(responseNotifierProvider, (previous, next) {
      next.whenOrNull(
        data: (updatedConversation) {
          if (updatedConversation != null) {
            AppLogger.userAction('✅ Response sent successfully', tag: 'INSTRUCTOR_REPLY');
            ref.read(refreshConversationsProvider);
            ref.read(responseTextProvider.notifier).state = '';
            ref.read(responseNotifierProvider.notifier).reset();
            
            ScaffoldMessenger.of(context).showSnackBar(
              const SnackBar(
                content: Text('Response sent successfully!'),
                backgroundColor: AColor.fitgoGreen,
              ),
            );
            
            // Go back to inbox
            context.pop();
          }
        },
        error: (error, stack) {
          AppLogger.userAction('❌ Error sending response: $error', tag: 'INSTRUCTOR_REPLY');
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Text('Error sending response: $error'),
              backgroundColor: Colors.red,
            ),
          );
        },
      );
    });

    return Scaffold(
      backgroundColor: const Color(0xFF0F172A),
      appBar: AppBar(
        backgroundColor: const Color(0xFF0F172A),
        elevation: 0,
        leading: IconButton(
          icon: const Icon(Icons.arrow_back, color: Colors.white),
          onPressed: () => context.pop(),
        ),
        title: TextWidget(
          'Reply to Question'.hardcoded,
          style: ATextStyle.title.copyWith(color: Colors.white),
        ),
      ),
      body: Column(
        children: [
          // Question display
          Expanded(
            child: SingleChildScrollView(
              padding: const EdgeInsets.all(16),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  // Student info header
                  Container(
                    width: double.infinity,
                    padding: const EdgeInsets.all(16),
                    decoration: BoxDecoration(
                      color: const Color(0xFF1E293B),
                      borderRadius: BorderRadius.circular(12),
                      border: Border.all(
                        color: AColor.fitgoGreen.withOpacity(0.3),
                      ),
                    ),
                    child: Row(
                      children: [
                        // Student avatar
                        CircleAvatar(
                          radius: 24,
                          backgroundColor: AColor.fitgoGreen,
                          backgroundImage: conversation.studentAvatar != null
                              ? NetworkImage(conversation.studentAvatar!)
                              : null,
                          child: conversation.studentAvatar == null
                              ? Text(
                                  conversation.studentName?.substring(0, 1).toUpperCase() ?? 'S',
                                  style: ATextStyle.large.copyWith(
                                    color: Colors.white,
                                    fontWeight: FontWeight.bold,
                                  ),
                                )
                              : null,
                        ),
                        const SizedBox(width: 16),
                        
                        // Student info
                        Expanded(
                          child: Column(
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: [
                              TextWidget(
                                conversation.studentName ?? 'Unknown Student',
                                style: ATextStyle.large.copyWith(
                                  color: Colors.white,
                                  fontWeight: FontWeight.w600,
                                ),
                              ),
                              const SizedBox(height: 4),
                              Row(
                                children: [
                                  Container(
                                    padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                                    decoration: BoxDecoration(
                                      color: AColor.fitgoGreen.withOpacity(0.2),
                                      borderRadius: BorderRadius.circular(12),
                                    ),
                                    child: TextWidget(
                                      conversation.tag?.displayName ?? 'General',
                                      style: ATextStyle.small.copyWith(
                                        color: AColor.fitgoGreen,
                                        fontWeight: FontWeight.w500,
                                      ),
                                    ),
                                  ),
                                  const SizedBox(width: 8),
                                  TextWidget(
                                    conversation.timeAgo,
                                    style: ATextStyle.small.copyWith(color: Colors.grey),
                                  ),
                                ],
                              ),
                            ],
                          ),
                        ),
                      ],
                    ),
                  ),
                  
                  const SizedBox(height: 20),
                  
                  // Question section
                  TextWidget(
                    'Student\'s Question:'.hardcoded,
                    style: ATextStyle.medium.copyWith(
                      color: Colors.white,
                      fontWeight: FontWeight.w600,
                    ),
                  ),
                  const SizedBox(height: 8),
                  Container(
                    width: double.infinity,
                    padding: const EdgeInsets.all(16),
                    decoration: BoxDecoration(
                      color: const Color(0xFF334155),
                      borderRadius: BorderRadius.circular(12),
                    ),
                    child: TextWidget(
                      conversation.questionText,
                      style: ATextStyle.medium.copyWith(color: Colors.white),
                    ),
                  ),
                  
                  const SizedBox(height: 24),
                  
                  // Response section
                  TextWidget(
                    'Your Response:'.hardcoded,
                    style: ATextStyle.medium.copyWith(
                      color: Colors.white,
                      fontWeight: FontWeight.w600,
                    ),
                  ),
                  const SizedBox(height: 8),
                  TextField(
                    controller: responseController,
                    maxLines: 8,
                    style: ATextStyle.medium.copyWith(color: Colors.white),
                    decoration: InputDecoration(
                      hintText: 'Type your response here...'.hardcoded,
                      hintStyle: ATextStyle.medium.copyWith(color: Colors.grey),
                      filled: true,
                      fillColor: const Color(0xFF334155),
                      border: OutlineInputBorder(
                        borderRadius: BorderRadius.circular(12),
                        borderSide: BorderSide.none,
                      ),
                      focusedBorder: OutlineInputBorder(
                        borderRadius: BorderRadius.circular(12),
                        borderSide: const BorderSide(color: AColor.fitgoGreen),
                      ),
                    ),
                  ),
                ],
              ),
            ),
          ),
          
          // Send button
          Container(
            padding: const EdgeInsets.all(16),
            decoration: BoxDecoration(
              color: const Color(0xFF1E293B),
              border: Border(
                top: BorderSide(
                  color: Colors.grey.withOpacity(0.2),
                ),
              ),
            ),
            child: SafeArea(
              child: SizedBox(
                width: double.infinity,
                child: ElevatedButton(
                  onPressed: canSend && !responseState.isLoading 
                      ? () => _sendResponse(ref, responseText)
                      : null,
                  style: ElevatedButton.styleFrom(
                    backgroundColor: canSend ? AColor.fitgoGreen : Colors.grey,
                    padding: const EdgeInsets.symmetric(vertical: 16),
                    shape: RoundedRectangleBorder(
                      borderRadius: BorderRadius.circular(12),
                    ),
                  ),
                  child: responseState.isLoading
                      ? const SizedBox(
                          height: 20,
                          width: 20,
                          child: CircularProgressIndicator(
                            color: Colors.white,
                            strokeWidth: 2,
                          ),
                        )
                      : TextWidget(
                          'Send Response'.hardcoded,
                          style: ATextStyle.medium.copyWith(
                            color: Colors.white,
                            fontWeight: FontWeight.w600,
                          ),
                        ),
                ),
              ),
            ),
          ),
        ],
      ),
    );
  }

  void _sendResponse(WidgetRef ref, String responseText) {
    ref.read(responseNotifierProvider.notifier).sendResponse(
      conversationId: conversation.id,
      responseText: responseText,
    );
  }
}
