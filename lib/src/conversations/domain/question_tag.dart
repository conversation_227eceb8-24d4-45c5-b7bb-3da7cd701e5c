import 'package:freezed_annotation/freezed_annotation.dart';

part 'question_tag.freezed.dart';
part 'question_tag.g.dart';

@freezed
abstract class QuestionTag with _$QuestionTag {
  const factory QuestionTag({
    required String id,
    required String name,
    required String displayName,
    String? description,
    @Default(true) bool isActive,
    @Default(0) int sortOrder,
    required DateTime createdAt,
    required DateTime updatedAt,
  }) = _QuestionTag;

  factory QuestionTag.fromJson(Map<String, dynamic> json) =>
      _$QuestionTagFromJson(json);
}

// Extension for easy access to common tags
extension QuestionTagExtension on QuestionTag {
  bool get isWorkout => name == 'workout';
  bool get isNutrition => name == 'nutrition';
  bool get isSupplementation => name == 'supplementation';
  bool get isRecovery => name == 'recovery';
  bool get isScheduling => name == 'scheduling';
  bool get isInjuryHealth => name == 'injury_health';
  bool get isGeneral => name == 'general';
}
