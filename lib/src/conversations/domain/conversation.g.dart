// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'conversation.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

_Conversation _$ConversationFromJson(Map<String, dynamic> json) =>
    _Conversation(
      id: json['id'] as String,
      studentId: json['studentId'] as String,
      instructorId: json['instructorId'] as String,
      tagId: json['tagId'] as String,
      questionText: json['questionText'] as String,
      responseText: json['responseText'] as String?,
      status:
          $enumDecodeNullable(_$ConversationStatusEnumMap, json['status']) ??
              ConversationStatus.pending,
      questionSentAt: DateTime.parse(json['questionSentAt'] as String),
      responseSentAt: json['responseSentAt'] == null
          ? null
          : DateTime.parse(json['responseSentAt'] as String),
      createdAt: DateTime.parse(json['createdAt'] as String),
      updatedAt: DateTime.parse(json['updatedAt'] as String),
      tag: json['tag'] == null
          ? null
          : QuestionTag.fromJson(json['tag'] as Map<String, dynamic>),
      studentName: json['studentName'] as String?,
      instructorName: json['instructorName'] as String?,
      studentAvatar: json['studentAvatar'] as String?,
      instructorAvatar: json['instructorAvatar'] as String?,
    );

Map<String, dynamic> _$ConversationToJson(_Conversation instance) =>
    <String, dynamic>{
      'id': instance.id,
      'studentId': instance.studentId,
      'instructorId': instance.instructorId,
      'tagId': instance.tagId,
      'questionText': instance.questionText,
      'responseText': instance.responseText,
      'status': _$ConversationStatusEnumMap[instance.status]!,
      'questionSentAt': instance.questionSentAt.toIso8601String(),
      'responseSentAt': instance.responseSentAt?.toIso8601String(),
      'createdAt': instance.createdAt.toIso8601String(),
      'updatedAt': instance.updatedAt.toIso8601String(),
      'tag': instance.tag,
      'studentName': instance.studentName,
      'instructorName': instance.instructorName,
      'studentAvatar': instance.studentAvatar,
      'instructorAvatar': instance.instructorAvatar,
    };

const _$ConversationStatusEnumMap = {
  ConversationStatus.pending: 'pending',
  ConversationStatus.answered: 'answered',
  ConversationStatus.closed: 'closed',
};
