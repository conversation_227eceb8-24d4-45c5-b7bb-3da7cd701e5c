import 'package:freezed_annotation/freezed_annotation.dart';
import 'question_tag.dart';

part 'conversation.freezed.dart';
part 'conversation.g.dart';

@freezed
abstract class Conversation with _$Conversation {
  const factory Conversation({
    required String id,
    required String studentId,
    required String instructorId,
    required String tagId,
    required String questionText,
    String? responseText,
    @Default(ConversationStatus.pending) ConversationStatus status,
    required DateTime questionSentAt,
    DateTime? responseSentAt,
    required DateTime createdAt,
    required DateTime updatedAt,
    
    // Optional populated fields
    QuestionTag? tag,
    String? studentName,
    String? instructorName,
    String? studentAvatar,
    String? instructorAvatar,
  }) = _Conversation;

  factory Conversation.fromJson(Map<String, dynamic> json) =>
      _$ConversationFromJson(json);
}

@JsonEnum()
enum ConversationStatus {
  @JsonValue('pending')
  pending,
  @JsonValue('answered')
  answered,
  @JsonValue('closed')
  closed,
}

// Extension for easy status checks
extension ConversationExtension on Conversation {
  bool get isPending => status == ConversationStatus.pending;
  bool get isAnswered => status == ConversationStatus.answered;
  bool get isClosed => status == ConversationStatus.closed;
  
  bool get canReply => isPending;
  bool get hasResponse => responseText != null && responseText!.isNotEmpty;
  
  String get statusDisplayName {
    switch (status) {
      case ConversationStatus.pending:
        return 'Pending';
      case ConversationStatus.answered:
        return 'Answered';
      case ConversationStatus.closed:
        return 'Closed';
    }
  }
  
  String get timeAgo {
    final now = DateTime.now();
    final difference = now.difference(questionSentAt);
    
    if (difference.inDays > 0) {
      return '${difference.inDays}d ago';
    } else if (difference.inHours > 0) {
      return '${difference.inHours}h ago';
    } else if (difference.inMinutes > 0) {
      return '${difference.inMinutes}m ago';
    } else {
      return 'Just now';
    }
  }
}
