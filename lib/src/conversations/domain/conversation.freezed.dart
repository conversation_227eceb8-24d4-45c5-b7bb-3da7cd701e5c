// dart format width=80
// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'conversation.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

// dart format off
T _$identity<T>(T value) => value;

/// @nodoc
mixin _$Conversation {
  String get id;
  String get studentId;
  String get instructorId;
  String get tagId;
  String get questionText;
  String? get responseText;
  ConversationStatus get status;
  DateTime get questionSentAt;
  DateTime? get responseSentAt;
  DateTime get createdAt;
  DateTime get updatedAt; // Optional populated fields
  QuestionTag? get tag;
  String? get studentName;
  String? get instructorName;
  String? get studentAvatar;
  String? get instructorAvatar;

  /// Create a copy of Conversation
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @pragma('vm:prefer-inline')
  $ConversationCopyWith<Conversation> get copyWith =>
      _$ConversationCopyWithImpl<Conversation>(
          this as Conversation, _$identity);

  /// Serializes this Conversation to a JSON map.
  Map<String, dynamic> toJson();

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is Conversation &&
            (identical(other.id, id) || other.id == id) &&
            (identical(other.studentId, studentId) ||
                other.studentId == studentId) &&
            (identical(other.instructorId, instructorId) ||
                other.instructorId == instructorId) &&
            (identical(other.tagId, tagId) || other.tagId == tagId) &&
            (identical(other.questionText, questionText) ||
                other.questionText == questionText) &&
            (identical(other.responseText, responseText) ||
                other.responseText == responseText) &&
            (identical(other.status, status) || other.status == status) &&
            (identical(other.questionSentAt, questionSentAt) ||
                other.questionSentAt == questionSentAt) &&
            (identical(other.responseSentAt, responseSentAt) ||
                other.responseSentAt == responseSentAt) &&
            (identical(other.createdAt, createdAt) ||
                other.createdAt == createdAt) &&
            (identical(other.updatedAt, updatedAt) ||
                other.updatedAt == updatedAt) &&
            (identical(other.tag, tag) || other.tag == tag) &&
            (identical(other.studentName, studentName) ||
                other.studentName == studentName) &&
            (identical(other.instructorName, instructorName) ||
                other.instructorName == instructorName) &&
            (identical(other.studentAvatar, studentAvatar) ||
                other.studentAvatar == studentAvatar) &&
            (identical(other.instructorAvatar, instructorAvatar) ||
                other.instructorAvatar == instructorAvatar));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hash(
      runtimeType,
      id,
      studentId,
      instructorId,
      tagId,
      questionText,
      responseText,
      status,
      questionSentAt,
      responseSentAt,
      createdAt,
      updatedAt,
      tag,
      studentName,
      instructorName,
      studentAvatar,
      instructorAvatar);

  @override
  String toString() {
    return 'Conversation(id: $id, studentId: $studentId, instructorId: $instructorId, tagId: $tagId, questionText: $questionText, responseText: $responseText, status: $status, questionSentAt: $questionSentAt, responseSentAt: $responseSentAt, createdAt: $createdAt, updatedAt: $updatedAt, tag: $tag, studentName: $studentName, instructorName: $instructorName, studentAvatar: $studentAvatar, instructorAvatar: $instructorAvatar)';
  }
}

/// @nodoc
abstract mixin class $ConversationCopyWith<$Res> {
  factory $ConversationCopyWith(
          Conversation value, $Res Function(Conversation) _then) =
      _$ConversationCopyWithImpl;
  @useResult
  $Res call(
      {String id,
      String studentId,
      String instructorId,
      String tagId,
      String questionText,
      String? responseText,
      ConversationStatus status,
      DateTime questionSentAt,
      DateTime? responseSentAt,
      DateTime createdAt,
      DateTime updatedAt,
      QuestionTag? tag,
      String? studentName,
      String? instructorName,
      String? studentAvatar,
      String? instructorAvatar});

  $QuestionTagCopyWith<$Res>? get tag;
}

/// @nodoc
class _$ConversationCopyWithImpl<$Res> implements $ConversationCopyWith<$Res> {
  _$ConversationCopyWithImpl(this._self, this._then);

  final Conversation _self;
  final $Res Function(Conversation) _then;

  /// Create a copy of Conversation
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? id = null,
    Object? studentId = null,
    Object? instructorId = null,
    Object? tagId = null,
    Object? questionText = null,
    Object? responseText = freezed,
    Object? status = null,
    Object? questionSentAt = null,
    Object? responseSentAt = freezed,
    Object? createdAt = null,
    Object? updatedAt = null,
    Object? tag = freezed,
    Object? studentName = freezed,
    Object? instructorName = freezed,
    Object? studentAvatar = freezed,
    Object? instructorAvatar = freezed,
  }) {
    return _then(_self.copyWith(
      id: null == id
          ? _self.id
          : id // ignore: cast_nullable_to_non_nullable
              as String,
      studentId: null == studentId
          ? _self.studentId
          : studentId // ignore: cast_nullable_to_non_nullable
              as String,
      instructorId: null == instructorId
          ? _self.instructorId
          : instructorId // ignore: cast_nullable_to_non_nullable
              as String,
      tagId: null == tagId
          ? _self.tagId
          : tagId // ignore: cast_nullable_to_non_nullable
              as String,
      questionText: null == questionText
          ? _self.questionText
          : questionText // ignore: cast_nullable_to_non_nullable
              as String,
      responseText: freezed == responseText
          ? _self.responseText
          : responseText // ignore: cast_nullable_to_non_nullable
              as String?,
      status: null == status
          ? _self.status
          : status // ignore: cast_nullable_to_non_nullable
              as ConversationStatus,
      questionSentAt: null == questionSentAt
          ? _self.questionSentAt
          : questionSentAt // ignore: cast_nullable_to_non_nullable
              as DateTime,
      responseSentAt: freezed == responseSentAt
          ? _self.responseSentAt
          : responseSentAt // ignore: cast_nullable_to_non_nullable
              as DateTime?,
      createdAt: null == createdAt
          ? _self.createdAt
          : createdAt // ignore: cast_nullable_to_non_nullable
              as DateTime,
      updatedAt: null == updatedAt
          ? _self.updatedAt
          : updatedAt // ignore: cast_nullable_to_non_nullable
              as DateTime,
      tag: freezed == tag
          ? _self.tag
          : tag // ignore: cast_nullable_to_non_nullable
              as QuestionTag?,
      studentName: freezed == studentName
          ? _self.studentName
          : studentName // ignore: cast_nullable_to_non_nullable
              as String?,
      instructorName: freezed == instructorName
          ? _self.instructorName
          : instructorName // ignore: cast_nullable_to_non_nullable
              as String?,
      studentAvatar: freezed == studentAvatar
          ? _self.studentAvatar
          : studentAvatar // ignore: cast_nullable_to_non_nullable
              as String?,
      instructorAvatar: freezed == instructorAvatar
          ? _self.instructorAvatar
          : instructorAvatar // ignore: cast_nullable_to_non_nullable
              as String?,
    ));
  }

  /// Create a copy of Conversation
  /// with the given fields replaced by the non-null parameter values.
  @override
  @pragma('vm:prefer-inline')
  $QuestionTagCopyWith<$Res>? get tag {
    if (_self.tag == null) {
      return null;
    }

    return $QuestionTagCopyWith<$Res>(_self.tag!, (value) {
      return _then(_self.copyWith(tag: value));
    });
  }
}

/// @nodoc
@JsonSerializable()
class _Conversation implements Conversation {
  const _Conversation(
      {required this.id,
      required this.studentId,
      required this.instructorId,
      required this.tagId,
      required this.questionText,
      this.responseText,
      this.status = ConversationStatus.pending,
      required this.questionSentAt,
      this.responseSentAt,
      required this.createdAt,
      required this.updatedAt,
      this.tag,
      this.studentName,
      this.instructorName,
      this.studentAvatar,
      this.instructorAvatar});
  factory _Conversation.fromJson(Map<String, dynamic> json) =>
      _$ConversationFromJson(json);

  @override
  final String id;
  @override
  final String studentId;
  @override
  final String instructorId;
  @override
  final String tagId;
  @override
  final String questionText;
  @override
  final String? responseText;
  @override
  @JsonKey()
  final ConversationStatus status;
  @override
  final DateTime questionSentAt;
  @override
  final DateTime? responseSentAt;
  @override
  final DateTime createdAt;
  @override
  final DateTime updatedAt;
// Optional populated fields
  @override
  final QuestionTag? tag;
  @override
  final String? studentName;
  @override
  final String? instructorName;
  @override
  final String? studentAvatar;
  @override
  final String? instructorAvatar;

  /// Create a copy of Conversation
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  @pragma('vm:prefer-inline')
  _$ConversationCopyWith<_Conversation> get copyWith =>
      __$ConversationCopyWithImpl<_Conversation>(this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$ConversationToJson(
      this,
    );
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _Conversation &&
            (identical(other.id, id) || other.id == id) &&
            (identical(other.studentId, studentId) ||
                other.studentId == studentId) &&
            (identical(other.instructorId, instructorId) ||
                other.instructorId == instructorId) &&
            (identical(other.tagId, tagId) || other.tagId == tagId) &&
            (identical(other.questionText, questionText) ||
                other.questionText == questionText) &&
            (identical(other.responseText, responseText) ||
                other.responseText == responseText) &&
            (identical(other.status, status) || other.status == status) &&
            (identical(other.questionSentAt, questionSentAt) ||
                other.questionSentAt == questionSentAt) &&
            (identical(other.responseSentAt, responseSentAt) ||
                other.responseSentAt == responseSentAt) &&
            (identical(other.createdAt, createdAt) ||
                other.createdAt == createdAt) &&
            (identical(other.updatedAt, updatedAt) ||
                other.updatedAt == updatedAt) &&
            (identical(other.tag, tag) || other.tag == tag) &&
            (identical(other.studentName, studentName) ||
                other.studentName == studentName) &&
            (identical(other.instructorName, instructorName) ||
                other.instructorName == instructorName) &&
            (identical(other.studentAvatar, studentAvatar) ||
                other.studentAvatar == studentAvatar) &&
            (identical(other.instructorAvatar, instructorAvatar) ||
                other.instructorAvatar == instructorAvatar));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hash(
      runtimeType,
      id,
      studentId,
      instructorId,
      tagId,
      questionText,
      responseText,
      status,
      questionSentAt,
      responseSentAt,
      createdAt,
      updatedAt,
      tag,
      studentName,
      instructorName,
      studentAvatar,
      instructorAvatar);

  @override
  String toString() {
    return 'Conversation(id: $id, studentId: $studentId, instructorId: $instructorId, tagId: $tagId, questionText: $questionText, responseText: $responseText, status: $status, questionSentAt: $questionSentAt, responseSentAt: $responseSentAt, createdAt: $createdAt, updatedAt: $updatedAt, tag: $tag, studentName: $studentName, instructorName: $instructorName, studentAvatar: $studentAvatar, instructorAvatar: $instructorAvatar)';
  }
}

/// @nodoc
abstract mixin class _$ConversationCopyWith<$Res>
    implements $ConversationCopyWith<$Res> {
  factory _$ConversationCopyWith(
          _Conversation value, $Res Function(_Conversation) _then) =
      __$ConversationCopyWithImpl;
  @override
  @useResult
  $Res call(
      {String id,
      String studentId,
      String instructorId,
      String tagId,
      String questionText,
      String? responseText,
      ConversationStatus status,
      DateTime questionSentAt,
      DateTime? responseSentAt,
      DateTime createdAt,
      DateTime updatedAt,
      QuestionTag? tag,
      String? studentName,
      String? instructorName,
      String? studentAvatar,
      String? instructorAvatar});

  @override
  $QuestionTagCopyWith<$Res>? get tag;
}

/// @nodoc
class __$ConversationCopyWithImpl<$Res>
    implements _$ConversationCopyWith<$Res> {
  __$ConversationCopyWithImpl(this._self, this._then);

  final _Conversation _self;
  final $Res Function(_Conversation) _then;

  /// Create a copy of Conversation
  /// with the given fields replaced by the non-null parameter values.
  @override
  @pragma('vm:prefer-inline')
  $Res call({
    Object? id = null,
    Object? studentId = null,
    Object? instructorId = null,
    Object? tagId = null,
    Object? questionText = null,
    Object? responseText = freezed,
    Object? status = null,
    Object? questionSentAt = null,
    Object? responseSentAt = freezed,
    Object? createdAt = null,
    Object? updatedAt = null,
    Object? tag = freezed,
    Object? studentName = freezed,
    Object? instructorName = freezed,
    Object? studentAvatar = freezed,
    Object? instructorAvatar = freezed,
  }) {
    return _then(_Conversation(
      id: null == id
          ? _self.id
          : id // ignore: cast_nullable_to_non_nullable
              as String,
      studentId: null == studentId
          ? _self.studentId
          : studentId // ignore: cast_nullable_to_non_nullable
              as String,
      instructorId: null == instructorId
          ? _self.instructorId
          : instructorId // ignore: cast_nullable_to_non_nullable
              as String,
      tagId: null == tagId
          ? _self.tagId
          : tagId // ignore: cast_nullable_to_non_nullable
              as String,
      questionText: null == questionText
          ? _self.questionText
          : questionText // ignore: cast_nullable_to_non_nullable
              as String,
      responseText: freezed == responseText
          ? _self.responseText
          : responseText // ignore: cast_nullable_to_non_nullable
              as String?,
      status: null == status
          ? _self.status
          : status // ignore: cast_nullable_to_non_nullable
              as ConversationStatus,
      questionSentAt: null == questionSentAt
          ? _self.questionSentAt
          : questionSentAt // ignore: cast_nullable_to_non_nullable
              as DateTime,
      responseSentAt: freezed == responseSentAt
          ? _self.responseSentAt
          : responseSentAt // ignore: cast_nullable_to_non_nullable
              as DateTime?,
      createdAt: null == createdAt
          ? _self.createdAt
          : createdAt // ignore: cast_nullable_to_non_nullable
              as DateTime,
      updatedAt: null == updatedAt
          ? _self.updatedAt
          : updatedAt // ignore: cast_nullable_to_non_nullable
              as DateTime,
      tag: freezed == tag
          ? _self.tag
          : tag // ignore: cast_nullable_to_non_nullable
              as QuestionTag?,
      studentName: freezed == studentName
          ? _self.studentName
          : studentName // ignore: cast_nullable_to_non_nullable
              as String?,
      instructorName: freezed == instructorName
          ? _self.instructorName
          : instructorName // ignore: cast_nullable_to_non_nullable
              as String?,
      studentAvatar: freezed == studentAvatar
          ? _self.studentAvatar
          : studentAvatar // ignore: cast_nullable_to_non_nullable
              as String?,
      instructorAvatar: freezed == instructorAvatar
          ? _self.instructorAvatar
          : instructorAvatar // ignore: cast_nullable_to_non_nullable
              as String?,
    ));
  }

  /// Create a copy of Conversation
  /// with the given fields replaced by the non-null parameter values.
  @override
  @pragma('vm:prefer-inline')
  $QuestionTagCopyWith<$Res>? get tag {
    if (_self.tag == null) {
      return null;
    }

    return $QuestionTagCopyWith<$Res>(_self.tag!, (value) {
      return _then(_self.copyWith(tag: value));
    });
  }
}

// dart format on
