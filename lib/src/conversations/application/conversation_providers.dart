import 'package:hooks_riverpod/hooks_riverpod.dart';
import '../data/conversation_repository.dart';
import '../domain/conversation.dart';
import '../domain/question_tag.dart';
import '../../shared/utils/app_logger.dart';

// Repository provider
final conversationRepositoryProvider = Provider<ConversationRepository>((ref) {
  return ConversationRepository();
});

// Question tags provider
final questionTagsProvider = FutureProvider<List<QuestionTag>>((ref) async {
  final repository = ref.read(conversationRepositoryProvider);
  return repository.getQuestionTags();
});

// Student conversation providers
final studentConversationsProvider = FutureProvider.family<List<Conversation>, String>((ref, studentId) async {
  final repository = ref.read(conversationRepositoryProvider);
  return repository.getStudentConversations(studentId);
});

final openConversationProvider = FutureProvider.family<Conversation?, ({String studentId, String instructorId})>((ref, params) async {
  final repository = ref.read(conversationRepositoryProvider);
  return repository.getOpenConversation(params.studentId, params.instructorId);
});

// Instructor conversation providers
final pendingQuestionsProvider = FutureProvider.family<List<Conversation>, String>((ref, instructorId) async {
  final repository = ref.read(conversationRepositoryProvider);
  return repository.getPendingQuestions(instructorId);
});

// State notifier for sending questions
class QuestionNotifier extends StateNotifier<AsyncValue<Conversation?>> {
  final ConversationRepository _repository;

  QuestionNotifier(this._repository) : super(const AsyncValue.data(null));

  Future<void> sendQuestion({
    required String studentId,
    required String instructorId,
    required String tagId,
    required String questionText,
  }) async {
    try {
      AppLogger.userAction('📤 Sending question via notifier', tag: 'QUESTION_NOTIFIER');
      state = const AsyncValue.loading();
      
      final conversation = await _repository.sendQuestion(
        studentId: studentId,
        instructorId: instructorId,
        tagId: tagId,
        questionText: questionText,
      );
      
      state = AsyncValue.data(conversation);
      AppLogger.userAction('✅ Question sent successfully', tag: 'QUESTION_NOTIFIER');
    } catch (e, stackTrace) {
      AppLogger.userAction('❌ Error sending question: $e', tag: 'QUESTION_NOTIFIER');
      state = AsyncValue.error(e, stackTrace);
    }
  }

  void reset() {
    state = const AsyncValue.data(null);
  }
}

final questionNotifierProvider = StateNotifierProvider<QuestionNotifier, AsyncValue<Conversation?>>((ref) {
  final repository = ref.read(conversationRepositoryProvider);
  return QuestionNotifier(repository);
});

// State notifier for sending responses
class ResponseNotifier extends StateNotifier<AsyncValue<Conversation?>> {
  final ConversationRepository _repository;

  ResponseNotifier(this._repository) : super(const AsyncValue.data(null));

  Future<void> sendResponse({
    required String conversationId,
    required String responseText,
  }) async {
    try {
      AppLogger.userAction('📤 Sending response via notifier', tag: 'RESPONSE_NOTIFIER');
      state = const AsyncValue.loading();
      
      final conversation = await _repository.sendResponse(
        conversationId: conversationId,
        responseText: responseText,
      );
      
      state = AsyncValue.data(conversation);
      AppLogger.userAction('✅ Response sent successfully', tag: 'RESPONSE_NOTIFIER');
    } catch (e, stackTrace) {
      AppLogger.userAction('❌ Error sending response: $e', tag: 'RESPONSE_NOTIFIER');
      state = AsyncValue.error(e, stackTrace);
    }
  }

  void reset() {
    state = const AsyncValue.data(null);
  }
}

final responseNotifierProvider = StateNotifierProvider<ResponseNotifier, AsyncValue<Conversation?>>((ref) {
  final repository = ref.read(conversationRepositoryProvider);
  return ResponseNotifier(repository);
});

// UI state providers for form management
final selectedTagProvider = StateProvider<QuestionTag?>((ref) => null);
final questionTextProvider = StateProvider<String>((ref) => '');
final responseTextProvider = StateProvider<String>((ref) => '');

// Computed providers for form validation
final canSendQuestionProvider = Provider<bool>((ref) {
  final selectedTag = ref.watch(selectedTagProvider);
  final questionText = ref.watch(questionTextProvider);
  
  return selectedTag != null && questionText.trim().isNotEmpty;
});

final canSendResponseProvider = Provider<bool>((ref) {
  final responseText = ref.watch(responseTextProvider);
  return responseText.trim().isNotEmpty;
});

// Provider to refresh conversations after sending
final refreshConversationsProvider = Provider<void>((ref) {
  // This will be used to invalidate conversation providers after sending
  ref.invalidate(studentConversationsProvider);
  ref.invalidate(pendingQuestionsProvider);
  ref.invalidate(openConversationProvider);
});
