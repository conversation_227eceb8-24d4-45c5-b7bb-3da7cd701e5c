import 'package:supabase_flutter/supabase_flutter.dart';
import '../domain/shopping_list_models.dart';
import '../../shared/utils/app_logger.dart';

class ShoppingListRepository {
  final SupabaseClient _supabase = Supabase.instance.client;

  /// Get student's shopping lists
  Future<List<ShoppingList>> getStudentShoppingLists(
    String studentId,
    String instructorId,
  ) async {
    try {
      final response = await _supabase
          .from('shopping_lists')
          .select('*')
          .eq('student_id', studentId)
          .eq('instructor_id', instructorId)
          .order('assigned_at', ascending: false);

      return (response as List)
          .map((json) => ShoppingList.fromJson(json))
          .toList();
    } catch (e) {
      AppLogger.error('Failed to fetch student shopping lists: $e', tag: 'SHOPPING_LIST_REPO');
      rethrow;
    }
  }

  /// Assign shopping list to student
  Future<ShoppingList> assignShoppingListToStudent({
    required String studentId,
    required String instructorId,
    required String title,
    required List<String> items,
    String? notes,
  }) async {
    try {
      final shoppingListData = {
        'student_id': studentId,
        'instructor_id': instructorId,
        'title': title,
        'items': items,
        'notes': notes,
        'status': ShoppingListStatus.active.name,
        'assigned_at': DateTime.now().toIso8601String(),
      };

      final response = await _supabase
          .from('shopping_lists')
          .insert(shoppingListData)
          .select('*')
          .single();

      AppLogger.success(
        'Shopping list assigned successfully to student $studentId',
        tag: 'SHOPPING_LIST_REPO',
      );

      return ShoppingList.fromJson(response);
    } catch (e) {
      AppLogger.error('Failed to assign shopping list: $e', tag: 'SHOPPING_LIST_REPO');
      rethrow;
    }
  }

  /// Update shopping list
  Future<ShoppingList> updateShoppingList({
    required String shoppingListId,
    String? title,
    List<String>? items,
    String? notes,
    ShoppingListStatus? status,
  }) async {
    try {
      final updateData = <String, dynamic>{};
      if (title != null) updateData['title'] = title;
      if (items != null) updateData['items'] = items;
      if (notes != null) updateData['notes'] = notes;
      if (status != null) updateData['status'] = status.name;
      updateData['updated_at'] = DateTime.now().toIso8601String();

      final response = await _supabase
          .from('shopping_lists')
          .update(updateData)
          .eq('id', shoppingListId)
          .select('*')
          .single();

      AppLogger.success(
        'Shopping list updated: $shoppingListId',
        tag: 'SHOPPING_LIST_REPO',
      );

      return ShoppingList.fromJson(response);
    } catch (e) {
      AppLogger.error('Failed to update shopping list: $e', tag: 'SHOPPING_LIST_REPO');
      rethrow;
    }
  }

  /// Delete shopping list
  Future<void> deleteShoppingList(String shoppingListId) async {
    try {
      await _supabase
          .from('shopping_lists')
          .delete()
          .eq('id', shoppingListId);

      AppLogger.success(
        'Shopping list deleted: $shoppingListId',
        tag: 'SHOPPING_LIST_REPO',
      );
    } catch (e) {
      AppLogger.error('Failed to delete shopping list: $e', tag: 'SHOPPING_LIST_REPO');
      rethrow;
    }
  }

  /// Mark shopping list as completed
  Future<void> markShoppingListAsCompleted(String shoppingListId) async {
    try {
      await _supabase
          .from('shopping_lists')
          .update({
            'status': ShoppingListStatus.completed.name,
            'updated_at': DateTime.now().toIso8601String(),
          })
          .eq('id', shoppingListId);

      AppLogger.success(
        'Shopping list marked as completed: $shoppingListId',
        tag: 'SHOPPING_LIST_REPO',
      );
    } catch (e) {
      AppLogger.error('Failed to mark shopping list as completed: $e', tag: 'SHOPPING_LIST_REPO');
      rethrow;
    }
  }
}
