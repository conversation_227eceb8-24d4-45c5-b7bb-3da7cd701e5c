import 'package:flutter/material.dart';
import 'package:flutter_hooks/flutter_hooks.dart';
import 'package:hooks_riverpod/hooks_riverpod.dart';
import '../../instructor/domain/student_models.dart';
import '../domain/shopping_list_models.dart';
import '../application/shopping_list_provider.dart';
import '../../shared/constants/app_text_style.dart';

class AssignShoppingListPage extends HookConsumerWidget {
  final InstructorStudent student;
  final String instructorId;

  const AssignShoppingListPage({
    super.key,
    required this.student,
    required this.instructorId,
  });

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final titleController = useTextEditingController(text: 'Shopping List');
    final notesController = useTextEditingController();
    final itemControllers = useState<List<TextEditingController>>([
      TextEditingController(),
    ]);
    final isLoading = useState(false);

    // Get existing shopping lists
    final shoppingListsAsync = ref.watch(instructorStudentShoppingListsProvider(
      StudentShoppingListParams(studentId: student.id, instructorId: instructorId),
    ));

    return Scaffold(
      backgroundColor: const Color(0xFF111827),
      appBar: AppBar(
        backgroundColor: const Color(0xFF111827),
        title: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'Shopping List',
              style: ATextStyle.large.copyWith(
                color: Colors.white,
                fontWeight: FontWeight.bold,
              ),
            ),
            Text(
              'for ${student.name}',
              style: ATextStyle.small.copyWith(color: Colors.grey),
            ),
          ],
        ),
        leading: IconButton(
          icon: const Icon(Icons.arrow_back, color: Colors.white),
          onPressed: () => Navigator.pop(context),
        ),
      ),
      body: Column(
        children: [
          // Current Shopping Lists Section
          _buildCurrentShoppingListsSection(context, ref, shoppingListsAsync),
          
          const Divider(color: Color(0xFF374151), height: 1),
          
          // Create New Shopping List Section
          Expanded(
            child: _buildCreateShoppingListSection(
              context,
              ref,
              titleController,
              notesController,
              itemControllers,
              isLoading,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildCurrentShoppingListsSection(
    BuildContext context,
    WidgetRef ref,
    AsyncValue<List<ShoppingList>> shoppingListsAsync,
  ) {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: const BoxDecoration(
        color: Color(0xFF1F2937),
        border: Border(bottom: BorderSide(color: Color(0xFF374151))),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              const Icon(Icons.shopping_cart, color: Color(0xFFFACC15), size: 20),
              const SizedBox(width: 8),
              Text(
                'Current Shopping Lists',
                style: ATextStyle.medium.copyWith(
                  color: Colors.white,
                  fontWeight: FontWeight.bold,
                ),
              ),
            ],
          ),
          const SizedBox(height: 12),
          shoppingListsAsync.when(
            data: (shoppingLists) {
              if (shoppingLists.isEmpty) {
                return Container(
                  padding: const EdgeInsets.all(16),
                  decoration: BoxDecoration(
                    color: const Color(0xFF374151),
                    borderRadius: BorderRadius.circular(8),
                  ),
                  child: Row(
                    children: [
                      const Icon(Icons.info_outline, color: Colors.grey, size: 16),
                      const SizedBox(width: 8),
                      Text(
                        'No shopping lists assigned yet',
                        style: ATextStyle.small.copyWith(color: Colors.grey),
                      ),
                    ],
                  ),
                );
              }

              return SizedBox(
                height: 120,
                child: ListView.builder(
                  scrollDirection: Axis.horizontal,
                  itemCount: shoppingLists.length,
                  itemBuilder: (context, index) {
                    final shoppingList = shoppingLists[index];
                    return _buildCurrentShoppingListCard(context, ref, shoppingList);
                  },
                ),
              );
            },
            loading: () => const Center(
              child: CircularProgressIndicator(color: Color(0xFFFACC15)),
            ),
            error: (error, stack) => Container(
              padding: const EdgeInsets.all(16),
              decoration: BoxDecoration(
                color: const Color(0xFF374151),
                borderRadius: BorderRadius.circular(8),
              ),
              child: Row(
                children: [
                  const Icon(Icons.error_outline, color: Colors.red, size: 16),
                  const SizedBox(width: 8),
                  Expanded(
                    child: Text(
                      'Error loading shopping lists: $error',
                      style: ATextStyle.small.copyWith(color: Colors.red),
                    ),
                  ),
                ],
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildCurrentShoppingListCard(
    BuildContext context,
    WidgetRef ref,
    ShoppingList shoppingList,
  ) {
    return Container(
      width: 200,
      margin: const EdgeInsets.only(right: 12),
      padding: const EdgeInsets.all(12),
      decoration: BoxDecoration(
        color: const Color(0xFF374151),
        borderRadius: BorderRadius.circular(8),
        border: Border.all(color: const Color(0xFF4B5563)),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Expanded(
                child: Text(
                  shoppingList.title,
                  style: ATextStyle.small.copyWith(
                    color: Colors.white,
                    fontWeight: FontWeight.bold,
                  ),
                  maxLines: 1,
                  overflow: TextOverflow.ellipsis,
                ),
              ),
              PopupMenuButton<String>(
                icon: const Icon(Icons.more_vert, color: Colors.grey, size: 16),
                color: const Color(0xFF374151),
                onSelected: (value) {
                  if (value == 'delete') {
                    _showDeleteShoppingListDialog(context, ref, shoppingList);
                  }
                },
                itemBuilder: (context) => [
                  PopupMenuItem(
                    value: 'delete',
                    child: Row(
                      children: [
                        const Icon(Icons.delete, color: Colors.red, size: 16),
                        const SizedBox(width: 8),
                        Text(
                          'Delete',
                          style: ATextStyle.small.copyWith(color: Colors.red),
                        ),
                      ],
                    ),
                  ),
                ],
              ),
            ],
          ),
          const SizedBox(height: 8),
          Text(
            '${shoppingList.items.length} items',
            style: ATextStyle.small.copyWith(color: Colors.grey),
          ),
          const SizedBox(height: 4),
          Text(
            shoppingList.status.name.toUpperCase(),
            style: ATextStyle.small.copyWith(
              color: shoppingList.status == ShoppingListStatus.active
                  ? const Color(0xFFFACC15)
                  : Colors.grey,
              fontWeight: FontWeight.bold,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildCreateShoppingListSection(
    BuildContext context,
    WidgetRef ref,
    TextEditingController titleController,
    TextEditingController notesController,
    ValueNotifier<List<TextEditingController>> itemControllers,
    ValueNotifier<bool> isLoading,
  ) {
    return Container(
      padding: const EdgeInsets.all(16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            'Create New Shopping List',
            style: ATextStyle.medium.copyWith(
              color: Colors.white,
              fontWeight: FontWeight.bold,
            ),
          ),
          const SizedBox(height: 16),
          
          // Title Field
          _buildTextField(
            controller: titleController,
            label: 'List Title',
            hint: 'e.g., Weekly Groceries',
          ),
          
          const SizedBox(height: 16),
          
          // Items Section
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Row(
                  children: [
                    Text(
                      'Shopping Items',
                      style: ATextStyle.small.copyWith(
                        color: Colors.white,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                    const Spacer(),
                    TextButton.icon(
                      onPressed: () {
                        itemControllers.value = [
                          ...itemControllers.value,
                          TextEditingController(),
                        ];
                      },
                      icon: const Icon(Icons.add, color: Color(0xFFFACC15), size: 16),
                      label: Text(
                        'Add Item',
                        style: ATextStyle.small.copyWith(color: const Color(0xFFFACC15)),
                      ),
                    ),
                  ],
                ),
                const SizedBox(height: 8),
                
                // Items List
                Expanded(
                  child: ListView.builder(
                    itemCount: itemControllers.value.length,
                    itemBuilder: (context, index) {
                      return _buildItemField(context, itemControllers, index);
                    },
                  ),
                ),
              ],
            ),
          ),
          
          const SizedBox(height: 16),
          
          // Notes Field
          _buildTextField(
            controller: notesController,
            label: 'Notes (Optional)',
            hint: 'Additional instructions...',
            maxLines: 3,
          ),
          
          const SizedBox(height: 24),
          
          // Assign Button
          SizedBox(
            width: double.infinity,
            child: ElevatedButton(
              onPressed: isLoading.value
                  ? null
                  : () => _assignShoppingList(
                        context,
                        ref,
                        titleController,
                        notesController,
                        itemControllers,
                        isLoading,
                      ),
              style: ElevatedButton.styleFrom(
                backgroundColor: const Color(0xFFFACC15),
                foregroundColor: Colors.black,
                padding: const EdgeInsets.symmetric(vertical: 16),
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(8),
                ),
              ),
              child: isLoading.value
                  ? const SizedBox(
                      height: 20,
                      width: 20,
                      child: CircularProgressIndicator(
                        strokeWidth: 2,
                        color: Colors.black,
                      ),
                    )
                  : Text(
                      'Assign Shopping List',
                      style: ATextStyle.medium.copyWith(
                        color: Colors.black,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildTextField({
    required TextEditingController controller,
    required String label,
    required String hint,
    int maxLines = 1,
  }) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          label,
          style: ATextStyle.small.copyWith(
            color: Colors.white,
            fontWeight: FontWeight.bold,
          ),
        ),
        const SizedBox(height: 8),
        TextField(
          controller: controller,
          maxLines: maxLines,
          style: ATextStyle.small.copyWith(color: Colors.white),
          decoration: InputDecoration(
            hintText: hint,
            hintStyle: ATextStyle.small.copyWith(color: Colors.grey),
            filled: true,
            fillColor: const Color(0xFF374151),
            border: OutlineInputBorder(
              borderRadius: BorderRadius.circular(8),
              borderSide: BorderSide.none,
            ),
            focusedBorder: OutlineInputBorder(
              borderRadius: BorderRadius.circular(8),
              borderSide: const BorderSide(color: Color(0xFFFACC15)),
            ),
          ),
        ),
      ],
    );
  }

  Widget _buildItemField(
    BuildContext context,
    ValueNotifier<List<TextEditingController>> itemControllers,
    int index,
  ) {
    return Padding(
      padding: const EdgeInsets.only(bottom: 8),
      child: Row(
        children: [
          Expanded(
            child: TextField(
              controller: itemControllers.value[index],
              style: ATextStyle.small.copyWith(color: Colors.white),
              decoration: InputDecoration(
                hintText: 'e.g., 1 kg un, 500g et...',
                hintStyle: ATextStyle.small.copyWith(color: Colors.grey),
                filled: true,
                fillColor: const Color(0xFF374151),
                border: OutlineInputBorder(
                  borderRadius: BorderRadius.circular(8),
                  borderSide: BorderSide.none,
                ),
                focusedBorder: OutlineInputBorder(
                  borderRadius: BorderRadius.circular(8),
                  borderSide: const BorderSide(color: Color(0xFFFACC15)),
                ),
                contentPadding: const EdgeInsets.symmetric(
                  horizontal: 12,
                  vertical: 12,
                ),
              ),
            ),
          ),
          if (itemControllers.value.length > 1) ...[
            const SizedBox(width: 8),
            IconButton(
              onPressed: () {
                final controllers = List<TextEditingController>.from(itemControllers.value);
                controllers[index].dispose();
                controllers.removeAt(index);
                itemControllers.value = controllers;
              },
              icon: const Icon(Icons.remove_circle, color: Colors.red),
            ),
          ],
        ],
      ),
    );
  }

  Future<void> _assignShoppingList(
    BuildContext context,
    WidgetRef ref,
    TextEditingController titleController,
    TextEditingController notesController,
    ValueNotifier<List<TextEditingController>> itemControllers,
    ValueNotifier<bool> isLoading,
  ) async {
    // Validate inputs
    if (titleController.text.trim().isEmpty) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(
          content: Text('Please enter a title for the shopping list'),
          backgroundColor: Colors.red,
        ),
      );
      return;
    }

    final items = itemControllers.value
        .map((controller) => controller.text.trim())
        .where((item) => item.isNotEmpty)
        .toList();

    if (items.isEmpty) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(
          content: Text('Please add at least one item to the shopping list'),
          backgroundColor: Colors.red,
        ),
      );
      return;
    }

    isLoading.value = true;

    try {
      final repository = ref.read(shoppingListRepositoryProvider);
      await repository.assignShoppingListToStudent(
        studentId: student.id,
        instructorId: instructorId,
        title: titleController.text.trim(),
        items: items,
        notes: notesController.text.trim().isEmpty ? null : notesController.text.trim(),
      );

      if (context.mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Shopping list assigned to ${student.name}'),
            backgroundColor: Colors.green,
          ),
        );

        // Clear form
        titleController.text = 'Shopping List';
        notesController.clear();
        for (final controller in itemControllers.value) {
          controller.dispose();
        }
        itemControllers.value = [TextEditingController()];

        // Refresh shopping lists
        ref.invalidate(studentShoppingListsProvider);
      }
    } catch (e) {
      if (context.mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Error assigning shopping list: $e'),
            backgroundColor: Colors.red,
          ),
        );
      }
    } finally {
      isLoading.value = false;
    }
  }

  void _showDeleteShoppingListDialog(
    BuildContext context,
    WidgetRef ref,
    ShoppingList shoppingList,
  ) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        backgroundColor: const Color(0xFF374151),
        title: Text(
          'Delete Shopping List',
          style: ATextStyle.medium.copyWith(color: Colors.white),
        ),
        content: Text(
          'Are you sure you want to delete "${shoppingList.title}"? This action cannot be undone.',
          style: ATextStyle.small.copyWith(color: Colors.grey),
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: Text(
              'Cancel',
              style: ATextStyle.small.copyWith(color: Colors.grey),
            ),
          ),
          TextButton(
            onPressed: () async {
              Navigator.pop(context);

              try {
                final repository = ref.read(shoppingListRepositoryProvider);
                await repository.deleteShoppingList(shoppingList.id);

                if (context.mounted) {
                  ScaffoldMessenger.of(context).showSnackBar(
                    const SnackBar(
                      content: Text('Shopping list deleted'),
                      backgroundColor: Colors.green,
                    ),
                  );
                  ref.invalidate(instructorStudentShoppingListsProvider(
                    StudentShoppingListParams(studentId: student.id, instructorId: instructorId),
                  ));
                }
              } catch (e) {
                if (context.mounted) {
                  ScaffoldMessenger.of(context).showSnackBar(
                    SnackBar(
                      content: Text('Error deleting shopping list: $e'),
                      backgroundColor: Colors.red,
                    ),
                  );
                }
              }
            },
            child: Text(
              'Delete',
              style: ATextStyle.small.copyWith(color: Colors.red),
            ),
          ),
        ],
      ),
    );
  }
}
