import 'package:flutter/material.dart';
import 'package:flutter_hooks/flutter_hooks.dart';
import 'package:hooks_riverpod/hooks_riverpod.dart';
import '../domain/shopping_list_models.dart';
import '../application/shopping_list_provider.dart';
import '../../shared/constants/app_text_style.dart';

class StudentShoppingListPage extends HookConsumerWidget {
  const StudentShoppingListPage({super.key});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final selectedFilter = useState<ShoppingListStatus?>(null);
    final shoppingListsAsync = ref.watch(studentShoppingListsProvider);

    return Scaffold(
      backgroundColor: const Color(0xFF111827),
      appBar: AppBar(
        backgroundColor: const Color(0xFF111827),
        title: Text(
          'My Shopping Lists',
          style: ATextStyle.large.copyWith(
            color: Colors.white,
            fontWeight: FontWeight.bold,
          ),
        ),
        leading: IconButton(
          icon: const Icon(Icons.arrow_back, color: Colors.white),
          onPressed: () => Navigator.pop(context),
        ),
        actions: [
          IconButton(
            icon: const Icon(Icons.refresh, color: Color(0xFFFACC15)),
            onPressed: () => ref.invalidate(studentShoppingListsProvider),
            tooltip: 'Refresh',
          ),
        ],
      ),
      body: Column(
        children: [
          // Filter Section
          _buildFilterSection(selectedFilter, ref),
          
          // Shopping Lists
          Expanded(
            child: _buildShoppingListsList(context, ref, shoppingListsAsync, selectedFilter.value),
          ),
        ],
      ),
    );
  }

  Widget _buildFilterSection(
    ValueNotifier<ShoppingListStatus?> selectedFilter,
    WidgetRef ref,
  ) {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: const BoxDecoration(
        color: Color(0xFF1F2937),
        border: Border(bottom: BorderSide(color: Color(0xFF374151))),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            'Filter by Status',
            style: ATextStyle.small.copyWith(
              color: Colors.white,
              fontWeight: FontWeight.bold,
            ),
          ),
          const SizedBox(height: 12),
          SingleChildScrollView(
            scrollDirection: Axis.horizontal,
            child: Row(
              children: [
                _buildFilterChip(
                  label: 'All',
                  isSelected: selectedFilter.value == null,
                  onTap: () {
                    selectedFilter.value = null;
                  },
                ),
                const SizedBox(width: 8),
                ...ShoppingListStatus.values.map((status) {
                  return Padding(
                    padding: const EdgeInsets.only(right: 8),
                    child: _buildFilterChip(
                      label: _getStatusDisplayName(status),
                      isSelected: selectedFilter.value == status,
                      onTap: () {
                        selectedFilter.value = selectedFilter.value == status ? null : status;
                      },
                    ),
                  );
                }),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildFilterChip({
    required String label,
    required bool isSelected,
    required VoidCallback onTap,
  }) {
    return GestureDetector(
      onTap: onTap,
      child: Container(
        padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
        decoration: BoxDecoration(
          color: isSelected ? const Color(0xFFFACC15) : const Color(0xFF374151),
          borderRadius: BorderRadius.circular(20),
          border: Border.all(
            color: isSelected ? const Color(0xFFFACC15) : const Color(0xFF4B5563),
          ),
        ),
        child: Text(
          label,
          style: ATextStyle.small.copyWith(
            color: isSelected ? Colors.black : Colors.white,
            fontWeight: isSelected ? FontWeight.bold : FontWeight.normal,
          ),
        ),
      ),
    );
  }

  Widget _buildShoppingListsList(
    BuildContext context,
    WidgetRef ref,
    AsyncValue<List<ShoppingList>> shoppingListsAsync,
    ShoppingListStatus? filter,
  ) {
    return shoppingListsAsync.when(
      data: (shoppingLists) {
        // Apply filter
        final filteredLists = filter != null
            ? shoppingLists.where((list) => list.status == filter).toList()
            : shoppingLists;

        if (filteredLists.isEmpty) {
          return Center(
            child: Column(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                const Icon(Icons.shopping_cart_outlined, color: Colors.grey, size: 64),
                const SizedBox(height: 16),
                Text(
                  filter != null 
                      ? 'No ${_getStatusDisplayName(filter).toLowerCase()} shopping lists'
                      : 'No shopping lists assigned yet',
                  style: ATextStyle.medium.copyWith(color: Colors.grey),
                ),
                const SizedBox(height: 8),
                Text(
                  'Your instructor will assign shopping lists here',
                  style: ATextStyle.small.copyWith(color: Colors.grey),
                ),
              ],
            ),
          );
        }

        return ListView.builder(
          padding: const EdgeInsets.all(16),
          itemCount: filteredLists.length,
          itemBuilder: (context, index) {
            final shoppingList = filteredLists[index];
            return _buildShoppingListCard(context, ref, shoppingList);
          },
        );
      },
      loading: () => const Center(
        child: CircularProgressIndicator(color: Color(0xFFFACC15)),
      ),
      error: (error, stack) => Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            const Icon(Icons.error_outline, color: Colors.red, size: 64),
            const SizedBox(height: 16),
            Text(
              'Error loading shopping lists',
              style: ATextStyle.medium.copyWith(color: Colors.red),
            ),
            const SizedBox(height: 8),
            Text(
              error.toString(),
              style: ATextStyle.small.copyWith(color: Colors.grey),
              textAlign: TextAlign.center,
            ),
            const SizedBox(height: 16),
            ElevatedButton(
              onPressed: () => ref.invalidate(studentShoppingListsProvider),
              style: ElevatedButton.styleFrom(
                backgroundColor: const Color(0xFFFACC15),
                foregroundColor: Colors.black,
              ),
              child: const Text('Retry'),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildShoppingListCard(
    BuildContext context,
    WidgetRef ref,
    ShoppingList shoppingList,
  ) {
    return Container(
      margin: const EdgeInsets.only(bottom: 16),
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: const Color(0xFF1F2937),
        borderRadius: BorderRadius.circular(12),
        border: Border.all(color: const Color(0xFF374151)),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Header
          Row(
            children: [
              Icon(
                Icons.shopping_cart,
                color: _getStatusColor(shoppingList.status),
                size: 20,
              ),
              const SizedBox(width: 8),
              Expanded(
                child: Text(
                  shoppingList.title,
                  style: ATextStyle.medium.copyWith(
                    color: Colors.white,
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ),
              Container(
                padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                decoration: BoxDecoration(
                  color: _getStatusColor(shoppingList.status).withOpacity(0.2),
                  borderRadius: BorderRadius.circular(12),
                  border: Border.all(
                    color: _getStatusColor(shoppingList.status).withOpacity(0.5),
                  ),
                ),
                child: Text(
                  _getStatusDisplayName(shoppingList.status),
                  style: ATextStyle.small.copyWith(
                    color: _getStatusColor(shoppingList.status),
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ),
            ],
          ),
          
          const SizedBox(height: 16),
          
          // Items List
          _buildItemsList(shoppingList.items),

          // Instructor Notes
          if (shoppingList.notes != null && shoppingList.notes!.isNotEmpty) ...[
            const SizedBox(height: 12),
            _buildInstructorNotes(shoppingList.notes!),
          ],

          const SizedBox(height: 12),

          // Date Information
          _buildDateInfo(shoppingList.assignedAt),
          
          // Action Button
          if (shoppingList.status == ShoppingListStatus.active) ...[
            const SizedBox(height: 16),
            SizedBox(
              width: double.infinity,
              child: ElevatedButton(
                onPressed: () => _markAsCompleted(context, ref, shoppingList),
                style: ElevatedButton.styleFrom(
                  backgroundColor: const Color(0xFFFACC15),
                  foregroundColor: Colors.black,
                  padding: const EdgeInsets.symmetric(vertical: 12),
                  shape: RoundedRectangleBorder(
                    borderRadius: BorderRadius.circular(8),
                  ),
                ),
                child: Text(
                  'Mark as Completed',
                  style: ATextStyle.medium.copyWith(
                    color: Colors.black,
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ),
            ),
          ],
        ],
      ),
    );
  }

  Widget _buildItemsList(List<String> items) {
    return Container(
      padding: const EdgeInsets.all(12),
      decoration: BoxDecoration(
        color: const Color(0xFF374151),
        borderRadius: BorderRadius.circular(8),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              const Icon(Icons.list, color: Color(0xFFFACC15), size: 16),
              const SizedBox(width: 8),
              Text(
                'Shopping Items (${items.length})',
                style: ATextStyle.small.copyWith(
                  color: Colors.white,
                  fontWeight: FontWeight.bold,
                ),
              ),
            ],
          ),
          const SizedBox(height: 8),
          ...items.map((item) => Padding(
            padding: const EdgeInsets.only(bottom: 4),
            child: Row(
              children: [
                const Icon(Icons.circle, color: Color(0xFFFACC15), size: 6),
                const SizedBox(width: 8),
                Expanded(
                  child: Text(
                    item,
                    style: ATextStyle.small.copyWith(color: Colors.white),
                  ),
                ),
              ],
            ),
          )),
        ],
      ),
    );
  }

  Widget _buildInstructorNotes(String notes) {
    return Container(
      padding: const EdgeInsets.all(12),
      decoration: BoxDecoration(
        color: const Color(0xFF374151),
        borderRadius: BorderRadius.circular(8),
        border: Border.all(color: const Color(0xFF4B5563)),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              const Icon(Icons.note, color: Color(0xFFFACC15), size: 16),
              const SizedBox(width: 8),
              Text(
                'Instructor Notes',
                style: ATextStyle.small.copyWith(
                  color: Colors.white,
                  fontWeight: FontWeight.bold,
                ),
              ),
            ],
          ),
          const SizedBox(height: 8),
          Text(
            notes,
            style: ATextStyle.small.copyWith(color: Colors.white),
          ),
        ],
      ),
    );
  }

  Widget _buildDateInfo(DateTime assignedAt) {
    return Container(
      padding: const EdgeInsets.all(8),
      decoration: BoxDecoration(
        color: const Color(0xFF374151),
        borderRadius: BorderRadius.circular(6),
      ),
      child: Row(
        children: [
          const Icon(Icons.schedule, color: Colors.grey, size: 14),
          const SizedBox(width: 6),
          Text(
            'Assigned: ${_formatDate(assignedAt)}',
            style: ATextStyle.small.copyWith(color: Colors.grey),
          ),
        ],
      ),
    );
  }

  String _getStatusDisplayName(ShoppingListStatus status) {
    switch (status) {
      case ShoppingListStatus.active:
        return 'Active';
      case ShoppingListStatus.completed:
        return 'Completed';
      case ShoppingListStatus.cancelled:
        return 'Cancelled';
    }
  }

  Color _getStatusColor(ShoppingListStatus status) {
    switch (status) {
      case ShoppingListStatus.active:
        return const Color(0xFFFACC15);
      case ShoppingListStatus.completed:
        return Colors.green;
      case ShoppingListStatus.cancelled:
        return Colors.red;
    }
  }

  String _formatDate(DateTime date) {
    return '${date.day}/${date.month}/${date.year}';
  }

  Future<void> _markAsCompleted(
    BuildContext context,
    WidgetRef ref,
    ShoppingList shoppingList,
  ) async {
    // Show confirmation dialog
    final shouldComplete = await showDialog<bool>(
      context: context,
      builder: (context) => AlertDialog(
        backgroundColor: const Color(0xFF374151),
        title: Text(
          'Mark as Completed',
          style: ATextStyle.medium.copyWith(color: Colors.white),
        ),
        content: Text(
          'Are you sure you want to mark "${shoppingList.title}" as completed?',
          style: ATextStyle.small.copyWith(color: Colors.grey),
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context, false),
            child: Text(
              'Cancel',
              style: ATextStyle.small.copyWith(color: Colors.grey),
            ),
          ),
          TextButton(
            onPressed: () => Navigator.pop(context, true),
            child: Text(
              'Mark as Completed',
              style: ATextStyle.small.copyWith(color: const Color(0xFFFACC15)),
            ),
          ),
        ],
      ),
    );

    if (shouldComplete == true && context.mounted) {
      try {
        final repository = ref.read(shoppingListRepositoryProvider);
        await repository.markShoppingListAsCompleted(shoppingList.id);

        // Refresh the shopping lists
        ref.invalidate(studentShoppingListsProvider);

        if (context.mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Text('${shoppingList.title} marked as completed'),
              backgroundColor: Colors.green,
            ),
          );
        }
      } catch (e) {
        if (context.mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Text('Error: $e'),
              backgroundColor: Colors.red,
            ),
          );
        }
      }
    }
  }
}
