import 'package:equatable/equatable.dart';
import 'package:flutter/foundation.dart';

enum ShoppingListStatus {
  active,
  completed,
  cancelled,
}

@immutable
class ShoppingList extends Equatable {
  final String id;
  final String studentId;
  final String instructorId;
  final String title;
  final List<String> items;
  final String? notes;
  final ShoppingListStatus status;
  final DateTime assignedAt;

  const ShoppingList({
    required this.id,
    required this.studentId,
    required this.instructorId,
    required this.title,
    required this.items,
    this.notes,
    required this.status,
    required this.assignedAt,
  });

  @override
  List<Object?> get props => [
        id,
        studentId,
        instructorId,
        title,
        items,
        notes,
        status,
        assignedAt,
      ];

  factory ShoppingList.fromJson(Map<String, dynamic> json) {
    return ShoppingList(
      id: json['id'] as String,
      studentId: json['student_id'] as String,
      instructorId: json['instructor_id'] as String,
      title: json['title'] as String? ?? 'Shopping List',
      items: (json['items'] as List<dynamic>?)?.cast<String>() ?? [],
      notes: json['notes'] as String?,
      status: ShoppingListStatus.values.firstWhere(
        (e) => e.name == json['status'],
        orElse: () => ShoppingListStatus.active,
      ),
      assignedAt: json['assigned_at'] != null
          ? (json['assigned_at'] is String
              ? DateTime.parse(json['assigned_at'] as String)
              : DateTime.parse(json['assigned_at'].toString()))
          : DateTime.now(),
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'student_id': studentId,
      'instructor_id': instructorId,
      'title': title,
      'items': items,
      'notes': notes,
      'status': status.name,
      'assigned_at': assignedAt.toIso8601String(),
    };
  }

  ShoppingList copyWith({
    String? id,
    String? studentId,
    String? instructorId,
    String? title,
    List<String>? items,
    String? notes,
    ShoppingListStatus? status,
    DateTime? assignedAt,
  }) {
    return ShoppingList(
      id: id ?? this.id,
      studentId: studentId ?? this.studentId,
      instructorId: instructorId ?? this.instructorId,
      title: title ?? this.title,
      items: items ?? this.items,
      notes: notes ?? this.notes,
      status: status ?? this.status,
      assignedAt: assignedAt ?? this.assignedAt,
    );
  }
}
