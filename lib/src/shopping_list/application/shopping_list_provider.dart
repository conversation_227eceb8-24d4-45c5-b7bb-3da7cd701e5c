import 'package:hooks_riverpod/hooks_riverpod.dart';
import 'package:supabase_flutter/supabase_flutter.dart';
import '../domain/shopping_list_models.dart';
import '../data/shopping_list_repository.dart';
import '../../shared/utils/app_logger.dart';

// Repository provider
final shoppingListRepositoryProvider = Provider<ShoppingListRepository>((ref) {
  return ShoppingListRepository();
});

// Student shopping lists provider
final studentShoppingListsProvider = FutureProvider<List<ShoppingList>>((ref) async {
  final repository = ref.read(shoppingListRepositoryProvider);
  final user = Supabase.instance.client.auth.currentUser;
  
  if (user == null) {
    throw Exception('User not authenticated');
  }

  // Get current user's instructor from enrollment
  // For now, we'll use a hardcoded instructor ID
  // TODO: Get from actual enrollment data
  final instructorId = '9be7d1dc-b119-4329-a5be-c02b63666af9'; // Current instructor
  
  return repository.getStudentShoppingLists(
    user.id,
    instructorId,
  );
});

// Instructor shopping lists provider for specific student
final instructorStudentShoppingListsProvider = FutureProvider.family<List<ShoppingList>, StudentShoppingListParams>((ref, params) async {
  final repository = ref.read(shoppingListRepositoryProvider);
  return repository.getStudentShoppingLists(params.studentId, params.instructorId);
});

// Mark shopping list as completed provider
final markShoppingListCompletedProvider = FutureProvider.family<void, String>((ref, shoppingListId) async {
  final repository = ref.read(shoppingListRepositoryProvider);
  await repository.markShoppingListAsCompleted(shoppingListId);
  
  // Invalidate the shopping lists to refresh the UI
  ref.invalidate(studentShoppingListsProvider);
});

// Assign shopping list provider
final assignShoppingListProvider = FutureProvider.family<ShoppingList, AssignShoppingListParams>((ref, params) async {
  final repository = ref.read(shoppingListRepositoryProvider);
  final result = await repository.assignShoppingListToStudent(
    studentId: params.studentId,
    instructorId: params.instructorId,
    title: params.title,
    items: params.items,
    notes: params.notes,
  );
  
  // Invalidate the shopping lists to refresh the UI
  ref.invalidate(instructorStudentShoppingListsProvider(
    StudentShoppingListParams(
      studentId: params.studentId,
      instructorId: params.instructorId,
    ),
  ));
  
  return result;
});

// Delete shopping list provider
final deleteShoppingListProvider = FutureProvider.family<void, DeleteShoppingListParams>((ref, params) async {
  final repository = ref.read(shoppingListRepositoryProvider);
  await repository.deleteShoppingList(params.shoppingListId);
  
  // Invalidate the shopping lists to refresh the UI
  ref.invalidate(instructorStudentShoppingListsProvider(
    StudentShoppingListParams(
      studentId: params.studentId,
      instructorId: params.instructorId,
    ),
  ));
});

// Parameter classes
class StudentShoppingListParams {
  final String studentId;
  final String instructorId;

  const StudentShoppingListParams({
    required this.studentId,
    required this.instructorId,
  });

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is StudentShoppingListParams &&
          runtimeType == other.runtimeType &&
          studentId == other.studentId &&
          instructorId == other.instructorId;

  @override
  int get hashCode => studentId.hashCode ^ instructorId.hashCode;
}

class AssignShoppingListParams {
  final String studentId;
  final String instructorId;
  final String title;
  final List<String> items;
  final String? notes;

  const AssignShoppingListParams({
    required this.studentId,
    required this.instructorId,
    required this.title,
    required this.items,
    this.notes,
  });
}

class DeleteShoppingListParams {
  final String shoppingListId;
  final String studentId;
  final String instructorId;

  const DeleteShoppingListParams({
    required this.shoppingListId,
    required this.studentId,
    required this.instructorId,
  });
}
