import 'package:fitgo_app/src/payment/domain/payment_models.dart';
import 'package:supabase_flutter/supabase_flutter.dart';

// Repository for payment operations following DDD pattern
class PaymentRepository {
  final SupabaseClient _supabase = Supabase.instance.client;

  // Process payment and save to database
  Future<PaymentResult> processPayment({
    required OrderSummary orderSummary,
    required MockPaymentCard paymentCard,
  }) async {
    try {
      // Simulate API call delay
      await Future.delayed(const Duration(milliseconds: 2000));

      // Mock validation - in real implementation this would call payment provider
      // if (!paymentCard.isValid) {
      //   return PaymentResult.failure('Kart bilgileri geçersiz');
      // }

      // Simulate random success/failure for demo purposes
      // In real implementation, this would be determined by payment provider response
      final isSuccess =
          DateTime.now().millisecond % 10 != 0; // 90% success rate

      if (isSuccess) {
        // Generate mock transaction ID
        final transactionId = 'TXN_${DateTime.now().millisecondsSinceEpoch}';

        // Create enrollment and payment records (CLEAN ARCHITECTURE)
        await _createEnrollmentAndPayment(orderSummary, transactionId);

        return PaymentResult.success(transactionId);
      } else {
        return PaymentResult.failure(
            'Ödeme işlemi başarısız oldu. Lütfen tekrar deneyin.');
      }
    } catch (e) {
      return PaymentResult.failure(
          'Ödeme işlemi sırasında hata oluştu: ${e.toString()}');
    }
  }

  // Create enrollment and payment records (CLEAN ARCHITECTURE)
  Future<void> _createEnrollmentAndPayment(
      OrderSummary orderSummary, String transactionId) async {
    final currentUser = _supabase.auth.currentUser;
    if (currentUser == null) {
      throw Exception('Kullanıcı girişi yapılmamış');
    }

    final durationMonths = _parseDurationToMonths(orderSummary.duration);
    final startDate = DateTime.now();
    final endDate = DateTime(
        startDate.year, startDate.month + durationMonths, startDate.day);

    // 1. Create or get enrollment record
    String enrollmentId = await _createOrGetEnrollment(
      currentUser.id,
      orderSummary.trainerId,
      orderSummary.packageId,
      startDate,
      endDate,
    );

    // 2. Create payment transaction record
    await _createPaymentTransaction(
      enrollmentId,
      orderSummary,
      transactionId,
      durationMonths,
    );
  }

  // Create or get existing enrollment (CLEAN METHOD)
  Future<String> _createOrGetEnrollment(
    String studentId,
    String instructorId,
    String packageId,
    DateTime startDate,
    DateTime endDate,
  ) async {
    // Check if enrollment already exists
    final existingEnrollment = await _supabase
        .from('enrollments')
        .select('id')
        .eq('student_id', studentId)
        .eq('instructor_id', instructorId)
        .maybeSingle();

    if (existingEnrollment != null) {
      // Update existing enrollment
      await _supabase.from('enrollments').update({
        'expires_at': endDate.toIso8601String(),
        'is_active': true,
        'updated_at': DateTime.now().toIso8601String(),
      }).eq('id', existingEnrollment['id']);

      return existingEnrollment['id'] as String;
    } else {
      // Create new enrollment
      final enrollmentData = {
        'student_id': studentId,
        'instructor_id': instructorId,
        'plan_type': _mapPackageIdToPlanType(packageId),
        'enrolled_at': startDate.toIso8601String(),
        'expires_at': endDate.toIso8601String(),
        'is_active': true,
        // Note: progress_percentage and profile_completed fields removed as they don't exist in current schema
        'created_at': DateTime.now().toIso8601String(),
        'updated_at': DateTime.now().toIso8601String(),
      };

      final response = await _supabase
          .from('enrollments')
          .insert(enrollmentData)
          .select('id')
          .single();

      return response['id'] as String;
    }
  }

  // Create payment transaction (CLEAN METHOD)
  Future<void> _createPaymentTransaction(
    String enrollmentId,
    OrderSummary orderSummary,
    String transactionId,
    int durationMonths,
  ) async {
    final paymentData = {
      'relationship_id': enrollmentId, // References enrollments table
      'amount': orderSummary.totalPrice,
      'currency': 'TRY',
      'payment_method': 'Kredi Kartı',
      'payment_status': 'completed',
      'payment_date': DateTime.now().toIso8601String(),
      'transaction_id': transactionId,
      'invoice_number': 'INV-${DateTime.now().millisecondsSinceEpoch}',
      'program_type': orderSummary.duration,
      'duration_months': durationMonths,
      'sessions_included':
          _calculateSessionsIncluded(orderSummary.packageId, durationMonths),
      'created_at': DateTime.now().toIso8601String(),
      'updated_at': DateTime.now().toIso8601String(),
    };

    await _supabase.from('payment_transactions').insert(paymentData);
  }

  // Helper method to map package ID to plan type (NEW METHOD)
  String _mapPackageIdToPlanType(String packageId) {
    // Map package IDs to plan types (remove VIP)
    switch (packageId.toLowerCase()) {
      case 'premium':
      case 'premium_plan':
        return 'premium';
      case 'basic':
      case 'basic_plan':
      case 'standart':
      default:
        return 'basic'; // Default to basic
    }
  }

  // Helper method to parse duration string to months
  int _parseDurationToMonths(String duration) {
    if (duration.contains('1 Ay') || duration.contains('1 Month')) return 1;
    if (duration.contains('3 Ay') || duration.contains('3 Month')) return 3;
    if (duration.contains('6 Ay') || duration.contains('6 Month')) return 6;
    if (duration.contains('12 Ay') || duration.contains('12 Month')) return 12;
    return 1; // Default to 1 month
  }

  // Helper method to calculate sessions included
  int _calculateSessionsIncluded(String packageId, int durationMonths) {
    // Basic calculation - can be made more sophisticated
    if (packageId.contains('premium')) {
      return durationMonths * 12; // 3 sessions per week
    } else {
      return durationMonths * 8; // 2 sessions per week
    }
  }

  // Helper method to generate UUID for plan_id (VIP removed)
  String _generatePlanUuid(String packageId) {
    // Generate consistent UUID based on package type
    final Map<String, String> planUuids = {
      'standart': '550e8400-e29b-41d4-a716-************',
      'premium': '550e8400-e29b-41d4-a716-************',
      // VIP removed - not supported
    };

    return planUuids[packageId] ?? '550e8400-e29b-41d4-a716-************';
  }

  // Validate card number format (mock validation)
  bool validateCardNumber(String cardNumber) {
    final cleanNumber = cardNumber.replaceAll(' ', '');
    return cleanNumber.length >= 16 && cleanNumber.length <= 19;
  }

  // Validate expiry date format (mock validation)
  bool validateExpiryDate(String expiryDate) {
    final regex = RegExp(r'^(0[1-9]|1[0-2])\/([0-9]{2})$');
    if (!regex.hasMatch(expiryDate)) return false;

    final parts = expiryDate.split('/');
    final month = int.parse(parts[0]);
    final year = int.parse('20${parts[1]}');
    final now = DateTime.now();
    final expiry = DateTime(year, month + 1, 0); // Last day of expiry month

    return expiry.isAfter(now);
  }

  // Validate CVV format (mock validation)
  bool validateCVV(String cvv) {
    return cvv.length >= 3 && cvv.length <= 4 && int.tryParse(cvv) != null;
  }

  // Get payment history for a student (CLEAN METHOD)
  Future<List<Map<String, dynamic>>> getPaymentHistory(String studentId) async {
    try {
      final response = await _supabase
          .from('payment_transactions')
          .select('''
            id,
            amount,
            currency,
            payment_method,
            payment_status,
            payment_date,
            transaction_id,
            invoice_number,
            program_type,
            duration_months,
            sessions_included,
            created_at,
            enrollments!inner(
              student_id,
              instructor_id,
              plan_type
            )
          ''')
          .eq('enrollments.student_id', studentId)
          .order('payment_date', ascending: false);

      return List<Map<String, dynamic>>.from(response);
    } catch (e) {
      throw Exception('Failed to get payment history: $e');
    }
  }

  // Get total spent by student (CLEAN METHOD)
  Future<double> getTotalSpentByStudent(String studentId) async {
    try {
      final response = await _supabase
          .from('payment_transactions')
          .select('amount, enrollments!inner(student_id)')
          .eq('enrollments.student_id', studentId)
          .eq('payment_status', 'completed');

      double total = 0.0;
      for (final payment in response) {
        total += double.parse(payment['amount'].toString());
      }
      return total;
    } catch (e) {
      throw Exception('Failed to calculate total spent: $e');
    }
  }

  // Format card number for display
  String formatCardNumber(String cardNumber) {
    final cleanNumber = cardNumber.replaceAll(' ', '');
    final buffer = StringBuffer();

    for (int i = 0; i < cleanNumber.length; i++) {
      if (i > 0 && i % 4 == 0) {
        buffer.write(' ');
      }
      buffer.write(cleanNumber[i]);
    }

    return buffer.toString();
  }

  // Format expiry date for display
  String formatExpiryDate(String expiryDate) {
    final cleanDate = expiryDate.replaceAll('/', '');
    if (cleanDate.length >= 2) {
      final month = cleanDate.substring(0, 2);
      final year = cleanDate.length > 2 ? cleanDate.substring(2) : '';
      return year.isEmpty ? month : '$month/$year';
    }
    return cleanDate;
  }

  // Get card type from number (for display purposes)
  String getCardType(String cardNumber) {
    final cleanNumber = cardNumber.replaceAll(' ', '');

    if (cleanNumber.startsWith('4')) {
      return 'Visa';
    } else if (cleanNumber.startsWith('5') || cleanNumber.startsWith('2')) {
      return 'Mastercard';
    } else if (cleanNumber.startsWith('3')) {
      return 'American Express';
    } else {
      return 'Kart';
    }
  }

  // Create order summary from enrollment data
  OrderSummary createOrderSummary({
    required String trainerId,
    required String trainerName,
    required String packageId,
    required String packageName,
    required String duration,
    required double basePrice,
    double? discountAmount,
    double? promoDiscount,
  }) {
    final totalPrice =
        (basePrice - (discountAmount ?? 0) - (promoDiscount ?? 0))
            .clamp(0.0, double.infinity);

    return OrderSummary(
      trainerId: trainerId,
      trainerName: trainerName,
      packageId: packageId,
      packageName: packageName,
      duration: duration,
      basePrice: basePrice,
      discountAmount: discountAmount,
      promoDiscount: promoDiscount,
      totalPrice: totalPrice,
    );
  }
}
