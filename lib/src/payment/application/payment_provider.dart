import 'package:hooks_riverpod/hooks_riverpod.dart';
import 'package:fitgo_app/src/payment/domain/payment_models.dart';
import 'package:fitgo_app/src/payment/application/payment_repository.dart';

// Repository provider following established patterns
final paymentRepositoryProvider = Provider<PaymentRepository>((ref) {
  return PaymentRepository();
});

// Main payment state notifier
class PaymentNotifier extends StateNotifier<PaymentState> {
  final PaymentRepository _repository;

  PaymentNotifier({
    required PaymentRepository repository,
  }) : _repository = repository, super(const PaymentState());

  // Initialize payment with order summary
  void initializePayment({
    required String trainerId,
    required String trainerName,
    required String packageId,
    required String packageName,
    required String duration,
    required double basePrice,
    double? discountAmount,
    double? promoDiscount,
    bool isPremium = false,
  }) {
    final orderSummary = _repository.createOrderSummary(
      trainerId: trainerId,
      trainerName: trainerName,
      packageId: packageId,
      packageName: packageName,
      duration: duration,
      basePrice: basePrice,
      discountAmount: discountAmount,
      promoDiscount: promoDiscount,
    );

    state = state.copyWith(
      orderSummary: orderSummary,
      theme: isPremium ? PaymentTheme.premium : PaymentTheme.standard,
      clearError: true,
    );
  }

  // Update payment card fields
  void updateCardName(String name) {
    state = state.copyWith(
      paymentCard: state.paymentCard.copyWith(nameOnCard: name),
      clearError: true,
    );
  }

  void updateCardNumber(String number) {
    final formattedNumber = _repository.formatCardNumber(number);
    state = state.copyWith(
      paymentCard: state.paymentCard.copyWith(cardNumber: formattedNumber),
      clearError: true,
    );
  }

  void updateExpiryDate(String expiry) {
    final formattedExpiry = _repository.formatExpiryDate(expiry);
    state = state.copyWith(
      paymentCard: state.paymentCard.copyWith(expiryDate: formattedExpiry),
      clearError: true,
    );
  }

  void updateCVV(String cvv) {
    state = state.copyWith(
      paymentCard: state.paymentCard.copyWith(cvv: cvv),
      clearError: true,
    );
  }

  // Validate payment form (DISABLED FOR DEVELOPMENT)
  String? validateForm() {
    if (state.orderSummary == null) {
      return 'Sipariş bilgileri bulunamadı';
    }

    // DEVELOPMENT MODE: Skip all form validations
    // TODO: Re-enable validations when integrating real payment
    /*
    if (state.paymentCard.nameOnCard.isEmpty) {
      return 'Kart üzerindeki isim gerekli';
    }

    if (!_repository.validateCardNumber(state.paymentCard.cardNumber)) {
      return 'Geçersiz kart numarası';
    }

    if (!_repository.validateExpiryDate(state.paymentCard.expiryDate)) {
      return 'Geçersiz son kullanma tarihi';
    }

    if (!_repository.validateCVV(state.paymentCard.cvv)) {
      return 'Geçersiz CVV';
    }
    */

    return null; // Always valid in development mode
  }

  // Process mock payment
  Future<PaymentResult> processPayment() async {
    if (state.isProcessing || state.orderSummary == null) {
      return PaymentResult.failure('İşlem zaten devam ediyor');
    }

    // DEVELOPMENT MODE: Skip validation
    // TODO: Re-enable for production
    // final validationError = validateForm();
    // if (validationError != null) {
    //   state = state.copyWith(error: validationError);
    //   return PaymentResult.failure(validationError);
    // }

    state = state.copyWith(
      isProcessing: true,
      clearError: true,
    );

    try {
      final result = await _repository.processPayment(
        orderSummary: state.orderSummary!,
        paymentCard: state.paymentCard,
      );

      if (result.isSuccess) {
        state = state.copyWith(
          isProcessing: false,
          isCompleted: true,
        );
      } else {
        state = state.copyWith(
          isProcessing: false,
          error: result.errorMessage ?? 'Ödeme işlemi başarısız',
        );
      }

      return result;
    } catch (e) {
      state = state.copyWith(
        isProcessing: false,
        error: 'Beklenmeyen bir hata oluştu: ${e.toString()}',
      );
      return PaymentResult.failure('Beklenmeyen bir hata oluştu');
    }
  }

  // Clear error
  void clearError() {
    state = state.copyWith(clearError: true);
  }

  // Reset payment state
  void reset() {
    state = const PaymentState();
  }

  // Get card type for display
  String getCardType() {
    return _repository.getCardType(state.paymentCard.cardNumber);
  }
}

// Main payment provider
final paymentProvider = StateNotifierProvider<PaymentNotifier, PaymentState>(
  (ref) {
    final repository = ref.read(paymentRepositoryProvider);
    return PaymentNotifier(repository: repository);
  },
);

// Convenience providers for specific state parts
final orderSummaryProvider = Provider<OrderSummary?>((ref) {
  return ref.watch(paymentProvider).orderSummary;
});

final paymentCardProvider = Provider<MockPaymentCard>((ref) {
  return ref.watch(paymentProvider).paymentCard;
});

final paymentThemeProvider = Provider<PaymentTheme>((ref) {
  return ref.watch(paymentProvider).theme;
});

final canProceedPaymentProvider = Provider<bool>((ref) {
  return ref.watch(paymentProvider).canProceed;
});

final isProcessingPaymentProvider = Provider<bool>((ref) {
  return ref.watch(paymentProvider).isProcessing;
});
