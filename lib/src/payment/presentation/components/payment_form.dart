import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:fitgo_app/src/shared/widgets/text_widget/text_widget.dart';
import 'package:fitgo_app/src/shared/constants/app_text_style.dart';
import 'package:fitgo_app/src/theme/colors.dart';
import 'package:fitgo_app/src/payment/domain/payment_models.dart';

class PaymentForm extends StatelessWidget {
  final MockPaymentCard paymentCard;
  final PaymentTheme theme;
  final ValueChanged<String> onCardNameChanged;
  final ValueChanged<String> onCardNumberChanged;
  final ValueChanged<String> onExpiryDateChanged;
  final ValueChanged<String> onCVVChanged;

  const PaymentForm({
    super.key,
    required this.paymentCard,
    required this.theme,
    required this.onCardNameChanged,
    required this.onCardNumberChanged,
    required this.onExpiryDateChanged,
    required this.onCVVChanged,
  });

  @override
  Widget build(BuildContext context) {
    return Container(
      padding: const EdgeInsets.all(20),
      decoration: BoxDecoration(
        color: _getBackgroundColor(),
        borderRadius: BorderRadius.circular(16),
        border: Border.all(
          color: _getBorderColor(),
          width: 1,
        ),
        boxShadow: _getBoxShadow(),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Icon(
                Icons.credit_card,
                color: _getAccentColor(),
                size: 24,
              ),
              const SizedBox(width: 8),
              TextWidget(
                'Kart Bilgileri (Demo)',
                style: ATextStyle.large.copyWith(
                  color: Colors.white,
                  fontWeight: FontWeight.w600,
                ),
              ),
            ],
          ),
          const SizedBox(height: 20),
          
          // Card name field
          _buildTextField(
            label: 'Kart Üzerindeki İsim',
            value: paymentCard.nameOnCard,
            onChanged: onCardNameChanged,
            hintText: 'Ahmet Yılmaz',
            keyboardType: TextInputType.name,
            textCapitalization: TextCapitalization.words,
          ),
          const SizedBox(height: 16),
          
          // Card number field
          _buildTextField(
            label: 'Kart Numarası',
            value: paymentCard.cardNumber,
            onChanged: onCardNumberChanged,
            hintText: '1234 5678 9012 3456',
            keyboardType: TextInputType.number,
            inputFormatters: [
              FilteringTextInputFormatter.digitsOnly,
              LengthLimitingTextInputFormatter(19),
              _CardNumberInputFormatter(),
            ],
            suffixIcon: _getCardTypeIcon(),
          ),
          const SizedBox(height: 16),
          
          // Expiry and CVV row
          Row(
            children: [
              Expanded(
                child: _buildTextField(
                  label: 'Son Kullanma Tarihi',
                  value: paymentCard.expiryDate,
                  onChanged: onExpiryDateChanged,
                  hintText: 'MM/YY',
                  keyboardType: TextInputType.number,
                  inputFormatters: [
                    FilteringTextInputFormatter.digitsOnly,
                    LengthLimitingTextInputFormatter(4),
                    _ExpiryDateInputFormatter(),
                  ],
                ),
              ),
              const SizedBox(width: 16),
              Expanded(
                child: _buildTextField(
                  label: 'CVV',
                  value: paymentCard.cvv,
                  onChanged: onCVVChanged,
                  hintText: '123',
                  keyboardType: TextInputType.number,
                  inputFormatters: [
                    FilteringTextInputFormatter.digitsOnly,
                    LengthLimitingTextInputFormatter(4),
                  ],
                  obscureText: true,
                ),
              ),
            ],
          ),
          
          const SizedBox(height: 16),
          
          // Development mode notice
          Container(
            padding: const EdgeInsets.all(12),
            decoration: BoxDecoration(
              color: Colors.green.withOpacity(0.1),
              borderRadius: BorderRadius.circular(8),
              border: Border.all(
                color: Colors.green.withOpacity(0.3),
                width: 1,
              ),
            ),
            child: Row(
              children: [
                Icon(
                  Icons.developer_mode,
                  color: Colors.green,
                  size: 16,
                ),
                const SizedBox(width: 8),
                Expanded(
                  child: TextWidget(
                    'GELİŞTİRME MODU: Form alanları opsiyoneldir. Direkt "Kaydı Tamamla" butonuna basabilirsiniz.',
                    style: ATextStyle.small.copyWith(
                      color: Colors.green,
                      fontWeight: FontWeight.w600,
                    ),
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildTextField({
    required String label,
    required String value,
    required ValueChanged<String> onChanged,
    required String hintText,
    TextInputType? keyboardType,
    List<TextInputFormatter>? inputFormatters,
    TextCapitalization? textCapitalization,
    bool obscureText = false,
    Widget? suffixIcon,
  }) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        TextWidget(
          label,
          style: ATextStyle.medium.copyWith(
            color: Colors.white70,
            fontWeight: FontWeight.w500,
          ),
        ),
        const SizedBox(height: 8),
        TextField(
          onChanged: onChanged,
          keyboardType: keyboardType,
          inputFormatters: inputFormatters,
          textCapitalization: textCapitalization ?? TextCapitalization.none,
          obscureText: obscureText,
          style: ATextStyle.medium.copyWith(color: Colors.white),
          decoration: InputDecoration(
            hintText: hintText,
            hintStyle: ATextStyle.medium.copyWith(color: Colors.white54),
            filled: true,
            fillColor: const Color(0xFF1E293B),
            border: OutlineInputBorder(
              borderRadius: BorderRadius.circular(12),
              borderSide: BorderSide(color: _getBorderColor()),
            ),
            enabledBorder: OutlineInputBorder(
              borderRadius: BorderRadius.circular(12),
              borderSide: BorderSide(color: _getBorderColor()),
            ),
            focusedBorder: OutlineInputBorder(
              borderRadius: BorderRadius.circular(12),
              borderSide: BorderSide(color: _getAccentColor(), width: 2),
            ),
            contentPadding: const EdgeInsets.all(16),
            suffixIcon: suffixIcon,
          ),
        ),
      ],
    );
  }

  Widget? _getCardTypeIcon() {
    if (paymentCard.cardNumber.isEmpty) return null;
    
    final cleanNumber = paymentCard.cardNumber.replaceAll(' ', '');
    IconData iconData;
    Color iconColor = Colors.white70;
    
    if (cleanNumber.startsWith('4')) {
      iconData = Icons.credit_card;
      iconColor = Colors.blue;
    } else if (cleanNumber.startsWith('5') || cleanNumber.startsWith('2')) {
      iconData = Icons.credit_card;
      iconColor = Colors.red;
    } else if (cleanNumber.startsWith('3')) {
      iconData = Icons.credit_card;
      iconColor = Colors.green;
    } else {
      iconData = Icons.credit_card;
    }
    
    return Icon(iconData, color: iconColor, size: 24);
  }

  Color _getBackgroundColor() {
    switch (theme) {
      case PaymentTheme.standard:
        return const Color(0xFF1E293B);
      case PaymentTheme.premium:
        return const Color(0xFF2D1B69).withOpacity(0.3);
    }
  }

  Color _getBorderColor() {
    switch (theme) {
      case PaymentTheme.standard:
        return const Color(0xFF334155);
      case PaymentTheme.premium:
        return const Color(0xFFFFD700).withOpacity(0.3);
    }
  }

  Color _getAccentColor() {
    switch (theme) {
      case PaymentTheme.standard:
        return AColor.fitgoGreen;
      case PaymentTheme.premium:
        return const Color(0xFFFFD700); // Gold
    }
  }

  List<BoxShadow> _getBoxShadow() {
    if (theme == PaymentTheme.premium) {
      return [
        BoxShadow(
          color: const Color(0xFFFFD700).withOpacity(0.1),
          blurRadius: 8,
          offset: const Offset(0, 2),
        ),
      ];
    }
    return [];
  }
}

// Input formatter for card number (adds spaces every 4 digits)
class _CardNumberInputFormatter extends TextInputFormatter {
  @override
  TextEditingValue formatEditUpdate(
    TextEditingValue oldValue,
    TextEditingValue newValue,
  ) {
    final text = newValue.text.replaceAll(' ', '');
    final buffer = StringBuffer();
    
    for (int i = 0; i < text.length; i++) {
      if (i > 0 && i % 4 == 0) {
        buffer.write(' ');
      }
      buffer.write(text[i]);
    }
    
    final formatted = buffer.toString();
    return TextEditingValue(
      text: formatted,
      selection: TextSelection.collapsed(offset: formatted.length),
    );
  }
}

// Input formatter for expiry date (adds slash after 2 digits)
class _ExpiryDateInputFormatter extends TextInputFormatter {
  @override
  TextEditingValue formatEditUpdate(
    TextEditingValue oldValue,
    TextEditingValue newValue,
  ) {
    final text = newValue.text.replaceAll('/', '');
    final buffer = StringBuffer();
    
    for (int i = 0; i < text.length; i++) {
      if (i == 2) {
        buffer.write('/');
      }
      buffer.write(text[i]);
    }
    
    final formatted = buffer.toString();
    return TextEditingValue(
      text: formatted,
      selection: TextSelection.collapsed(offset: formatted.length),
    );
  }
}
