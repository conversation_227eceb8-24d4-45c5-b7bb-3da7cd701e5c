import 'package:flutter/material.dart';
import 'package:fitgo_app/src/shared/widgets/text_widget/text_widget.dart';
import 'package:fitgo_app/src/shared/constants/app_text_style.dart';
import 'package:fitgo_app/src/theme/colors.dart';
import 'package:fitgo_app/src/payment/domain/payment_models.dart';

class OrderSummaryCard extends StatelessWidget {
  final OrderSummary orderSummary;
  final PaymentTheme theme;

  const OrderSummaryCard({
    super.key,
    required this.orderSummary,
    required this.theme,
  });

  @override
  Widget build(BuildContext context) {
    return Container(
      padding: const EdgeInsets.all(20),
      decoration: BoxDecoration(
        color: _getBackgroundColor(),
        borderRadius: BorderRadius.circular(16),
        border: Border.all(
          color: _getBorderColor(),
          width: 1,
        ),
        boxShadow: _getBoxShadow(),
      ),
      child: <PERSON>umn(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Icon(
                Icons.receipt_long,
                color: _getAccentColor(),
                size: 24,
              ),
              const SizedBox(width: 8),
              TextWidget(
                'Sipariş Özeti',
                style: ATextStyle.large.copyWith(
                  color: Colors.white,
                  fontWeight: FontWeight.w600,
                ),
              ),
            ],
          ),
          const SizedBox(height: 16),
          
          // Trainer info
          _buildInfoRow(
            'Eğitmen',
            orderSummary.trainerName,
            icon: Icons.person,
          ),
          const SizedBox(height: 12),
          
          // Package info
          _buildInfoRow(
            'Paket',
            orderSummary.packageName,
            icon: theme == PaymentTheme.premium 
                ? Icons.workspace_premium 
                : Icons.fitness_center,
          ),
          const SizedBox(height: 12),
          
          // Duration info
          _buildInfoRow(
            'Süre',
            orderSummary.duration,
            icon: Icons.schedule,
          ),
          
          const SizedBox(height: 16),
          Divider(color: Colors.white24),
          const SizedBox(height: 16),
          
          // Pricing breakdown
          _buildPriceRow(
            'Paket Fiyatı',
            orderSummary.basePrice,
            isSubtotal: true,
          ),
          
          if (orderSummary.discountAmount != null && orderSummary.discountAmount! > 0) ...[
            const SizedBox(height: 8),
            _buildPriceRow(
              'Paket İndirimi',
              -orderSummary.discountAmount!,
              isDiscount: true,
            ),
          ],
          
          if (orderSummary.promoDiscount != null && orderSummary.promoDiscount! > 0) ...[
            const SizedBox(height: 8),
            _buildPriceRow(
              'Promosyon İndirimi',
              -orderSummary.promoDiscount!,
              isDiscount: true,
            ),
          ],
          
          const SizedBox(height: 12),
          Divider(color: Colors.white24),
          const SizedBox(height: 12),
          
          // Total
          _buildPriceRow(
            'Toplam',
            orderSummary.totalPrice,
            isTotal: true,
          ),
        ],
      ),
    );
  }

  Widget _buildInfoRow(String label, String value, {IconData? icon}) {
    return Row(
      children: [
        if (icon != null) ...[
          Icon(
            icon,
            color: _getAccentColor(),
            size: 20,
          ),
          const SizedBox(width: 8),
        ],
        Expanded(
          child: TextWidget(
            label,
            style: ATextStyle.medium.copyWith(
              color: Colors.white70,
            ),
          ),
        ),
        TextWidget(
          value,
          style: ATextStyle.medium.copyWith(
            color: Colors.white,
            fontWeight: FontWeight.w600,
          ),
        ),
      ],
    );
  }

  Widget _buildPriceRow(
    String label, 
    double amount, {
    bool isSubtotal = false,
    bool isDiscount = false,
    bool isTotal = false,
  }) {
    Color textColor = Colors.white70;
    FontWeight fontWeight = FontWeight.normal;
    double fontSize = 14;
    
    if (isDiscount) {
      textColor = Colors.green;
      fontWeight = FontWeight.w600;
    } else if (isTotal) {
      textColor = _getAccentColor();
      fontWeight = FontWeight.bold;
      fontSize = 18;
    } else if (isSubtotal) {
      textColor = Colors.white;
      fontWeight = FontWeight.w500;
    }

    return Row(
      children: [
        Expanded(
          child: TextWidget(
            label,
            style: ATextStyle.medium.copyWith(
              color: isTotal ? Colors.white : Colors.white70,
              fontWeight: isTotal ? FontWeight.bold : FontWeight.normal,
              fontSize: fontSize,
            ),
          ),
        ),
        TextWidget(
          '${amount.toStringAsFixed(0)}${orderSummary.currency}',
          style: ATextStyle.medium.copyWith(
            color: textColor,
            fontWeight: fontWeight,
            fontSize: fontSize,
          ),
        ),
      ],
    );
  }

  Color _getBackgroundColor() {
    switch (theme) {
      case PaymentTheme.standard:
        return const Color(0xFF1E293B);
      case PaymentTheme.premium:
        return const Color(0xFF2D1B69).withOpacity(0.3);
    }
  }

  Color _getBorderColor() {
    switch (theme) {
      case PaymentTheme.standard:
        return const Color(0xFF334155);
      case PaymentTheme.premium:
        return const Color(0xFFFFD700).withOpacity(0.3);
    }
  }

  Color _getAccentColor() {
    switch (theme) {
      case PaymentTheme.standard:
        return AColor.fitgoGreen;
      case PaymentTheme.premium:
        return const Color(0xFFFFD700); // Gold
    }
  }

  List<BoxShadow> _getBoxShadow() {
    if (theme == PaymentTheme.premium) {
      return [
        BoxShadow(
          color: const Color(0xFFFFD700).withOpacity(0.1),
          blurRadius: 8,
          offset: const Offset(0, 2),
        ),
      ];
    }
    return [];
  }
}
