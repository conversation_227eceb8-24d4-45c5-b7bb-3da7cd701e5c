import 'package:flutter/material.dart';
import 'package:fitgo_app/src/shared/widgets/text_widget/text_widget.dart';
import 'package:fitgo_app/src/shared/constants/app_text_style.dart';
import 'package:fitgo_app/src/theme/colors.dart';
import 'package:fitgo_app/src/payment/domain/payment_models.dart';

class PaymentButton extends StatelessWidget {
  final bool canProceed;
  final bool isProcessing;
  final PaymentTheme theme;
  final VoidCallback onPressed;

  const PaymentButton({
    super.key,
    required this.canProceed,
    required this.isProcessing,
    required this.theme,
    required this.onPressed,
  });

  @override
  Widget build(BuildContext context) {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: _getBackgroundColor(),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withOpacity(0.1),
            blurRadius: 8,
            offset: const Offset(0, -2),
          ),
        ],
      ),
      child: <PERSON><PERSON><PERSON>(
        child: AnimatedContainer(
          duration: const Duration(milliseconds: 300),
          curve: Curves.easeInOut,
          child: ElevatedButton(
            onPressed: (canProceed && !isProcessing) ? onPressed : null,
            style: ElevatedButton.styleFrom(
              backgroundColor: _getButtonColor(),
              foregroundColor: _getTextColor(),
              disabledBackgroundColor: Colors.grey[600],
              disabledForegroundColor: Colors.grey[400],
              padding: const EdgeInsets.symmetric(vertical: 18),
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(16),
              ),
              elevation: (canProceed && !isProcessing) ? 4 : 0,
              shadowColor: _getButtonColor().withOpacity(0.3),
            ),
            child: Container(
              width: double.infinity,
              child: Stack(
                alignment: Alignment.center,
                children: [
                  // Gradient overlay for premium
                  if (theme == PaymentTheme.premium && canProceed && !isProcessing)
                    Container(
                      decoration: BoxDecoration(
                        borderRadius: BorderRadius.circular(16),
                        gradient: LinearGradient(
                          colors: [
                            const Color(0xFFFFD700).withOpacity(0.8),
                            const Color(0xFFFFA500).withOpacity(0.8),
                          ],
                          begin: Alignment.topLeft,
                          end: Alignment.bottomRight,
                        ),
                      ),
                    ),
                  
                  // Button content
                  Row(
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: [
                      if (isProcessing) ...[
                        SizedBox(
                          height: 20,
                          width: 20,
                          child: CircularProgressIndicator(
                            strokeWidth: 2,
                            valueColor: AlwaysStoppedAnimation<Color>(_getTextColor()),
                          ),
                        ),
                        const SizedBox(width: 12),
                        TextWidget(
                          'İşleniyor...',
                          style: ATextStyle.large.copyWith(
                            color: _getTextColor(),
                            fontWeight: FontWeight.bold,
                            fontSize: 18,
                          ),
                        ),
                      ] else ...[
                        Icon(
                          Icons.lock,
                          color: _getTextColor(),
                          size: 20,
                        ),
                        const SizedBox(width: 8),
                        
                        TextWidget(
                          'Kaydı Tamamla',
                          style: ATextStyle.large.copyWith(
                            color: _getTextColor(),
                            fontWeight: FontWeight.bold,
                            fontSize: 18,
                          ),
                        ),
                        
                        const SizedBox(width: 8),
                        Icon(
                          Icons.arrow_forward,
                          color: _getTextColor(),
                          size: 20,
                        ),
                      ],
                    ],
                  ),
                ],
              ),
            ),
          ),
        ),
      ),
    );
  }

  Color _getBackgroundColor() {
    switch (theme) {
      case PaymentTheme.standard:
        return const Color(0xFF0F172A);
      case PaymentTheme.premium:
        return const Color(0xFF1A1A2E);
    }
  }

  Color _getButtonColor() {
    if (!canProceed || isProcessing) return Colors.grey[600]!;
    
    switch (theme) {
      case PaymentTheme.standard:
        return AColor.fitgoGreen;
      case PaymentTheme.premium:
        return const Color(0xFFFFD700); // Gold
    }
  }

  Color _getTextColor() {
    if (!canProceed || isProcessing) return Colors.grey[400]!;
    
    switch (theme) {
      case PaymentTheme.standard:
        return Colors.white;
      case PaymentTheme.premium:
        return Colors.black; // Black text on gold background
    }
  }
}
