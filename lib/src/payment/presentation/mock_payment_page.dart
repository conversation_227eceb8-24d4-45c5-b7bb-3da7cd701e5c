import 'package:flutter/material.dart';
import 'package:flutter_hooks/flutter_hooks.dart';
import 'package:hooks_riverpod/hooks_riverpod.dart';
import 'package:fitgo_app/src/shared/widgets/text_widget/text_widget.dart';
import 'package:fitgo_app/src/shared/constants/app_text_style.dart';
import 'package:fitgo_app/src/shared/extensions/string_hardcoded.dart';
import 'package:fitgo_app/src/theme/colors.dart';
import 'package:fitgo_app/src/payment/domain/payment_models.dart';
import 'package:fitgo_app/src/payment/application/payment_provider.dart';
import 'package:fitgo_app/src/payment/presentation/components/order_summary_card.dart';
import 'package:fitgo_app/src/payment/presentation/components/payment_form.dart';
import 'package:fitgo_app/src/payment/presentation/components/payment_button.dart';
import 'package:fitgo_app/src/payment/presentation/payment_success_page.dart';

class MockPaymentPage extends HookConsumerWidget {
  final String trainerId;
  final String trainerName;
  final String packageId;
  final String packageName;
  final String duration;
  final double basePrice;
  final double? discountAmount;
  final double? promoDiscount;
  final bool isPremium;

  const MockPaymentPage({
    super.key,
    required this.trainerId,
    required this.trainerName,
    required this.packageId,
    required this.packageName,
    required this.duration,
    required this.basePrice,
    this.discountAmount,
    this.promoDiscount,
    this.isPremium = false,
  });

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final paymentState = ref.watch(paymentProvider);
    final theme = ref.watch(paymentThemeProvider);

    // Initialize payment when screen loads
    useEffect(() {
      WidgetsBinding.instance.addPostFrameCallback((_) {
        ref.read(paymentProvider.notifier).initializePayment(
          trainerId: trainerId,
          trainerName: trainerName,
          packageId: packageId,
          packageName: packageName,
          duration: duration,
          basePrice: basePrice,
          discountAmount: discountAmount,
          promoDiscount: promoDiscount,
          isPremium: isPremium,
        );
      });
      return null;
    }, []);

    return Scaffold(
      backgroundColor: _getBackgroundColor(theme),
      appBar: AppBar(
        backgroundColor: _getBackgroundColor(theme),
        elevation: 0,
        leading: IconButton(
          icon: const Icon(
            Icons.arrow_back_ios,
            color: Colors.white,
          ),
          onPressed: () => Navigator.of(context).pop(),
        ),
        title: Row(
          children: [
            Icon(
              Icons.credit_card,
              color: _getAccentColor(theme),
              size: 24,
            ),
            const SizedBox(width: 8),
            TextWidget(
              'Ödeme Sayfası (Demo)'.hardcoded,
              style: ATextStyle.large.copyWith(
                color: Colors.white,
                fontWeight: FontWeight.bold,
              ),
            ),
          ],
        ),
        centerTitle: false,
      ),
      body: Column(
        children: [
          Expanded(
            child: SingleChildScrollView(
              padding: const EdgeInsets.all(16),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  // Demo disclaimer
                  _buildDemoDisclaimer(theme),
                  const SizedBox(height: 24),
                  
                  // Order summary
                  if (paymentState.orderSummary != null)
                    OrderSummaryCard(
                      orderSummary: paymentState.orderSummary!,
                      theme: theme,
                    ),
                  const SizedBox(height: 24),
                  
                  // Payment form
                  PaymentForm(
                    paymentCard: paymentState.paymentCard,
                    theme: theme,
                    onCardNameChanged: (name) {
                      ref.read(paymentProvider.notifier).updateCardName(name);
                    },
                    onCardNumberChanged: (number) {
                      ref.read(paymentProvider.notifier).updateCardNumber(number);
                    },
                    onExpiryDateChanged: (expiry) {
                      ref.read(paymentProvider.notifier).updateExpiryDate(expiry);
                    },
                    onCVVChanged: (cvv) {
                      ref.read(paymentProvider.notifier).updateCVV(cvv);
                    },
                  ),
                  
                  // Error message
                  if (paymentState.error != null) ...[
                    const SizedBox(height: 16),
                    _buildErrorMessage(paymentState.error!),
                  ],
                  
                  const SizedBox(height: 100), // Space for sticky button
                ],
              ),
            ),
          ),
          
          // Sticky payment button
          PaymentButton(
            canProceed: paymentState.canProceed,
            isProcessing: paymentState.isProcessing,
            theme: theme,
            onPressed: () async {
              final result = await ref.read(paymentProvider.notifier).processPayment();
              
              if (result.isSuccess && context.mounted) {
                Navigator.of(context).pushReplacement(
                  MaterialPageRoute(
                    builder: (context) => PaymentSuccessPage(
                      orderSummary: paymentState.orderSummary!,
                      transactionId: result.transactionId!,
                      theme: theme,
                    ),
                  ),
                );
              }
            },
          ),
        ],
      ),
    );
  }

  Widget _buildDemoDisclaimer(PaymentTheme theme) {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Colors.orange.withOpacity(0.1),
        borderRadius: BorderRadius.circular(12),
        border: Border.all(
          color: Colors.orange.withOpacity(0.3),
          width: 1,
        ),
      ),
      child: Row(
        children: [
          Icon(
            Icons.info_outline,
            color: Colors.orange,
            size: 24,
          ),
          const SizedBox(width: 12),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                TextWidget(
                  'Demo Uyarısı'.hardcoded,
                  style: ATextStyle.medium.copyWith(
                    color: Colors.orange,
                    fontWeight: FontWeight.w600,
                  ),
                ),
                const SizedBox(height: 4),
                TextWidget(
                  'Bu ekran test amaçlıdır. Gerçek ödeme alınmamaktadır.'.hardcoded,
                  style: ATextStyle.small.copyWith(
                    color: Colors.white70,
                    fontStyle: FontStyle.italic,
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildErrorMessage(String error) {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Colors.red.withOpacity(0.1),
        borderRadius: BorderRadius.circular(12),
        border: Border.all(
          color: Colors.red.withOpacity(0.3),
          width: 1,
        ),
      ),
      child: Row(
        children: [
          Icon(
            Icons.error_outline,
            color: Colors.red,
            size: 24,
          ),
          const SizedBox(width: 12),
          Expanded(
            child: TextWidget(
              error,
              style: ATextStyle.small.copyWith(
                color: Colors.red,
              ),
            ),
          ),
        ],
      ),
    );
  }

  Color _getBackgroundColor(PaymentTheme theme) {
    switch (theme) {
      case PaymentTheme.standard:
        return const Color(0xFF0F172A);
      case PaymentTheme.premium:
        return const Color(0xFF1A1A2E);
    }
  }

  Color _getAccentColor(PaymentTheme theme) {
    switch (theme) {
      case PaymentTheme.standard:
        return AColor.fitgoGreen;
      case PaymentTheme.premium:
        return const Color(0xFFFFD700); // Gold
    }
  }
}
