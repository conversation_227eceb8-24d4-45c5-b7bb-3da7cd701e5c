import 'dart:io';
import 'package:supabase_flutter/supabase_flutter.dart';
import '../domain/feedback_models.dart';

class FeedbackRepository {
  final SupabaseClient _supabase;

  FeedbackRepository(this._supabase);

  // Get current user ID
  String? get currentUserId => _supabase.auth.currentUser?.id;

  // Check if user is authenticated
  bool get isAuthenticated => _supabase.auth.currentUser != null;

  // Upload feedback photo to storage
  Future<String> uploadFeedbackPhoto({
    required File imageFile,
    required FeedbackPhotoType photoType,
    required String feedbackId,
    required String userId,
    Function(double)? onProgress,
  }) async {
    try {
      // Create organized path: {userId}/feedback/{feedbackId}/{photoType}.jpg
      final fileName = '${photoType.name}.jpg';
      final filePath = '$userId/feedback/$feedbackId/$fileName';

      // Uploading feedback photo to path: $filePath

      // Upload to Supabase Storage with upsert option
      await _supabase.storage.from('feedback_photos').upload(
            filePath,
            imageFile,
            fileOptions: const FileOptions(
              cacheControl: '3600',
              upsert: true, // Allow overwriting existing files
            ),
          );

      // Get public URL
      final publicUrl =
          _supabase.storage.from('feedback_photos').getPublicUrl(filePath);

      // Feedback photo uploaded successfully. URL: $publicUrl
      return publicUrl;
    } on StorageException catch (e) {
      // Handle specific storage errors
      final statusCode = int.tryParse(e.statusCode ?? '0') ?? 0;
      if (statusCode == 404 || e.message.contains('Bucket not found')) {
        throw Exception('Fotoğraf yükleme servisi bulunamadı.');
      } else if (statusCode == 413 || e.message.contains('too large')) {
        throw Exception('Fotoğraf boyutu çok büyük (max 5MB).');
      } else {
        throw Exception('Fotoğraf yüklenemedi: ${e.message}');
      }
    } catch (e) {
      // Handle network and other errors
      if (e.toString().contains('SocketException') ||
          e.toString().contains('TimeoutException')) {
        throw Exception(
            'İnternet bağlantınızı kontrol edin ve tekrar deneyin.');
      }
      throw Exception('Fotoğraf yüklenirken bir hata oluştu: ${e.toString()}');
    }
  }

  // Create new feedback
  Future<String> createFeedback({
    required String instructorId,
    required String title,
    required String studentNotes,
    String? enrollmentId,
  }) async {
    try {
      if (!isAuthenticated) {
        throw Exception('Kullanıcı girişi yapılmamış');
      }

      final response = await _supabase
          .from('feedback')
          .insert({
            'student_id': currentUserId,
            'instructor_id': instructorId,
            'title': title,
            'description': studentNotes,
            'status': 'pending',
            'feedback_date': DateTime.now().toIso8601String(),
          })
          .select('id')
          .single();

      return response['id'] as String;
    } catch (e) {
      throw Exception('Geri bildirim oluşturulamadı: ${e.toString()}');
    }
  }

  // Add photos to feedback
  Future<void> addFeedbackPhotos({
    required String feedbackId,
    required List<FeedbackPhoto> photos,
  }) async {
    try {
      if (photos.isEmpty) return;

      // Extract photo URLs to save in the photos array field
      final photoUrls = photos.map((photo) => photo.photoUrl).toList();

      // Update the feedback record with the photos array
      await _supabase
          .from('feedback')
          .update({'photos': photoUrls}).eq('id', feedbackId);
    } catch (e) {
      throw Exception('Fotoğraflar eklenemedi: ${e.toString()}');
    }
  }

  // Get student's feedback history
  Future<List<FeedbackListItem>> getStudentFeedbackHistory({
    int page = 0,
    int limit = 20,
  }) async {
    try {
      if (!isAuthenticated) {
        throw Exception('Kullanıcı girişi yapılmamış');
      }

      final response = await _supabase
          .from('feedback')
          .select('''
            id,
            title,
            status,
            feedback_date,
            instructor_response,
            instructor_id
          ''')
          .eq('student_id', currentUserId!)
          .order('feedback_date', ascending: false)
          .range(page * limit, (page + 1) * limit - 1);

      // Get instructor names separately to avoid relationship issues
      final instructorIds =
          response.map((item) => item['instructor_id']).toSet().toList();
      Map<String, String> instructorNames = {};

      if (instructorIds.isNotEmpty) {
        final instructorsResponse = await _supabase
            .from('profiles')
            .select('id, name, surname')
            .inFilter('id', instructorIds);

        for (final instructor in instructorsResponse) {
          final name = '${instructor['name']} ${instructor['surname']}'.trim();
          instructorNames[instructor['id']] =
              name.isNotEmpty ? name : 'Bilinmeyen Eğitmen';
        }
      }

      return response.map<FeedbackListItem>((item) {
        final instructorId = item['instructor_id'] as String;
        final instructorName =
            instructorNames[instructorId] ?? 'Bilinmeyen Eğitmen';

        return FeedbackListItem(
          id: item['id'],
          title: item['title'],
          status: FeedbackStatus.values.firstWhere(
            (s) => s.name == item['status'],
            orElse: () => FeedbackStatus.pending,
          ),
          feedbackDate: DateTime.parse(item['feedback_date']),
          instructorName: instructorName,
          hasResponse: item['instructor_response'] != null,
        );
      }).toList();
    } catch (e) {
      if (e.toString().contains('relation') &&
          e.toString().contains('does not exist')) {
        throw Exception(
            'Geri bildirim sistemi henüz kurulmamış. Lütfen sistem yöneticisi ile iletişime geçin.');
      } else if (e.toString().contains('permission denied')) {
        throw Exception('Bu işlem için yetkiniz bulunmuyor.');
      } else {
        throw Exception(
            'Geri bildirimler yüklenirken bir hata oluştu. Lütfen tekrar deneyin.');
      }
    }
  }

  // Get instructor's feedback queue
  Future<List<FeedbackListItem>> getInstructorFeedbackQueue({
    int page = 0,
    int limit = 20,
  }) async {
    try {
      if (!isAuthenticated) {
        throw Exception('Kullanıcı girişi yapılmamış');
      }

      final response = await _supabase
          .from('feedback')
          .select('''
            id,
            title,
            status,
            feedback_date,
            student_id
          ''')
          .eq('instructor_id', currentUserId!)
          .order('status')
          .order('feedback_date', ascending: false)
          .range(page * limit, (page + 1) * limit - 1);

      // Get student names separately to avoid relationship issues
      final studentIds =
          response.map((item) => item['student_id']).toSet().toList();
      Map<String, String> studentNames = {};

      if (studentIds.isNotEmpty) {
        final studentsResponse = await _supabase
            .from('profiles')
            .select('id, name, surname')
            .inFilter('id', studentIds);

        for (final student in studentsResponse) {
          final name = '${student['name']} ${student['surname']}'.trim();
          studentNames[student['id']] =
              name.isNotEmpty ? name : 'Bilinmeyen Öğrenci';
        }
      }

      return response.map<FeedbackListItem>((item) {
        final studentId = item['student_id'] as String;
        final studentName = studentNames[studentId] ?? 'Bilinmeyen Öğrenci';

        final feedbackDate = DateTime.parse(item['feedback_date']);
        final daysWaiting = DateTime.now().difference(feedbackDate).inDays;

        return FeedbackListItem(
          id: item['id'],
          title: item['title'],
          status: FeedbackStatus.values.firstWhere(
            (s) => s.name == item['status'],
            orElse: () => FeedbackStatus.pending,
          ),
          feedbackDate: feedbackDate,
          studentName: studentName,
          daysWaiting: daysWaiting,
        );
      }).toList();
    } catch (e) {
      if (e.toString().contains('relation') &&
          e.toString().contains('does not exist')) {
        throw Exception(
            'Geri bildirim sistemi henüz kurulmamış. Lütfen sistem yöneticisi ile iletişime geçin.');
      } else if (e.toString().contains('permission denied')) {
        throw Exception('Bu işlem için yetkiniz bulunmuyor.');
      } else {
        throw Exception(
            'Geri bildirimler yüklenirken bir hata oluştu. Lütfen tekrar deneyin.');
      }
    }
  }

  // Get feedback detail with photos
  Future<Feedback> getFeedbackDetail(String feedbackId) async {
    try {
      // Get feedback data
      final feedbackResponse = await _supabase
          .from('feedback')
          .select('*')
          .eq('id', feedbackId)
          .single();

      // Get instructor and student names separately
      final instructorId = feedbackResponse['instructor_id'] as String;
      final studentId = feedbackResponse['student_id'] as String;

      final instructorResponse = await _supabase
          .from('profiles')
          .select('name, surname')
          .eq('id', instructorId)
          .single();

      final studentResponse = await _supabase
          .from('profiles')
          .select('name, surname')
          .eq('id', studentId)
          .single();

      // Get photos from array field in feedback table
      final photosArray = feedbackResponse['photos'] as List<dynamic>? ?? [];
      final photos = photosArray.asMap().entries.map<FeedbackPhoto>((entry) {
        final index = entry.key;
        final photoUrl = entry.value as String;

        // Determine photo type based on index (front, side, back)
        final photoType = index < FeedbackPhotoType.values.length
            ? FeedbackPhotoType.values[index]
            : FeedbackPhotoType.front;

        return FeedbackPhoto(
          id: '$feedbackId-$index', // Generate ID from feedback ID and index
          feedbackId: feedbackId,
          photoType: photoType,
          photoUrl: photoUrl,
          uploadDate: DateTime.parse(feedbackResponse['created_at']),
          fileSizeBytes: null,
        );
      }).toList();

      // Use the separately fetched profiles
      final instructorName =
          '${instructorResponse['name']} ${instructorResponse['surname']}';
      final studentName =
          '${studentResponse['name']} ${studentResponse['surname']}';

      return Feedback(
        id: feedbackResponse['id'],
        studentId: feedbackResponse['student_id'],
        instructorId: feedbackResponse['instructor_id'],
        title: feedbackResponse['title'],
        studentNotes: feedbackResponse['description'] ?? '',
        status: FeedbackStatus.values.firstWhere(
          (s) => s.name == feedbackResponse['status'],
          orElse: () => FeedbackStatus.pending,
        ),
        instructorResponse: feedbackResponse['instructor_response'],
        instructorResponseDate: feedbackResponse['response_date'] != null
            ? DateTime.parse(feedbackResponse['response_date'])
            : null,
        feedbackDate: DateTime.parse(feedbackResponse['feedback_date']),
        createdAt: DateTime.parse(feedbackResponse['created_at']),
        updatedAt: DateTime.parse(feedbackResponse['updated_at']),
        photos: photos,
        instructorName: instructorName,
        studentName: studentName,
      );
    } catch (e) {
      throw Exception('Geri bildirim detayı alınamadı: ${e.toString()}');
    }
  }

  // Submit instructor response
  Future<void> submitInstructorResponse({
    required String feedbackId,
    required String response,
  }) async {
    try {
      if (!isAuthenticated) {
        throw Exception('Kullanıcı girişi yapılmamış');
      }

      await _supabase
          .from('feedback')
          .update({
            'instructor_response': response,
            'response_date': DateTime.now().toIso8601String(),
            'status': 'reviewed',
          })
          .eq('id', feedbackId)
          .eq('instructor_id',
              currentUserId!) // Ensure instructor owns this feedback
          .eq('status', 'pending'); // Only allow response to pending feedback
    } catch (e) {
      throw Exception('Yanıt gönderilemedi: ${e.toString()}');
    }
  }

  // Check if student can create feedback for instructor
  Future<bool> canCreateFeedbackForInstructor(String instructorId) async {
    try {
      if (!isAuthenticated) return false;

      // Check if student is enrolled with this instructor
      final enrollmentResponse = await _supabase
          .from('enrollments')
          .select('id')
          .eq('student_id', currentUserId!)
          .eq('instructor_id', instructorId)
          .eq('is_active', true)
          .limit(1);

      if (enrollmentResponse.isEmpty) return false;

      // Check if student already has waiting feedback for today
      final today = DateTime.now();
      final todayStart = DateTime(today.year, today.month, today.day);
      final todayEnd = todayStart.add(const Duration(days: 1));

      final existingFeedback = await _supabase
          .from('feedback')
          .select('id')
          .eq('student_id', currentUserId!)
          .eq('instructor_id', instructorId)
          .gte('feedback_date', todayStart.toIso8601String())
          .lt('feedback_date', todayEnd.toIso8601String())
          .limit(1);

      return existingFeedback.isEmpty;
    } catch (e) {
      // Error checking feedback permission: $e
      return false;
    }
  }

  // Validate image file
  bool isValidImageFile(File file) {
    final extension = file.path.toLowerCase().split('.').last;
    return ['jpg', 'jpeg', 'png', 'webp'].contains(extension);
  }

  // Validate image size (max 5MB)
  bool isValidImageSize(File file) {
    return file.lengthSync() <= 5 * 1024 * 1024; // 5MB
  }

  // Get image size in MB
  double getImageSizeInMB(File file) {
    return file.lengthSync() / (1024 * 1024);
  }
}
