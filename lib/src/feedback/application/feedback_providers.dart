import 'dart:io';
import 'package:hooks_riverpod/hooks_riverpod.dart';
import 'package:supabase_flutter/supabase_flutter.dart';
import '../domain/feedback_models.dart';
import '../repository/feedback_repository.dart';

// Repository provider
final feedbackRepositoryProvider = Provider<FeedbackRepository>((ref) {
  return FeedbackRepository(Supabase.instance.client);
});

// Student feedback history provider
final studentFeedbackHistoryProvider =
    StateNotifierProvider<StudentFeedbackHistoryNotifier, FeedbackState>((ref) {
  final repository = ref.watch(feedbackRepositoryProvider);
  return StudentFeedbackHistoryNotifier(repository);
});

class StudentFeedbackHistoryNotifier extends StateNotifier<FeedbackState> {
  final FeedbackRepository _repository;

  StudentFeedbackHistoryNotifier(this._repository)
      : super(const FeedbackState());

  Future<void> loadFeedbackHistory({bool refresh = false}) async {
    if (refresh) {
      state = state.copyWith(
        isLoading: true,
        error: null,
        currentPage: 0,
        hasMore: true,
      );
    } else if (state.isLoading || state.isLoadingMore) {
      return;
    } else {
      state = state.copyWith(isLoadingMore: true, error: null);
    }

    try {
      final page = refresh ? 0 : (state.currentPage ?? 0);
      final feedbacks = await _repository.getStudentFeedbackHistory(page: page);

      if (refresh) {
        state = state.copyWith(
          feedbacks: feedbacks,
          isLoading: false,
          isLoadingMore: false,
          currentPage: 0,
          hasMore: feedbacks.length >= 20,
        );
      } else {
        state = state.copyWith(
          feedbacks: [...state.feedbacks, ...feedbacks],
          isLoadingMore: false,
          currentPage: page + 1,
          hasMore: feedbacks.length >= 20,
        );
      }
    } catch (e) {
      state = state.copyWith(
        isLoading: false,
        isLoadingMore: false,
        error: e.toString(),
      );
    }
  }

  Future<void> refresh() => loadFeedbackHistory(refresh: true);

  Future<void> loadMore() => loadFeedbackHistory();
}

// Instructor feedback queue provider
final instructorFeedbackQueueProvider =
    StateNotifierProvider<InstructorFeedbackQueueNotifier, FeedbackState>(
        (ref) {
  final repository = ref.watch(feedbackRepositoryProvider);
  return InstructorFeedbackQueueNotifier(repository);
});

class InstructorFeedbackQueueNotifier extends StateNotifier<FeedbackState> {
  final FeedbackRepository _repository;

  InstructorFeedbackQueueNotifier(this._repository)
      : super(const FeedbackState());

  Future<void> loadFeedbackQueue({bool refresh = false}) async {
    if (refresh) {
      state = state.copyWith(
        isLoading: true,
        error: null,
        currentPage: 0,
        hasMore: true,
      );
    } else if (state.isLoading || state.isLoadingMore) {
      return;
    } else {
      state = state.copyWith(isLoadingMore: true, error: null);
    }

    try {
      final page = refresh ? 0 : (state.currentPage ?? 0);
      final feedbacks =
          await _repository.getInstructorFeedbackQueue(page: page);

      if (refresh) {
        state = state.copyWith(
          feedbacks: feedbacks,
          isLoading: false,
          isLoadingMore: false,
          currentPage: 0,
          hasMore: feedbacks.length >= 20,
        );
      } else {
        state = state.copyWith(
          feedbacks: [...state.feedbacks, ...feedbacks],
          isLoadingMore: false,
          currentPage: page + 1,
          hasMore: feedbacks.length >= 20,
        );
      }
    } catch (e) {
      state = state.copyWith(
        isLoading: false,
        isLoadingMore: false,
        error: e.toString(),
      );
    }
  }

  Future<void> refresh() => loadFeedbackQueue(refresh: true);

  Future<void> loadMore() => loadFeedbackQueue();
}

// Feedback detail provider
final feedbackDetailProvider = StateNotifierProvider.family<
    FeedbackDetailNotifier, FeedbackDetailState, String>((ref, feedbackId) {
  final repository = ref.watch(feedbackRepositoryProvider);
  return FeedbackDetailNotifier(repository, feedbackId);
});

class FeedbackDetailNotifier extends StateNotifier<FeedbackDetailState> {
  final FeedbackRepository _repository;
  final String _feedbackId;

  FeedbackDetailNotifier(this._repository, this._feedbackId)
      : super(const FeedbackDetailState()) {
    loadFeedbackDetail();
  }

  Future<void> loadFeedbackDetail() async {
    state = state.copyWith(isLoading: true, error: null);

    try {
      final feedback = await _repository.getFeedbackDetail(_feedbackId);
      state = state.copyWith(
        feedback: feedback,
        isLoading: false,
        responseForm: FeedbackResponseForm(feedbackId: _feedbackId),
      );
    } catch (e) {
      state = state.copyWith(
        isLoading: false,
        error: e.toString(),
      );
    }
  }

  void updateResponseText(String response) {
    if (state.responseForm != null) {
      state = state.copyWith(
        responseForm: state.responseForm!.copyWith(response: response),
      );
    }
  }

  Future<void> submitResponse() async {
    if (state.responseForm == null ||
        state.responseForm!.response.trim().isEmpty) {
      state = state.copyWith(error: 'Lütfen bir yanıt yazın');
      return;
    }

    state = state.copyWith(
      isSubmittingResponse: true,
      error: null,
      responseForm: state.responseForm!.copyWith(isSubmitting: true),
    );

    try {
      await _repository.submitInstructorResponse(
        feedbackId: _feedbackId,
        response: state.responseForm!.response.trim(),
      );

      // Reload feedback to get updated data
      await loadFeedbackDetail();
    } catch (e) {
      state = state.copyWith(
        isSubmittingResponse: false,
        error: e.toString(),
        responseForm: state.responseForm!.copyWith(isSubmitting: false),
      );
    }
  }

  Future<void> refresh() => loadFeedbackDetail();
}

// Feedback form provider
final feedbackFormProvider =
    StateNotifierProvider<FeedbackFormNotifier, FeedbackFormState>((ref) {
  final repository = ref.watch(feedbackRepositoryProvider);
  return FeedbackFormNotifier(repository);
});

class FeedbackFormNotifier extends StateNotifier<FeedbackFormState> {
  final FeedbackRepository _repository;

  // Local state for files (not serialized)
  final Map<FeedbackPhotoType, File?> _localFiles = {};

  FeedbackFormNotifier(this._repository) : super(_createInitialState());

  static FeedbackFormState _createInitialState() {
    final initialPhotos = FeedbackPhotoType.values
        .map((type) => FeedbackPhotoUpload(type: type))
        .toList();

    return FeedbackFormState(
      form: FeedbackForm(photos: initialPhotos),
    );
  }

  void _initializeForm() {
    final initialPhotos = FeedbackPhotoType.values
        .map((type) => FeedbackPhotoUpload(type: type))
        .toList();

    state = state.copyWith(
      form: state.form.copyWith(photos: initialPhotos),
    );

    // Clear local files
    _localFiles.clear();
  }

  void updateTitle(String title) {
    state = state.copyWith(
      form: state.form.copyWith(title: title),
      error: null,
    );
  }

  void updateStudentNotes(String notes) {
    state = state.copyWith(
      form: state.form.copyWith(studentNotes: notes),
      error: null,
    );
  }

  void setInstructor(String instructorId) {
    state = state.copyWith(
      form: state.form.copyWith(instructorId: instructorId),
    );
  }

  void setEnrollment(String enrollmentId) {
    state = state.copyWith(
      form: state.form.copyWith(enrollmentId: enrollmentId),
    );
  }

  Future<void> addPhoto(FeedbackPhotoType photoType, File imageFile) async {
    // Validate image
    if (!_repository.isValidImageFile(imageFile)) {
      state = state.copyWith(
          error: 'Lütfen geçerli bir resim dosyası seçin (JPG, PNG)');
      return;
    }

    if (!_repository.isValidImageSize(imageFile)) {
      state = state.copyWith(error: 'Resim boyutu 5MB\'dan küçük olmalıdır');
      return;
    }

    // Store local file
    _localFiles[photoType] = imageFile;

    // Update photo upload state
    final updatedPhotos = state.form.photos.map((photo) {
      if (photo.type == photoType) {
        return photo.copyWith(
          uploadedUrl: null,
          isUploading: false,
          uploadProgress: null,
        );
      }
      return photo;
    }).toList();

    state = state.copyWith(
      form: state.form.copyWith(photos: updatedPhotos),
      error: null,
    );
  }

  void removePhoto(FeedbackPhotoType photoType) {
    // Remove local file
    _localFiles.remove(photoType);

    final updatedPhotos = state.form.photos.map((photo) {
      if (photo.type == photoType) {
        return FeedbackPhotoUpload(type: photoType);
      }
      return photo;
    }).toList();

    state = state.copyWith(
      form: state.form.copyWith(photos: updatedPhotos),
      error: null,
    );
  }

  Future<void> submitFeedback() async {
    if (!state.form.isValid) {
      state = state.copyWith(error: 'Lütfen başlık ve notları doldurun');
      return;
    }

    if (state.form.instructorId == null) {
      state = state.copyWith(error: 'Eğitmen seçilmedi');
      return;
    }

    state = state.copyWith(isSubmitting: true, error: null);

    try {
      // Create feedback
      final feedbackId = await _repository.createFeedback(
        instructorId: state.form.instructorId!,
        title: state.form.title.trim(),
        studentNotes: state.form.studentNotes.trim(),
        enrollmentId: state.form.enrollmentId,
      );

      // Upload photos if any
      if (state.form.hasPhotos) {
        await _uploadPhotos(feedbackId);
      }

      // Reset form
      _initializeForm();
      state = state.copyWith(
        isSubmitting: false,
        successMessage: 'Geri bildirim başarıyla gönderildi',
      );
    } catch (e) {
      state = state.copyWith(
        isSubmitting: false,
        error: e.toString(),
      );
    }
  }

  Future<void> _uploadPhotos(String feedbackId) async {
    final userId = _repository.currentUserId;
    if (userId == null) {
      throw Exception('Kullanıcı girişi yapılmamış');
    }

    state = state.copyWith(isUploadingPhotos: true);

    try {
      final uploadedPhotos = <FeedbackPhoto>[];

      for (final photo in state.form.photos) {
        final localFile = _localFiles[photo.type];
        if (localFile != null) {
          // Update progress
          final progressMap =
              Map<FeedbackPhotoType, double>.from(state.uploadProgress);
          progressMap[photo.type] = 0.0;
          state = state.copyWith(uploadProgress: progressMap);

          // Upload photo
          final uploadedUrl = await _repository.uploadFeedbackPhoto(
            imageFile: localFile,
            photoType: photo.type,
            feedbackId: feedbackId,
            userId: userId,
            onProgress: (progress) {
              final progressMap =
                  Map<FeedbackPhotoType, double>.from(state.uploadProgress);
              progressMap[photo.type] = progress;
              state = state.copyWith(uploadProgress: progressMap);
            },
          );

          uploadedPhotos.add(FeedbackPhoto(
            feedbackId: feedbackId,
            photoType: photo.type,
            photoUrl: uploadedUrl,
            fileSizeBytes: localFile.lengthSync(),
          ));
        }
      }

      // Add photos to feedback
      if (uploadedPhotos.isNotEmpty) {
        await _repository.addFeedbackPhotos(
          feedbackId: feedbackId,
          photos: uploadedPhotos,
        );
      }

      state = state.copyWith(
        isUploadingPhotos: false,
        uploadProgress: {},
      );
    } catch (e) {
      state = state.copyWith(
        isUploadingPhotos: false,
        uploadProgress: {},
      );
      rethrow;
    }
  }

  void clearMessages() {
    state = state.copyWith(
      error: null,
      successMessage: null,
    );
  }

  void resetForm() {
    _initializeForm();
    state = state.copyWith(
      error: null,
      successMessage: null,
      isSubmitting: false,
      isUploadingPhotos: false,
      uploadProgress: {},
    );
  }

  // Helper methods for UI
  bool hasPhoto(FeedbackPhotoType type) {
    return _localFiles[type] != null ||
        state.form.photos.firstWhere((p) => p.type == type).uploadedUrl != null;
  }

  File? getLocalFile(FeedbackPhotoType type) {
    return _localFiles[type];
  }

  bool get hasAnyPhotos {
    return _localFiles.values.any((file) => file != null) ||
        state.form.photos.any((photo) => photo.uploadedUrl != null);
  }

  int get uploadedPhotoCount {
    return state.form.photos.where((photo) => photo.uploadedUrl != null).length;
  }

  bool get allPhotosUploaded {
    return _localFiles.isEmpty ||
        _localFiles.values.every((file) => file == null);
  }
}

// Helper provider to check if student can create feedback for instructor
final canCreateFeedbackProvider =
    FutureProvider.family<bool, String>((ref, instructorId) async {
  final repository = ref.watch(feedbackRepositoryProvider);
  return repository.canCreateFeedbackForInstructor(instructorId);
});
