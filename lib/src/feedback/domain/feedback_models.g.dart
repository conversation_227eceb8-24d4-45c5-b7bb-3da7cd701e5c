// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'feedback_models.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

_FeedbackPhoto _$FeedbackPhotoFromJson(Map<String, dynamic> json) =>
    _FeedbackPhoto(
      id: json['id'] as String?,
      feedbackId: json['feedbackId'] as String,
      photoType: $enumDecode(_$FeedbackPhotoTypeEnumMap, json['photoType']),
      photoUrl: json['photoUrl'] as String,
      uploadDate: json['uploadDate'] == null
          ? null
          : DateTime.parse(json['uploadDate'] as String),
      fileSizeBytes: (json['fileSizeBytes'] as num?)?.toInt(),
      isUploading: json['isUploading'] as bool? ?? false,
      uploadProgress: (json['uploadProgress'] as num?)?.toDouble(),
    );

Map<String, dynamic> _$FeedbackPhotoToJson(_FeedbackPhoto instance) =>
    <String, dynamic>{
      'id': instance.id,
      'feedbackId': instance.feedbackId,
      'photoType': _$FeedbackPhotoTypeEnumMap[instance.photoType]!,
      'photoUrl': instance.photoUrl,
      'uploadDate': instance.uploadDate?.toIso8601String(),
      'fileSizeBytes': instance.fileSizeBytes,
      'isUploading': instance.isUploading,
      'uploadProgress': instance.uploadProgress,
    };

const _$FeedbackPhotoTypeEnumMap = {
  FeedbackPhotoType.front: 'front',
  FeedbackPhotoType.side: 'side',
  FeedbackPhotoType.back: 'back',
};

_Feedback _$FeedbackFromJson(Map<String, dynamic> json) => _Feedback(
      id: json['id'] as String,
      studentId: json['studentId'] as String,
      instructorId: json['instructorId'] as String,
      enrollmentId: json['enrollmentId'] as String?,
      title: json['title'] as String,
      studentNotes: json['studentNotes'] as String,
      status: $enumDecodeNullable(_$FeedbackStatusEnumMap, json['status']) ??
          FeedbackStatus.pending,
      instructorResponse: json['instructorResponse'] as String?,
      instructorResponseDate: json['instructorResponseDate'] == null
          ? null
          : DateTime.parse(json['instructorResponseDate'] as String),
      respondedBy: json['respondedBy'] as String?,
      feedbackDate: DateTime.parse(json['feedbackDate'] as String),
      createdAt: DateTime.parse(json['createdAt'] as String),
      updatedAt: DateTime.parse(json['updatedAt'] as String),
      photos: (json['photos'] as List<dynamic>?)
              ?.map((e) => FeedbackPhoto.fromJson(e as Map<String, dynamic>))
              .toList() ??
          const [],
      instructorName: json['instructorName'] as String?,
      studentName: json['studentName'] as String?,
      daysWaiting: (json['daysWaiting'] as num?)?.toInt(),
    );

Map<String, dynamic> _$FeedbackToJson(_Feedback instance) => <String, dynamic>{
      'id': instance.id,
      'studentId': instance.studentId,
      'instructorId': instance.instructorId,
      'enrollmentId': instance.enrollmentId,
      'title': instance.title,
      'studentNotes': instance.studentNotes,
      'status': _$FeedbackStatusEnumMap[instance.status]!,
      'instructorResponse': instance.instructorResponse,
      'instructorResponseDate':
          instance.instructorResponseDate?.toIso8601String(),
      'respondedBy': instance.respondedBy,
      'feedbackDate': instance.feedbackDate.toIso8601String(),
      'createdAt': instance.createdAt.toIso8601String(),
      'updatedAt': instance.updatedAt.toIso8601String(),
      'photos': instance.photos,
      'instructorName': instance.instructorName,
      'studentName': instance.studentName,
      'daysWaiting': instance.daysWaiting,
    };

const _$FeedbackStatusEnumMap = {
  FeedbackStatus.pending: 'pending',
  FeedbackStatus.reviewed: 'reviewed',
  FeedbackStatus.completed: 'completed',
};

_FeedbackForm _$FeedbackFormFromJson(Map<String, dynamic> json) =>
    _FeedbackForm(
      title: json['title'] as String? ?? '',
      studentNotes: json['studentNotes'] as String? ?? '',
      photos: (json['photos'] as List<dynamic>?)
              ?.map((e) =>
                  FeedbackPhotoUpload.fromJson(e as Map<String, dynamic>))
              .toList() ??
          const [],
      instructorId: json['instructorId'] as String?,
      enrollmentId: json['enrollmentId'] as String?,
    );

Map<String, dynamic> _$FeedbackFormToJson(_FeedbackForm instance) =>
    <String, dynamic>{
      'title': instance.title,
      'studentNotes': instance.studentNotes,
      'photos': instance.photos,
      'instructorId': instance.instructorId,
      'enrollmentId': instance.enrollmentId,
    };

_FeedbackPhotoUpload _$FeedbackPhotoUploadFromJson(Map<String, dynamic> json) =>
    _FeedbackPhotoUpload(
      type: $enumDecode(_$FeedbackPhotoTypeEnumMap, json['type']),
      uploadedUrl: json['uploadedUrl'] as String?,
      isUploading: json['isUploading'] as bool? ?? false,
      uploadProgress: (json['uploadProgress'] as num?)?.toDouble(),
    );

Map<String, dynamic> _$FeedbackPhotoUploadToJson(
        _FeedbackPhotoUpload instance) =>
    <String, dynamic>{
      'type': _$FeedbackPhotoTypeEnumMap[instance.type]!,
      'uploadedUrl': instance.uploadedUrl,
      'isUploading': instance.isUploading,
      'uploadProgress': instance.uploadProgress,
    };

_FeedbackListItem _$FeedbackListItemFromJson(Map<String, dynamic> json) =>
    _FeedbackListItem(
      id: json['id'] as String,
      title: json['title'] as String,
      status: $enumDecode(_$FeedbackStatusEnumMap, json['status']),
      feedbackDate: DateTime.parse(json['feedbackDate'] as String),
      instructorName: json['instructorName'] as String?,
      studentName: json['studentName'] as String?,
      hasResponse: json['hasResponse'] as bool? ?? false,
      photoCount: (json['photoCount'] as num?)?.toInt() ?? 0,
      daysWaiting: (json['daysWaiting'] as num?)?.toInt(),
    );

Map<String, dynamic> _$FeedbackListItemToJson(_FeedbackListItem instance) =>
    <String, dynamic>{
      'id': instance.id,
      'title': instance.title,
      'status': _$FeedbackStatusEnumMap[instance.status]!,
      'feedbackDate': instance.feedbackDate.toIso8601String(),
      'instructorName': instance.instructorName,
      'studentName': instance.studentName,
      'hasResponse': instance.hasResponse,
      'photoCount': instance.photoCount,
      'daysWaiting': instance.daysWaiting,
    };

_FeedbackResponseForm _$FeedbackResponseFormFromJson(
        Map<String, dynamic> json) =>
    _FeedbackResponseForm(
      feedbackId: json['feedbackId'] as String,
      response: json['response'] as String? ?? '',
      isSubmitting: json['isSubmitting'] as bool? ?? false,
    );

Map<String, dynamic> _$FeedbackResponseFormToJson(
        _FeedbackResponseForm instance) =>
    <String, dynamic>{
      'feedbackId': instance.feedbackId,
      'response': instance.response,
      'isSubmitting': instance.isSubmitting,
    };

_FeedbackState _$FeedbackStateFromJson(Map<String, dynamic> json) =>
    _FeedbackState(
      feedbacks: (json['feedbacks'] as List<dynamic>?)
              ?.map((e) => FeedbackListItem.fromJson(e as Map<String, dynamic>))
              .toList() ??
          const [],
      isLoading: json['isLoading'] as bool? ?? false,
      isLoadingMore: json['isLoadingMore'] as bool? ?? false,
      hasMore: json['hasMore'] as bool? ?? false,
      error: json['error'] as String?,
      currentPage: (json['currentPage'] as num?)?.toInt(),
    );

Map<String, dynamic> _$FeedbackStateToJson(_FeedbackState instance) =>
    <String, dynamic>{
      'feedbacks': instance.feedbacks,
      'isLoading': instance.isLoading,
      'isLoadingMore': instance.isLoadingMore,
      'hasMore': instance.hasMore,
      'error': instance.error,
      'currentPage': instance.currentPage,
    };

_FeedbackDetailState _$FeedbackDetailStateFromJson(Map<String, dynamic> json) =>
    _FeedbackDetailState(
      feedback: json['feedback'] == null
          ? null
          : Feedback.fromJson(json['feedback'] as Map<String, dynamic>),
      isLoading: json['isLoading'] as bool? ?? false,
      isSubmittingResponse: json['isSubmittingResponse'] as bool? ?? false,
      error: json['error'] as String?,
      responseForm: json['responseForm'] == null
          ? null
          : FeedbackResponseForm.fromJson(
              json['responseForm'] as Map<String, dynamic>),
    );

Map<String, dynamic> _$FeedbackDetailStateToJson(
        _FeedbackDetailState instance) =>
    <String, dynamic>{
      'feedback': instance.feedback,
      'isLoading': instance.isLoading,
      'isSubmittingResponse': instance.isSubmittingResponse,
      'error': instance.error,
      'responseForm': instance.responseForm,
    };

_FeedbackFormState _$FeedbackFormStateFromJson(Map<String, dynamic> json) =>
    _FeedbackFormState(
      form: json['form'] == null
          ? const FeedbackForm()
          : FeedbackForm.fromJson(json['form'] as Map<String, dynamic>),
      isLoading: json['isLoading'] as bool? ?? false,
      isSubmitting: json['isSubmitting'] as bool? ?? false,
      isUploadingPhotos: json['isUploadingPhotos'] as bool? ?? false,
      uploadProgress: (json['uploadProgress'] as Map<String, dynamic>?)?.map(
            (k, e) => MapEntry($enumDecode(_$FeedbackPhotoTypeEnumMap, k),
                (e as num).toDouble()),
          ) ??
          const {},
      error: json['error'] as String?,
      successMessage: json['successMessage'] as String?,
    );

Map<String, dynamic> _$FeedbackFormStateToJson(_FeedbackFormState instance) =>
    <String, dynamic>{
      'form': instance.form,
      'isLoading': instance.isLoading,
      'isSubmitting': instance.isSubmitting,
      'isUploadingPhotos': instance.isUploadingPhotos,
      'uploadProgress': instance.uploadProgress
          .map((k, e) => MapEntry(_$FeedbackPhotoTypeEnumMap[k]!, e)),
      'error': instance.error,
      'successMessage': instance.successMessage,
    };
