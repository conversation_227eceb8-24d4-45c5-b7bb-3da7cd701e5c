import 'dart:io';
import 'package:freezed_annotation/freezed_annotation.dart';

part 'feedback_models.freezed.dart';
part 'feedback_models.g.dart';

// Feedback status enum
enum FeedbackStatus {
  @JsonValue('pending')
  pending,
  @JsonValue('reviewed')
  reviewed,
  @JsonValue('completed')
  completed;

  String get displayName {
    switch (this) {
      case FeedbackStatus.pending:
        return 'Bekliyor';
      case FeedbackStatus.reviewed:
        return '<PERSON>ncelendi';
      case FeedbackStatus.completed:
        return 'Tamamlandı';
    }
  }

  String get description {
    switch (this) {
      case FeedbackStatus.pending:
        return 'Eğitmen yanıtı bekleniyor';
      case FeedbackStatus.reviewed:
        return 'Eğitmen tarafından yanıtlandı';
      case FeedbackStatus.completed:
        return 'Geri bildirim tamamlandı';
    }
  }
}

// Feedback photo type enum (reusing from profile)
enum FeedbackPhotoType {
  @JsonValue('front')
  front,
  @JsonValue('side')
  side,
  @JsonValue('back')
  back;

  String get displayName {
    switch (this) {
      case FeedbackPhotoType.front:
        return 'Ön';
      case FeedbackPhotoType.side:
        return 'Yan';
      case FeedbackPhotoType.back:
        return 'Arka';
    }
  }
}

// Feedback photo model
@freezed
abstract class FeedbackPhoto with _$FeedbackPhoto {
  const factory FeedbackPhoto({
    String? id,
    required String feedbackId,
    required FeedbackPhotoType photoType,
    required String photoUrl,
    DateTime? uploadDate,
    int? fileSizeBytes,

    // Local state for uploads (not serialized)
    @Default(false) bool isUploading,
    double? uploadProgress,
  }) = _FeedbackPhoto;

  factory FeedbackPhoto.fromJson(Map<String, dynamic> json) =>
      _$FeedbackPhotoFromJson(json);
}

// Feedback model
@freezed
abstract class Feedback with _$Feedback {
  const factory Feedback({
    required String id,
    required String studentId,
    required String instructorId,
    String? enrollmentId,

    // Content
    required String title,
    required String studentNotes,

    // Status
    @Default(FeedbackStatus.pending) FeedbackStatus status,

    // Instructor response
    String? instructorResponse,
    DateTime? instructorResponseDate,
    String? respondedBy,

    // Metadata
    required DateTime feedbackDate,
    required DateTime createdAt,
    required DateTime updatedAt,

    // Photos
    @Default([]) List<FeedbackPhoto> photos,

    // Additional info (from joins)
    String? instructorName,
    String? studentName,
    int? daysWaiting,
  }) = _Feedback;

  factory Feedback.fromJson(Map<String, dynamic> json) =>
      _$FeedbackFromJson(json);
}

// Feedback creation form model
@freezed
abstract class FeedbackForm with _$FeedbackForm {
  const factory FeedbackForm({
    @Default('') String title,
    @Default('') String studentNotes,
    @Default([]) List<FeedbackPhotoUpload> photos,
    String? instructorId,
    String? enrollmentId,
  }) = _FeedbackForm;

  factory FeedbackForm.fromJson(Map<String, dynamic> json) =>
      _$FeedbackFormFromJson(json);
}

// Photo upload model for form
@freezed
abstract class FeedbackPhotoUpload with _$FeedbackPhotoUpload {
  const factory FeedbackPhotoUpload({
    required FeedbackPhotoType type,
    String? uploadedUrl,
    @Default(false) bool isUploading,
    double? uploadProgress,
  }) = _FeedbackPhotoUpload;

  factory FeedbackPhotoUpload.fromJson(Map<String, dynamic> json) =>
      _$FeedbackPhotoUploadFromJson(json);
}

// Feedback list item for UI
@freezed
abstract class FeedbackListItem with _$FeedbackListItem {
  const factory FeedbackListItem({
    required String id,
    required String title,
    required FeedbackStatus status,
    required DateTime feedbackDate,
    String? instructorName,
    String? studentName,
    @Default(false) bool hasResponse,
    @Default(0) int photoCount,
    int? daysWaiting,
  }) = _FeedbackListItem;

  factory FeedbackListItem.fromJson(Map<String, dynamic> json) =>
      _$FeedbackListItemFromJson(json);
}

// Feedback response form model
@freezed
abstract class FeedbackResponseForm with _$FeedbackResponseForm {
  const factory FeedbackResponseForm({
    required String feedbackId,
    @Default('') String response,
    @Default(false) bool isSubmitting,
  }) = _FeedbackResponseForm;

  factory FeedbackResponseForm.fromJson(Map<String, dynamic> json) =>
      _$FeedbackResponseFormFromJson(json);
}

// Feedback submission result
@freezed
class FeedbackSubmissionResult with _$FeedbackSubmissionResult {
  const factory FeedbackSubmissionResult.success(String feedbackId) = _Success;
  const factory FeedbackSubmissionResult.failure(String error) = _Failure;
}

// Feedback state for providers
@freezed
abstract class FeedbackState with _$FeedbackState {
  const factory FeedbackState({
    @Default([]) List<FeedbackListItem> feedbacks,
    @Default(false) bool isLoading,
    @Default(false) bool isLoadingMore,
    @Default(false) bool hasMore,
    String? error,
    int? currentPage,
  }) = _FeedbackState;

  factory FeedbackState.fromJson(Map<String, dynamic> json) =>
      _$FeedbackStateFromJson(json);
}

// Feedback detail state
@freezed
abstract class FeedbackDetailState with _$FeedbackDetailState {
  const factory FeedbackDetailState({
    Feedback? feedback,
    @Default(false) bool isLoading,
    @Default(false) bool isSubmittingResponse,
    String? error,
    FeedbackResponseForm? responseForm,
  }) = _FeedbackDetailState;

  factory FeedbackDetailState.fromJson(Map<String, dynamic> json) =>
      _$FeedbackDetailStateFromJson(json);
}

// Feedback form state
@freezed
abstract class FeedbackFormState with _$FeedbackFormState {
  const factory FeedbackFormState({
    @Default(FeedbackForm()) FeedbackForm form,
    @Default(false) bool isLoading,
    @Default(false) bool isSubmitting,
    @Default(false) bool isUploadingPhotos,
    @Default({}) Map<FeedbackPhotoType, double> uploadProgress,
    String? error,
    String? successMessage,
  }) = _FeedbackFormState;

  factory FeedbackFormState.fromJson(Map<String, dynamic> json) =>
      _$FeedbackFormStateFromJson(json);
}

// Helper class for managing local files with photo uploads
class FeedbackPhotoWithFile {
  final FeedbackPhotoUpload upload;
  final File? localFile;

  const FeedbackPhotoWithFile({
    required this.upload,
    this.localFile,
  });

  bool get hasPhoto => localFile != null || upload.uploadedUrl != null;
  bool get isUploaded => upload.uploadedUrl != null;
  FeedbackPhotoType get type => upload.type;

  FeedbackPhotoWithFile copyWithFile(File file) {
    return FeedbackPhotoWithFile(
      upload: upload.copyWith(
        uploadedUrl: null,
        isUploading: false,
        uploadProgress: null,
      ),
      localFile: file,
    );
  }

  FeedbackPhotoWithFile copyWithUploadedUrl(String url) {
    return FeedbackPhotoWithFile(
      upload: upload.copyWith(
        uploadedUrl: url,
        isUploading: false,
        uploadProgress: null,
      ),
      localFile: null,
    );
  }

  FeedbackPhotoWithFile copyWithProgress(double progress) {
    return FeedbackPhotoWithFile(
      upload: upload.copyWith(
        isUploading: true,
        uploadProgress: progress,
      ),
      localFile: localFile,
    );
  }
}

// Extensions for convenience
extension FeedbackPhotoUploadX on FeedbackPhotoUpload {
  bool get hasPhoto => uploadedUrl != null;
  bool get isUploaded => uploadedUrl != null;
}

extension FeedbackX on Feedback {
  bool get canBeEdited => status == FeedbackStatus.pending;
  bool get hasInstructorResponse => instructorResponse != null;
  bool get isWaitingForResponse => status == FeedbackStatus.pending;

  int get photoCount => photos.length;

  String get statusDisplayText => status.displayName;
  String get statusDescription => status.description;
}

extension FeedbackFormX on FeedbackForm {
  bool get isValid => title.trim().isNotEmpty && studentNotes.trim().isNotEmpty;

  bool get hasPhotos => photos.any((photo) => photo.hasPhoto);

  int get uploadedPhotoCount =>
      photos.where((photo) => photo.isUploaded).length;

  bool get allPhotosUploaded =>
      photos.every((photo) => !photo.hasPhoto || photo.isUploaded);
}
