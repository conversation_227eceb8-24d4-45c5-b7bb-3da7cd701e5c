// dart format width=80
// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'feedback_models.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

// dart format off
T _$identity<T>(T value) => value;

/// @nodoc
mixin _$FeedbackPhoto {
  String? get id;
  String get feedbackId;
  FeedbackPhotoType get photoType;
  String get photoUrl;
  DateTime? get uploadDate;
  int? get fileSizeBytes; // Local state for uploads (not serialized)
  bool get isUploading;
  double? get uploadProgress;

  /// Create a copy of FeedbackPhoto
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @pragma('vm:prefer-inline')
  $FeedbackPhotoCopyWith<FeedbackPhoto> get copyWith =>
      _$FeedbackPhotoCopyWithImpl<FeedbackPhoto>(
          this as FeedbackPhoto, _$identity);

  /// Serializes this FeedbackPhoto to a JSON map.
  Map<String, dynamic> toJson();

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is FeedbackPhoto &&
            (identical(other.id, id) || other.id == id) &&
            (identical(other.feedbackId, feedbackId) ||
                other.feedbackId == feedbackId) &&
            (identical(other.photoType, photoType) ||
                other.photoType == photoType) &&
            (identical(other.photoUrl, photoUrl) ||
                other.photoUrl == photoUrl) &&
            (identical(other.uploadDate, uploadDate) ||
                other.uploadDate == uploadDate) &&
            (identical(other.fileSizeBytes, fileSizeBytes) ||
                other.fileSizeBytes == fileSizeBytes) &&
            (identical(other.isUploading, isUploading) ||
                other.isUploading == isUploading) &&
            (identical(other.uploadProgress, uploadProgress) ||
                other.uploadProgress == uploadProgress));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hash(runtimeType, id, feedbackId, photoType,
      photoUrl, uploadDate, fileSizeBytes, isUploading, uploadProgress);

  @override
  String toString() {
    return 'FeedbackPhoto(id: $id, feedbackId: $feedbackId, photoType: $photoType, photoUrl: $photoUrl, uploadDate: $uploadDate, fileSizeBytes: $fileSizeBytes, isUploading: $isUploading, uploadProgress: $uploadProgress)';
  }
}

/// @nodoc
abstract mixin class $FeedbackPhotoCopyWith<$Res> {
  factory $FeedbackPhotoCopyWith(
          FeedbackPhoto value, $Res Function(FeedbackPhoto) _then) =
      _$FeedbackPhotoCopyWithImpl;
  @useResult
  $Res call(
      {String? id,
      String feedbackId,
      FeedbackPhotoType photoType,
      String photoUrl,
      DateTime? uploadDate,
      int? fileSizeBytes,
      bool isUploading,
      double? uploadProgress});
}

/// @nodoc
class _$FeedbackPhotoCopyWithImpl<$Res>
    implements $FeedbackPhotoCopyWith<$Res> {
  _$FeedbackPhotoCopyWithImpl(this._self, this._then);

  final FeedbackPhoto _self;
  final $Res Function(FeedbackPhoto) _then;

  /// Create a copy of FeedbackPhoto
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? id = freezed,
    Object? feedbackId = null,
    Object? photoType = null,
    Object? photoUrl = null,
    Object? uploadDate = freezed,
    Object? fileSizeBytes = freezed,
    Object? isUploading = null,
    Object? uploadProgress = freezed,
  }) {
    return _then(_self.copyWith(
      id: freezed == id
          ? _self.id
          : id // ignore: cast_nullable_to_non_nullable
              as String?,
      feedbackId: null == feedbackId
          ? _self.feedbackId
          : feedbackId // ignore: cast_nullable_to_non_nullable
              as String,
      photoType: null == photoType
          ? _self.photoType
          : photoType // ignore: cast_nullable_to_non_nullable
              as FeedbackPhotoType,
      photoUrl: null == photoUrl
          ? _self.photoUrl
          : photoUrl // ignore: cast_nullable_to_non_nullable
              as String,
      uploadDate: freezed == uploadDate
          ? _self.uploadDate
          : uploadDate // ignore: cast_nullable_to_non_nullable
              as DateTime?,
      fileSizeBytes: freezed == fileSizeBytes
          ? _self.fileSizeBytes
          : fileSizeBytes // ignore: cast_nullable_to_non_nullable
              as int?,
      isUploading: null == isUploading
          ? _self.isUploading
          : isUploading // ignore: cast_nullable_to_non_nullable
              as bool,
      uploadProgress: freezed == uploadProgress
          ? _self.uploadProgress
          : uploadProgress // ignore: cast_nullable_to_non_nullable
              as double?,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _FeedbackPhoto implements FeedbackPhoto {
  const _FeedbackPhoto(
      {this.id,
      required this.feedbackId,
      required this.photoType,
      required this.photoUrl,
      this.uploadDate,
      this.fileSizeBytes,
      this.isUploading = false,
      this.uploadProgress});
  factory _FeedbackPhoto.fromJson(Map<String, dynamic> json) =>
      _$FeedbackPhotoFromJson(json);

  @override
  final String? id;
  @override
  final String feedbackId;
  @override
  final FeedbackPhotoType photoType;
  @override
  final String photoUrl;
  @override
  final DateTime? uploadDate;
  @override
  final int? fileSizeBytes;
// Local state for uploads (not serialized)
  @override
  @JsonKey()
  final bool isUploading;
  @override
  final double? uploadProgress;

  /// Create a copy of FeedbackPhoto
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  @pragma('vm:prefer-inline')
  _$FeedbackPhotoCopyWith<_FeedbackPhoto> get copyWith =>
      __$FeedbackPhotoCopyWithImpl<_FeedbackPhoto>(this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$FeedbackPhotoToJson(
      this,
    );
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _FeedbackPhoto &&
            (identical(other.id, id) || other.id == id) &&
            (identical(other.feedbackId, feedbackId) ||
                other.feedbackId == feedbackId) &&
            (identical(other.photoType, photoType) ||
                other.photoType == photoType) &&
            (identical(other.photoUrl, photoUrl) ||
                other.photoUrl == photoUrl) &&
            (identical(other.uploadDate, uploadDate) ||
                other.uploadDate == uploadDate) &&
            (identical(other.fileSizeBytes, fileSizeBytes) ||
                other.fileSizeBytes == fileSizeBytes) &&
            (identical(other.isUploading, isUploading) ||
                other.isUploading == isUploading) &&
            (identical(other.uploadProgress, uploadProgress) ||
                other.uploadProgress == uploadProgress));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hash(runtimeType, id, feedbackId, photoType,
      photoUrl, uploadDate, fileSizeBytes, isUploading, uploadProgress);

  @override
  String toString() {
    return 'FeedbackPhoto(id: $id, feedbackId: $feedbackId, photoType: $photoType, photoUrl: $photoUrl, uploadDate: $uploadDate, fileSizeBytes: $fileSizeBytes, isUploading: $isUploading, uploadProgress: $uploadProgress)';
  }
}

/// @nodoc
abstract mixin class _$FeedbackPhotoCopyWith<$Res>
    implements $FeedbackPhotoCopyWith<$Res> {
  factory _$FeedbackPhotoCopyWith(
          _FeedbackPhoto value, $Res Function(_FeedbackPhoto) _then) =
      __$FeedbackPhotoCopyWithImpl;
  @override
  @useResult
  $Res call(
      {String? id,
      String feedbackId,
      FeedbackPhotoType photoType,
      String photoUrl,
      DateTime? uploadDate,
      int? fileSizeBytes,
      bool isUploading,
      double? uploadProgress});
}

/// @nodoc
class __$FeedbackPhotoCopyWithImpl<$Res>
    implements _$FeedbackPhotoCopyWith<$Res> {
  __$FeedbackPhotoCopyWithImpl(this._self, this._then);

  final _FeedbackPhoto _self;
  final $Res Function(_FeedbackPhoto) _then;

  /// Create a copy of FeedbackPhoto
  /// with the given fields replaced by the non-null parameter values.
  @override
  @pragma('vm:prefer-inline')
  $Res call({
    Object? id = freezed,
    Object? feedbackId = null,
    Object? photoType = null,
    Object? photoUrl = null,
    Object? uploadDate = freezed,
    Object? fileSizeBytes = freezed,
    Object? isUploading = null,
    Object? uploadProgress = freezed,
  }) {
    return _then(_FeedbackPhoto(
      id: freezed == id
          ? _self.id
          : id // ignore: cast_nullable_to_non_nullable
              as String?,
      feedbackId: null == feedbackId
          ? _self.feedbackId
          : feedbackId // ignore: cast_nullable_to_non_nullable
              as String,
      photoType: null == photoType
          ? _self.photoType
          : photoType // ignore: cast_nullable_to_non_nullable
              as FeedbackPhotoType,
      photoUrl: null == photoUrl
          ? _self.photoUrl
          : photoUrl // ignore: cast_nullable_to_non_nullable
              as String,
      uploadDate: freezed == uploadDate
          ? _self.uploadDate
          : uploadDate // ignore: cast_nullable_to_non_nullable
              as DateTime?,
      fileSizeBytes: freezed == fileSizeBytes
          ? _self.fileSizeBytes
          : fileSizeBytes // ignore: cast_nullable_to_non_nullable
              as int?,
      isUploading: null == isUploading
          ? _self.isUploading
          : isUploading // ignore: cast_nullable_to_non_nullable
              as bool,
      uploadProgress: freezed == uploadProgress
          ? _self.uploadProgress
          : uploadProgress // ignore: cast_nullable_to_non_nullable
              as double?,
    ));
  }
}

/// @nodoc
mixin _$Feedback {
  String get id;
  String get studentId;
  String get instructorId;
  String? get enrollmentId; // Content
  String get title;
  String get studentNotes; // Status
  FeedbackStatus get status; // Instructor response
  String? get instructorResponse;
  DateTime? get instructorResponseDate;
  String? get respondedBy; // Metadata
  DateTime get feedbackDate;
  DateTime get createdAt;
  DateTime get updatedAt; // Photos
  List<FeedbackPhoto> get photos; // Additional info (from joins)
  String? get instructorName;
  String? get studentName;
  int? get daysWaiting;

  /// Create a copy of Feedback
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @pragma('vm:prefer-inline')
  $FeedbackCopyWith<Feedback> get copyWith =>
      _$FeedbackCopyWithImpl<Feedback>(this as Feedback, _$identity);

  /// Serializes this Feedback to a JSON map.
  Map<String, dynamic> toJson();

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is Feedback &&
            (identical(other.id, id) || other.id == id) &&
            (identical(other.studentId, studentId) ||
                other.studentId == studentId) &&
            (identical(other.instructorId, instructorId) ||
                other.instructorId == instructorId) &&
            (identical(other.enrollmentId, enrollmentId) ||
                other.enrollmentId == enrollmentId) &&
            (identical(other.title, title) || other.title == title) &&
            (identical(other.studentNotes, studentNotes) ||
                other.studentNotes == studentNotes) &&
            (identical(other.status, status) || other.status == status) &&
            (identical(other.instructorResponse, instructorResponse) ||
                other.instructorResponse == instructorResponse) &&
            (identical(other.instructorResponseDate, instructorResponseDate) ||
                other.instructorResponseDate == instructorResponseDate) &&
            (identical(other.respondedBy, respondedBy) ||
                other.respondedBy == respondedBy) &&
            (identical(other.feedbackDate, feedbackDate) ||
                other.feedbackDate == feedbackDate) &&
            (identical(other.createdAt, createdAt) ||
                other.createdAt == createdAt) &&
            (identical(other.updatedAt, updatedAt) ||
                other.updatedAt == updatedAt) &&
            const DeepCollectionEquality().equals(other.photos, photos) &&
            (identical(other.instructorName, instructorName) ||
                other.instructorName == instructorName) &&
            (identical(other.studentName, studentName) ||
                other.studentName == studentName) &&
            (identical(other.daysWaiting, daysWaiting) ||
                other.daysWaiting == daysWaiting));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hash(
      runtimeType,
      id,
      studentId,
      instructorId,
      enrollmentId,
      title,
      studentNotes,
      status,
      instructorResponse,
      instructorResponseDate,
      respondedBy,
      feedbackDate,
      createdAt,
      updatedAt,
      const DeepCollectionEquality().hash(photos),
      instructorName,
      studentName,
      daysWaiting);

  @override
  String toString() {
    return 'Feedback(id: $id, studentId: $studentId, instructorId: $instructorId, enrollmentId: $enrollmentId, title: $title, studentNotes: $studentNotes, status: $status, instructorResponse: $instructorResponse, instructorResponseDate: $instructorResponseDate, respondedBy: $respondedBy, feedbackDate: $feedbackDate, createdAt: $createdAt, updatedAt: $updatedAt, photos: $photos, instructorName: $instructorName, studentName: $studentName, daysWaiting: $daysWaiting)';
  }
}

/// @nodoc
abstract mixin class $FeedbackCopyWith<$Res> {
  factory $FeedbackCopyWith(Feedback value, $Res Function(Feedback) _then) =
      _$FeedbackCopyWithImpl;
  @useResult
  $Res call(
      {String id,
      String studentId,
      String instructorId,
      String? enrollmentId,
      String title,
      String studentNotes,
      FeedbackStatus status,
      String? instructorResponse,
      DateTime? instructorResponseDate,
      String? respondedBy,
      DateTime feedbackDate,
      DateTime createdAt,
      DateTime updatedAt,
      List<FeedbackPhoto> photos,
      String? instructorName,
      String? studentName,
      int? daysWaiting});
}

/// @nodoc
class _$FeedbackCopyWithImpl<$Res> implements $FeedbackCopyWith<$Res> {
  _$FeedbackCopyWithImpl(this._self, this._then);

  final Feedback _self;
  final $Res Function(Feedback) _then;

  /// Create a copy of Feedback
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? id = null,
    Object? studentId = null,
    Object? instructorId = null,
    Object? enrollmentId = freezed,
    Object? title = null,
    Object? studentNotes = null,
    Object? status = null,
    Object? instructorResponse = freezed,
    Object? instructorResponseDate = freezed,
    Object? respondedBy = freezed,
    Object? feedbackDate = null,
    Object? createdAt = null,
    Object? updatedAt = null,
    Object? photos = null,
    Object? instructorName = freezed,
    Object? studentName = freezed,
    Object? daysWaiting = freezed,
  }) {
    return _then(_self.copyWith(
      id: null == id
          ? _self.id
          : id // ignore: cast_nullable_to_non_nullable
              as String,
      studentId: null == studentId
          ? _self.studentId
          : studentId // ignore: cast_nullable_to_non_nullable
              as String,
      instructorId: null == instructorId
          ? _self.instructorId
          : instructorId // ignore: cast_nullable_to_non_nullable
              as String,
      enrollmentId: freezed == enrollmentId
          ? _self.enrollmentId
          : enrollmentId // ignore: cast_nullable_to_non_nullable
              as String?,
      title: null == title
          ? _self.title
          : title // ignore: cast_nullable_to_non_nullable
              as String,
      studentNotes: null == studentNotes
          ? _self.studentNotes
          : studentNotes // ignore: cast_nullable_to_non_nullable
              as String,
      status: null == status
          ? _self.status
          : status // ignore: cast_nullable_to_non_nullable
              as FeedbackStatus,
      instructorResponse: freezed == instructorResponse
          ? _self.instructorResponse
          : instructorResponse // ignore: cast_nullable_to_non_nullable
              as String?,
      instructorResponseDate: freezed == instructorResponseDate
          ? _self.instructorResponseDate
          : instructorResponseDate // ignore: cast_nullable_to_non_nullable
              as DateTime?,
      respondedBy: freezed == respondedBy
          ? _self.respondedBy
          : respondedBy // ignore: cast_nullable_to_non_nullable
              as String?,
      feedbackDate: null == feedbackDate
          ? _self.feedbackDate
          : feedbackDate // ignore: cast_nullable_to_non_nullable
              as DateTime,
      createdAt: null == createdAt
          ? _self.createdAt
          : createdAt // ignore: cast_nullable_to_non_nullable
              as DateTime,
      updatedAt: null == updatedAt
          ? _self.updatedAt
          : updatedAt // ignore: cast_nullable_to_non_nullable
              as DateTime,
      photos: null == photos
          ? _self.photos
          : photos // ignore: cast_nullable_to_non_nullable
              as List<FeedbackPhoto>,
      instructorName: freezed == instructorName
          ? _self.instructorName
          : instructorName // ignore: cast_nullable_to_non_nullable
              as String?,
      studentName: freezed == studentName
          ? _self.studentName
          : studentName // ignore: cast_nullable_to_non_nullable
              as String?,
      daysWaiting: freezed == daysWaiting
          ? _self.daysWaiting
          : daysWaiting // ignore: cast_nullable_to_non_nullable
              as int?,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _Feedback implements Feedback {
  const _Feedback(
      {required this.id,
      required this.studentId,
      required this.instructorId,
      this.enrollmentId,
      required this.title,
      required this.studentNotes,
      this.status = FeedbackStatus.waiting,
      this.instructorResponse,
      this.instructorResponseDate,
      this.respondedBy,
      required this.feedbackDate,
      required this.createdAt,
      required this.updatedAt,
      final List<FeedbackPhoto> photos = const [],
      this.instructorName,
      this.studentName,
      this.daysWaiting})
      : _photos = photos;
  factory _Feedback.fromJson(Map<String, dynamic> json) =>
      _$FeedbackFromJson(json);

  @override
  final String id;
  @override
  final String studentId;
  @override
  final String instructorId;
  @override
  final String? enrollmentId;
// Content
  @override
  final String title;
  @override
  final String studentNotes;
// Status
  @override
  @JsonKey()
  final FeedbackStatus status;
// Instructor response
  @override
  final String? instructorResponse;
  @override
  final DateTime? instructorResponseDate;
  @override
  final String? respondedBy;
// Metadata
  @override
  final DateTime feedbackDate;
  @override
  final DateTime createdAt;
  @override
  final DateTime updatedAt;
// Photos
  final List<FeedbackPhoto> _photos;
// Photos
  @override
  @JsonKey()
  List<FeedbackPhoto> get photos {
    if (_photos is EqualUnmodifiableListView) return _photos;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(_photos);
  }

// Additional info (from joins)
  @override
  final String? instructorName;
  @override
  final String? studentName;
  @override
  final int? daysWaiting;

  /// Create a copy of Feedback
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  @pragma('vm:prefer-inline')
  _$FeedbackCopyWith<_Feedback> get copyWith =>
      __$FeedbackCopyWithImpl<_Feedback>(this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$FeedbackToJson(
      this,
    );
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _Feedback &&
            (identical(other.id, id) || other.id == id) &&
            (identical(other.studentId, studentId) ||
                other.studentId == studentId) &&
            (identical(other.instructorId, instructorId) ||
                other.instructorId == instructorId) &&
            (identical(other.enrollmentId, enrollmentId) ||
                other.enrollmentId == enrollmentId) &&
            (identical(other.title, title) || other.title == title) &&
            (identical(other.studentNotes, studentNotes) ||
                other.studentNotes == studentNotes) &&
            (identical(other.status, status) || other.status == status) &&
            (identical(other.instructorResponse, instructorResponse) ||
                other.instructorResponse == instructorResponse) &&
            (identical(other.instructorResponseDate, instructorResponseDate) ||
                other.instructorResponseDate == instructorResponseDate) &&
            (identical(other.respondedBy, respondedBy) ||
                other.respondedBy == respondedBy) &&
            (identical(other.feedbackDate, feedbackDate) ||
                other.feedbackDate == feedbackDate) &&
            (identical(other.createdAt, createdAt) ||
                other.createdAt == createdAt) &&
            (identical(other.updatedAt, updatedAt) ||
                other.updatedAt == updatedAt) &&
            const DeepCollectionEquality().equals(other._photos, _photos) &&
            (identical(other.instructorName, instructorName) ||
                other.instructorName == instructorName) &&
            (identical(other.studentName, studentName) ||
                other.studentName == studentName) &&
            (identical(other.daysWaiting, daysWaiting) ||
                other.daysWaiting == daysWaiting));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hash(
      runtimeType,
      id,
      studentId,
      instructorId,
      enrollmentId,
      title,
      studentNotes,
      status,
      instructorResponse,
      instructorResponseDate,
      respondedBy,
      feedbackDate,
      createdAt,
      updatedAt,
      const DeepCollectionEquality().hash(_photos),
      instructorName,
      studentName,
      daysWaiting);

  @override
  String toString() {
    return 'Feedback(id: $id, studentId: $studentId, instructorId: $instructorId, enrollmentId: $enrollmentId, title: $title, studentNotes: $studentNotes, status: $status, instructorResponse: $instructorResponse, instructorResponseDate: $instructorResponseDate, respondedBy: $respondedBy, feedbackDate: $feedbackDate, createdAt: $createdAt, updatedAt: $updatedAt, photos: $photos, instructorName: $instructorName, studentName: $studentName, daysWaiting: $daysWaiting)';
  }
}

/// @nodoc
abstract mixin class _$FeedbackCopyWith<$Res>
    implements $FeedbackCopyWith<$Res> {
  factory _$FeedbackCopyWith(_Feedback value, $Res Function(_Feedback) _then) =
      __$FeedbackCopyWithImpl;
  @override
  @useResult
  $Res call(
      {String id,
      String studentId,
      String instructorId,
      String? enrollmentId,
      String title,
      String studentNotes,
      FeedbackStatus status,
      String? instructorResponse,
      DateTime? instructorResponseDate,
      String? respondedBy,
      DateTime feedbackDate,
      DateTime createdAt,
      DateTime updatedAt,
      List<FeedbackPhoto> photos,
      String? instructorName,
      String? studentName,
      int? daysWaiting});
}

/// @nodoc
class __$FeedbackCopyWithImpl<$Res> implements _$FeedbackCopyWith<$Res> {
  __$FeedbackCopyWithImpl(this._self, this._then);

  final _Feedback _self;
  final $Res Function(_Feedback) _then;

  /// Create a copy of Feedback
  /// with the given fields replaced by the non-null parameter values.
  @override
  @pragma('vm:prefer-inline')
  $Res call({
    Object? id = null,
    Object? studentId = null,
    Object? instructorId = null,
    Object? enrollmentId = freezed,
    Object? title = null,
    Object? studentNotes = null,
    Object? status = null,
    Object? instructorResponse = freezed,
    Object? instructorResponseDate = freezed,
    Object? respondedBy = freezed,
    Object? feedbackDate = null,
    Object? createdAt = null,
    Object? updatedAt = null,
    Object? photos = null,
    Object? instructorName = freezed,
    Object? studentName = freezed,
    Object? daysWaiting = freezed,
  }) {
    return _then(_Feedback(
      id: null == id
          ? _self.id
          : id // ignore: cast_nullable_to_non_nullable
              as String,
      studentId: null == studentId
          ? _self.studentId
          : studentId // ignore: cast_nullable_to_non_nullable
              as String,
      instructorId: null == instructorId
          ? _self.instructorId
          : instructorId // ignore: cast_nullable_to_non_nullable
              as String,
      enrollmentId: freezed == enrollmentId
          ? _self.enrollmentId
          : enrollmentId // ignore: cast_nullable_to_non_nullable
              as String?,
      title: null == title
          ? _self.title
          : title // ignore: cast_nullable_to_non_nullable
              as String,
      studentNotes: null == studentNotes
          ? _self.studentNotes
          : studentNotes // ignore: cast_nullable_to_non_nullable
              as String,
      status: null == status
          ? _self.status
          : status // ignore: cast_nullable_to_non_nullable
              as FeedbackStatus,
      instructorResponse: freezed == instructorResponse
          ? _self.instructorResponse
          : instructorResponse // ignore: cast_nullable_to_non_nullable
              as String?,
      instructorResponseDate: freezed == instructorResponseDate
          ? _self.instructorResponseDate
          : instructorResponseDate // ignore: cast_nullable_to_non_nullable
              as DateTime?,
      respondedBy: freezed == respondedBy
          ? _self.respondedBy
          : respondedBy // ignore: cast_nullable_to_non_nullable
              as String?,
      feedbackDate: null == feedbackDate
          ? _self.feedbackDate
          : feedbackDate // ignore: cast_nullable_to_non_nullable
              as DateTime,
      createdAt: null == createdAt
          ? _self.createdAt
          : createdAt // ignore: cast_nullable_to_non_nullable
              as DateTime,
      updatedAt: null == updatedAt
          ? _self.updatedAt
          : updatedAt // ignore: cast_nullable_to_non_nullable
              as DateTime,
      photos: null == photos
          ? _self._photos
          : photos // ignore: cast_nullable_to_non_nullable
              as List<FeedbackPhoto>,
      instructorName: freezed == instructorName
          ? _self.instructorName
          : instructorName // ignore: cast_nullable_to_non_nullable
              as String?,
      studentName: freezed == studentName
          ? _self.studentName
          : studentName // ignore: cast_nullable_to_non_nullable
              as String?,
      daysWaiting: freezed == daysWaiting
          ? _self.daysWaiting
          : daysWaiting // ignore: cast_nullable_to_non_nullable
              as int?,
    ));
  }
}

/// @nodoc
mixin _$FeedbackForm {
  String get title;
  String get studentNotes;
  List<FeedbackPhotoUpload> get photos;
  String? get instructorId;
  String? get enrollmentId;

  /// Create a copy of FeedbackForm
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @pragma('vm:prefer-inline')
  $FeedbackFormCopyWith<FeedbackForm> get copyWith =>
      _$FeedbackFormCopyWithImpl<FeedbackForm>(
          this as FeedbackForm, _$identity);

  /// Serializes this FeedbackForm to a JSON map.
  Map<String, dynamic> toJson();

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is FeedbackForm &&
            (identical(other.title, title) || other.title == title) &&
            (identical(other.studentNotes, studentNotes) ||
                other.studentNotes == studentNotes) &&
            const DeepCollectionEquality().equals(other.photos, photos) &&
            (identical(other.instructorId, instructorId) ||
                other.instructorId == instructorId) &&
            (identical(other.enrollmentId, enrollmentId) ||
                other.enrollmentId == enrollmentId));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hash(runtimeType, title, studentNotes,
      const DeepCollectionEquality().hash(photos), instructorId, enrollmentId);

  @override
  String toString() {
    return 'FeedbackForm(title: $title, studentNotes: $studentNotes, photos: $photos, instructorId: $instructorId, enrollmentId: $enrollmentId)';
  }
}

/// @nodoc
abstract mixin class $FeedbackFormCopyWith<$Res> {
  factory $FeedbackFormCopyWith(
          FeedbackForm value, $Res Function(FeedbackForm) _then) =
      _$FeedbackFormCopyWithImpl;
  @useResult
  $Res call(
      {String title,
      String studentNotes,
      List<FeedbackPhotoUpload> photos,
      String? instructorId,
      String? enrollmentId});
}

/// @nodoc
class _$FeedbackFormCopyWithImpl<$Res> implements $FeedbackFormCopyWith<$Res> {
  _$FeedbackFormCopyWithImpl(this._self, this._then);

  final FeedbackForm _self;
  final $Res Function(FeedbackForm) _then;

  /// Create a copy of FeedbackForm
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? title = null,
    Object? studentNotes = null,
    Object? photos = null,
    Object? instructorId = freezed,
    Object? enrollmentId = freezed,
  }) {
    return _then(_self.copyWith(
      title: null == title
          ? _self.title
          : title // ignore: cast_nullable_to_non_nullable
              as String,
      studentNotes: null == studentNotes
          ? _self.studentNotes
          : studentNotes // ignore: cast_nullable_to_non_nullable
              as String,
      photos: null == photos
          ? _self.photos
          : photos // ignore: cast_nullable_to_non_nullable
              as List<FeedbackPhotoUpload>,
      instructorId: freezed == instructorId
          ? _self.instructorId
          : instructorId // ignore: cast_nullable_to_non_nullable
              as String?,
      enrollmentId: freezed == enrollmentId
          ? _self.enrollmentId
          : enrollmentId // ignore: cast_nullable_to_non_nullable
              as String?,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _FeedbackForm implements FeedbackForm {
  const _FeedbackForm(
      {this.title = '',
      this.studentNotes = '',
      final List<FeedbackPhotoUpload> photos = const [],
      this.instructorId,
      this.enrollmentId})
      : _photos = photos;
  factory _FeedbackForm.fromJson(Map<String, dynamic> json) =>
      _$FeedbackFormFromJson(json);

  @override
  @JsonKey()
  final String title;
  @override
  @JsonKey()
  final String studentNotes;
  final List<FeedbackPhotoUpload> _photos;
  @override
  @JsonKey()
  List<FeedbackPhotoUpload> get photos {
    if (_photos is EqualUnmodifiableListView) return _photos;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(_photos);
  }

  @override
  final String? instructorId;
  @override
  final String? enrollmentId;

  /// Create a copy of FeedbackForm
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  @pragma('vm:prefer-inline')
  _$FeedbackFormCopyWith<_FeedbackForm> get copyWith =>
      __$FeedbackFormCopyWithImpl<_FeedbackForm>(this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$FeedbackFormToJson(
      this,
    );
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _FeedbackForm &&
            (identical(other.title, title) || other.title == title) &&
            (identical(other.studentNotes, studentNotes) ||
                other.studentNotes == studentNotes) &&
            const DeepCollectionEquality().equals(other._photos, _photos) &&
            (identical(other.instructorId, instructorId) ||
                other.instructorId == instructorId) &&
            (identical(other.enrollmentId, enrollmentId) ||
                other.enrollmentId == enrollmentId));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hash(runtimeType, title, studentNotes,
      const DeepCollectionEquality().hash(_photos), instructorId, enrollmentId);

  @override
  String toString() {
    return 'FeedbackForm(title: $title, studentNotes: $studentNotes, photos: $photos, instructorId: $instructorId, enrollmentId: $enrollmentId)';
  }
}

/// @nodoc
abstract mixin class _$FeedbackFormCopyWith<$Res>
    implements $FeedbackFormCopyWith<$Res> {
  factory _$FeedbackFormCopyWith(
          _FeedbackForm value, $Res Function(_FeedbackForm) _then) =
      __$FeedbackFormCopyWithImpl;
  @override
  @useResult
  $Res call(
      {String title,
      String studentNotes,
      List<FeedbackPhotoUpload> photos,
      String? instructorId,
      String? enrollmentId});
}

/// @nodoc
class __$FeedbackFormCopyWithImpl<$Res>
    implements _$FeedbackFormCopyWith<$Res> {
  __$FeedbackFormCopyWithImpl(this._self, this._then);

  final _FeedbackForm _self;
  final $Res Function(_FeedbackForm) _then;

  /// Create a copy of FeedbackForm
  /// with the given fields replaced by the non-null parameter values.
  @override
  @pragma('vm:prefer-inline')
  $Res call({
    Object? title = null,
    Object? studentNotes = null,
    Object? photos = null,
    Object? instructorId = freezed,
    Object? enrollmentId = freezed,
  }) {
    return _then(_FeedbackForm(
      title: null == title
          ? _self.title
          : title // ignore: cast_nullable_to_non_nullable
              as String,
      studentNotes: null == studentNotes
          ? _self.studentNotes
          : studentNotes // ignore: cast_nullable_to_non_nullable
              as String,
      photos: null == photos
          ? _self._photos
          : photos // ignore: cast_nullable_to_non_nullable
              as List<FeedbackPhotoUpload>,
      instructorId: freezed == instructorId
          ? _self.instructorId
          : instructorId // ignore: cast_nullable_to_non_nullable
              as String?,
      enrollmentId: freezed == enrollmentId
          ? _self.enrollmentId
          : enrollmentId // ignore: cast_nullable_to_non_nullable
              as String?,
    ));
  }
}

/// @nodoc
mixin _$FeedbackPhotoUpload {
  FeedbackPhotoType get type;
  String? get uploadedUrl;
  bool get isUploading;
  double? get uploadProgress;

  /// Create a copy of FeedbackPhotoUpload
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @pragma('vm:prefer-inline')
  $FeedbackPhotoUploadCopyWith<FeedbackPhotoUpload> get copyWith =>
      _$FeedbackPhotoUploadCopyWithImpl<FeedbackPhotoUpload>(
          this as FeedbackPhotoUpload, _$identity);

  /// Serializes this FeedbackPhotoUpload to a JSON map.
  Map<String, dynamic> toJson();

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is FeedbackPhotoUpload &&
            (identical(other.type, type) || other.type == type) &&
            (identical(other.uploadedUrl, uploadedUrl) ||
                other.uploadedUrl == uploadedUrl) &&
            (identical(other.isUploading, isUploading) ||
                other.isUploading == isUploading) &&
            (identical(other.uploadProgress, uploadProgress) ||
                other.uploadProgress == uploadProgress));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode =>
      Object.hash(runtimeType, type, uploadedUrl, isUploading, uploadProgress);

  @override
  String toString() {
    return 'FeedbackPhotoUpload(type: $type, uploadedUrl: $uploadedUrl, isUploading: $isUploading, uploadProgress: $uploadProgress)';
  }
}

/// @nodoc
abstract mixin class $FeedbackPhotoUploadCopyWith<$Res> {
  factory $FeedbackPhotoUploadCopyWith(
          FeedbackPhotoUpload value, $Res Function(FeedbackPhotoUpload) _then) =
      _$FeedbackPhotoUploadCopyWithImpl;
  @useResult
  $Res call(
      {FeedbackPhotoType type,
      String? uploadedUrl,
      bool isUploading,
      double? uploadProgress});
}

/// @nodoc
class _$FeedbackPhotoUploadCopyWithImpl<$Res>
    implements $FeedbackPhotoUploadCopyWith<$Res> {
  _$FeedbackPhotoUploadCopyWithImpl(this._self, this._then);

  final FeedbackPhotoUpload _self;
  final $Res Function(FeedbackPhotoUpload) _then;

  /// Create a copy of FeedbackPhotoUpload
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? type = null,
    Object? uploadedUrl = freezed,
    Object? isUploading = null,
    Object? uploadProgress = freezed,
  }) {
    return _then(_self.copyWith(
      type: null == type
          ? _self.type
          : type // ignore: cast_nullable_to_non_nullable
              as FeedbackPhotoType,
      uploadedUrl: freezed == uploadedUrl
          ? _self.uploadedUrl
          : uploadedUrl // ignore: cast_nullable_to_non_nullable
              as String?,
      isUploading: null == isUploading
          ? _self.isUploading
          : isUploading // ignore: cast_nullable_to_non_nullable
              as bool,
      uploadProgress: freezed == uploadProgress
          ? _self.uploadProgress
          : uploadProgress // ignore: cast_nullable_to_non_nullable
              as double?,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _FeedbackPhotoUpload implements FeedbackPhotoUpload {
  const _FeedbackPhotoUpload(
      {required this.type,
      this.uploadedUrl,
      this.isUploading = false,
      this.uploadProgress});
  factory _FeedbackPhotoUpload.fromJson(Map<String, dynamic> json) =>
      _$FeedbackPhotoUploadFromJson(json);

  @override
  final FeedbackPhotoType type;
  @override
  final String? uploadedUrl;
  @override
  @JsonKey()
  final bool isUploading;
  @override
  final double? uploadProgress;

  /// Create a copy of FeedbackPhotoUpload
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  @pragma('vm:prefer-inline')
  _$FeedbackPhotoUploadCopyWith<_FeedbackPhotoUpload> get copyWith =>
      __$FeedbackPhotoUploadCopyWithImpl<_FeedbackPhotoUpload>(
          this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$FeedbackPhotoUploadToJson(
      this,
    );
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _FeedbackPhotoUpload &&
            (identical(other.type, type) || other.type == type) &&
            (identical(other.uploadedUrl, uploadedUrl) ||
                other.uploadedUrl == uploadedUrl) &&
            (identical(other.isUploading, isUploading) ||
                other.isUploading == isUploading) &&
            (identical(other.uploadProgress, uploadProgress) ||
                other.uploadProgress == uploadProgress));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode =>
      Object.hash(runtimeType, type, uploadedUrl, isUploading, uploadProgress);

  @override
  String toString() {
    return 'FeedbackPhotoUpload(type: $type, uploadedUrl: $uploadedUrl, isUploading: $isUploading, uploadProgress: $uploadProgress)';
  }
}

/// @nodoc
abstract mixin class _$FeedbackPhotoUploadCopyWith<$Res>
    implements $FeedbackPhotoUploadCopyWith<$Res> {
  factory _$FeedbackPhotoUploadCopyWith(_FeedbackPhotoUpload value,
          $Res Function(_FeedbackPhotoUpload) _then) =
      __$FeedbackPhotoUploadCopyWithImpl;
  @override
  @useResult
  $Res call(
      {FeedbackPhotoType type,
      String? uploadedUrl,
      bool isUploading,
      double? uploadProgress});
}

/// @nodoc
class __$FeedbackPhotoUploadCopyWithImpl<$Res>
    implements _$FeedbackPhotoUploadCopyWith<$Res> {
  __$FeedbackPhotoUploadCopyWithImpl(this._self, this._then);

  final _FeedbackPhotoUpload _self;
  final $Res Function(_FeedbackPhotoUpload) _then;

  /// Create a copy of FeedbackPhotoUpload
  /// with the given fields replaced by the non-null parameter values.
  @override
  @pragma('vm:prefer-inline')
  $Res call({
    Object? type = null,
    Object? uploadedUrl = freezed,
    Object? isUploading = null,
    Object? uploadProgress = freezed,
  }) {
    return _then(_FeedbackPhotoUpload(
      type: null == type
          ? _self.type
          : type // ignore: cast_nullable_to_non_nullable
              as FeedbackPhotoType,
      uploadedUrl: freezed == uploadedUrl
          ? _self.uploadedUrl
          : uploadedUrl // ignore: cast_nullable_to_non_nullable
              as String?,
      isUploading: null == isUploading
          ? _self.isUploading
          : isUploading // ignore: cast_nullable_to_non_nullable
              as bool,
      uploadProgress: freezed == uploadProgress
          ? _self.uploadProgress
          : uploadProgress // ignore: cast_nullable_to_non_nullable
              as double?,
    ));
  }
}

/// @nodoc
mixin _$FeedbackListItem {
  String get id;
  String get title;
  FeedbackStatus get status;
  DateTime get feedbackDate;
  String? get instructorName;
  String? get studentName;
  bool get hasResponse;
  int get photoCount;
  int? get daysWaiting;

  /// Create a copy of FeedbackListItem
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @pragma('vm:prefer-inline')
  $FeedbackListItemCopyWith<FeedbackListItem> get copyWith =>
      _$FeedbackListItemCopyWithImpl<FeedbackListItem>(
          this as FeedbackListItem, _$identity);

  /// Serializes this FeedbackListItem to a JSON map.
  Map<String, dynamic> toJson();

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is FeedbackListItem &&
            (identical(other.id, id) || other.id == id) &&
            (identical(other.title, title) || other.title == title) &&
            (identical(other.status, status) || other.status == status) &&
            (identical(other.feedbackDate, feedbackDate) ||
                other.feedbackDate == feedbackDate) &&
            (identical(other.instructorName, instructorName) ||
                other.instructorName == instructorName) &&
            (identical(other.studentName, studentName) ||
                other.studentName == studentName) &&
            (identical(other.hasResponse, hasResponse) ||
                other.hasResponse == hasResponse) &&
            (identical(other.photoCount, photoCount) ||
                other.photoCount == photoCount) &&
            (identical(other.daysWaiting, daysWaiting) ||
                other.daysWaiting == daysWaiting));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hash(runtimeType, id, title, status, feedbackDate,
      instructorName, studentName, hasResponse, photoCount, daysWaiting);

  @override
  String toString() {
    return 'FeedbackListItem(id: $id, title: $title, status: $status, feedbackDate: $feedbackDate, instructorName: $instructorName, studentName: $studentName, hasResponse: $hasResponse, photoCount: $photoCount, daysWaiting: $daysWaiting)';
  }
}

/// @nodoc
abstract mixin class $FeedbackListItemCopyWith<$Res> {
  factory $FeedbackListItemCopyWith(
          FeedbackListItem value, $Res Function(FeedbackListItem) _then) =
      _$FeedbackListItemCopyWithImpl;
  @useResult
  $Res call(
      {String id,
      String title,
      FeedbackStatus status,
      DateTime feedbackDate,
      String? instructorName,
      String? studentName,
      bool hasResponse,
      int photoCount,
      int? daysWaiting});
}

/// @nodoc
class _$FeedbackListItemCopyWithImpl<$Res>
    implements $FeedbackListItemCopyWith<$Res> {
  _$FeedbackListItemCopyWithImpl(this._self, this._then);

  final FeedbackListItem _self;
  final $Res Function(FeedbackListItem) _then;

  /// Create a copy of FeedbackListItem
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? id = null,
    Object? title = null,
    Object? status = null,
    Object? feedbackDate = null,
    Object? instructorName = freezed,
    Object? studentName = freezed,
    Object? hasResponse = null,
    Object? photoCount = null,
    Object? daysWaiting = freezed,
  }) {
    return _then(_self.copyWith(
      id: null == id
          ? _self.id
          : id // ignore: cast_nullable_to_non_nullable
              as String,
      title: null == title
          ? _self.title
          : title // ignore: cast_nullable_to_non_nullable
              as String,
      status: null == status
          ? _self.status
          : status // ignore: cast_nullable_to_non_nullable
              as FeedbackStatus,
      feedbackDate: null == feedbackDate
          ? _self.feedbackDate
          : feedbackDate // ignore: cast_nullable_to_non_nullable
              as DateTime,
      instructorName: freezed == instructorName
          ? _self.instructorName
          : instructorName // ignore: cast_nullable_to_non_nullable
              as String?,
      studentName: freezed == studentName
          ? _self.studentName
          : studentName // ignore: cast_nullable_to_non_nullable
              as String?,
      hasResponse: null == hasResponse
          ? _self.hasResponse
          : hasResponse // ignore: cast_nullable_to_non_nullable
              as bool,
      photoCount: null == photoCount
          ? _self.photoCount
          : photoCount // ignore: cast_nullable_to_non_nullable
              as int,
      daysWaiting: freezed == daysWaiting
          ? _self.daysWaiting
          : daysWaiting // ignore: cast_nullable_to_non_nullable
              as int?,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _FeedbackListItem implements FeedbackListItem {
  const _FeedbackListItem(
      {required this.id,
      required this.title,
      required this.status,
      required this.feedbackDate,
      this.instructorName,
      this.studentName,
      this.hasResponse = false,
      this.photoCount = 0,
      this.daysWaiting});
  factory _FeedbackListItem.fromJson(Map<String, dynamic> json) =>
      _$FeedbackListItemFromJson(json);

  @override
  final String id;
  @override
  final String title;
  @override
  final FeedbackStatus status;
  @override
  final DateTime feedbackDate;
  @override
  final String? instructorName;
  @override
  final String? studentName;
  @override
  @JsonKey()
  final bool hasResponse;
  @override
  @JsonKey()
  final int photoCount;
  @override
  final int? daysWaiting;

  /// Create a copy of FeedbackListItem
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  @pragma('vm:prefer-inline')
  _$FeedbackListItemCopyWith<_FeedbackListItem> get copyWith =>
      __$FeedbackListItemCopyWithImpl<_FeedbackListItem>(this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$FeedbackListItemToJson(
      this,
    );
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _FeedbackListItem &&
            (identical(other.id, id) || other.id == id) &&
            (identical(other.title, title) || other.title == title) &&
            (identical(other.status, status) || other.status == status) &&
            (identical(other.feedbackDate, feedbackDate) ||
                other.feedbackDate == feedbackDate) &&
            (identical(other.instructorName, instructorName) ||
                other.instructorName == instructorName) &&
            (identical(other.studentName, studentName) ||
                other.studentName == studentName) &&
            (identical(other.hasResponse, hasResponse) ||
                other.hasResponse == hasResponse) &&
            (identical(other.photoCount, photoCount) ||
                other.photoCount == photoCount) &&
            (identical(other.daysWaiting, daysWaiting) ||
                other.daysWaiting == daysWaiting));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hash(runtimeType, id, title, status, feedbackDate,
      instructorName, studentName, hasResponse, photoCount, daysWaiting);

  @override
  String toString() {
    return 'FeedbackListItem(id: $id, title: $title, status: $status, feedbackDate: $feedbackDate, instructorName: $instructorName, studentName: $studentName, hasResponse: $hasResponse, photoCount: $photoCount, daysWaiting: $daysWaiting)';
  }
}

/// @nodoc
abstract mixin class _$FeedbackListItemCopyWith<$Res>
    implements $FeedbackListItemCopyWith<$Res> {
  factory _$FeedbackListItemCopyWith(
          _FeedbackListItem value, $Res Function(_FeedbackListItem) _then) =
      __$FeedbackListItemCopyWithImpl;
  @override
  @useResult
  $Res call(
      {String id,
      String title,
      FeedbackStatus status,
      DateTime feedbackDate,
      String? instructorName,
      String? studentName,
      bool hasResponse,
      int photoCount,
      int? daysWaiting});
}

/// @nodoc
class __$FeedbackListItemCopyWithImpl<$Res>
    implements _$FeedbackListItemCopyWith<$Res> {
  __$FeedbackListItemCopyWithImpl(this._self, this._then);

  final _FeedbackListItem _self;
  final $Res Function(_FeedbackListItem) _then;

  /// Create a copy of FeedbackListItem
  /// with the given fields replaced by the non-null parameter values.
  @override
  @pragma('vm:prefer-inline')
  $Res call({
    Object? id = null,
    Object? title = null,
    Object? status = null,
    Object? feedbackDate = null,
    Object? instructorName = freezed,
    Object? studentName = freezed,
    Object? hasResponse = null,
    Object? photoCount = null,
    Object? daysWaiting = freezed,
  }) {
    return _then(_FeedbackListItem(
      id: null == id
          ? _self.id
          : id // ignore: cast_nullable_to_non_nullable
              as String,
      title: null == title
          ? _self.title
          : title // ignore: cast_nullable_to_non_nullable
              as String,
      status: null == status
          ? _self.status
          : status // ignore: cast_nullable_to_non_nullable
              as FeedbackStatus,
      feedbackDate: null == feedbackDate
          ? _self.feedbackDate
          : feedbackDate // ignore: cast_nullable_to_non_nullable
              as DateTime,
      instructorName: freezed == instructorName
          ? _self.instructorName
          : instructorName // ignore: cast_nullable_to_non_nullable
              as String?,
      studentName: freezed == studentName
          ? _self.studentName
          : studentName // ignore: cast_nullable_to_non_nullable
              as String?,
      hasResponse: null == hasResponse
          ? _self.hasResponse
          : hasResponse // ignore: cast_nullable_to_non_nullable
              as bool,
      photoCount: null == photoCount
          ? _self.photoCount
          : photoCount // ignore: cast_nullable_to_non_nullable
              as int,
      daysWaiting: freezed == daysWaiting
          ? _self.daysWaiting
          : daysWaiting // ignore: cast_nullable_to_non_nullable
              as int?,
    ));
  }
}

/// @nodoc
mixin _$FeedbackResponseForm {
  String get feedbackId;
  String get response;
  bool get isSubmitting;

  /// Create a copy of FeedbackResponseForm
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @pragma('vm:prefer-inline')
  $FeedbackResponseFormCopyWith<FeedbackResponseForm> get copyWith =>
      _$FeedbackResponseFormCopyWithImpl<FeedbackResponseForm>(
          this as FeedbackResponseForm, _$identity);

  /// Serializes this FeedbackResponseForm to a JSON map.
  Map<String, dynamic> toJson();

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is FeedbackResponseForm &&
            (identical(other.feedbackId, feedbackId) ||
                other.feedbackId == feedbackId) &&
            (identical(other.response, response) ||
                other.response == response) &&
            (identical(other.isSubmitting, isSubmitting) ||
                other.isSubmitting == isSubmitting));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode =>
      Object.hash(runtimeType, feedbackId, response, isSubmitting);

  @override
  String toString() {
    return 'FeedbackResponseForm(feedbackId: $feedbackId, response: $response, isSubmitting: $isSubmitting)';
  }
}

/// @nodoc
abstract mixin class $FeedbackResponseFormCopyWith<$Res> {
  factory $FeedbackResponseFormCopyWith(FeedbackResponseForm value,
          $Res Function(FeedbackResponseForm) _then) =
      _$FeedbackResponseFormCopyWithImpl;
  @useResult
  $Res call({String feedbackId, String response, bool isSubmitting});
}

/// @nodoc
class _$FeedbackResponseFormCopyWithImpl<$Res>
    implements $FeedbackResponseFormCopyWith<$Res> {
  _$FeedbackResponseFormCopyWithImpl(this._self, this._then);

  final FeedbackResponseForm _self;
  final $Res Function(FeedbackResponseForm) _then;

  /// Create a copy of FeedbackResponseForm
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? feedbackId = null,
    Object? response = null,
    Object? isSubmitting = null,
  }) {
    return _then(_self.copyWith(
      feedbackId: null == feedbackId
          ? _self.feedbackId
          : feedbackId // ignore: cast_nullable_to_non_nullable
              as String,
      response: null == response
          ? _self.response
          : response // ignore: cast_nullable_to_non_nullable
              as String,
      isSubmitting: null == isSubmitting
          ? _self.isSubmitting
          : isSubmitting // ignore: cast_nullable_to_non_nullable
              as bool,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _FeedbackResponseForm implements FeedbackResponseForm {
  const _FeedbackResponseForm(
      {required this.feedbackId,
      this.response = '',
      this.isSubmitting = false});
  factory _FeedbackResponseForm.fromJson(Map<String, dynamic> json) =>
      _$FeedbackResponseFormFromJson(json);

  @override
  final String feedbackId;
  @override
  @JsonKey()
  final String response;
  @override
  @JsonKey()
  final bool isSubmitting;

  /// Create a copy of FeedbackResponseForm
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  @pragma('vm:prefer-inline')
  _$FeedbackResponseFormCopyWith<_FeedbackResponseForm> get copyWith =>
      __$FeedbackResponseFormCopyWithImpl<_FeedbackResponseForm>(
          this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$FeedbackResponseFormToJson(
      this,
    );
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _FeedbackResponseForm &&
            (identical(other.feedbackId, feedbackId) ||
                other.feedbackId == feedbackId) &&
            (identical(other.response, response) ||
                other.response == response) &&
            (identical(other.isSubmitting, isSubmitting) ||
                other.isSubmitting == isSubmitting));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode =>
      Object.hash(runtimeType, feedbackId, response, isSubmitting);

  @override
  String toString() {
    return 'FeedbackResponseForm(feedbackId: $feedbackId, response: $response, isSubmitting: $isSubmitting)';
  }
}

/// @nodoc
abstract mixin class _$FeedbackResponseFormCopyWith<$Res>
    implements $FeedbackResponseFormCopyWith<$Res> {
  factory _$FeedbackResponseFormCopyWith(_FeedbackResponseForm value,
          $Res Function(_FeedbackResponseForm) _then) =
      __$FeedbackResponseFormCopyWithImpl;
  @override
  @useResult
  $Res call({String feedbackId, String response, bool isSubmitting});
}

/// @nodoc
class __$FeedbackResponseFormCopyWithImpl<$Res>
    implements _$FeedbackResponseFormCopyWith<$Res> {
  __$FeedbackResponseFormCopyWithImpl(this._self, this._then);

  final _FeedbackResponseForm _self;
  final $Res Function(_FeedbackResponseForm) _then;

  /// Create a copy of FeedbackResponseForm
  /// with the given fields replaced by the non-null parameter values.
  @override
  @pragma('vm:prefer-inline')
  $Res call({
    Object? feedbackId = null,
    Object? response = null,
    Object? isSubmitting = null,
  }) {
    return _then(_FeedbackResponseForm(
      feedbackId: null == feedbackId
          ? _self.feedbackId
          : feedbackId // ignore: cast_nullable_to_non_nullable
              as String,
      response: null == response
          ? _self.response
          : response // ignore: cast_nullable_to_non_nullable
              as String,
      isSubmitting: null == isSubmitting
          ? _self.isSubmitting
          : isSubmitting // ignore: cast_nullable_to_non_nullable
              as bool,
    ));
  }
}

/// @nodoc
mixin _$FeedbackSubmissionResult {
  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType && other is FeedbackSubmissionResult);
  }

  @override
  int get hashCode => runtimeType.hashCode;

  @override
  String toString() {
    return 'FeedbackSubmissionResult()';
  }
}

/// @nodoc
class $FeedbackSubmissionResultCopyWith<$Res> {
  $FeedbackSubmissionResultCopyWith(
      FeedbackSubmissionResult _, $Res Function(FeedbackSubmissionResult) __);
}

/// @nodoc

class _Success implements FeedbackSubmissionResult {
  const _Success(this.feedbackId);

  final String feedbackId;

  /// Create a copy of FeedbackSubmissionResult
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @pragma('vm:prefer-inline')
  _$SuccessCopyWith<_Success> get copyWith =>
      __$SuccessCopyWithImpl<_Success>(this, _$identity);

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _Success &&
            (identical(other.feedbackId, feedbackId) ||
                other.feedbackId == feedbackId));
  }

  @override
  int get hashCode => Object.hash(runtimeType, feedbackId);

  @override
  String toString() {
    return 'FeedbackSubmissionResult.success(feedbackId: $feedbackId)';
  }
}

/// @nodoc
abstract mixin class _$SuccessCopyWith<$Res>
    implements $FeedbackSubmissionResultCopyWith<$Res> {
  factory _$SuccessCopyWith(_Success value, $Res Function(_Success) _then) =
      __$SuccessCopyWithImpl;
  @useResult
  $Res call({String feedbackId});
}

/// @nodoc
class __$SuccessCopyWithImpl<$Res> implements _$SuccessCopyWith<$Res> {
  __$SuccessCopyWithImpl(this._self, this._then);

  final _Success _self;
  final $Res Function(_Success) _then;

  /// Create a copy of FeedbackSubmissionResult
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  $Res call({
    Object? feedbackId = null,
  }) {
    return _then(_Success(
      null == feedbackId
          ? _self.feedbackId
          : feedbackId // ignore: cast_nullable_to_non_nullable
              as String,
    ));
  }
}

/// @nodoc

class _Failure implements FeedbackSubmissionResult {
  const _Failure(this.error);

  final String error;

  /// Create a copy of FeedbackSubmissionResult
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @pragma('vm:prefer-inline')
  _$FailureCopyWith<_Failure> get copyWith =>
      __$FailureCopyWithImpl<_Failure>(this, _$identity);

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _Failure &&
            (identical(other.error, error) || other.error == error));
  }

  @override
  int get hashCode => Object.hash(runtimeType, error);

  @override
  String toString() {
    return 'FeedbackSubmissionResult.failure(error: $error)';
  }
}

/// @nodoc
abstract mixin class _$FailureCopyWith<$Res>
    implements $FeedbackSubmissionResultCopyWith<$Res> {
  factory _$FailureCopyWith(_Failure value, $Res Function(_Failure) _then) =
      __$FailureCopyWithImpl;
  @useResult
  $Res call({String error});
}

/// @nodoc
class __$FailureCopyWithImpl<$Res> implements _$FailureCopyWith<$Res> {
  __$FailureCopyWithImpl(this._self, this._then);

  final _Failure _self;
  final $Res Function(_Failure) _then;

  /// Create a copy of FeedbackSubmissionResult
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  $Res call({
    Object? error = null,
  }) {
    return _then(_Failure(
      null == error
          ? _self.error
          : error // ignore: cast_nullable_to_non_nullable
              as String,
    ));
  }
}

/// @nodoc
mixin _$FeedbackState {
  List<FeedbackListItem> get feedbacks;
  bool get isLoading;
  bool get isLoadingMore;
  bool get hasMore;
  String? get error;
  int? get currentPage;

  /// Create a copy of FeedbackState
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @pragma('vm:prefer-inline')
  $FeedbackStateCopyWith<FeedbackState> get copyWith =>
      _$FeedbackStateCopyWithImpl<FeedbackState>(
          this as FeedbackState, _$identity);

  /// Serializes this FeedbackState to a JSON map.
  Map<String, dynamic> toJson();

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is FeedbackState &&
            const DeepCollectionEquality().equals(other.feedbacks, feedbacks) &&
            (identical(other.isLoading, isLoading) ||
                other.isLoading == isLoading) &&
            (identical(other.isLoadingMore, isLoadingMore) ||
                other.isLoadingMore == isLoadingMore) &&
            (identical(other.hasMore, hasMore) || other.hasMore == hasMore) &&
            (identical(other.error, error) || other.error == error) &&
            (identical(other.currentPage, currentPage) ||
                other.currentPage == currentPage));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hash(
      runtimeType,
      const DeepCollectionEquality().hash(feedbacks),
      isLoading,
      isLoadingMore,
      hasMore,
      error,
      currentPage);

  @override
  String toString() {
    return 'FeedbackState(feedbacks: $feedbacks, isLoading: $isLoading, isLoadingMore: $isLoadingMore, hasMore: $hasMore, error: $error, currentPage: $currentPage)';
  }
}

/// @nodoc
abstract mixin class $FeedbackStateCopyWith<$Res> {
  factory $FeedbackStateCopyWith(
          FeedbackState value, $Res Function(FeedbackState) _then) =
      _$FeedbackStateCopyWithImpl;
  @useResult
  $Res call(
      {List<FeedbackListItem> feedbacks,
      bool isLoading,
      bool isLoadingMore,
      bool hasMore,
      String? error,
      int? currentPage});
}

/// @nodoc
class _$FeedbackStateCopyWithImpl<$Res>
    implements $FeedbackStateCopyWith<$Res> {
  _$FeedbackStateCopyWithImpl(this._self, this._then);

  final FeedbackState _self;
  final $Res Function(FeedbackState) _then;

  /// Create a copy of FeedbackState
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? feedbacks = null,
    Object? isLoading = null,
    Object? isLoadingMore = null,
    Object? hasMore = null,
    Object? error = freezed,
    Object? currentPage = freezed,
  }) {
    return _then(_self.copyWith(
      feedbacks: null == feedbacks
          ? _self.feedbacks
          : feedbacks // ignore: cast_nullable_to_non_nullable
              as List<FeedbackListItem>,
      isLoading: null == isLoading
          ? _self.isLoading
          : isLoading // ignore: cast_nullable_to_non_nullable
              as bool,
      isLoadingMore: null == isLoadingMore
          ? _self.isLoadingMore
          : isLoadingMore // ignore: cast_nullable_to_non_nullable
              as bool,
      hasMore: null == hasMore
          ? _self.hasMore
          : hasMore // ignore: cast_nullable_to_non_nullable
              as bool,
      error: freezed == error
          ? _self.error
          : error // ignore: cast_nullable_to_non_nullable
              as String?,
      currentPage: freezed == currentPage
          ? _self.currentPage
          : currentPage // ignore: cast_nullable_to_non_nullable
              as int?,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _FeedbackState implements FeedbackState {
  const _FeedbackState(
      {final List<FeedbackListItem> feedbacks = const [],
      this.isLoading = false,
      this.isLoadingMore = false,
      this.hasMore = false,
      this.error,
      this.currentPage})
      : _feedbacks = feedbacks;
  factory _FeedbackState.fromJson(Map<String, dynamic> json) =>
      _$FeedbackStateFromJson(json);

  final List<FeedbackListItem> _feedbacks;
  @override
  @JsonKey()
  List<FeedbackListItem> get feedbacks {
    if (_feedbacks is EqualUnmodifiableListView) return _feedbacks;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(_feedbacks);
  }

  @override
  @JsonKey()
  final bool isLoading;
  @override
  @JsonKey()
  final bool isLoadingMore;
  @override
  @JsonKey()
  final bool hasMore;
  @override
  final String? error;
  @override
  final int? currentPage;

  /// Create a copy of FeedbackState
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  @pragma('vm:prefer-inline')
  _$FeedbackStateCopyWith<_FeedbackState> get copyWith =>
      __$FeedbackStateCopyWithImpl<_FeedbackState>(this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$FeedbackStateToJson(
      this,
    );
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _FeedbackState &&
            const DeepCollectionEquality()
                .equals(other._feedbacks, _feedbacks) &&
            (identical(other.isLoading, isLoading) ||
                other.isLoading == isLoading) &&
            (identical(other.isLoadingMore, isLoadingMore) ||
                other.isLoadingMore == isLoadingMore) &&
            (identical(other.hasMore, hasMore) || other.hasMore == hasMore) &&
            (identical(other.error, error) || other.error == error) &&
            (identical(other.currentPage, currentPage) ||
                other.currentPage == currentPage));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hash(
      runtimeType,
      const DeepCollectionEquality().hash(_feedbacks),
      isLoading,
      isLoadingMore,
      hasMore,
      error,
      currentPage);

  @override
  String toString() {
    return 'FeedbackState(feedbacks: $feedbacks, isLoading: $isLoading, isLoadingMore: $isLoadingMore, hasMore: $hasMore, error: $error, currentPage: $currentPage)';
  }
}

/// @nodoc
abstract mixin class _$FeedbackStateCopyWith<$Res>
    implements $FeedbackStateCopyWith<$Res> {
  factory _$FeedbackStateCopyWith(
          _FeedbackState value, $Res Function(_FeedbackState) _then) =
      __$FeedbackStateCopyWithImpl;
  @override
  @useResult
  $Res call(
      {List<FeedbackListItem> feedbacks,
      bool isLoading,
      bool isLoadingMore,
      bool hasMore,
      String? error,
      int? currentPage});
}

/// @nodoc
class __$FeedbackStateCopyWithImpl<$Res>
    implements _$FeedbackStateCopyWith<$Res> {
  __$FeedbackStateCopyWithImpl(this._self, this._then);

  final _FeedbackState _self;
  final $Res Function(_FeedbackState) _then;

  /// Create a copy of FeedbackState
  /// with the given fields replaced by the non-null parameter values.
  @override
  @pragma('vm:prefer-inline')
  $Res call({
    Object? feedbacks = null,
    Object? isLoading = null,
    Object? isLoadingMore = null,
    Object? hasMore = null,
    Object? error = freezed,
    Object? currentPage = freezed,
  }) {
    return _then(_FeedbackState(
      feedbacks: null == feedbacks
          ? _self._feedbacks
          : feedbacks // ignore: cast_nullable_to_non_nullable
              as List<FeedbackListItem>,
      isLoading: null == isLoading
          ? _self.isLoading
          : isLoading // ignore: cast_nullable_to_non_nullable
              as bool,
      isLoadingMore: null == isLoadingMore
          ? _self.isLoadingMore
          : isLoadingMore // ignore: cast_nullable_to_non_nullable
              as bool,
      hasMore: null == hasMore
          ? _self.hasMore
          : hasMore // ignore: cast_nullable_to_non_nullable
              as bool,
      error: freezed == error
          ? _self.error
          : error // ignore: cast_nullable_to_non_nullable
              as String?,
      currentPage: freezed == currentPage
          ? _self.currentPage
          : currentPage // ignore: cast_nullable_to_non_nullable
              as int?,
    ));
  }
}

/// @nodoc
mixin _$FeedbackDetailState {
  Feedback? get feedback;
  bool get isLoading;
  bool get isSubmittingResponse;
  String? get error;
  FeedbackResponseForm? get responseForm;

  /// Create a copy of FeedbackDetailState
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @pragma('vm:prefer-inline')
  $FeedbackDetailStateCopyWith<FeedbackDetailState> get copyWith =>
      _$FeedbackDetailStateCopyWithImpl<FeedbackDetailState>(
          this as FeedbackDetailState, _$identity);

  /// Serializes this FeedbackDetailState to a JSON map.
  Map<String, dynamic> toJson();

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is FeedbackDetailState &&
            (identical(other.feedback, feedback) ||
                other.feedback == feedback) &&
            (identical(other.isLoading, isLoading) ||
                other.isLoading == isLoading) &&
            (identical(other.isSubmittingResponse, isSubmittingResponse) ||
                other.isSubmittingResponse == isSubmittingResponse) &&
            (identical(other.error, error) || other.error == error) &&
            (identical(other.responseForm, responseForm) ||
                other.responseForm == responseForm));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hash(runtimeType, feedback, isLoading,
      isSubmittingResponse, error, responseForm);

  @override
  String toString() {
    return 'FeedbackDetailState(feedback: $feedback, isLoading: $isLoading, isSubmittingResponse: $isSubmittingResponse, error: $error, responseForm: $responseForm)';
  }
}

/// @nodoc
abstract mixin class $FeedbackDetailStateCopyWith<$Res> {
  factory $FeedbackDetailStateCopyWith(
          FeedbackDetailState value, $Res Function(FeedbackDetailState) _then) =
      _$FeedbackDetailStateCopyWithImpl;
  @useResult
  $Res call(
      {Feedback? feedback,
      bool isLoading,
      bool isSubmittingResponse,
      String? error,
      FeedbackResponseForm? responseForm});

  $FeedbackCopyWith<$Res>? get feedback;
  $FeedbackResponseFormCopyWith<$Res>? get responseForm;
}

/// @nodoc
class _$FeedbackDetailStateCopyWithImpl<$Res>
    implements $FeedbackDetailStateCopyWith<$Res> {
  _$FeedbackDetailStateCopyWithImpl(this._self, this._then);

  final FeedbackDetailState _self;
  final $Res Function(FeedbackDetailState) _then;

  /// Create a copy of FeedbackDetailState
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? feedback = freezed,
    Object? isLoading = null,
    Object? isSubmittingResponse = null,
    Object? error = freezed,
    Object? responseForm = freezed,
  }) {
    return _then(_self.copyWith(
      feedback: freezed == feedback
          ? _self.feedback
          : feedback // ignore: cast_nullable_to_non_nullable
              as Feedback?,
      isLoading: null == isLoading
          ? _self.isLoading
          : isLoading // ignore: cast_nullable_to_non_nullable
              as bool,
      isSubmittingResponse: null == isSubmittingResponse
          ? _self.isSubmittingResponse
          : isSubmittingResponse // ignore: cast_nullable_to_non_nullable
              as bool,
      error: freezed == error
          ? _self.error
          : error // ignore: cast_nullable_to_non_nullable
              as String?,
      responseForm: freezed == responseForm
          ? _self.responseForm
          : responseForm // ignore: cast_nullable_to_non_nullable
              as FeedbackResponseForm?,
    ));
  }

  /// Create a copy of FeedbackDetailState
  /// with the given fields replaced by the non-null parameter values.
  @override
  @pragma('vm:prefer-inline')
  $FeedbackCopyWith<$Res>? get feedback {
    if (_self.feedback == null) {
      return null;
    }

    return $FeedbackCopyWith<$Res>(_self.feedback!, (value) {
      return _then(_self.copyWith(feedback: value));
    });
  }

  /// Create a copy of FeedbackDetailState
  /// with the given fields replaced by the non-null parameter values.
  @override
  @pragma('vm:prefer-inline')
  $FeedbackResponseFormCopyWith<$Res>? get responseForm {
    if (_self.responseForm == null) {
      return null;
    }

    return $FeedbackResponseFormCopyWith<$Res>(_self.responseForm!, (value) {
      return _then(_self.copyWith(responseForm: value));
    });
  }
}

/// @nodoc
@JsonSerializable()
class _FeedbackDetailState implements FeedbackDetailState {
  const _FeedbackDetailState(
      {this.feedback,
      this.isLoading = false,
      this.isSubmittingResponse = false,
      this.error,
      this.responseForm});
  factory _FeedbackDetailState.fromJson(Map<String, dynamic> json) =>
      _$FeedbackDetailStateFromJson(json);

  @override
  final Feedback? feedback;
  @override
  @JsonKey()
  final bool isLoading;
  @override
  @JsonKey()
  final bool isSubmittingResponse;
  @override
  final String? error;
  @override
  final FeedbackResponseForm? responseForm;

  /// Create a copy of FeedbackDetailState
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  @pragma('vm:prefer-inline')
  _$FeedbackDetailStateCopyWith<_FeedbackDetailState> get copyWith =>
      __$FeedbackDetailStateCopyWithImpl<_FeedbackDetailState>(
          this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$FeedbackDetailStateToJson(
      this,
    );
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _FeedbackDetailState &&
            (identical(other.feedback, feedback) ||
                other.feedback == feedback) &&
            (identical(other.isLoading, isLoading) ||
                other.isLoading == isLoading) &&
            (identical(other.isSubmittingResponse, isSubmittingResponse) ||
                other.isSubmittingResponse == isSubmittingResponse) &&
            (identical(other.error, error) || other.error == error) &&
            (identical(other.responseForm, responseForm) ||
                other.responseForm == responseForm));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hash(runtimeType, feedback, isLoading,
      isSubmittingResponse, error, responseForm);

  @override
  String toString() {
    return 'FeedbackDetailState(feedback: $feedback, isLoading: $isLoading, isSubmittingResponse: $isSubmittingResponse, error: $error, responseForm: $responseForm)';
  }
}

/// @nodoc
abstract mixin class _$FeedbackDetailStateCopyWith<$Res>
    implements $FeedbackDetailStateCopyWith<$Res> {
  factory _$FeedbackDetailStateCopyWith(_FeedbackDetailState value,
          $Res Function(_FeedbackDetailState) _then) =
      __$FeedbackDetailStateCopyWithImpl;
  @override
  @useResult
  $Res call(
      {Feedback? feedback,
      bool isLoading,
      bool isSubmittingResponse,
      String? error,
      FeedbackResponseForm? responseForm});

  @override
  $FeedbackCopyWith<$Res>? get feedback;
  @override
  $FeedbackResponseFormCopyWith<$Res>? get responseForm;
}

/// @nodoc
class __$FeedbackDetailStateCopyWithImpl<$Res>
    implements _$FeedbackDetailStateCopyWith<$Res> {
  __$FeedbackDetailStateCopyWithImpl(this._self, this._then);

  final _FeedbackDetailState _self;
  final $Res Function(_FeedbackDetailState) _then;

  /// Create a copy of FeedbackDetailState
  /// with the given fields replaced by the non-null parameter values.
  @override
  @pragma('vm:prefer-inline')
  $Res call({
    Object? feedback = freezed,
    Object? isLoading = null,
    Object? isSubmittingResponse = null,
    Object? error = freezed,
    Object? responseForm = freezed,
  }) {
    return _then(_FeedbackDetailState(
      feedback: freezed == feedback
          ? _self.feedback
          : feedback // ignore: cast_nullable_to_non_nullable
              as Feedback?,
      isLoading: null == isLoading
          ? _self.isLoading
          : isLoading // ignore: cast_nullable_to_non_nullable
              as bool,
      isSubmittingResponse: null == isSubmittingResponse
          ? _self.isSubmittingResponse
          : isSubmittingResponse // ignore: cast_nullable_to_non_nullable
              as bool,
      error: freezed == error
          ? _self.error
          : error // ignore: cast_nullable_to_non_nullable
              as String?,
      responseForm: freezed == responseForm
          ? _self.responseForm
          : responseForm // ignore: cast_nullable_to_non_nullable
              as FeedbackResponseForm?,
    ));
  }

  /// Create a copy of FeedbackDetailState
  /// with the given fields replaced by the non-null parameter values.
  @override
  @pragma('vm:prefer-inline')
  $FeedbackCopyWith<$Res>? get feedback {
    if (_self.feedback == null) {
      return null;
    }

    return $FeedbackCopyWith<$Res>(_self.feedback!, (value) {
      return _then(_self.copyWith(feedback: value));
    });
  }

  /// Create a copy of FeedbackDetailState
  /// with the given fields replaced by the non-null parameter values.
  @override
  @pragma('vm:prefer-inline')
  $FeedbackResponseFormCopyWith<$Res>? get responseForm {
    if (_self.responseForm == null) {
      return null;
    }

    return $FeedbackResponseFormCopyWith<$Res>(_self.responseForm!, (value) {
      return _then(_self.copyWith(responseForm: value));
    });
  }
}

/// @nodoc
mixin _$FeedbackFormState {
  FeedbackForm get form;
  bool get isLoading;
  bool get isSubmitting;
  bool get isUploadingPhotos;
  Map<FeedbackPhotoType, double> get uploadProgress;
  String? get error;
  String? get successMessage;

  /// Create a copy of FeedbackFormState
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @pragma('vm:prefer-inline')
  $FeedbackFormStateCopyWith<FeedbackFormState> get copyWith =>
      _$FeedbackFormStateCopyWithImpl<FeedbackFormState>(
          this as FeedbackFormState, _$identity);

  /// Serializes this FeedbackFormState to a JSON map.
  Map<String, dynamic> toJson();

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is FeedbackFormState &&
            (identical(other.form, form) || other.form == form) &&
            (identical(other.isLoading, isLoading) ||
                other.isLoading == isLoading) &&
            (identical(other.isSubmitting, isSubmitting) ||
                other.isSubmitting == isSubmitting) &&
            (identical(other.isUploadingPhotos, isUploadingPhotos) ||
                other.isUploadingPhotos == isUploadingPhotos) &&
            const DeepCollectionEquality()
                .equals(other.uploadProgress, uploadProgress) &&
            (identical(other.error, error) || other.error == error) &&
            (identical(other.successMessage, successMessage) ||
                other.successMessage == successMessage));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hash(
      runtimeType,
      form,
      isLoading,
      isSubmitting,
      isUploadingPhotos,
      const DeepCollectionEquality().hash(uploadProgress),
      error,
      successMessage);

  @override
  String toString() {
    return 'FeedbackFormState(form: $form, isLoading: $isLoading, isSubmitting: $isSubmitting, isUploadingPhotos: $isUploadingPhotos, uploadProgress: $uploadProgress, error: $error, successMessage: $successMessage)';
  }
}

/// @nodoc
abstract mixin class $FeedbackFormStateCopyWith<$Res> {
  factory $FeedbackFormStateCopyWith(
          FeedbackFormState value, $Res Function(FeedbackFormState) _then) =
      _$FeedbackFormStateCopyWithImpl;
  @useResult
  $Res call(
      {FeedbackForm form,
      bool isLoading,
      bool isSubmitting,
      bool isUploadingPhotos,
      Map<FeedbackPhotoType, double> uploadProgress,
      String? error,
      String? successMessage});

  $FeedbackFormCopyWith<$Res> get form;
}

/// @nodoc
class _$FeedbackFormStateCopyWithImpl<$Res>
    implements $FeedbackFormStateCopyWith<$Res> {
  _$FeedbackFormStateCopyWithImpl(this._self, this._then);

  final FeedbackFormState _self;
  final $Res Function(FeedbackFormState) _then;

  /// Create a copy of FeedbackFormState
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? form = null,
    Object? isLoading = null,
    Object? isSubmitting = null,
    Object? isUploadingPhotos = null,
    Object? uploadProgress = null,
    Object? error = freezed,
    Object? successMessage = freezed,
  }) {
    return _then(_self.copyWith(
      form: null == form
          ? _self.form
          : form // ignore: cast_nullable_to_non_nullable
              as FeedbackForm,
      isLoading: null == isLoading
          ? _self.isLoading
          : isLoading // ignore: cast_nullable_to_non_nullable
              as bool,
      isSubmitting: null == isSubmitting
          ? _self.isSubmitting
          : isSubmitting // ignore: cast_nullable_to_non_nullable
              as bool,
      isUploadingPhotos: null == isUploadingPhotos
          ? _self.isUploadingPhotos
          : isUploadingPhotos // ignore: cast_nullable_to_non_nullable
              as bool,
      uploadProgress: null == uploadProgress
          ? _self.uploadProgress
          : uploadProgress // ignore: cast_nullable_to_non_nullable
              as Map<FeedbackPhotoType, double>,
      error: freezed == error
          ? _self.error
          : error // ignore: cast_nullable_to_non_nullable
              as String?,
      successMessage: freezed == successMessage
          ? _self.successMessage
          : successMessage // ignore: cast_nullable_to_non_nullable
              as String?,
    ));
  }

  /// Create a copy of FeedbackFormState
  /// with the given fields replaced by the non-null parameter values.
  @override
  @pragma('vm:prefer-inline')
  $FeedbackFormCopyWith<$Res> get form {
    return $FeedbackFormCopyWith<$Res>(_self.form, (value) {
      return _then(_self.copyWith(form: value));
    });
  }
}

/// @nodoc
@JsonSerializable()
class _FeedbackFormState implements FeedbackFormState {
  const _FeedbackFormState(
      {this.form = const FeedbackForm(),
      this.isLoading = false,
      this.isSubmitting = false,
      this.isUploadingPhotos = false,
      final Map<FeedbackPhotoType, double> uploadProgress = const {},
      this.error,
      this.successMessage})
      : _uploadProgress = uploadProgress;
  factory _FeedbackFormState.fromJson(Map<String, dynamic> json) =>
      _$FeedbackFormStateFromJson(json);

  @override
  @JsonKey()
  final FeedbackForm form;
  @override
  @JsonKey()
  final bool isLoading;
  @override
  @JsonKey()
  final bool isSubmitting;
  @override
  @JsonKey()
  final bool isUploadingPhotos;
  final Map<FeedbackPhotoType, double> _uploadProgress;
  @override
  @JsonKey()
  Map<FeedbackPhotoType, double> get uploadProgress {
    if (_uploadProgress is EqualUnmodifiableMapView) return _uploadProgress;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableMapView(_uploadProgress);
  }

  @override
  final String? error;
  @override
  final String? successMessage;

  /// Create a copy of FeedbackFormState
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  @pragma('vm:prefer-inline')
  _$FeedbackFormStateCopyWith<_FeedbackFormState> get copyWith =>
      __$FeedbackFormStateCopyWithImpl<_FeedbackFormState>(this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$FeedbackFormStateToJson(
      this,
    );
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _FeedbackFormState &&
            (identical(other.form, form) || other.form == form) &&
            (identical(other.isLoading, isLoading) ||
                other.isLoading == isLoading) &&
            (identical(other.isSubmitting, isSubmitting) ||
                other.isSubmitting == isSubmitting) &&
            (identical(other.isUploadingPhotos, isUploadingPhotos) ||
                other.isUploadingPhotos == isUploadingPhotos) &&
            const DeepCollectionEquality()
                .equals(other._uploadProgress, _uploadProgress) &&
            (identical(other.error, error) || other.error == error) &&
            (identical(other.successMessage, successMessage) ||
                other.successMessage == successMessage));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hash(
      runtimeType,
      form,
      isLoading,
      isSubmitting,
      isUploadingPhotos,
      const DeepCollectionEquality().hash(_uploadProgress),
      error,
      successMessage);

  @override
  String toString() {
    return 'FeedbackFormState(form: $form, isLoading: $isLoading, isSubmitting: $isSubmitting, isUploadingPhotos: $isUploadingPhotos, uploadProgress: $uploadProgress, error: $error, successMessage: $successMessage)';
  }
}

/// @nodoc
abstract mixin class _$FeedbackFormStateCopyWith<$Res>
    implements $FeedbackFormStateCopyWith<$Res> {
  factory _$FeedbackFormStateCopyWith(
          _FeedbackFormState value, $Res Function(_FeedbackFormState) _then) =
      __$FeedbackFormStateCopyWithImpl;
  @override
  @useResult
  $Res call(
      {FeedbackForm form,
      bool isLoading,
      bool isSubmitting,
      bool isUploadingPhotos,
      Map<FeedbackPhotoType, double> uploadProgress,
      String? error,
      String? successMessage});

  @override
  $FeedbackFormCopyWith<$Res> get form;
}

/// @nodoc
class __$FeedbackFormStateCopyWithImpl<$Res>
    implements _$FeedbackFormStateCopyWith<$Res> {
  __$FeedbackFormStateCopyWithImpl(this._self, this._then);

  final _FeedbackFormState _self;
  final $Res Function(_FeedbackFormState) _then;

  /// Create a copy of FeedbackFormState
  /// with the given fields replaced by the non-null parameter values.
  @override
  @pragma('vm:prefer-inline')
  $Res call({
    Object? form = null,
    Object? isLoading = null,
    Object? isSubmitting = null,
    Object? isUploadingPhotos = null,
    Object? uploadProgress = null,
    Object? error = freezed,
    Object? successMessage = freezed,
  }) {
    return _then(_FeedbackFormState(
      form: null == form
          ? _self.form
          : form // ignore: cast_nullable_to_non_nullable
              as FeedbackForm,
      isLoading: null == isLoading
          ? _self.isLoading
          : isLoading // ignore: cast_nullable_to_non_nullable
              as bool,
      isSubmitting: null == isSubmitting
          ? _self.isSubmitting
          : isSubmitting // ignore: cast_nullable_to_non_nullable
              as bool,
      isUploadingPhotos: null == isUploadingPhotos
          ? _self.isUploadingPhotos
          : isUploadingPhotos // ignore: cast_nullable_to_non_nullable
              as bool,
      uploadProgress: null == uploadProgress
          ? _self._uploadProgress
          : uploadProgress // ignore: cast_nullable_to_non_nullable
              as Map<FeedbackPhotoType, double>,
      error: freezed == error
          ? _self.error
          : error // ignore: cast_nullable_to_non_nullable
              as String?,
      successMessage: freezed == successMessage
          ? _self.successMessage
          : successMessage // ignore: cast_nullable_to_non_nullable
              as String?,
    ));
  }

  /// Create a copy of FeedbackFormState
  /// with the given fields replaced by the non-null parameter values.
  @override
  @pragma('vm:prefer-inline')
  $FeedbackFormCopyWith<$Res> get form {
    return $FeedbackFormCopyWith<$Res>(_self.form, (value) {
      return _then(_self.copyWith(form: value));
    });
  }
}

// dart format on
