import 'dart:io';
import 'package:flutter/material.dart';
import 'package:flutter_hooks/flutter_hooks.dart';
import 'package:hooks_riverpod/hooks_riverpod.dart';
import 'package:image_picker/image_picker.dart';
import '../../application/feedback_providers.dart';
import '../../domain/feedback_models.dart';
import '../../../shared/widgets/text_form_field/custom_text_form_field.dart';
import '../../../theme/colors.dart';
import '../../../shared/constants/app_text_style.dart';

class FeedbackCreateScreen extends HookConsumerWidget {
  final String instructorId;
  final String? instructorName;
  final String? enrollmentId;

  const FeedbackCreateScreen({
    super.key,
    required this.instructorId,
    this.instructorName,
    this.enrollmentId,
  });

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final formState = ref.watch(feedbackFormProvider);
    final formNotifier = ref.read(feedbackFormProvider.notifier);

    final titleController = useTextEditingController();
    final notesController = useTextEditingController();

    // Initialize form with instructor data
    useEffect(() {
      formNotifier.setInstructor(instructorId);
      if (enrollmentId != null) {
        formNotifier.setEnrollment(enrollmentId!);
      }
      return null;
    }, [instructorId, enrollmentId]);

    // Handle success message
    useEffect(() {
      if (formState.successMessage != null) {
        WidgetsBinding.instance.addPostFrameCallback((_) {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Text(formState.successMessage!),
              backgroundColor: AColor.fitgoGreen,
            ),
          );
          formNotifier.clearMessages();
          Navigator.of(context).pop();
        });
      }
      return null;
    }, [formState.successMessage]);

    return Scaffold(
      backgroundColor: const Color(0xFF0F172A),
      appBar: AppBar(
        backgroundColor: const Color(0xFF0F172A),
        title: Text(
          'Geri Bildirim Gönder',
          style: ATextStyle.title.copyWith(color: Colors.white),
        ),
        leading: IconButton(
          icon: const Icon(Icons.arrow_back, color: Colors.white),
          onPressed: () => Navigator.of(context).pop(),
        ),
      ),
      body: SingleChildScrollView(
        child: Padding(
          padding: const EdgeInsets.all(16.0),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // Instructor info
              if (instructorName != null) ...[
                Container(
                  padding: const EdgeInsets.all(16),
                  decoration: BoxDecoration(
                    color: const Color(0xFF1E293B),
                    borderRadius: BorderRadius.circular(12),
                    border:
                        Border.all(color: AColor.fitgoGreen.withOpacity(0.3)),
                  ),
                  child: Row(
                    children: [
                      Icon(
                        Icons.person,
                        color: AColor.fitgoGreen,
                        size: 24,
                      ),
                      const SizedBox(width: 12),
                      Expanded(
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            Text(
                              'Eğitmen',
                              style: ATextStyle.small.copyWith(
                                color: Colors.white70,
                              ),
                            ),
                            Text(
                              instructorName!,
                              style: ATextStyle.medium.copyWith(
                                color: Colors.white,
                                fontWeight: FontWeight.w600,
                              ),
                            ),
                          ],
                        ),
                      ),
                    ],
                  ),
                ),
                const SizedBox(height: 24),
              ],

              // Title field
              Text(
                'Başlık',
                style: ATextStyle.medium.copyWith(
                  color: Colors.white,
                  fontWeight: FontWeight.w600,
                ),
              ),
              const SizedBox(height: 8),
              CustomTextFormField(
                controller: titleController,
                hintText: 'Geri bildirim başlığı',
                onChanged: formNotifier.updateTitle,
                maxLines: 1,
              ),
              const SizedBox(height: 20),

              // Notes field
              Text(
                'Notlarınız',
                style: ATextStyle.medium.copyWith(
                  color: Colors.white,
                  fontWeight: FontWeight.w600,
                ),
              ),
              const SizedBox(height: 8),
              CustomTextFormField(
                controller: notesController,
                hintText:
                    'Antrenman deneyiminizi, gelişiminizi ve sorularınızı detaylı olarak açıklayın...',
                onChanged: formNotifier.updateStudentNotes,
                maxLines: 6,
                minLines: 4,
              ),
              const SizedBox(height: 24),

              // Photos section
              Text(
                'Fotoğraflar (İsteğe bağlı)',
                style: ATextStyle.medium.copyWith(
                  color: Colors.white,
                  fontWeight: FontWeight.w600,
                ),
              ),
              const SizedBox(height: 8),
              Text(
                'Gelişiminizi göstermek için vücut fotoğraflarınızı ekleyebilirsiniz',
                style: ATextStyle.small.copyWith(
                  color: Colors.white70,
                ),
              ),
              const SizedBox(height: 16),

              // Photo upload cards
              Row(
                children: FeedbackPhotoType.values
                    .map((type) => Expanded(
                          child: Padding(
                            padding: EdgeInsets.only(
                              right: type != FeedbackPhotoType.back ? 12 : 0,
                            ),
                            child: _buildPhotoCard(
                              context,
                              type,
                              formNotifier,
                              formState.uploadProgress[type] ?? 0.0,
                              ref,
                            ),
                          ),
                        ))
                    .toList(),
              ),
              const SizedBox(height: 32),

              // Error message
              if (formState.error != null) ...[
                Container(
                  padding: const EdgeInsets.all(12),
                  decoration: BoxDecoration(
                    color: Colors.red.withOpacity(0.1),
                    borderRadius: BorderRadius.circular(8),
                    border: Border.all(color: Colors.red.withOpacity(0.3)),
                  ),
                  child: Row(
                    children: [
                      Icon(Icons.error_outline, color: Colors.red, size: 20),
                      const SizedBox(width: 8),
                      Expanded(
                        child: Text(
                          formState.error!,
                          style: ATextStyle.small.copyWith(color: Colors.red),
                        ),
                      ),
                    ],
                  ),
                ),
                const SizedBox(height: 16),
              ],

              // Submit button
              SizedBox(
                width: double.infinity,
                height: 50,
                child: ElevatedButton(
                  onPressed:
                      formState.isSubmitting || formState.isUploadingPhotos
                          ? null
                          : () => formNotifier.submitFeedback(),
                  style: ElevatedButton.styleFrom(
                    backgroundColor: AColor.fitgoGreen,
                    foregroundColor: Colors.white,
                    shape: RoundedRectangleBorder(
                      borderRadius: BorderRadius.circular(12),
                    ),
                  ),
                  child: formState.isSubmitting || formState.isUploadingPhotos
                      ? Row(
                          mainAxisAlignment: MainAxisAlignment.center,
                          children: [
                            SizedBox(
                              width: 20,
                              height: 20,
                              child: CircularProgressIndicator(
                                strokeWidth: 2,
                                valueColor:
                                    AlwaysStoppedAnimation<Color>(Colors.white),
                              ),
                            ),
                            const SizedBox(width: 12),
                            Text(
                              formState.isUploadingPhotos
                                  ? 'Fotoğraflar yükleniyor...'
                                  : 'Gönderiliyor...',
                              style: ATextStyle.medium.copyWith(
                                color: Colors.white,
                                fontWeight: FontWeight.w600,
                              ),
                            ),
                          ],
                        )
                      : Text(
                          'Geri Bildirim Gönder',
                          style: ATextStyle.medium.copyWith(
                            color: Colors.white,
                            fontWeight: FontWeight.w600,
                          ),
                        ),
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildPhotoCard(
    BuildContext context,
    FeedbackPhotoType photoType,
    FeedbackFormNotifier notifier,
    double uploadProgress,
    WidgetRef ref,
  ) {
    final hasPhoto = notifier.hasPhoto(photoType);
    final localFile = notifier.getLocalFile(photoType);
    final formState = ref.watch(feedbackFormProvider);
    final photo = formState.form.photos.firstWhere((p) => p.type == photoType);

    return GestureDetector(
      onTap: () => _showPhotoOptions(context, photoType, notifier),
      child: Container(
        height: 160,
        decoration: BoxDecoration(
          color: const Color(0xFF1E293B),
          borderRadius: BorderRadius.circular(12),
          border: Border.all(
            color:
                hasPhoto ? AColor.fitgoGreen.withOpacity(0.5) : Colors.white24,
            width: hasPhoto ? 2 : 1,
          ),
        ),
        child: Stack(
          children: [
            // Photo content
            ClipRRect(
              borderRadius: BorderRadius.circular(11),
              child: _buildPhotoContent(localFile, photo),
            ),

            // Upload progress overlay
            if (photo.isUploading || uploadProgress > 0)
              _buildUploadProgress(uploadProgress),

            // Remove button
            if (hasPhoto && !photo.isUploading)
              Positioned(
                top: 8,
                right: 8,
                child: GestureDetector(
                  onTap: () => notifier.removePhoto(photoType),
                  child: Container(
                    padding: const EdgeInsets.all(4),
                    decoration: BoxDecoration(
                      color: Colors.red.withOpacity(0.8),
                      shape: BoxShape.circle,
                    ),
                    child: const Icon(
                      Icons.close,
                      color: Colors.white,
                      size: 16,
                    ),
                  ),
                ),
              ),

            // Photo type label
            Positioned(
              bottom: 8,
              left: 8,
              child: Container(
                padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                decoration: BoxDecoration(
                  color: Colors.black.withOpacity(0.7),
                  borderRadius: BorderRadius.circular(6),
                ),
                child: Text(
                  photoType.displayName,
                  style: ATextStyle.small.copyWith(
                    color: Colors.white,
                    fontWeight: FontWeight.w500,
                  ),
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildPhotoContent(File? localFile, FeedbackPhotoUpload photo) {
    if (localFile != null) {
      // Show local image
      return Container(
        width: double.infinity,
        height: double.infinity,
        child: Image.file(
          localFile,
          fit: BoxFit.cover,
        ),
      );
    } else if (photo.uploadedUrl != null) {
      // Show uploaded image
      return Container(
        width: double.infinity,
        height: double.infinity,
        child: Image.network(
          photo.uploadedUrl!,
          fit: BoxFit.cover,
          loadingBuilder: (context, child, loadingProgress) {
            if (loadingProgress == null) return child;
            return Center(
              child: CircularProgressIndicator(
                valueColor: AlwaysStoppedAnimation<Color>(AColor.fitgoGreen),
                value: loadingProgress.expectedTotalBytes != null
                    ? loadingProgress.cumulativeBytesLoaded /
                        loadingProgress.expectedTotalBytes!
                    : null,
              ),
            );
          },
          errorBuilder: (context, error, stackTrace) {
            return _buildPlaceholder(photo.type, hasError: true);
          },
        ),
      );
    } else {
      // Show placeholder
      return _buildPlaceholder(photo.type);
    }
  }

  Widget _buildPlaceholder(FeedbackPhotoType type, {bool hasError = false}) {
    return Container(
      width: double.infinity,
      height: double.infinity,
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(
            hasError ? Icons.error_outline : Icons.add_photo_alternate,
            color: hasError ? Colors.red : Colors.white54,
            size: 32,
          ),
          const SizedBox(height: 8),
          Text(
            hasError ? 'Hata' : 'Fotoğraf\nEkle',
            textAlign: TextAlign.center,
            style: ATextStyle.small.copyWith(
              color: hasError ? Colors.red : Colors.white54,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildUploadProgress(double progress) {
    return Container(
      width: double.infinity,
      height: double.infinity,
      decoration: BoxDecoration(
        color: Colors.black.withOpacity(0.7),
        borderRadius: BorderRadius.circular(11),
      ),
      child: Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            CircularProgressIndicator(
              value: progress,
              valueColor: AlwaysStoppedAnimation<Color>(AColor.fitgoGreen),
              strokeWidth: 3,
            ),
            const SizedBox(height: 8),
            Text(
              '${(progress * 100).toInt()}%',
              style: ATextStyle.small.copyWith(
                color: Colors.white,
                fontWeight: FontWeight.w600,
              ),
            ),
          ],
        ),
      ),
    );
  }

  void _showPhotoOptions(BuildContext context, FeedbackPhotoType type,
      FeedbackFormNotifier notifier) {
    showModalBottomSheet(
      context: context,
      backgroundColor: const Color(0xFF1E293B),
      shape: const RoundedRectangleBorder(
        borderRadius: BorderRadius.vertical(top: Radius.circular(20)),
      ),
      builder: (context) => Container(
        padding: const EdgeInsets.all(20),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            Text(
              '${type.displayName} Fotoğraf',
              style: ATextStyle.large.copyWith(color: Colors.white),
            ),
            const SizedBox(height: 20),
            Row(
              children: [
                Expanded(
                  child: _buildPhotoOption(
                    context,
                    'Kamera',
                    Icons.camera_alt,
                    () =>
                        _pickImage(context, ImageSource.camera, type, notifier),
                  ),
                ),
                const SizedBox(width: 16),
                Expanded(
                  child: _buildPhotoOption(
                    context,
                    'Galeri',
                    Icons.photo_library,
                    () => _pickImage(
                        context, ImageSource.gallery, type, notifier),
                  ),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildPhotoOption(
      BuildContext context, String title, IconData icon, VoidCallback onTap) {
    return GestureDetector(
      onTap: () {
        Navigator.of(context).pop();
        onTap();
      },
      child: Container(
        padding: const EdgeInsets.all(16),
        decoration: BoxDecoration(
          color: const Color(0xFF334155),
          borderRadius: BorderRadius.circular(12),
        ),
        child: Column(
          children: [
            Icon(icon, color: AColor.fitgoGreen, size: 32),
            const SizedBox(height: 8),
            Text(
              title,
              style: ATextStyle.medium.copyWith(color: Colors.white),
            ),
          ],
        ),
      ),
    );
  }

  Future<void> _pickImage(BuildContext context, ImageSource source,
      FeedbackPhotoType type, FeedbackFormNotifier notifier) async {
    try {
      final picker = ImagePicker();
      final pickedFile = await picker.pickImage(
        source: source,
        maxWidth: 1920,
        maxHeight: 1920,
        imageQuality: 85,
      );

      if (pickedFile != null) {
        await notifier.addPhoto(type, File(pickedFile.path));
      }
    } catch (e) {
      if (context.mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Fotoğraf seçilirken hata oluştu: $e'),
            backgroundColor: Colors.red,
          ),
        );
      }
    }
  }
}
