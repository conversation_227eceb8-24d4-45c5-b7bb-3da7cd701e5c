import 'package:flutter/material.dart' hide Feedback;
import 'package:hooks_riverpod/hooks_riverpod.dart';
import 'package:intl/intl.dart';
import '../../application/feedback_providers.dart';
import '../../domain/feedback_models.dart';
import '../../../theme/colors.dart';
import '../../../shared/constants/app_text_style.dart';

class FeedbackDetailScreen extends ConsumerStatefulWidget {
  final String feedbackId;

  const FeedbackDetailScreen({
    super.key,
    required this.feedbackId,
  });

  @override
  ConsumerState<FeedbackDetailScreen> createState() =>
      _FeedbackDetailScreenState();
}

class _FeedbackDetailScreenState extends ConsumerState<FeedbackDetailScreen> {
  @override
  Widget build(BuildContext context) {
    final detailState = ref.watch(feedbackDetailProvider(widget.feedbackId));

    return Scaffold(
      backgroundColor: const Color(0xFF0F172A),
      appBar: AppBar(
        backgroundColor: const Color(0xFF0F172A),
        title: Text(
          '<PERSON><PERSON>dirim <PERSON>',
          style: ATextStyle.title.copyWith(color: Colors.white),
        ),
        leading: IconButton(
          icon: const Icon(Icons.arrow_back, color: Colors.white),
          onPressed: () => Navigator.of(context).pop(),
        ),
        actions: [
          IconButton(
            icon: const Icon(Icons.refresh, color: Colors.white),
            onPressed: () => ref
                .read(feedbackDetailProvider(widget.feedbackId).notifier)
                .refresh(),
          ),
        ],
      ),
      body: _buildBody(detailState),
    );
  }

  Widget _buildBody(FeedbackDetailState state) {
    if (state.isLoading) {
      return const Center(
        child: CircularProgressIndicator(
          valueColor: AlwaysStoppedAnimation<Color>(AColor.fitgoGreen),
        ),
      );
    }

    if (state.error != null) {
      return _buildErrorState(state.error!);
    }

    if (state.feedback == null) {
      return _buildNotFoundState();
    }

    return RefreshIndicator(
      onRefresh: () => ref
          .read(feedbackDetailProvider(widget.feedbackId).notifier)
          .refresh(),
      color: AColor.fitgoGreen,
      backgroundColor: const Color(0xFF1E293B),
      child: SingleChildScrollView(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            _buildFeedbackHeader(state.feedback!),
            const SizedBox(height: 24),
            _buildFeedbackContent(state.feedback!),
            if (state.feedback!.photos.isNotEmpty) ...[
              const SizedBox(height: 24),
              _buildPhotosSection(state.feedback!.photos),
            ],
            if (state.feedback!.hasInstructorResponse) ...[
              const SizedBox(height: 24),
              _buildInstructorResponse(state.feedback!),
            ],
          ],
        ),
      ),
    );
  }

  Widget _buildFeedbackHeader(Feedback feedback) {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: const Color(0xFF1E293B),
        borderRadius: BorderRadius.circular(12),
        border: Border.all(
          color: _getStatusColor(feedback.status).withValues(alpha: 0.3),
        ),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Expanded(
                child: Text(
                  feedback.title,
                  style: ATextStyle.large.copyWith(
                    color: Colors.white,
                    fontWeight: FontWeight.w600,
                  ),
                ),
              ),
              _buildStatusBadge(feedback.status),
            ],
          ),
          const SizedBox(height: 16),

          // Instructor info
          if (feedback.instructorName != null) ...[
            Row(
              children: [
                Icon(
                  Icons.person,
                  color: AColor.fitgoGreen,
                  size: 20,
                ),
                const SizedBox(width: 8),
                Text(
                  'Eğitmen: ${feedback.instructorName}',
                  style: ATextStyle.medium.copyWith(
                    color: Colors.white70,
                    fontWeight: FontWeight.w500,
                  ),
                ),
              ],
            ),
            const SizedBox(height: 8),
          ],

          // Date info
          Row(
            children: [
              Icon(
                Icons.calendar_today,
                color: Colors.white54,
                size: 18,
              ),
              const SizedBox(width: 8),
              Text(
                'Gönderilme: ${DateFormat('dd MMM yyyy, HH:mm').format(feedback.feedbackDate)}',
                style: ATextStyle.medium.copyWith(
                  color: Colors.white54,
                ),
              ),
            ],
          ),

          // Response date if available
          if (feedback.instructorResponseDate != null) ...[
            const SizedBox(height: 8),
            Row(
              children: [
                Icon(
                  Icons.reply,
                  color: AColor.fitgoGreen,
                  size: 18,
                ),
                const SizedBox(width: 8),
                Text(
                  'Yanıtlanma: ${DateFormat('dd MMM yyyy, HH:mm').format(feedback.instructorResponseDate!)}',
                  style: ATextStyle.medium.copyWith(
                    color: AColor.fitgoGreen,
                  ),
                ),
              ],
            ),
          ],
        ],
      ),
    );
  }

  Widget _buildFeedbackContent(Feedback feedback) {
    return Container(
      width: double.infinity,
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: const Color(0xFF1E293B),
        borderRadius: BorderRadius.circular(12),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            'Notlarınız',
            style: ATextStyle.large.copyWith(
              color: Colors.white,
              fontWeight: FontWeight.w600,
            ),
          ),
          const SizedBox(height: 12),
          Text(
            feedback.studentNotes,
            style: ATextStyle.medium.copyWith(
              color: Colors.white70,
              height: 1.5,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildPhotosSection(List<FeedbackPhoto> photos) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'Fotoğraflar',
          style: ATextStyle.large.copyWith(
            color: Colors.white,
            fontWeight: FontWeight.w600,
          ),
        ),
        const SizedBox(height: 12),
        Container(
          padding: const EdgeInsets.all(16),
          decoration: BoxDecoration(
            color: const Color(0xFF1E293B),
            borderRadius: BorderRadius.circular(12),
          ),
          child: Column(
            children: [
              Row(
                children: photos
                    .map((photo) => Expanded(
                          child: Padding(
                            padding: EdgeInsets.only(
                              right: photo != photos.last ? 12 : 0,
                            ),
                            child: _buildPhotoCard(photo),
                          ),
                        ))
                    .toList(),
              ),
            ],
          ),
        ),
      ],
    );
  }

  Widget _buildPhotoCard(FeedbackPhoto photo) {
    return GestureDetector(
      onTap: () => _showPhotoDialog(photo),
      child: Container(
        height: 120,
        decoration: BoxDecoration(
          borderRadius: BorderRadius.circular(8),
          border: Border.all(color: Colors.white24),
        ),
        child: Stack(
          children: [
            ClipRRect(
              borderRadius: BorderRadius.circular(7),
              child: Image.network(
                photo.photoUrl,
                width: double.infinity,
                height: double.infinity,
                fit: BoxFit.cover,
                loadingBuilder: (context, child, loadingProgress) {
                  if (loadingProgress == null) return child;
                  return Center(
                    child: CircularProgressIndicator(
                      valueColor:
                          AlwaysStoppedAnimation<Color>(AColor.fitgoGreen),
                    ),
                  );
                },
                errorBuilder: (context, error, stackTrace) {
                  return Container(
                    color: Colors.grey[800],
                    child: Icon(
                      Icons.error_outline,
                      color: Colors.white54,
                      size: 32,
                    ),
                  );
                },
              ),
            ),
            Positioned(
              bottom: 4,
              left: 4,
              child: Container(
                padding: const EdgeInsets.symmetric(horizontal: 6, vertical: 2),
                decoration: BoxDecoration(
                  color: Colors.black.withValues(alpha: 0.7),
                  borderRadius: BorderRadius.circular(4),
                ),
                child: Text(
                  photo.photoType.displayName,
                  style: ATextStyle.small.copyWith(
                    color: Colors.white,
                    fontSize: 10,
                  ),
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildInstructorResponse(Feedback feedback) {
    return Container(
      width: double.infinity,
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: const Color(0xFF1E293B),
        borderRadius: BorderRadius.circular(12),
        border: Border.all(
          color: AColor.fitgoGreen.withValues(alpha: 0.3),
        ),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Icon(
                Icons.reply,
                color: AColor.fitgoGreen,
                size: 20,
              ),
              const SizedBox(width: 8),
              Text(
                'Eğitmen Yanıtı',
                style: ATextStyle.large.copyWith(
                  color: AColor.fitgoGreen,
                  fontWeight: FontWeight.w600,
                ),
              ),
            ],
          ),
          const SizedBox(height: 12),
          Text(
            feedback.instructorResponse!,
            style: ATextStyle.medium.copyWith(
              color: Colors.white70,
              height: 1.5,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildStatusBadge(FeedbackStatus status) {
    final color = _getStatusColor(status);
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
      decoration: BoxDecoration(
        color: color.withValues(alpha: 0.2),
        borderRadius: BorderRadius.circular(6),
        border: Border.all(color: color.withValues(alpha: 0.5)),
      ),
      child: Text(
        status.displayName,
        style: ATextStyle.small.copyWith(
          color: color,
          fontWeight: FontWeight.w500,
        ),
      ),
    );
  }

  Color _getStatusColor(FeedbackStatus status) {
    switch (status) {
      case FeedbackStatus.pending:
        return Colors.orange;
      case FeedbackStatus.reviewed:
        return AColor.fitgoGreen;
      case FeedbackStatus.completed:
        return Colors.grey;
    }
  }

  Widget _buildErrorState(String error) {
    return Center(
      child: Padding(
        padding: const EdgeInsets.all(32),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              Icons.error_outline,
              size: 80,
              color: Colors.red.withValues(alpha: 0.7),
            ),
            const SizedBox(height: 24),
            Text(
              'Bir hata oluştu',
              style: ATextStyle.large.copyWith(
                color: Colors.white70,
              ),
              textAlign: TextAlign.center,
            ),
            const SizedBox(height: 12),
            Text(
              error,
              style: ATextStyle.medium.copyWith(
                color: Colors.white54,
              ),
              textAlign: TextAlign.center,
            ),
            const SizedBox(height: 24),
            ElevatedButton(
              onPressed: () => ref
                  .read(feedbackDetailProvider(widget.feedbackId).notifier)
                  .refresh(),
              style: ElevatedButton.styleFrom(
                backgroundColor: AColor.fitgoGreen,
                foregroundColor: Colors.white,
              ),
              child: Text('Tekrar Dene'),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildNotFoundState() {
    return Center(
      child: Padding(
        padding: const EdgeInsets.all(32),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              Icons.search_off,
              size: 80,
              color: Colors.white24,
            ),
            const SizedBox(height: 24),
            Text(
              'Geri bildirim bulunamadı',
              style: ATextStyle.large.copyWith(
                color: Colors.white70,
              ),
              textAlign: TextAlign.center,
            ),
            const SizedBox(height: 12),
            Text(
              'Bu geri bildirim silinmiş olabilir veya erişim izniniz bulunmuyor',
              style: ATextStyle.medium.copyWith(
                color: Colors.white54,
              ),
              textAlign: TextAlign.center,
            ),
          ],
        ),
      ),
    );
  }

  void _showPhotoDialog(FeedbackPhoto photo) {
    showDialog(
      context: context,
      builder: (context) => Dialog(
        backgroundColor: Colors.transparent,
        child: Stack(
          children: [
            Center(
              child: Container(
                constraints: BoxConstraints(
                  maxWidth: MediaQuery.of(context).size.width * 0.9,
                  maxHeight: MediaQuery.of(context).size.height * 0.8,
                ),
                child: ClipRRect(
                  borderRadius: BorderRadius.circular(12),
                  child: Image.network(
                    photo.photoUrl,
                    fit: BoxFit.contain,
                  ),
                ),
              ),
            ),
            Positioned(
              top: 40,
              right: 20,
              child: GestureDetector(
                onTap: () => Navigator.of(context).pop(),
                child: Container(
                  padding: const EdgeInsets.all(8),
                  decoration: BoxDecoration(
                    color: Colors.black.withValues(alpha: 0.7),
                    shape: BoxShape.circle,
                  ),
                  child: const Icon(
                    Icons.close,
                    color: Colors.white,
                    size: 24,
                  ),
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }
}
