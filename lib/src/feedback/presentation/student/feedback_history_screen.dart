import 'package:flutter/material.dart';
import 'package:hooks_riverpod/hooks_riverpod.dart';
import 'package:intl/intl.dart';
import 'package:supabase_flutter/supabase_flutter.dart';
import '../../application/feedback_providers.dart';
import '../../domain/feedback_models.dart';
import '../../../theme/colors.dart';
import '../../../shared/constants/app_text_style.dart';
import 'feedback_detail_screen.dart';
import 'feedback_create_screen.dart';

class FeedbackHistoryScreen extends ConsumerStatefulWidget {
  const FeedbackHistoryScreen({super.key});

  @override
  ConsumerState<FeedbackHistoryScreen> createState() =>
      _FeedbackHistoryScreenState();
}

class _FeedbackHistoryScreenState extends ConsumerState<FeedbackHistoryScreen> {
  final ScrollController _scrollController = ScrollController();

  @override
  void initState() {
    super.initState();
    _scrollController.addListener(_onScroll);

    // Load initial data
    WidgetsBinding.instance.addPostFrameCallback((_) {
      ref
          .read(studentFeedbackHistoryProvider.notifier)
          .loadFeedbackHistory(refresh: true);
    });
  }

  @override
  void dispose() {
    _scrollController.dispose();
    super.dispose();
  }

  void _onScroll() {
    if (_scrollController.position.pixels >=
        _scrollController.position.maxScrollExtent - 200) {
      final state = ref.read(studentFeedbackHistoryProvider);
      if (state.hasMore && !state.isLoadingMore) {
        ref.read(studentFeedbackHistoryProvider.notifier).loadMore();
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    final feedbackState = ref.watch(studentFeedbackHistoryProvider);

    return Scaffold(
      backgroundColor: const Color(0xFF0F172A),
      appBar: AppBar(
        backgroundColor: const Color(0xFF0F172A),
        title: Text(
          'Geri Bildirim Geçmişi',
          style: ATextStyle.title.copyWith(color: Colors.white),
        ),
        leading: IconButton(
          icon: const Icon(Icons.arrow_back, color: Colors.white),
          onPressed: () => Navigator.of(context).pop(),
        ),
        actions: [
          IconButton(
            icon: const Icon(Icons.refresh, color: Colors.white),
            onPressed: () =>
                ref.read(studentFeedbackHistoryProvider.notifier).refresh(),
          ),
        ],
      ),
      floatingActionButton: FloatingActionButton.extended(
        onPressed: _navigateToCreateFeedback,
        backgroundColor: AColor.fitgoGreen,
        foregroundColor: Colors.white,
        icon: const Icon(Icons.add),
        label: const Text('Yeni Geri Bildirim'),
      ),
      body: RefreshIndicator(
        onRefresh: () =>
            ref.read(studentFeedbackHistoryProvider.notifier).refresh(),
        color: AColor.fitgoGreen,
        child: _buildBody(feedbackState),
      ),
    );
  }

  Widget _buildBody(FeedbackState state) {
    if (state.isLoading && state.feedbacks.isEmpty) {
      return const Center(
        child: CircularProgressIndicator(
          valueColor: AlwaysStoppedAnimation<Color>(AColor.fitgoGreen),
        ),
      );
    }

    if (state.error != null && state.feedbacks.isEmpty) {
      return _buildErrorState(state.error!);
    }

    if (state.feedbacks.isEmpty) {
      return _buildEmptyState();
    }

    return ListView.builder(
      controller: _scrollController,
      padding: const EdgeInsets.all(16),
      itemCount: state.feedbacks.length + (state.hasMore ? 1 : 0),
      itemBuilder: (context, index) {
        if (index == state.feedbacks.length) {
          return _buildLoadingMore();
        }

        final feedback = state.feedbacks[index];
        return _buildFeedbackCard(feedback);
      },
    );
  }

  Widget _buildFeedbackCard(FeedbackListItem feedback) {
    return Container(
      margin: const EdgeInsets.only(bottom: 16),
      decoration: BoxDecoration(
        color: const Color(0xFF1E293B),
        borderRadius: BorderRadius.circular(12),
        border: Border.all(
          color: _getStatusColor(feedback.status).withOpacity(0.3),
          width: 1,
        ),
      ),
      child: InkWell(
        onTap: () => _navigateToDetail(feedback.id),
        borderRadius: BorderRadius.circular(12),
        child: Padding(
          padding: const EdgeInsets.all(16),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // Header with status
              Row(
                children: [
                  Expanded(
                    child: Text(
                      feedback.title,
                      style: ATextStyle.large.copyWith(
                        color: Colors.white,
                        fontWeight: FontWeight.w600,
                      ),
                      maxLines: 2,
                      overflow: TextOverflow.ellipsis,
                    ),
                  ),
                  const SizedBox(width: 12),
                  _buildStatusBadge(feedback.status),
                ],
              ),
              const SizedBox(height: 12),

              // Instructor info
              if (feedback.instructorName != null) ...[
                Row(
                  children: [
                    Icon(
                      Icons.person,
                      color: AColor.fitgoGreen,
                      size: 16,
                    ),
                    const SizedBox(width: 8),
                    Text(
                      feedback.instructorName!,
                      style: ATextStyle.medium.copyWith(
                        color: Colors.white70,
                      ),
                    ),
                  ],
                ),
                const SizedBox(height: 8),
              ],

              // Date and response info
              Row(
                children: [
                  Icon(
                    Icons.calendar_today,
                    color: Colors.white54,
                    size: 16,
                  ),
                  const SizedBox(width: 8),
                  Text(
                    DateFormat('dd MMM yyyy, HH:mm')
                        .format(feedback.feedbackDate),
                    style: ATextStyle.small.copyWith(
                      color: Colors.white54,
                    ),
                  ),
                  const Spacer(),
                  if (feedback.hasResponse)
                    Row(
                      children: [
                        Icon(
                          Icons.reply,
                          color: AColor.fitgoGreen,
                          size: 16,
                        ),
                        const SizedBox(width: 4),
                        Text(
                          'Yanıtlandı',
                          style: ATextStyle.small.copyWith(
                            color: AColor.fitgoGreen,
                            fontWeight: FontWeight.w500,
                          ),
                        ),
                      ],
                    ),
                ],
              ),

              // Photo count if any
              if (feedback.photoCount > 0) ...[
                const SizedBox(height: 8),
                Row(
                  children: [
                    Icon(
                      Icons.photo_library,
                      color: Colors.white54,
                      size: 16,
                    ),
                    const SizedBox(width: 8),
                    Text(
                      '${feedback.photoCount} fotoğraf',
                      style: ATextStyle.small.copyWith(
                        color: Colors.white54,
                      ),
                    ),
                  ],
                ),
              ],
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildStatusBadge(FeedbackStatus status) {
    final color = _getStatusColor(status);
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
      decoration: BoxDecoration(
        color: color.withOpacity(0.2),
        borderRadius: BorderRadius.circular(6),
        border: Border.all(color: color.withOpacity(0.5)),
      ),
      child: Text(
        status.displayName,
        style: ATextStyle.small.copyWith(
          color: color,
          fontWeight: FontWeight.w500,
        ),
      ),
    );
  }

  Color _getStatusColor(FeedbackStatus status) {
    switch (status) {
      case FeedbackStatus.waiting:
        return Colors.orange;
      case FeedbackStatus.reviewed:
        return AColor.fitgoGreen;
      case FeedbackStatus.closed:
        return Colors.grey;
    }
  }

  Widget _buildEmptyState() {
    return Center(
      child: Padding(
        padding: const EdgeInsets.all(32),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              Icons.feedback_outlined,
              size: 80,
              color: Colors.white24,
            ),
            const SizedBox(height: 24),
            Text(
              'Henüz geri bildirim yok',
              style: ATextStyle.large.copyWith(
                color: Colors.white70,
              ),
              textAlign: TextAlign.center,
            ),
            const SizedBox(height: 12),
            Text(
              'Eğitmeninizle iletişim kurmak için ilk geri bildiriminizi gönderin',
              style: ATextStyle.medium.copyWith(
                color: Colors.white54,
              ),
              textAlign: TextAlign.center,
            ),
            const SizedBox(height: 32),
            ElevatedButton.icon(
              onPressed: () => _navigateToCreateFeedback(),
              icon: const Icon(Icons.add),
              label: const Text('Geri Bildirim Oluştur'),
              style: ElevatedButton.styleFrom(
                backgroundColor: AColor.fitgoGreen,
                foregroundColor: Colors.white,
                padding:
                    const EdgeInsets.symmetric(horizontal: 24, vertical: 12),
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(12),
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildErrorState(String error) {
    return Center(
      child: Padding(
        padding: const EdgeInsets.all(32),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              Icons.error_outline,
              size: 80,
              color: Colors.red.withOpacity(0.7),
            ),
            const SizedBox(height: 24),
            Text(
              'Bir hata oluştu',
              style: ATextStyle.large.copyWith(
                color: Colors.white70,
              ),
              textAlign: TextAlign.center,
            ),
            const SizedBox(height: 12),
            Text(
              error,
              style: ATextStyle.medium.copyWith(
                color: Colors.white54,
              ),
              textAlign: TextAlign.center,
            ),
            const SizedBox(height: 24),
            ElevatedButton(
              onPressed: () =>
                  ref.read(studentFeedbackHistoryProvider.notifier).refresh(),
              style: ElevatedButton.styleFrom(
                backgroundColor: AColor.fitgoGreen,
                foregroundColor: Colors.white,
              ),
              child: Text('Tekrar Dene'),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildLoadingMore() {
    return Container(
      padding: const EdgeInsets.all(16),
      child: Center(
        child: CircularProgressIndicator(
          valueColor: AlwaysStoppedAnimation<Color>(AColor.fitgoGreen),
        ),
      ),
    );
  }

  void _navigateToDetail(String feedbackId) {
    Navigator.of(context).push(
      MaterialPageRoute(
        builder: (context) => FeedbackDetailScreen(feedbackId: feedbackId),
      ),
    );
  }

  void _navigateToCreateFeedback() async {
    try {
      // Get current user's enrollment info
      final currentUser = Supabase.instance.client.auth.currentUser;
      if (currentUser == null) {
        _showErrorSnackBar('Kullanıcı oturumu bulunamadı');
        return;
      }

      // Get enrollment info
      final enrollmentResponse = await Supabase.instance.client
          .from('enrollments')
          .select('id, instructor_id')
          .eq('student_id', currentUser.id)
          .eq('is_active', true)
          .maybeSingle();

      if (enrollmentResponse == null) {
        _showErrorSnackBar('Aktif eğitmen kaydı bulunamadı');
        return;
      }

      // Get instructor info
      final instructorResponse = await Supabase.instance.client
          .from('profiles')
          .select('name, surname')
          .eq('id', enrollmentResponse['instructor_id'])
          .single();

      final instructorName =
          '${instructorResponse['name']} ${instructorResponse['surname']}';

      // Navigate to create feedback screen
      if (mounted) {
        Navigator.of(context).push(
          MaterialPageRoute(
            builder: (context) => FeedbackCreateScreen(
              instructorId: enrollmentResponse['instructor_id'],
              instructorName: instructorName,
              enrollmentId: enrollmentResponse['id'],
            ),
          ),
        );
      }
    } catch (e) {
      _showErrorSnackBar(
          'Geri bildirim oluşturma sayfasına gidilirken hata oluştu');
    }
  }

  void _showErrorSnackBar(String message) {
    if (mounted) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text(message),
          backgroundColor: Colors.red,
        ),
      );
    }
  }
}
