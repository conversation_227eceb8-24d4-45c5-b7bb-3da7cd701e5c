import 'package:flutter/material.dart';
import 'package:hooks_riverpod/hooks_riverpod.dart';
import 'package:intl/intl.dart';
import '../../application/feedback_providers.dart';
import '../../domain/feedback_models.dart';
import '../../../theme/colors.dart';
import '../../../shared/constants/app_text_style.dart';
import 'instructor_feedback_detail_screen.dart';

class FeedbackQueueScreen extends ConsumerStatefulWidget {
  const FeedbackQueueScreen({super.key});

  @override
  ConsumerState<FeedbackQueueScreen> createState() =>
      _FeedbackQueueScreenState();
}

class _FeedbackQueueScreenState extends ConsumerState<FeedbackQueueScreen> {
  final ScrollController _scrollController = ScrollController();

  @override
  void initState() {
    super.initState();
    _scrollController.addListener(_onScroll);

    // Load initial data
    WidgetsBinding.instance.addPostFrameCallback((_) {
      ref
          .read(instructorFeedbackQueueProvider.notifier)
          .loadFeedbackQueue(refresh: true);
    });
  }

  @override
  void dispose() {
    _scrollController.dispose();
    super.dispose();
  }

  void _onScroll() {
    if (_scrollController.position.pixels >=
        _scrollController.position.maxScrollExtent - 200) {
      final state = ref.read(instructorFeedbackQueueProvider);
      if (state.hasMore && !state.isLoadingMore) {
        ref.read(instructorFeedbackQueueProvider.notifier).loadMore();
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    final feedbackState = ref.watch(instructorFeedbackQueueProvider);

    return Scaffold(
      backgroundColor: const Color(0xFF0F172A),
      appBar: AppBar(
        backgroundColor: const Color(0xFF0F172A),
        title: Text(
          'Öğrenci Geri Bildirimleri',
          style: ATextStyle.title.copyWith(color: Colors.white),
        ),
        leading: IconButton(
          icon: const Icon(Icons.arrow_back, color: Colors.white),
          onPressed: () => Navigator.of(context).pop(),
        ),
        actions: [
          IconButton(
            icon: const Icon(Icons.refresh, color: Colors.white),
            onPressed: () =>
                ref.read(instructorFeedbackQueueProvider.notifier).refresh(),
          ),
        ],
      ),
      body: RefreshIndicator(
        onRefresh: () =>
            ref.read(instructorFeedbackQueueProvider.notifier).refresh(),
        color: AColor.fitgoGreen,
        child: _buildBody(feedbackState),
      ),
    );
  }

  Widget _buildBody(FeedbackState state) {
    if (state.isLoading && state.feedbacks.isEmpty) {
      return const Center(
        child: CircularProgressIndicator(
          valueColor: AlwaysStoppedAnimation<Color>(AColor.fitgoGreen),
        ),
      );
    }

    if (state.error != null && state.feedbacks.isEmpty) {
      return _buildErrorState(state.error!);
    }

    if (state.feedbacks.isEmpty) {
      return _buildEmptyState();
    }

    // Separate pending and reviewed feedbacks
    final pendingFeedbacks = state.feedbacks
        .where((f) => f.status == FeedbackStatus.pending)
        .toList();
    final reviewedFeedbacks = state.feedbacks
        .where((f) => f.status == FeedbackStatus.reviewed)
        .toList();

    return RefreshIndicator(
      onRefresh: () => ref
          .read(instructorFeedbackQueueProvider.notifier)
          .loadFeedbackQueue(refresh: true),
      color: AColor.fitgoGreen,
      backgroundColor: const Color(0xFF1E293B),
      child: ListView(
        controller: _scrollController,
        padding: const EdgeInsets.all(16),
        children: [
          // Pending feedbacks section
          if (pendingFeedbacks.isNotEmpty) ...[
            _buildSectionHeader(
                'Yanıt Bekleyen (${pendingFeedbacks.length})', Colors.orange),
            const SizedBox(height: 12),
            ...pendingFeedbacks.map(
                (feedback) => _buildFeedbackCard(feedback, isUrgent: true)),
            const SizedBox(height: 24),
          ],

          // Reviewed feedbacks section
          if (reviewedFeedbacks.isNotEmpty) ...[
            _buildSectionHeader(
                'Yanıtlanan (${reviewedFeedbacks.length})', AColor.fitgoGreen),
            const SizedBox(height: 12),
            ...reviewedFeedbacks
                .map((feedback) => _buildFeedbackCard(feedback)),
          ],

          // Load more indicator
          if (state.hasMore) _buildLoadingMore(),
        ],
      ),
    );
  }

  Widget _buildSectionHeader(String title, Color color) {
    return Row(
      children: [
        Container(
          width: 4,
          height: 20,
          decoration: BoxDecoration(
            color: color,
            borderRadius: BorderRadius.circular(2),
          ),
        ),
        const SizedBox(width: 12),
        Text(
          title,
          style: ATextStyle.large.copyWith(
            color: Colors.white,
            fontWeight: FontWeight.w600,
          ),
        ),
      ],
    );
  }

  Widget _buildFeedbackCard(FeedbackListItem feedback,
      {bool isUrgent = false}) {
    return Container(
      margin: const EdgeInsets.only(bottom: 12),
      decoration: BoxDecoration(
        color: const Color(0xFF1E293B),
        borderRadius: BorderRadius.circular(12),
        border: Border.all(
          color: isUrgent
              ? Colors.orange.withValues(alpha: 0.5)
              : _getStatusColor(feedback.status).withValues(alpha: 0.3),
          width: isUrgent ? 2 : 1,
        ),
      ),
      child: InkWell(
        onTap: () => _navigateToDetail(feedback.id),
        borderRadius: BorderRadius.circular(12),
        child: Padding(
          padding: const EdgeInsets.all(16),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // Header with urgency indicator
              Row(
                children: [
                  if (isUrgent) ...[
                    Container(
                      padding: const EdgeInsets.all(4),
                      decoration: BoxDecoration(
                        color: Colors.orange.withValues(alpha: 0.2),
                        shape: BoxShape.circle,
                      ),
                      child: Icon(
                        Icons.priority_high,
                        color: Colors.orange,
                        size: 16,
                      ),
                    ),
                    const SizedBox(width: 8),
                  ],
                  Expanded(
                    child: Text(
                      feedback.title,
                      style: ATextStyle.large.copyWith(
                        color: Colors.white,
                        fontWeight: FontWeight.w600,
                      ),
                      maxLines: 2,
                      overflow: TextOverflow.ellipsis,
                    ),
                  ),
                  const SizedBox(width: 12),
                  _buildStatusBadge(feedback.status),
                ],
              ),
              const SizedBox(height: 12),

              // Student info
              if (feedback.studentName != null) ...[
                Row(
                  children: [
                    Icon(
                      Icons.person,
                      color: AColor.fitgoGreen,
                      size: 16,
                    ),
                    const SizedBox(width: 8),
                    Text(
                      feedback.studentName!,
                      style: ATextStyle.medium.copyWith(
                        color: Colors.white70,
                        fontWeight: FontWeight.w500,
                      ),
                    ),
                  ],
                ),
                const SizedBox(height: 8),
              ],

              // Date and waiting time
              Row(
                children: [
                  Icon(
                    Icons.calendar_today,
                    color: Colors.white54,
                    size: 16,
                  ),
                  const SizedBox(width: 8),
                  Text(
                    DateFormat('dd MMM yyyy').format(feedback.feedbackDate),
                    style: ATextStyle.small.copyWith(
                      color: Colors.white54,
                    ),
                  ),
                  const Spacer(),
                  if (feedback.daysWaiting != null && feedback.daysWaiting! > 0)
                    Container(
                      padding: const EdgeInsets.symmetric(
                          horizontal: 8, vertical: 4),
                      decoration: BoxDecoration(
                        color: _getWaitingColor(feedback.daysWaiting!)
                            .withValues(alpha: 0.2),
                        borderRadius: BorderRadius.circular(6),
                      ),
                      child: Text(
                        '${feedback.daysWaiting} gün',
                        style: ATextStyle.small.copyWith(
                          color: _getWaitingColor(feedback.daysWaiting!),
                          fontWeight: FontWeight.w500,
                        ),
                      ),
                    ),
                ],
              ),

              // Photo count if any
              if (feedback.photoCount > 0) ...[
                const SizedBox(height: 8),
                Row(
                  children: [
                    Icon(
                      Icons.photo_library,
                      color: AColor.fitgoGreen,
                      size: 16,
                    ),
                    const SizedBox(width: 8),
                    Text(
                      '${feedback.photoCount} fotoğraf',
                      style: ATextStyle.small.copyWith(
                        color: AColor.fitgoGreen,
                        fontWeight: FontWeight.w500,
                      ),
                    ),
                  ],
                ),
              ],
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildStatusBadge(FeedbackStatus status) {
    final color = _getStatusColor(status);
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
      decoration: BoxDecoration(
        color: color.withValues(alpha: 0.2),
        borderRadius: BorderRadius.circular(6),
        border: Border.all(color: color.withValues(alpha: 0.5)),
      ),
      child: Text(
        status.displayName,
        style: ATextStyle.small.copyWith(
          color: color,
          fontWeight: FontWeight.w500,
        ),
      ),
    );
  }

  Color _getStatusColor(FeedbackStatus status) {
    switch (status) {
      case FeedbackStatus.pending:
        return Colors.orange;
      case FeedbackStatus.reviewed:
        return AColor.fitgoGreen;
      case FeedbackStatus.completed:
        return Colors.grey;
    }
  }

  Color _getWaitingColor(int days) {
    if (days >= 7) return Colors.red;
    if (days >= 3) return Colors.orange;
    return Colors.yellow;
  }

  Widget _buildEmptyState() {
    return Center(
      child: Padding(
        padding: const EdgeInsets.all(32),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              Icons.feedback_outlined,
              size: 80,
              color: Colors.white24,
            ),
            const SizedBox(height: 24),
            Text(
              'Henüz geri bildirim yok',
              style: ATextStyle.large.copyWith(
                color: Colors.white70,
              ),
              textAlign: TextAlign.center,
            ),
            const SizedBox(height: 12),
            Text(
              'Öğrencilerinizden geri bildirim geldiğinde burada görünecek',
              style: ATextStyle.medium.copyWith(
                color: Colors.white54,
              ),
              textAlign: TextAlign.center,
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildErrorState(String error) {
    return Center(
      child: Padding(
        padding: const EdgeInsets.all(32),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              Icons.error_outline,
              size: 80,
              color: Colors.red.withValues(alpha: 0.7),
            ),
            const SizedBox(height: 24),
            Text(
              'Bir hata oluştu',
              style: ATextStyle.large.copyWith(
                color: Colors.white70,
              ),
              textAlign: TextAlign.center,
            ),
            const SizedBox(height: 12),
            Text(
              error,
              style: ATextStyle.medium.copyWith(
                color: Colors.white54,
              ),
              textAlign: TextAlign.center,
            ),
            const SizedBox(height: 24),
            ElevatedButton(
              onPressed: () =>
                  ref.read(instructorFeedbackQueueProvider.notifier).refresh(),
              style: ElevatedButton.styleFrom(
                backgroundColor: AColor.fitgoGreen,
                foregroundColor: Colors.white,
              ),
              child: Text('Tekrar Dene'),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildLoadingMore() {
    return Container(
      padding: const EdgeInsets.all(16),
      child: Center(
        child: CircularProgressIndicator(
          valueColor: AlwaysStoppedAnimation<Color>(AColor.fitgoGreen),
        ),
      ),
    );
  }

  void _navigateToDetail(String feedbackId) {
    Navigator.of(context).push(
      MaterialPageRoute(
        builder: (context) =>
            InstructorFeedbackDetailScreen(feedbackId: feedbackId),
      ),
    );
  }
}
