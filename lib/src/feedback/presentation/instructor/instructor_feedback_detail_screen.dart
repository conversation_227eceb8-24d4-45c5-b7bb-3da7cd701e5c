import 'package:flutter/material.dart' hide Feedback;
import 'package:flutter_hooks/flutter_hooks.dart';
import 'package:hooks_riverpod/hooks_riverpod.dart';
import 'package:intl/intl.dart';
import '../../application/feedback_providers.dart';
import '../../domain/feedback_models.dart';
import '../../../shared/widgets/text_form_field/custom_text_form_field.dart';
import '../../../theme/colors.dart';
import '../../../shared/constants/app_text_style.dart';

class InstructorFeedbackDetailScreen extends HookConsumerWidget {
  final String feedbackId;

  const InstructorFeedbackDetailScreen({
    super.key,
    required this.feedbackId,
  });

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final detailState = ref.watch(feedbackDetailProvider(feedbackId));
    final detailNotifier =
        ref.read(feedbackDetailProvider(feedbackId).notifier);

    final responseController = useTextEditingController();

    // Update controller when response form changes
    useEffect(() {
      if (detailState.responseForm != null) {
        responseController.text = detailState.responseForm!.response;
      }
      return null;
    }, [detailState.responseForm?.response]);

    return Scaffold(
      backgroundColor: const Color(0xFF0F172A),
      appBar: AppBar(
        backgroundColor: const Color(0xFF0F172A),
        title: Text(
          'Geri Bildirim Detayı',
          style: ATextStyle.title.copyWith(color: Colors.white),
        ),
        leading: IconButton(
          icon: const Icon(Icons.arrow_back, color: Colors.white),
          onPressed: () => Navigator.of(context).pop(),
        ),
        actions: [
          IconButton(
            icon: const Icon(Icons.refresh, color: Colors.white),
            onPressed: () => detailNotifier.refresh(),
          ),
        ],
      ),
      body:
          _buildBody(context, detailState, detailNotifier, responseController),
    );
  }

  Widget _buildBody(
    BuildContext context,
    FeedbackDetailState state,
    FeedbackDetailNotifier notifier,
    TextEditingController responseController,
  ) {
    if (state.isLoading) {
      return const Center(
        child: CircularProgressIndicator(
          valueColor: AlwaysStoppedAnimation<Color>(AColor.fitgoGreen),
        ),
      );
    }

    if (state.error != null) {
      return _buildErrorState(state.error!, notifier);
    }

    if (state.feedback == null) {
      return _buildNotFoundState();
    }

    return SingleChildScrollView(
      padding: const EdgeInsets.all(16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          _buildFeedbackHeader(state.feedback!),
          const SizedBox(height: 24),
          _buildStudentNotes(state.feedback!),
          if (state.feedback!.photos.isNotEmpty) ...[
            const SizedBox(height: 24),
            _buildPhotosSection(context, state.feedback!.photos),
          ],
          const SizedBox(height: 24),
          _buildResponseSection(
            context,
            state.feedback!,
            state,
            notifier,
            responseController,
          ),
        ],
      ),
    );
  }

  Widget _buildFeedbackHeader(Feedback feedback) {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: const Color(0xFF1E293B),
        borderRadius: BorderRadius.circular(12),
        border: Border.all(
          color: _getStatusColor(feedback.status).withValues(alpha: 0.3),
        ),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Expanded(
                child: Text(
                  feedback.title,
                  style: ATextStyle.large.copyWith(
                    color: Colors.white,
                    fontWeight: FontWeight.w600,
                  ),
                ),
              ),
              _buildStatusBadge(feedback.status),
            ],
          ),
          const SizedBox(height: 16),

          // Student info
          if (feedback.studentName != null) ...[
            Row(
              children: [
                Icon(
                  Icons.person,
                  color: AColor.fitgoGreen,
                  size: 20,
                ),
                const SizedBox(width: 8),
                Text(
                  'Öğrenci: ${feedback.studentName}',
                  style: ATextStyle.medium.copyWith(
                    color: Colors.white70,
                    fontWeight: FontWeight.w500,
                  ),
                ),
              ],
            ),
            const SizedBox(height: 8),
          ],

          // Date info
          Row(
            children: [
              Icon(
                Icons.calendar_today,
                color: Colors.white54,
                size: 18,
              ),
              const SizedBox(width: 8),
              Text(
                'Gönderilme: ${DateFormat('dd MMM yyyy, HH:mm').format(feedback.feedbackDate)}',
                style: ATextStyle.medium.copyWith(
                  color: Colors.white54,
                ),
              ),
            ],
          ),

          // Waiting time for unresponded feedback
          if (feedback.status == FeedbackStatus.pending) ...[
            const SizedBox(height: 8),
            Row(
              children: [
                Icon(
                  Icons.schedule,
                  color: Colors.orange,
                  size: 18,
                ),
                const SizedBox(width: 8),
                Text(
                  'Bekliyor: ${DateTime.now().difference(feedback.feedbackDate).inDays} gün',
                  style: ATextStyle.medium.copyWith(
                    color: Colors.orange,
                    fontWeight: FontWeight.w500,
                  ),
                ),
              ],
            ),
          ],

          // Response date if available
          if (feedback.instructorResponseDate != null) ...[
            const SizedBox(height: 8),
            Row(
              children: [
                Icon(
                  Icons.reply,
                  color: AColor.fitgoGreen,
                  size: 18,
                ),
                const SizedBox(width: 8),
                Text(
                  'Yanıtlanma: ${DateFormat('dd MMM yyyy, HH:mm').format(feedback.instructorResponseDate!)}',
                  style: ATextStyle.medium.copyWith(
                    color: AColor.fitgoGreen,
                  ),
                ),
              ],
            ),
          ],
        ],
      ),
    );
  }

  Widget _buildStudentNotes(Feedback feedback) {
    return Container(
      width: double.infinity,
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: const Color(0xFF1E293B),
        borderRadius: BorderRadius.circular(12),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            'Öğrenci Notları',
            style: ATextStyle.large.copyWith(
              color: Colors.white,
              fontWeight: FontWeight.w600,
            ),
          ),
          const SizedBox(height: 12),
          Text(
            feedback.studentNotes,
            style: ATextStyle.medium.copyWith(
              color: Colors.white70,
              height: 1.5,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildPhotosSection(BuildContext context, List<FeedbackPhoto> photos) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'Öğrenci Fotoğrafları',
          style: ATextStyle.large.copyWith(
            color: Colors.white,
            fontWeight: FontWeight.w600,
          ),
        ),
        const SizedBox(height: 12),
        Container(
          padding: const EdgeInsets.all(16),
          decoration: BoxDecoration(
            color: const Color(0xFF1E293B),
            borderRadius: BorderRadius.circular(12),
          ),
          child: Column(
            children: [
              Row(
                children: photos
                    .map((photo) => Expanded(
                          child: Padding(
                            padding: EdgeInsets.only(
                              right: photo != photos.last ? 12 : 0,
                            ),
                            child: _buildPhotoCard(context, photo),
                          ),
                        ))
                    .toList(),
              ),
            ],
          ),
        ),
      ],
    );
  }

  Widget _buildPhotoCard(BuildContext context, FeedbackPhoto photo) {
    return GestureDetector(
      onTap: () => _showPhotoDialog(context, photo),
      child: Container(
        height: 120,
        decoration: BoxDecoration(
          borderRadius: BorderRadius.circular(8),
          border: Border.all(color: Colors.white24),
        ),
        child: Stack(
          children: [
            ClipRRect(
              borderRadius: BorderRadius.circular(7),
              child: Image.network(
                photo.photoUrl,
                width: double.infinity,
                height: double.infinity,
                fit: BoxFit.cover,
                loadingBuilder: (context, child, loadingProgress) {
                  if (loadingProgress == null) return child;
                  return Center(
                    child: CircularProgressIndicator(
                      valueColor:
                          AlwaysStoppedAnimation<Color>(AColor.fitgoGreen),
                    ),
                  );
                },
                errorBuilder: (context, error, stackTrace) {
                  return Container(
                    color: Colors.grey[800],
                    child: Icon(
                      Icons.error_outline,
                      color: Colors.white54,
                      size: 32,
                    ),
                  );
                },
              ),
            ),
            Positioned(
              bottom: 4,
              left: 4,
              child: Container(
                padding: const EdgeInsets.symmetric(horizontal: 6, vertical: 2),
                decoration: BoxDecoration(
                  color: Colors.black.withValues(alpha: 0.7),
                  borderRadius: BorderRadius.circular(4),
                ),
                child: Text(
                  photo.photoType.displayName,
                  style: ATextStyle.small.copyWith(
                    color: Colors.white,
                    fontSize: 10,
                  ),
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildResponseSection(
    BuildContext context,
    Feedback feedback,
    FeedbackDetailState state,
    FeedbackDetailNotifier notifier,
    TextEditingController responseController,
  ) {
    if (feedback.hasInstructorResponse) {
      return _buildExistingResponse(feedback);
    }

    return _buildResponseForm(context, state, notifier, responseController);
  }

  Widget _buildExistingResponse(Feedback feedback) {
    return Container(
      width: double.infinity,
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: const Color(0xFF1E293B),
        borderRadius: BorderRadius.circular(12),
        border: Border.all(
          color: AColor.fitgoGreen.withValues(alpha: 0.3),
        ),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Icon(
                Icons.reply,
                color: AColor.fitgoGreen,
                size: 20,
              ),
              const SizedBox(width: 8),
              Text(
                'Yanıtınız',
                style: ATextStyle.large.copyWith(
                  color: AColor.fitgoGreen,
                  fontWeight: FontWeight.w600,
                ),
              ),
            ],
          ),
          const SizedBox(height: 12),
          Text(
            feedback.instructorResponse!,
            style: ATextStyle.medium.copyWith(
              color: Colors.white70,
              height: 1.5,
            ),
          ),
          const SizedBox(height: 12),
          Container(
            padding: const EdgeInsets.all(8),
            decoration: BoxDecoration(
              color: AColor.fitgoGreen.withValues(alpha: 0.1),
              borderRadius: BorderRadius.circular(6),
            ),
            child: Row(
              children: [
                Icon(
                  Icons.info_outline,
                  color: AColor.fitgoGreen,
                  size: 16,
                ),
                const SizedBox(width: 8),
                Expanded(
                  child: Text(
                    'Bu geri bildirim yanıtlandı ve kapatıldı. Yeni bir yanıt gönderilemez.',
                    style: ATextStyle.small.copyWith(
                      color: AColor.fitgoGreen,
                    ),
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildResponseForm(
    BuildContext context,
    FeedbackDetailState state,
    FeedbackDetailNotifier notifier,
    TextEditingController responseController,
  ) {
    return Container(
      width: double.infinity,
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: const Color(0xFF1E293B),
        borderRadius: BorderRadius.circular(12),
        border: Border.all(
          color: Colors.orange.withValues(alpha: 0.3),
        ),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Icon(
                Icons.edit,
                color: Colors.orange,
                size: 20,
              ),
              const SizedBox(width: 8),
              Text(
                'Yanıt Yazın',
                style: ATextStyle.large.copyWith(
                  color: Colors.orange,
                  fontWeight: FontWeight.w600,
                ),
              ),
            ],
          ),
          const SizedBox(height: 12),

          Text(
            'Öğrencinize detaylı geri bildirim verin. Bu yanıt tek seferlik olacak ve geri bildirim kapatılacak.',
            style: ATextStyle.small.copyWith(
              color: Colors.white54,
            ),
          ),
          const SizedBox(height: 16),

          CustomTextFormField(
            controller: responseController,
            hintText: 'Öğrencinize detaylı geri bildiriminizi yazın...',
            onChanged: notifier.updateResponseText,
            maxLines: 8,
            minLines: 4,
          ),
          const SizedBox(height: 16),

          // Error message
          if (state.error != null) ...[
            Container(
              padding: const EdgeInsets.all(12),
              decoration: BoxDecoration(
                color: Colors.red.withValues(alpha: 0.1),
                borderRadius: BorderRadius.circular(8),
                border: Border.all(color: Colors.red.withValues(alpha: 0.3)),
              ),
              child: Row(
                children: [
                  Icon(Icons.error_outline, color: Colors.red, size: 20),
                  const SizedBox(width: 8),
                  Expanded(
                    child: Text(
                      state.error!,
                      style: ATextStyle.small.copyWith(color: Colors.red),
                    ),
                  ),
                ],
              ),
            ),
            const SizedBox(height: 16),
          ],

          // Submit button
          SizedBox(
            width: double.infinity,
            height: 50,
            child: ElevatedButton(
              onPressed: state.isSubmittingResponse
                  ? null
                  : () => notifier.submitResponse(),
              style: ElevatedButton.styleFrom(
                backgroundColor: AColor.fitgoGreen,
                foregroundColor: Colors.white,
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(12),
                ),
              ),
              child: state.isSubmittingResponse
                  ? Row(
                      mainAxisAlignment: MainAxisAlignment.center,
                      children: [
                        SizedBox(
                          width: 20,
                          height: 20,
                          child: CircularProgressIndicator(
                            strokeWidth: 2,
                            valueColor:
                                AlwaysStoppedAnimation<Color>(Colors.white),
                          ),
                        ),
                        const SizedBox(width: 12),
                        Text(
                          'Gönderiliyor...',
                          style: ATextStyle.medium.copyWith(
                            color: Colors.white,
                            fontWeight: FontWeight.w600,
                          ),
                        ),
                      ],
                    )
                  : Text(
                      'Yanıt Gönder',
                      style: ATextStyle.medium.copyWith(
                        color: Colors.white,
                        fontWeight: FontWeight.w600,
                      ),
                    ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildStatusBadge(FeedbackStatus status) {
    final color = _getStatusColor(status);
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
      decoration: BoxDecoration(
        color: color.withValues(alpha: 0.2),
        borderRadius: BorderRadius.circular(6),
        border: Border.all(color: color.withValues(alpha: 0.5)),
      ),
      child: Text(
        status.displayName,
        style: ATextStyle.small.copyWith(
          color: color,
          fontWeight: FontWeight.w500,
        ),
      ),
    );
  }

  Color _getStatusColor(FeedbackStatus status) {
    switch (status) {
      case FeedbackStatus.pending:
        return Colors.orange;
      case FeedbackStatus.reviewed:
        return AColor.fitgoGreen;
      case FeedbackStatus.completed:
        return Colors.grey;
    }
  }

  Widget _buildErrorState(String error, FeedbackDetailNotifier notifier) {
    return Center(
      child: Padding(
        padding: const EdgeInsets.all(32),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              Icons.error_outline,
              size: 80,
              color: Colors.red.withValues(alpha: 0.7),
            ),
            const SizedBox(height: 24),
            Text(
              'Bir hata oluştu',
              style: ATextStyle.large.copyWith(
                color: Colors.white70,
              ),
              textAlign: TextAlign.center,
            ),
            const SizedBox(height: 12),
            Text(
              error,
              style: ATextStyle.medium.copyWith(
                color: Colors.white54,
              ),
              textAlign: TextAlign.center,
            ),
            const SizedBox(height: 24),
            ElevatedButton(
              onPressed: () => notifier.refresh(),
              style: ElevatedButton.styleFrom(
                backgroundColor: AColor.fitgoGreen,
                foregroundColor: Colors.white,
              ),
              child: Text('Tekrar Dene'),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildNotFoundState() {
    return Center(
      child: Padding(
        padding: const EdgeInsets.all(32),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              Icons.search_off,
              size: 80,
              color: Colors.white24,
            ),
            const SizedBox(height: 24),
            Text(
              'Geri bildirim bulunamadı',
              style: ATextStyle.large.copyWith(
                color: Colors.white70,
              ),
              textAlign: TextAlign.center,
            ),
            const SizedBox(height: 12),
            Text(
              'Bu geri bildirim silinmiş olabilir veya erişim izniniz bulunmuyor',
              style: ATextStyle.medium.copyWith(
                color: Colors.white54,
              ),
              textAlign: TextAlign.center,
            ),
          ],
        ),
      ),
    );
  }

  void _showPhotoDialog(BuildContext context, FeedbackPhoto photo) {
    showDialog(
      context: context,
      builder: (context) => Dialog(
        backgroundColor: Colors.transparent,
        child: Stack(
          children: [
            Center(
              child: Container(
                constraints: BoxConstraints(
                  maxWidth: MediaQuery.of(context).size.width * 0.9,
                  maxHeight: MediaQuery.of(context).size.height * 0.8,
                ),
                child: ClipRRect(
                  borderRadius: BorderRadius.circular(12),
                  child: Image.network(
                    photo.photoUrl,
                    fit: BoxFit.contain,
                  ),
                ),
              ),
            ),
            Positioned(
              top: 40,
              right: 20,
              child: GestureDetector(
                onTap: () => Navigator.of(context).pop(),
                child: Container(
                  padding: const EdgeInsets.all(8),
                  decoration: BoxDecoration(
                    color: Colors.black.withValues(alpha: 0.7),
                    shape: BoxShape.circle,
                  ),
                  child: const Icon(
                    Icons.close,
                    color: Colors.white,
                    size: 24,
                  ),
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }
}
