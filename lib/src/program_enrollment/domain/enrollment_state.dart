import 'package:fitgo_app/src/program_enrollment/domain/enrollment_package.dart';

// Main enrollment state
class EnrollmentState {
  final String? trainerId;
  final String? trainerName;
  final DurationOption selectedDuration;
  final EnrollmentPackage? selectedPackage;
  final List<EnrollmentPackage> availablePackages;
  final Map<String, List<PackagePricing>> pricing;
  final PromoCode? appliedPromoCode;
  final bool isLoadingPackages;
  final bool isValidatingPromoCode;
  final String? error;
  final EnrollmentTheme theme;

  const EnrollmentState({
    this.trainerId,
    this.trainerName,
    this.selectedDuration = DurationOption.oneMonth,
    this.selectedPackage,
    this.availablePackages = const [],
    this.pricing = const {},
    this.appliedPromoCode,
    this.isLoadingPackages = false,
    this.isValidatingPromoCode = false,
    this.error,
    this.theme = EnrollmentTheme.standard,
  });

  EnrollmentState copyWith({
    String? trainerId,
    String? trainerName,
    DurationOption? selectedDuration,
    EnrollmentPackage? selectedPackage,
    List<EnrollmentPackage>? availablePackages,
    Map<String, List<PackagePricing>>? pricing,
    PromoCode? appliedPromoCode,
    bool? isLoadingPackages,
    bool? isValidatingPromoCode,
    String? error,
    EnrollmentTheme? theme,
    bool clearPromoCode = false,
    bool clearError = false,
  }) {
    return EnrollmentState(
      trainerId: trainerId ?? this.trainerId,
      trainerName: trainerName ?? this.trainerName,
      selectedDuration: selectedDuration ?? this.selectedDuration,
      selectedPackage: selectedPackage ?? this.selectedPackage,
      availablePackages: availablePackages ?? this.availablePackages,
      pricing: pricing ?? this.pricing,
      appliedPromoCode: clearPromoCode ? null : (appliedPromoCode ?? this.appliedPromoCode),
      isLoadingPackages: isLoadingPackages ?? this.isLoadingPackages,
      isValidatingPromoCode: isValidatingPromoCode ?? this.isValidatingPromoCode,
      error: clearError ? null : (error ?? this.error),
      theme: theme ?? this.theme,
    );
  }

  // Get current pricing for selected package and duration
  PackagePricing? get currentPricing {
    if (selectedPackage == null) return null;
    
    final packagePricing = pricing[selectedPackage!.id];
    if (packagePricing == null) return null;
    
    return packagePricing.firstWhere(
      (p) => p.duration == selectedDuration,
      orElse: () => packagePricing.first,
    );
  }

  // Calculate final price with promo code
  double? get finalPrice {
    final basePricing = currentPricing;
    if (basePricing == null) return null;
    
    double price = basePricing.finalPrice;
    
    if (appliedPromoCode != null) {
      final discount = appliedPromoCode!.calculateDiscount(price);
      price = (price - discount).clamp(0.0, double.infinity);
    }
    
    return price;
  }

  // Get discount amount from promo code
  double? get promoDiscount {
    final basePricing = currentPricing;
    if (basePricing == null || appliedPromoCode == null) return null;
    
    return appliedPromoCode!.calculateDiscount(basePricing.finalPrice);
  }

  // Check if enrollment is ready
  bool get canProceed {
    return trainerId != null && 
           selectedPackage != null && 
           currentPricing != null &&
           !isLoadingPackages &&
           !isValidatingPromoCode;
  }
}

// Theme for enrollment screen
enum EnrollmentTheme {
  standard,
  premium;

  bool get isPremium => this == EnrollmentTheme.premium;
}

// Promo code validation result
class PromoCodeValidationResult {
  final bool isValid;
  final PromoCode? promoCode;
  final String? errorMessage;

  const PromoCodeValidationResult({
    required this.isValid,
    this.promoCode,
    this.errorMessage,
  });

  factory PromoCodeValidationResult.success(PromoCode promoCode) {
    return PromoCodeValidationResult(
      isValid: true,
      promoCode: promoCode,
    );
  }

  factory PromoCodeValidationResult.error(String message) {
    return PromoCodeValidationResult(
      isValid: false,
      errorMessage: message,
    );
  }
}
