// Domain model for enrollment packages
class EnrollmentPackage {
  final String id;
  final String name;
  final String displayName;
  final PackageType type;
  final List<PackageFeature> features;
  final bool isPopular;

  const EnrollmentPackage({
    required this.id,
    required this.name,
    required this.displayName,
    required this.type,
    required this.features,
    this.isPopular = false,
  });

  factory EnrollmentPackage.fromJson(Map<String, dynamic> json) {
    return EnrollmentPackage(
      id: json['id'] as String,
      name: json['name'] as String,
      displayName: json['display_name'] as String,
      type: PackageType.values.firstWhere(
        (e) => e.name == json['type'],
        orElse: () => PackageType.standard,
      ),
      features: (json['features'] as List<dynamic>?)
          ?.map((e) => PackageFeature.fromJson(e as Map<String, dynamic>))
          .toList() ?? [],
      isPopular: json['is_popular'] as bool? ?? false,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'name': name,
      'display_name': displayName,
      'type': type.name,
      'features': features.map((e) => e.toJson()).toList(),
      'is_popular': isPopular,
    };
  }
}

// Package types
enum PackageType {
  standard,
  premium;

  String get displayName {
    switch (this) {
      case PackageType.standard:
        return 'Standard Paket';
      case PackageType.premium:
        return 'FitGo Premium ile';
    }
  }

  bool get isPremium => this == PackageType.premium;
}

// Package features
class PackageFeature {
  final String id;
  final String title;
  final String? description;
  final String? icon;
  final bool isHighlight;
  final bool isNew;

  const PackageFeature({
    required this.id,
    required this.title,
    this.description,
    this.icon,
    this.isHighlight = false,
    this.isNew = false,
  });

  factory PackageFeature.fromJson(Map<String, dynamic> json) {
    return PackageFeature(
      id: json['id'] as String,
      title: json['title'] as String,
      description: json['description'] as String?,
      icon: json['icon'] as String?,
      isHighlight: json['is_highlight'] as bool? ?? false,
      isNew: json['is_new'] as bool? ?? false,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'title': title,
      'description': description,
      'icon': icon,
      'is_highlight': isHighlight,
      'is_new': isNew,
    };
  }
}

// Duration options
enum DurationOption {
  oneMonth,
  threeMonths,
  sixMonths,
  oneYear;

  String get displayName {
    switch (this) {
      case DurationOption.oneMonth:
        return '1 Ay';
      case DurationOption.threeMonths:
        return '3 Ay';
      case DurationOption.sixMonths:
        return '6 Ay';
      case DurationOption.oneYear:
        return '1 Yıl';
    }
  }

  int get months {
    switch (this) {
      case DurationOption.oneMonth:
        return 1;
      case DurationOption.threeMonths:
        return 3;
      case DurationOption.sixMonths:
        return 6;
      case DurationOption.oneYear:
        return 12;
    }
  }
}

// Pricing model
class PackagePricing {
  final String packageId;
  final DurationOption duration;
  final double originalPrice;
  final double? discountedPrice;
  final double? discountPercentage;
  final String currency;

  const PackagePricing({
    required this.packageId,
    required this.duration,
    required this.originalPrice,
    this.discountedPrice,
    this.discountPercentage,
    this.currency = '₺',
  });

  double get finalPrice => discountedPrice ?? originalPrice;

  bool get hasDiscount => discountedPrice != null && discountedPrice! < originalPrice;

  factory PackagePricing.fromJson(Map<String, dynamic> json) {
    return PackagePricing(
      packageId: json['package_id'] as String,
      duration: DurationOption.values.firstWhere(
        (e) => e.name == json['duration'],
        orElse: () => DurationOption.oneMonth,
      ),
      originalPrice: (json['original_price'] as num).toDouble(),
      discountedPrice: (json['discounted_price'] as num?)?.toDouble(),
      discountPercentage: (json['discount_percentage'] as num?)?.toDouble(),
      currency: json['currency'] as String? ?? '₺',
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'package_id': packageId,
      'duration': duration.name,
      'original_price': originalPrice,
      'discounted_price': discountedPrice,
      'discount_percentage': discountPercentage,
      'currency': currency,
    };
  }
}

// Promo code model
class PromoCode {
  final String code;
  final String? description;
  final double? discountAmount;
  final double? discountPercentage;
  final DateTime? expiryDate;
  final bool isValid;

  const PromoCode({
    required this.code,
    this.description,
    this.discountAmount,
    this.discountPercentage,
    this.expiryDate,
    this.isValid = true,
  });

  bool get hasDiscount => discountAmount != null || discountPercentage != null;

  double calculateDiscount(double originalPrice) {
    if (!isValid || !hasDiscount) return 0.0;
    
    if (discountAmount != null) {
      return discountAmount!;
    }
    
    if (discountPercentage != null) {
      return originalPrice * (discountPercentage! / 100);
    }
    
    return 0.0;
  }

  factory PromoCode.fromJson(Map<String, dynamic> json) {
    return PromoCode(
      code: json['code'] as String,
      description: json['description'] as String?,
      discountAmount: (json['discount_amount'] as num?)?.toDouble(),
      discountPercentage: (json['discount_percentage'] as num?)?.toDouble(),
      expiryDate: json['expiry_date'] != null 
          ? DateTime.parse(json['expiry_date'] as String)
          : null,
      isValid: json['is_valid'] as bool? ?? true,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'code': code,
      'description': description,
      'discount_amount': discountAmount,
      'discount_percentage': discountPercentage,
      'expiry_date': expiryDate?.toIso8601String(),
      'is_valid': isValid,
    };
  }
}
