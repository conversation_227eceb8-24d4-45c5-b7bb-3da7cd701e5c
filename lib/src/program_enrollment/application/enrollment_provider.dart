import 'package:hooks_riverpod/hooks_riverpod.dart';
import 'package:fitgo_app/src/program_enrollment/domain/enrollment_package.dart';
import 'package:fitgo_app/src/program_enrollment/domain/enrollment_state.dart';
import 'package:fitgo_app/src/program_enrollment/application/enrollment_repository.dart';

// Repository provider following established patterns
final enrollmentRepositoryProvider = Provider<EnrollmentRepository>((ref) {
  return EnrollmentRepository();
});

// Main enrollment state notifier
class EnrollmentNotifier extends StateNotifier<EnrollmentState> {
  final EnrollmentRepository _repository;

  EnrollmentNotifier({
    required EnrollmentRepository repository,
  }) : _repository = repository, super(const EnrollmentState());

  // Initialize enrollment for a trainer
  Future<void> initializeEnrollment({
    required String trainerId,
    required String trainerName,
  }) async {
    state = state.copyWith(
      trainerId: trainerId,
      trainerName: trainerName,
      isLoadingPackages: true,
      clearError: true,
    );

    try {
      // Load packages and pricing concurrently
      final results = await Future.wait([
        _repository.getAvailablePackages(trainerId),
        _repository.getPackagePricing(),
      ]);

      final packages = results[0] as List<EnrollmentPackage>;
      final pricing = results[1] as Map<String, List<PackagePricing>>;

      // Select standard package by default
      final defaultPackage = packages.firstWhere(
        (p) => p.type == PackageType.standard,
        orElse: () => packages.first,
      );

      state = state.copyWith(
        availablePackages: packages,
        pricing: pricing,
        selectedPackage: defaultPackage,
        isLoadingPackages: false,
        theme: defaultPackage.type.isPremium 
            ? EnrollmentTheme.premium 
            : EnrollmentTheme.standard,
      );
    } catch (e) {
      state = state.copyWith(
        isLoadingPackages: false,
        error: 'Paketler yüklenirken hata oluştu: ${e.toString()}',
      );
    }
  }

  // Select duration
  void selectDuration(DurationOption duration) {
    state = state.copyWith(selectedDuration: duration);
  }

  // Select package
  void selectPackage(EnrollmentPackage package) {
    state = state.copyWith(
      selectedPackage: package,
      theme: package.type.isPremium 
          ? EnrollmentTheme.premium 
          : EnrollmentTheme.standard,
      clearError: true,
    );
  }

  // Apply promo code
  Future<void> applyPromoCode(String code) async {
    if (code.trim().isEmpty) return;

    state = state.copyWith(
      isValidatingPromoCode: true,
      clearError: true,
    );

    try {
      final result = await _repository.validatePromoCode(code.trim());
      
      if (result.isValid && result.promoCode != null) {
        state = state.copyWith(
          appliedPromoCode: result.promoCode,
          isValidatingPromoCode: false,
        );
      } else {
        state = state.copyWith(
          isValidatingPromoCode: false,
          error: result.errorMessage ?? 'Geçersiz promosyon kodu',
        );
      }
    } catch (e) {
      state = state.copyWith(
        isValidatingPromoCode: false,
        error: 'Promosyon kodu doğrulanırken hata oluştu',
      );
    }
  }

  // Remove promo code
  void removePromoCode() {
    state = state.copyWith(clearPromoCode: true);
  }

  // Clear error
  void clearError() {
    state = state.copyWith(clearError: true);
  }

  // Submit enrollment
  Future<bool> submitEnrollment() async {
    if (!state.canProceed) return false;

    try {
      final success = await _repository.submitEnrollment(
        trainerId: state.trainerId!,
        packageId: state.selectedPackage!.id,
        duration: state.selectedDuration,
        promoCode: state.appliedPromoCode?.code,
      );

      if (!success) {
        state = state.copyWith(
          error: 'Kayıt işlemi sırasında hata oluştu',
        );
      }

      return success;
    } catch (e) {
      state = state.copyWith(
        error: 'Kayıt işlemi sırasında hata oluştu: ${e.toString()}',
      );
      return false;
    }
  }

  // Reset state
  void reset() {
    state = const EnrollmentState();
  }
}

// Main enrollment provider
final enrollmentProvider = StateNotifierProvider<EnrollmentNotifier, EnrollmentState>(
  (ref) {
    final repository = ref.read(enrollmentRepositoryProvider);
    return EnrollmentNotifier(repository: repository);
  },
);

// Convenience providers for specific state parts
final selectedPackageProvider = Provider<EnrollmentPackage?>((ref) {
  return ref.watch(enrollmentProvider).selectedPackage;
});

final currentPricingProvider = Provider<PackagePricing?>((ref) {
  return ref.watch(enrollmentProvider).currentPricing;
});

final finalPriceProvider = Provider<double?>((ref) {
  return ref.watch(enrollmentProvider).finalPrice;
});

final enrollmentThemeProvider = Provider<EnrollmentTheme>((ref) {
  return ref.watch(enrollmentProvider).theme;
});

final canProceedProvider = Provider<bool>((ref) {
  return ref.watch(enrollmentProvider).canProceed;
});
