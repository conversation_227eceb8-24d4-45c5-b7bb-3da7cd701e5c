import 'package:flutter/material.dart';
import 'package:flutter_hooks/flutter_hooks.dart';
import 'package:hooks_riverpod/hooks_riverpod.dart';
import 'package:fitgo_app/src/shared/widgets/text_widget/text_widget.dart';
import 'package:fitgo_app/src/shared/constants/app_text_style.dart';
import 'package:fitgo_app/src/shared/extensions/string_hardcoded.dart';
import 'package:fitgo_app/src/theme/colors.dart';
import 'package:fitgo_app/src/program_enrollment/domain/enrollment_package.dart';
import 'package:fitgo_app/src/program_enrollment/domain/enrollment_state.dart';
import 'package:fitgo_app/src/program_enrollment/application/enrollment_provider.dart';
import 'package:fitgo_app/src/program_enrollment/presentation/components/duration_selector.dart';
import 'package:fitgo_app/src/program_enrollment/presentation/components/package_card.dart';
import 'package:fitgo_app/src/program_enrollment/presentation/components/pricing_display.dart';
import 'package:fitgo_app/src/program_enrollment/presentation/components/feature_list.dart';
import 'package:fitgo_app/src/program_enrollment/presentation/components/promo_code_section.dart';
import 'package:fitgo_app/src/program_enrollment/presentation/components/enrollment_button.dart';
import 'package:fitgo_app/src/payment/presentation/mock_payment_page.dart';

class ProgramEnrollmentView extends HookConsumerWidget {
  final String trainerId;
  final String trainerName;

  const ProgramEnrollmentView({
    super.key,
    required this.trainerId,
    required this.trainerName,
  });

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final enrollmentState = ref.watch(enrollmentProvider);
    final theme = ref.watch(enrollmentThemeProvider);

    // Initialize enrollment when screen loads
    useEffect(() {
      WidgetsBinding.instance.addPostFrameCallback((_) {
        ref.read(enrollmentProvider.notifier).initializeEnrollment(
          trainerId: trainerId,
          trainerName: trainerName,
        );
      });
      return null;
    }, []);

    return Scaffold(
      backgroundColor: _getBackgroundColor(theme),
      appBar: AppBar(
        backgroundColor: _getBackgroundColor(theme),
        elevation: 0,
        leading: IconButton(
          icon: Icon(
            Icons.arrow_back_ios,
            color: Colors.white,
          ),
          onPressed: () => Navigator.of(context).pop(),
        ),
        title: TextWidget(
          'Planını Seç'.hardcoded,
          style: ATextStyle.large.copyWith(
            color: Colors.white,
            fontWeight: FontWeight.bold,
          ),
        ),
        centerTitle: true,
      ),
      body: enrollmentState.isLoadingPackages
          ? _buildLoadingState()
          : enrollmentState.error != null
              ? _buildErrorState(context, ref, enrollmentState.error!)
              : _buildEnrollmentContent(context, ref, enrollmentState, theme),
    );
  }

  Widget _buildLoadingState() {
    return const Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          CircularProgressIndicator(
            valueColor: AlwaysStoppedAnimation<Color>(AColor.fitgoGreen),
          ),
          SizedBox(height: 16),
          TextWidget(
            'Paketler yükleniyor...',
            style: TextStyle(color: Colors.white70),
          ),
        ],
      ),
    );
  }

  Widget _buildErrorState(BuildContext context, WidgetRef ref, String error) {
    return Center(
      child: Padding(
        padding: const EdgeInsets.all(24),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              Icons.error_outline,
              size: 64,
              color: Colors.red[400],
            ),
            const SizedBox(height: 16),
            TextWidget(
              error,
              style: ATextStyle.medium.copyWith(
                color: Colors.white,

              ),
            ),
            const SizedBox(height: 24),
            ElevatedButton(
              onPressed: () {
                ref.read(enrollmentProvider.notifier).initializeEnrollment(
                  trainerId: trainerId,
                  trainerName: trainerName,
                );
              },
              style: ElevatedButton.styleFrom(
                backgroundColor: AColor.fitgoGreen,
                foregroundColor: Colors.white,
                padding: const EdgeInsets.symmetric(horizontal: 32, vertical: 12),
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(8),
                ),
              ),
              child: const TextWidget('Tekrar Dene'),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildEnrollmentContent(
    BuildContext context,
    WidgetRef ref,
    EnrollmentState state,
    EnrollmentTheme theme,
  ) {
    return Column(
      children: [
        Expanded(
          child: SingleChildScrollView(
            padding: const EdgeInsets.all(16),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                _buildHeader(state.trainerName ?? trainerName),
                const SizedBox(height: 24),
                
                // Duration selector
                DurationSelector(
                  selectedDuration: state.selectedDuration,
                  onDurationChanged: (duration) {
                    ref.read(enrollmentProvider.notifier).selectDuration(duration);
                  },
                ),
                const SizedBox(height: 24),
                
                // Package selection
                _buildPackageSelection(ref, state, theme),
                const SizedBox(height: 24),
                
                // Pricing display
                if (state.selectedPackage != null)
                  PricingDisplay(
                    pricing: state.currentPricing,
                    finalPrice: state.finalPrice,
                    promoDiscount: state.promoDiscount,
                    theme: theme,
                  ),
                const SizedBox(height: 24),
                
                // Feature list
                if (state.selectedPackage != null)
                  FeatureList(
                    features: state.selectedPackage!.features,
                    theme: theme,
                  ),
                const SizedBox(height: 24),
                
                // Promo code section
                PromoCodeSection(
                  appliedPromoCode: state.appliedPromoCode,
                  isValidating: state.isValidatingPromoCode,
                  theme: theme,
                ),
                
                const SizedBox(height: 100), // Space for sticky button
              ],
            ),
          ),
        ),
        
        // Sticky bottom button
        EnrollmentButton(
          canProceed: state.canProceed,
          theme: theme,
          onPressed: () {
            // Navigate to payment screen
            Navigator.of(context).push(
              MaterialPageRoute(
                builder: (context) => MockPaymentPage(
                  trainerId: state.trainerId!,
                  trainerName: state.trainerName!,
                  packageId: state.selectedPackage!.id,
                  packageName: state.selectedPackage!.displayName,
                  duration: state.selectedDuration.displayName,
                  basePrice: state.currentPricing?.originalPrice ?? 0.0,
                  discountAmount: state.currentPricing?.hasDiscount == true
                      ? (state.currentPricing!.originalPrice - state.currentPricing!.discountedPrice!)
                      : null,
                  promoDiscount: state.promoDiscount,
                  isPremium: state.selectedPackage!.type.isPremium,
                ),
              ),
            );
          },
        ),
      ],
    );
  }

  Widget _buildHeader(String trainerName) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        TextWidget(
          'Planını Seç'.hardcoded,
          style: ATextStyle.large.copyWith(
            color: Colors.white,
            fontWeight: FontWeight.bold,
            fontSize: 28,
          ),
        ),
        const SizedBox(height: 8),
        TextWidget(
          'Sana uygun olan paketi seçerek $trainerName eğitmenine kayıt olabilirsin.'.hardcoded,
          style: ATextStyle.medium.copyWith(
            color: Colors.white70,
            height: 1.4,
          ),
        ),
      ],
    );
  }

  Widget _buildPackageSelection(
    WidgetRef ref,
    EnrollmentState state,
    EnrollmentTheme theme,
  ) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        TextWidget(
          'Paket Seçimi'.hardcoded,
          style: ATextStyle.large.copyWith(
            color: Colors.white,
            fontWeight: FontWeight.w600,
          ),
        ),
        const SizedBox(height: 16),
        ...state.availablePackages.map((package) {
          final isSelected = state.selectedPackage?.id == package.id;
          return Padding(
            padding: const EdgeInsets.only(bottom: 12),
            child: PackageCard(
              package: package,
              isSelected: isSelected,
              theme: theme,
              onTap: () {
                ref.read(enrollmentProvider.notifier).selectPackage(package);
              },
            ),
          );
        }),
      ],
    );
  }

  Color _getBackgroundColor(EnrollmentTheme theme) {
    switch (theme) {
      case EnrollmentTheme.standard:
        return const Color(0xFF0F172A); // Standard dark
      case EnrollmentTheme.premium:
        return const Color(0xFF1A1A2E); // Premium dark with slight purple tint
    }
  }
}
