import 'package:flutter/material.dart';
import 'package:fitgo_app/src/shared/widgets/text_widget/text_widget.dart';
import 'package:fitgo_app/src/shared/constants/app_text_style.dart';
import 'package:fitgo_app/src/shared/constants/currency_constants.dart';
import 'package:fitgo_app/src/theme/colors.dart';
import 'package:fitgo_app/src/program_enrollment/domain/enrollment_package.dart';
import 'package:fitgo_app/src/program_enrollment/domain/enrollment_state.dart';

class PricingDisplay extends StatelessWidget {
  final PackagePricing? pricing;
  final double? finalPrice;
  final double? promoDiscount;
  final EnrollmentTheme theme;

  const PricingDisplay({
    super.key,
    required this.pricing,
    required this.finalPrice,
    required this.promoDiscount,
    required this.theme,
  });

  @override
  Widget build(BuildContext context) {
    if (pricing == null || finalPrice == null) {
      return const SizedBox.shrink();
    }

    return Container(
      padding: const EdgeInsets.all(20),
      decoration: BoxDecoration(
        color: _getBackgroundColor(),
        borderRadius: BorderRadius.circular(16),
        border: Border.all(color: _getBorderColor(), width: 1),
        boxShadow: _getBoxShadow(),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Icon(Icons.payments, color: _getAccentColor(), size: 24),
              const SizedBox(width: 8),
              TextWidget(
                'Fiyat Detayları',
                style: ATextStyle.large.copyWith(
                  color: Colors.white,
                  fontWeight: FontWeight.w600,
                ),
              ),
            ],
          ),
          const SizedBox(height: 16),

          // Original price (if there's a discount)
          if (pricing!.hasDiscount) ...[
            Row(
              children: [
                TextWidget(
                  'Liste Fiyatı:',
                  style: ATextStyle.medium.copyWith(color: Colors.white70),
                ),
                const Spacer(),
                TextWidget(
                  pricing!.originalPrice.asCurrency,
                  style: ATextStyle.medium.copyWith(
                    color: Colors.white70,
                    decoration: TextDecoration.lineThrough,
                  ),
                ),
              ],
            ),
            const SizedBox(height: 8),

            Row(
              children: [
                TextWidget(
                  'İndirimli Fiyat:',
                  style: ATextStyle.medium.copyWith(color: Colors.white70),
                ),
                const Spacer(),
                TextWidget(
                  pricing!.discountedPrice!.asCurrency,
                  style: ATextStyle.medium.copyWith(
                    color: AColor.fitgoGreen,
                    fontWeight: FontWeight.w600,
                  ),
                ),
              ],
            ),
            const SizedBox(height: 8),
          ],

          // Promo code discount
          if (promoDiscount != null && promoDiscount! > 0) ...[
            Row(
              children: [
                TextWidget(
                  'Promosyon İndirimi:',
                  style: ATextStyle.medium.copyWith(color: Colors.white70),
                ),
                const Spacer(),
                TextWidget(
                  '-${promoDiscount!.asCurrency}',
                  style: ATextStyle.medium.copyWith(
                    color: Colors.orange,
                    fontWeight: FontWeight.w600,
                  ),
                ),
              ],
            ),
            const SizedBox(height: 8),

            Divider(color: Colors.white24, thickness: 1),
            const SizedBox(height: 8),
          ],

          // Final price
          Row(
            children: [
              TextWidget(
                'Toplam:',
                style: ATextStyle.large.copyWith(
                  color: Colors.white,
                  fontWeight: FontWeight.bold,
                ),
              ),
              const Spacer(),
              AnimatedSwitcher(
                duration: const Duration(milliseconds: 300),
                child: TextWidget(
                  finalPrice!.asCurrency,
                  key: ValueKey(finalPrice),
                  style: ATextStyle.large.copyWith(
                    color: _getAccentColor(),
                    fontWeight: FontWeight.bold,
                    fontSize: 28,
                  ),
                ),
              ),
            ],
          ),

          // Duration info
          const SizedBox(height: 12),
          Container(
            padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
            decoration: BoxDecoration(
              color: _getAccentColor().withOpacity(0.1),
              borderRadius: BorderRadius.circular(8),
            ),
            child: TextWidget(
              '${pricing!.duration.displayName} için',
              style: ATextStyle.small.copyWith(
                color: _getAccentColor(),
                fontWeight: FontWeight.w500,
              ),
            ),
          ),

          // Savings info for longer durations
          if (pricing!.hasDiscount) ...[
            const SizedBox(height: 12),
            Container(
              padding: const EdgeInsets.all(12),
              decoration: BoxDecoration(
                color: AColor.fitgoGreen.withOpacity(0.1),
                borderRadius: BorderRadius.circular(8),
                border: Border.all(
                  color: AColor.fitgoGreen.withOpacity(0.3),
                  width: 1,
                ),
              ),
              child: Row(
                children: [
                  Icon(Icons.savings, color: AColor.fitgoGreen, size: 20),
                  const SizedBox(width: 8),
                  Expanded(
                    child: TextWidget(
                      '%${pricing!.discountPercentage!.toStringAsFixed(0)} tasarruf ediyorsun!',
                      style: ATextStyle.small.copyWith(
                        color: AColor.fitgoGreen,
                        fontWeight: FontWeight.w600,
                      ),
                    ),
                  ),
                ],
              ),
            ),
          ],
        ],
      ),
    );
  }

  Color _getBackgroundColor() {
    switch (theme) {
      case EnrollmentTheme.standard:
        return const Color(0xFF1E293B);
      case EnrollmentTheme.premium:
        return const Color(0xFF2D1B69).withOpacity(0.3);
    }
  }

  Color _getBorderColor() {
    switch (theme) {
      case EnrollmentTheme.standard:
        return const Color(0xFF334155);
      case EnrollmentTheme.premium:
        return const Color(0xFFFFD700).withOpacity(0.3);
    }
  }

  Color _getAccentColor() {
    switch (theme) {
      case EnrollmentTheme.standard:
        return AColor.fitgoGreen;
      case EnrollmentTheme.premium:
        return const Color(0xFFFFD700); // Gold
    }
  }

  List<BoxShadow> _getBoxShadow() {
    if (theme == EnrollmentTheme.premium) {
      return [
        BoxShadow(
          color: const Color(0xFFFFD700).withOpacity(0.1),
          blurRadius: 8,
          offset: const Offset(0, 2),
        ),
      ];
    }
    return [];
  }
}
