import 'package:flutter/material.dart';
import 'package:fitgo_app/src/shared/widgets/text_widget/text_widget.dart';
import 'package:fitgo_app/src/shared/constants/app_text_style.dart';
import 'package:fitgo_app/src/theme/colors.dart';
import 'package:fitgo_app/src/program_enrollment/domain/enrollment_package.dart';
import 'package:fitgo_app/src/program_enrollment/domain/enrollment_state.dart';

class FeatureList extends StatelessWidget {
  final List<PackageFeature> features;
  final EnrollmentTheme theme;

  const FeatureList({
    super.key,
    required this.features,
    required this.theme,
  });

  @override
  Widget build(BuildContext context) {
    return Container(
      padding: const EdgeInsets.all(20),
      decoration: BoxDecoration(
        color: _getBackgroundColor(),
        borderRadius: BorderRadius.circular(16),
        border: Border.all(
          color: _getBorderColor(),
          width: 1,
        ),
        boxShadow: _getBoxShadow(),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Icon(
                theme == EnrollmentTheme.premium 
                    ? Icons.workspace_premium 
                    : Icons.featured_play_list,
                color: _getAccentColor(),
                size: 24,
              ),
              const SizedBox(width: 8),
              TextWidget(
                'Paket Özellikleri',
                style: ATextStyle.large.copyWith(
                  color: Colors.white,
                  fontWeight: FontWeight.w600,
                ),
              ),
            ],
          ),
          const SizedBox(height: 16),
          
          ...features.asMap().entries.map((entry) {
            final index = entry.key;
            final feature = entry.value;
            
            return AnimatedContainer(
              duration: Duration(milliseconds: 200 + (index * 50)),
              curve: Curves.easeInOut,
              margin: const EdgeInsets.only(bottom: 12),
              child: _buildFeatureItem(feature),
            );
          }).toList(),
        ],
      ),
    );
  }

  Widget _buildFeatureItem(PackageFeature feature) {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: _getFeatureBackgroundColor(feature),
        borderRadius: BorderRadius.circular(12),
        border: feature.isHighlight 
            ? Border.all(
                color: _getAccentColor().withOpacity(0.3),
                width: 1,
              )
            : null,
      ),
      child: Row(
        children: [
          // Feature icon
          Container(
            width: 40,
            height: 40,
            decoration: BoxDecoration(
              color: _getIconBackgroundColor(feature),
              borderRadius: BorderRadius.circular(10),
            ),
            child: Center(
              child: feature.icon != null
                  ? TextWidget(
                      feature.icon!,
                      style: const TextStyle(fontSize: 20),
                    )
                  : Icon(
                      _getDefaultIcon(feature),
                      color: _getIconColor(feature),
                      size: 20,
                    ),
            ),
          ),
          const SizedBox(width: 16),
          
          // Feature content
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Row(
                  children: [
                    Expanded(
                      child: TextWidget(
                        feature.title,
                        style: ATextStyle.medium.copyWith(
                          color: _getFeatureTitleColor(feature),
                          fontWeight: feature.isHighlight 
                              ? FontWeight.bold 
                              : FontWeight.w600,
                        ),
                      ),
                    ),
                    
                    // Badges
                    if (feature.isNew) ...[
                      const SizedBox(width: 8),
                      Container(
                        padding: const EdgeInsets.symmetric(
                          horizontal: 8,
                          vertical: 4,
                        ),
                        decoration: BoxDecoration(
                          color: Colors.orange.withOpacity(0.2),
                          borderRadius: BorderRadius.circular(6),
                        ),
                        child: TextWidget(
                          'YENİ',
                          style: ATextStyle.small.copyWith(
                            color: Colors.orange,
                            fontWeight: FontWeight.bold,
                          ),
                        ),
                      ),
                    ],
                    
                    if (feature.isHighlight && theme == EnrollmentTheme.premium) ...[
                      const SizedBox(width: 8),
                      Icon(
                        Icons.star,
                        color: _getAccentColor(),
                        size: 16,
                      ),
                    ],
                  ],
                ),
                
                if (feature.description != null) ...[
                  const SizedBox(height: 4),
                  TextWidget(
                    feature.description!,
                    style: ATextStyle.small.copyWith(
                      color: Colors.white60,
                      height: 1.3,
                    ),
                  ),
                ],
              ],
            ),
          ),
        ],
      ),
    );
  }

  Color _getBackgroundColor() {
    switch (theme) {
      case EnrollmentTheme.standard:
        return const Color(0xFF1E293B);
      case EnrollmentTheme.premium:
        return const Color(0xFF2D1B69).withOpacity(0.3);
    }
  }

  Color _getBorderColor() {
    switch (theme) {
      case EnrollmentTheme.standard:
        return const Color(0xFF334155);
      case EnrollmentTheme.premium:
        return const Color(0xFFFFD700).withOpacity(0.3);
    }
  }

  Color _getAccentColor() {
    switch (theme) {
      case EnrollmentTheme.standard:
        return AColor.fitgoGreen;
      case EnrollmentTheme.premium:
        return const Color(0xFFFFD700); // Gold
    }
  }

  Color _getFeatureBackgroundColor(PackageFeature feature) {
    if (feature.isHighlight && theme == EnrollmentTheme.premium) {
      return _getAccentColor().withOpacity(0.1);
    }
    return const Color(0xFF0F172A).withOpacity(0.5);
  }

  Color _getIconBackgroundColor(PackageFeature feature) {
    if (feature.isHighlight) {
      return _getAccentColor().withOpacity(0.2);
    }
    return const Color(0xFF334155);
  }

  Color _getIconColor(PackageFeature feature) {
    if (feature.isHighlight) {
      return _getAccentColor();
    }
    return Colors.white70;
  }

  Color _getFeatureTitleColor(PackageFeature feature) {
    if (feature.isHighlight && theme == EnrollmentTheme.premium) {
      return _getAccentColor();
    }
    return Colors.white;
  }

  IconData _getDefaultIcon(PackageFeature feature) {
    if (theme == EnrollmentTheme.premium && feature.isHighlight) {
      return Icons.star;
    }
    return Icons.check_circle;
  }

  List<BoxShadow> _getBoxShadow() {
    if (theme == EnrollmentTheme.premium) {
      return [
        BoxShadow(
          color: const Color(0xFFFFD700).withOpacity(0.1),
          blurRadius: 8,
          offset: const Offset(0, 2),
        ),
      ];
    }
    return [];
  }
}
