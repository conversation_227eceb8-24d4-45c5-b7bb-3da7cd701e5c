import 'package:flutter/material.dart';
import 'package:flutter_hooks/flutter_hooks.dart';
import 'package:hooks_riverpod/hooks_riverpod.dart';
import 'package:fitgo_app/src/shared/widgets/text_widget/text_widget.dart';
import 'package:fitgo_app/src/shared/constants/app_text_style.dart';
import 'package:fitgo_app/src/shared/extensions/build_context/screen_util_ext.dart';
import 'package:fitgo_app/src/theme/colors.dart';
import 'package:fitgo_app/src/program_enrollment/domain/enrollment_package.dart';
import 'package:fitgo_app/src/program_enrollment/domain/enrollment_state.dart';
import 'package:fitgo_app/src/program_enrollment/application/enrollment_provider.dart';

class PromoCodeSection extends HookConsumerWidget {
  final PromoCode? appliedPromoCode;
  final bool isValidating;
  final EnrollmentTheme theme;

  const PromoCodeSection({
    super.key,
    required this.appliedPromoCode,
    required this.isValidating,
    required this.theme,
  });

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        if (appliedPromoCode != null) ...[
          _buildAppliedPromoCode(ref),
        ] else ...[
          _buildPromoCodeButton(context, ref),
        ],
      ],
    );
  }

  Widget _buildAppliedPromoCode(WidgetRef ref) {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Colors.green.withOpacity(0.1),
        borderRadius: BorderRadius.circular(12),
        border: Border.all(color: Colors.green.withOpacity(0.3), width: 1),
      ),
      child: Row(
        children: [
          Icon(Icons.local_offer, color: Colors.green, size: 24),
          const SizedBox(width: 12),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                TextWidget(
                  'Promosyon Kodu Uygulandı',
                  style: ATextStyle.medium.copyWith(
                    color: Colors.green,
                    fontWeight: FontWeight.w600,
                  ),
                ),
                const SizedBox(height: 4),
                TextWidget(
                  '${appliedPromoCode!.code} - ${appliedPromoCode!.description ?? 'İndirim uygulandı'}',
                  style: ATextStyle.small.copyWith(color: Colors.white70),
                ),
              ],
            ),
          ),
          GestureDetector(
            onTap: () {
              ref.read(enrollmentProvider.notifier).removePromoCode();
            },
            child: Container(
              padding: const EdgeInsets.all(6),
              decoration: BoxDecoration(
                color: Colors.red.withOpacity(0.2),
                borderRadius: BorderRadius.circular(6),
              ),
              child: Icon(Icons.close, color: Colors.red, size: 16),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildPromoCodeButton(BuildContext context, WidgetRef ref) {
    return GestureDetector(
      onTap: () => _showPromoCodeModal(context, ref),
      child: Container(
        padding: const EdgeInsets.all(16),
        decoration: BoxDecoration(
          color: _getBackgroundColor().withOpacity(0.5),
          borderRadius: BorderRadius.circular(12),
          border: Border.all(color: _getBorderColor(), width: 1),
        ),
        child: Row(
          children: [
            Icon(
              Icons.local_offer_outlined,
              color: _getAccentColor(),
              size: 24,
            ),
            const SizedBox(width: 12),
            Expanded(
              child: TextWidget(
                'Promosyon Kodu',
                style: ATextStyle.medium.copyWith(
                  color: _getAccentColor(),
                  fontWeight: FontWeight.w600,
                ),
              ),
            ),
            Icon(Icons.arrow_forward_ios, color: _getAccentColor(), size: 16),
          ],
        ),
      ),
    );
  }

  void _showPromoCodeModal(BuildContext context, WidgetRef ref) {
    showModalBottomSheet(
      context: context,
      isScrollControlled: true,
      backgroundColor: Colors.transparent,
      builder: (context) => PromoCodeModal(theme: theme),
    );
  }

  Color _getBackgroundColor() {
    switch (theme) {
      case EnrollmentTheme.standard:
        return const Color(0xFF1E293B);
      case EnrollmentTheme.premium:
        return const Color(0xFF2D1B69);
    }
  }

  Color _getBorderColor() {
    switch (theme) {
      case EnrollmentTheme.standard:
        return const Color(0xFF334155);
      case EnrollmentTheme.premium:
        return const Color(0xFFFFD700).withOpacity(0.3);
    }
  }

  Color _getAccentColor() {
    switch (theme) {
      case EnrollmentTheme.standard:
        return AColor.fitgoGreen;
      case EnrollmentTheme.premium:
        return const Color(0xFFFFD700); // Gold
    }
  }
}

class PromoCodeModal extends HookConsumerWidget {
  final EnrollmentTheme theme;

  const PromoCodeModal({super.key, required this.theme});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final textController = useTextEditingController();
    final enrollmentState = ref.watch(enrollmentProvider);

    return Container(
      height: context.height * 0.6,
      decoration: BoxDecoration(
        color: _getBackgroundColor(),
        borderRadius: const BorderRadius.only(
          topLeft: Radius.circular(20),
          topRight: Radius.circular(20),
        ),
      ),
      child: Padding(
        padding: EdgeInsets.only(
          left: 20,
          right: 20,
          top: 20,
          bottom: context.viewInsets.bottom + 20,
        ),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Modal header
            Row(
              children: [
                Expanded(
                  child: TextWidget(
                    'Promosyon Kodu',
                    style: ATextStyle.large.copyWith(
                      color: Colors.white,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                ),
                GestureDetector(
                  onTap: () => Navigator.of(context).pop(),
                  child: Container(
                    padding: const EdgeInsets.all(8),
                    decoration: BoxDecoration(
                      color: const Color(0xFF1E293B),
                      borderRadius: BorderRadius.circular(8),
                    ),
                    child: const Icon(
                      Icons.close,
                      color: Colors.white,
                      size: 20,
                    ),
                  ),
                ),
              ],
            ),
            const SizedBox(height: 24),

            // Input field
            TextWidget(
              'Promosyon kodunu girin',
              style: ATextStyle.medium.copyWith(color: Colors.white70),
            ),
            const SizedBox(height: 12),

            TextField(
              controller: textController,
              style: ATextStyle.medium.copyWith(color: Colors.white),
              decoration: InputDecoration(
                hintText: 'Örn: FITGO10',
                hintStyle: ATextStyle.medium.copyWith(color: Colors.white54),
                filled: true,
                fillColor: const Color(0xFF1E293B),
                border: OutlineInputBorder(
                  borderRadius: BorderRadius.circular(12),
                  borderSide: BorderSide(color: _getBorderColor()),
                ),
                enabledBorder: OutlineInputBorder(
                  borderRadius: BorderRadius.circular(12),
                  borderSide: BorderSide(color: _getBorderColor()),
                ),
                focusedBorder: OutlineInputBorder(
                  borderRadius: BorderRadius.circular(12),
                  borderSide: BorderSide(color: _getAccentColor(), width: 2),
                ),
                contentPadding: const EdgeInsets.all(16),
              ),
              textCapitalization: TextCapitalization.characters,
            ),

            const SizedBox(height: 16),

            // Error message
            if (enrollmentState.error != null) ...[
              Container(
                padding: const EdgeInsets.all(12),
                decoration: BoxDecoration(
                  color: Colors.red.withOpacity(0.1),
                  borderRadius: BorderRadius.circular(8),
                  border: Border.all(
                    color: Colors.red.withOpacity(0.3),
                    width: 1,
                  ),
                ),
                child: Row(
                  children: [
                    Icon(Icons.error_outline, color: Colors.red, size: 20),
                    const SizedBox(width: 8),
                    Expanded(
                      child: TextWidget(
                        enrollmentState.error!,
                        style: ATextStyle.small.copyWith(color: Colors.red),
                      ),
                    ),
                  ],
                ),
              ),
              const SizedBox(height: 16),
            ],

            const Spacer(),

            // Apply button
            SizedBox(
              width: double.infinity,
              child: ElevatedButton(
                onPressed:
                    enrollmentState.isValidatingPromoCode
                        ? null
                        : () async {
                          if (textController.text.trim().isNotEmpty) {
                            await ref
                                .read(enrollmentProvider.notifier)
                                .applyPromoCode(textController.text.trim());

                            final newState = ref.read(enrollmentProvider);
                            if (newState.appliedPromoCode != null &&
                                context.mounted) {
                              Navigator.of(context).pop();
                            }
                          }
                        },
                style: ElevatedButton.styleFrom(
                  backgroundColor: _getAccentColor(),
                  foregroundColor:
                      theme == EnrollmentTheme.premium
                          ? Colors.black
                          : Colors.white,
                  padding: const EdgeInsets.symmetric(vertical: 16),
                  shape: RoundedRectangleBorder(
                    borderRadius: BorderRadius.circular(12),
                  ),
                ),
                child:
                    enrollmentState.isValidatingPromoCode
                        ? SizedBox(
                          height: 20,
                          width: 20,
                          child: CircularProgressIndicator(
                            strokeWidth: 2,
                            valueColor: AlwaysStoppedAnimation<Color>(
                              theme == EnrollmentTheme.premium
                                  ? Colors.black
                                  : Colors.white,
                            ),
                          ),
                        )
                        : TextWidget(
                          'Uygula',
                          style: ATextStyle.medium.copyWith(
                            fontWeight: FontWeight.w600,
                          ),
                        ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  Color _getBackgroundColor() {
    switch (theme) {
      case EnrollmentTheme.standard:
        return const Color(0xFF0F172A);
      case EnrollmentTheme.premium:
        return const Color(0xFF1A1A2E);
    }
  }

  Color _getBorderColor() {
    switch (theme) {
      case EnrollmentTheme.standard:
        return const Color(0xFF334155);
      case EnrollmentTheme.premium:
        return const Color(0xFFFFD700).withOpacity(0.3);
    }
  }

  Color _getAccentColor() {
    switch (theme) {
      case EnrollmentTheme.standard:
        return AColor.fitgoGreen;
      case EnrollmentTheme.premium:
        return const Color(0xFFFFD700); // Gold
    }
  }
}
