import 'package:flutter/material.dart';
import 'package:fitgo_app/src/shared/widgets/text_widget/text_widget.dart';
import 'package:fitgo_app/src/shared/constants/app_text_style.dart';
import 'package:fitgo_app/src/theme/colors.dart';
import 'package:fitgo_app/src/program_enrollment/domain/enrollment_package.dart';

class DurationSelector extends StatelessWidget {
  final DurationOption selectedDuration;
  final ValueChanged<DurationOption> onDurationChanged;

  const DurationSelector({
    super.key,
    required this.selectedDuration,
    required this.onDurationChanged,
  });

  @override
  Widget build(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        TextWidget(
          'Süre Seçimi',
          style: ATextStyle.large.copyWith(
            color: Colors.white,
            fontWeight: FontWeight.w600,
          ),
        ),
        const SizedBox(height: 16),
        Container(
          decoration: BoxDecoration(
            color: const Color(0xFF1E293B),
            borderRadius: BorderRadius.circular(12),
            border: Border.all(
              color: const Color(0xFF334155),
              width: 1,
            ),
          ),
          child: DropdownButtonHideUnderline(
            child: DropdownButton<DurationOption>(
              value: selectedDuration,
              onChanged: (DurationOption? newValue) {
                if (newValue != null) {
                  onDurationChanged(newValue);
                }
              },
              dropdownColor: const Color(0xFF1E293B),
              icon: const Icon(
                Icons.keyboard_arrow_down,
                color: Colors.white70,
              ),
              isExpanded: true,
              style: ATextStyle.medium.copyWith(
                color: Colors.white,
              ),
              items: DurationOption.values.map<DropdownMenuItem<DurationOption>>(
                (DurationOption duration) {
                  return DropdownMenuItem<DurationOption>(
                    value: duration,
                    child: Padding(
                      padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
                      child: Row(
                        children: [
                          Icon(
                            _getDurationIcon(duration),
                            color: AColor.fitgoGreen,
                            size: 20,
                          ),
                          const SizedBox(width: 12),
                          TextWidget(
                            duration.displayName,
                            style: ATextStyle.medium.copyWith(
                              color: Colors.white,
                              fontWeight: FontWeight.w500,
                            ),
                          ),
                          const Spacer(),
                          if (_hasDiscount(duration))
                            Container(
                              padding: const EdgeInsets.symmetric(
                                horizontal: 8,
                                vertical: 2,
                              ),
                              decoration: BoxDecoration(
                                color: AColor.fitgoGreen.withOpacity(0.2),
                                borderRadius: BorderRadius.circular(4),
                              ),
                              child: TextWidget(
                                'İndirim',
                                style: ATextStyle.small.copyWith(
                                  color: AColor.fitgoGreen,
                                  fontWeight: FontWeight.w600,
                                ),
                              ),
                            ),
                        ],
                      ),
                    ),
                  );
                },
              ).toList(),
            ),
          ),
        ),
      ],
    );
  }

  IconData _getDurationIcon(DurationOption duration) {
    switch (duration) {
      case DurationOption.oneMonth:
        return Icons.calendar_today;
      case DurationOption.threeMonths:
        return Icons.calendar_view_month;
      case DurationOption.sixMonths:
        return Icons.date_range;
      case DurationOption.oneYear:
        return Icons.event;
    }
  }

  bool _hasDiscount(DurationOption duration) {
    // Longer durations typically have discounts
    return duration.months >= 3;
  }
}
