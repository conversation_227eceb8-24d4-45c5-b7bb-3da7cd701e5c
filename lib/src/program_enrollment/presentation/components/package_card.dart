import 'package:flutter/material.dart';
import 'package:fitgo_app/src/shared/widgets/text_widget/text_widget.dart';
import 'package:fitgo_app/src/shared/constants/app_text_style.dart';
import 'package:fitgo_app/src/theme/colors.dart';
import 'package:fitgo_app/src/program_enrollment/domain/enrollment_package.dart';
import 'package:fitgo_app/src/program_enrollment/domain/enrollment_state.dart';

class PackageCard extends StatelessWidget {
  final EnrollmentPackage package;
  final bool isSelected;
  final EnrollmentTheme theme;
  final VoidCallback onTap;

  const PackageCard({
    super.key,
    required this.package,
    required this.isSelected,
    required this.theme,
    required this.onTap,
  });

  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      onTap: onTap,
      child: AnimatedContainer(
        duration: const Duration(milliseconds: 300),
        curve: Curves.easeInOut,
        decoration: BoxDecoration(
          color: _getCardColor(),
          borderRadius: BorderRadius.circular(16),
          border: Border.all(
            color: _getBorderColor(),
            width: isSelected ? 2 : 1,
          ),
          boxShadow: _getBoxShadow(),
        ),
        child: Padding(
          padding: const EdgeInsets.all(20),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Row(
                children: [
                  Expanded(
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Row(
                          children: [
                            if (package.type.isPremium) ...[
                              Icon(
                                Icons.workspace_premium,
                                color: _getPremiumColor(),
                                size: 24,
                              ),
                              const SizedBox(width: 8),
                            ],
                            Expanded(
                              child: TextWidget(
                                package.displayName,
                                style: ATextStyle.large.copyWith(
                                  color: _getTitleColor(),
                                  fontWeight: FontWeight.bold,
                                  fontSize: 18,
                                ),
                              ),
                            ),
                          ],
                        ),
                        if (package.isPopular) ...[
                          const SizedBox(height: 8),
                          Container(
                            padding: const EdgeInsets.symmetric(
                              horizontal: 8,
                              vertical: 4,
                            ),
                            decoration: BoxDecoration(
                              color: _getPremiumColor().withOpacity(0.2),
                              borderRadius: BorderRadius.circular(6),
                            ),
                            child: TextWidget(
                              'En Popüler',
                              style: ATextStyle.small.copyWith(
                                color: _getPremiumColor(),
                                fontWeight: FontWeight.w600,
                              ),
                            ),
                          ),
                        ],
                      ],
                    ),
                  ),
                  AnimatedContainer(
                    duration: const Duration(milliseconds: 200),
                    width: 24,
                    height: 24,
                    decoration: BoxDecoration(
                      shape: BoxShape.circle,
                      color: isSelected ? AColor.fitgoGreen : Colors.transparent,
                      border: Border.all(
                        color: isSelected ? AColor.fitgoGreen : Colors.white54,
                        width: 2,
                      ),
                    ),
                    child: isSelected
                        ? const Icon(
                            Icons.check,
                            color: Colors.white,
                            size: 16,
                          )
                        : null,
                  ),
                ],
              ),
              const SizedBox(height: 16),
              
              // Feature preview (first 3 features)
              ...package.features.take(3).map((feature) {
                return Padding(
                  padding: const EdgeInsets.only(bottom: 8),
                  child: Row(
                    children: [
                      if (feature.icon != null) ...[
                        TextWidget(
                          feature.icon!,
                          style: const TextStyle(fontSize: 16),
                        ),
                        const SizedBox(width: 8),
                      ] else ...[
                        Icon(
                          package.type.isPremium ? Icons.star : Icons.check,
                          color: package.type.isPremium 
                              ? _getPremiumColor() 
                              : AColor.fitgoGreen,
                          size: 16,
                        ),
                        const SizedBox(width: 8),
                      ],
                      Expanded(
                        child: TextWidget(
                          feature.title,
                          style: ATextStyle.small.copyWith(
                            color: _getFeatureTextColor(),
                            fontWeight: feature.isHighlight 
                                ? FontWeight.w600 
                                : FontWeight.normal,
                          ),
                        ),
                      ),
                      if (feature.isNew) ...[
                        const SizedBox(width: 8),
                        Container(
                          padding: const EdgeInsets.symmetric(
                            horizontal: 6,
                            vertical: 2,
                          ),
                          decoration: BoxDecoration(
                            color: Colors.orange.withOpacity(0.2),
                            borderRadius: BorderRadius.circular(4),
                          ),
                          child: TextWidget(
                            'YENİ',
                            style: ATextStyle.small.copyWith(
                              color: Colors.orange,
                              fontWeight: FontWeight.bold,
                            ),
                          ),
                        ),
                      ],
                    ],
                  ),
                );
              }).toList(),
              
              if (package.features.length > 3) ...[
                const SizedBox(height: 8),
                TextWidget(
                  '+${package.features.length - 3} özellik daha',
                  style: ATextStyle.small.copyWith(
                    color: _getPremiumColor(),
                    fontWeight: FontWeight.w500,
                  ),
                ),
              ],
            ],
          ),
        ),
      ),
    );
  }

  Color _getCardColor() {
    if (package.type.isPremium) {
      return isSelected 
          ? const Color(0xFF2D1B69).withOpacity(0.3)
          : const Color(0xFF1E293B);
    } else {
      return isSelected 
          ? const Color(0xFF1E293B)
          : const Color(0xFF0F172A);
    }
  }

  Color _getBorderColor() {
    if (isSelected) {
      return package.type.isPremium ? _getPremiumColor() : AColor.fitgoGreen;
    } else {
      return const Color(0xFF334155);
    }
  }

  List<BoxShadow> _getBoxShadow() {
    if (isSelected && package.type.isPremium) {
      return [
        BoxShadow(
          color: _getPremiumColor().withOpacity(0.3),
          blurRadius: 12,
          offset: const Offset(0, 4),
        ),
      ];
    } else if (isSelected) {
      return [
        BoxShadow(
          color: AColor.fitgoGreen.withOpacity(0.3),
          blurRadius: 8,
          offset: const Offset(0, 2),
        ),
      ];
    }
    return [];
  }

  Color _getTitleColor() {
    if (package.type.isPremium) {
      return _getPremiumColor();
    } else {
      return Colors.white;
    }
  }

  Color _getFeatureTextColor() {
    return package.type.isPremium ? Colors.white : Colors.white70;
  }

  Color _getPremiumColor() {
    return const Color(0xFFFFD700); // Gold color for premium
  }
}
