import 'package:flutter/material.dart';
import 'package:fitgo_app/src/shared/widgets/text_widget/text_widget.dart';
import 'package:fitgo_app/src/shared/constants/app_text_style.dart';
import 'package:fitgo_app/src/theme/colors.dart';
import 'package:fitgo_app/src/program_enrollment/domain/enrollment_state.dart';

class EnrollmentButton extends StatelessWidget {
  final bool canProceed;
  final EnrollmentTheme theme;
  final VoidCallback onPressed;

  const EnrollmentButton({
    super.key,
    required this.canProceed,
    required this.theme,
    required this.onPressed,
  });

  @override
  Widget build(BuildContext context) {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: _getBackgroundColor(),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withOpacity(0.1),
            blurRadius: 8,
            offset: const Offset(0, -2),
          ),
        ],
      ),
      child: Safe<PERSON>rea(
        child: Animated<PERSON>ontainer(
          duration: const Duration(milliseconds: 300),
          curve: Curves.easeInOut,
          child: ElevatedButton(
            onPressed: canProceed ? onPressed : null,
            style: ElevatedButton.styleFrom(
              backgroundColor: _getButtonColor(),
              foregroundColor: _getTextColor(),
              disabledBackgroundColor: Colors.grey[600],
              disabledForegroundColor: Colors.grey[400],
              padding: const EdgeInsets.symmetric(vertical: 18),
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(16),
              ),
              elevation: canProceed ? 4 : 0,
              shadowColor: _getButtonColor().withOpacity(0.3),
            ),
            child: Container(
              width: double.infinity,
              child: Stack(
                alignment: Alignment.center,
                children: [
                  // Gradient overlay for premium
                  if (theme == EnrollmentTheme.premium && canProceed)
                    Container(
                      decoration: BoxDecoration(
                        borderRadius: BorderRadius.circular(16),
                        gradient: LinearGradient(
                          colors: [
                            const Color(0xFFFFD700).withOpacity(0.8),
                            const Color(0xFFFFA500).withOpacity(0.8),
                          ],
                          begin: Alignment.topLeft,
                          end: Alignment.bottomRight,
                        ),
                      ),
                    ),
                  
                  // Button content
                  Row(
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: [
                      if (theme == EnrollmentTheme.premium) ...[
                        Icon(
                          Icons.workspace_premium,
                          color: _getTextColor(),
                          size: 24,
                        ),
                        const SizedBox(width: 8),
                      ],
                      
                      TextWidget(
                        'Devam Et',
                        style: ATextStyle.large.copyWith(
                          color: _getTextColor(),
                          fontWeight: FontWeight.bold,
                          fontSize: 18,
                        ),
                      ),
                      
                      const SizedBox(width: 8),
                      Icon(
                        Icons.arrow_forward,
                        color: _getTextColor(),
                        size: 20,
                      ),
                    ],
                  ),
                ],
              ),
            ),
          ),
        ),
      ),
    );
  }

  Color _getBackgroundColor() {
    switch (theme) {
      case EnrollmentTheme.standard:
        return const Color(0xFF0F172A);
      case EnrollmentTheme.premium:
        return const Color(0xFF1A1A2E);
    }
  }

  Color _getButtonColor() {
    if (!canProceed) return Colors.grey[600]!;
    
    switch (theme) {
      case EnrollmentTheme.standard:
        return AColor.fitgoGreen;
      case EnrollmentTheme.premium:
        return const Color(0xFFFFD700); // Gold
    }
  }

  Color _getTextColor() {
    if (!canProceed) return Colors.grey[400]!;
    
    switch (theme) {
      case EnrollmentTheme.standard:
        return Colors.white;
      case EnrollmentTheme.premium:
        return Colors.black; // Black text on gold background
    }
  }
}
