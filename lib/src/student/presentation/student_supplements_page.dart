import 'package:flutter/material.dart';
import 'package:flutter_hooks/flutter_hooks.dart';
import 'package:hooks_riverpod/hooks_riverpod.dart';
import '../application/student_supplement_provider.dart';
import '../../instructor/domain/supplement_models.dart';
import '../../shared/constants/app_text_style.dart';

class StudentSupplementsPage extends HookConsumerWidget {
  const StudentSupplementsPage({super.key});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final selectedFilter = useState<SupplementAssignmentStatus?>(null);
    final supplementsAsync = ref.watch(studentAssignedSupplementsProvider);

    return Scaffold(
      backgroundColor: const Color(0xFF111827),
      appBar: AppBar(
        backgroundColor: const Color(0xFF111827),
        title: Text(
          'My Supplements',
          style: ATextStyle.large.copyWith(
            color: Colors.white,
            fontWeight: FontWeight.bold,
          ),
        ),
        leading: IconButton(
          icon: const Icon(Icons.arrow_back, color: Colors.white),
          onPressed: () => Navigator.pop(context),
        ),
        actions: [
          IconButton(
            icon: const Icon(Icons.refresh, color: Color(0xFFFACC15)),
            onPressed: () => ref.invalidate(studentAssignedSupplementsProvider),
            tooltip: 'Refresh',
          ),
        ],
      ),
      body: Column(
        children: [
          // Filter Section
          _buildFilterSection(selectedFilter, ref),
          
          // Supplements List
          Expanded(
            child: _buildSupplementsList(context, ref, supplementsAsync, selectedFilter.value),
          ),
        ],
      ),
    );
  }

  Widget _buildFilterSection(
    ValueNotifier<SupplementAssignmentStatus?> selectedFilter,
    WidgetRef ref,
  ) {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: const BoxDecoration(
        color: Color(0xFF1F2937),
        border: Border(bottom: BorderSide(color: Color(0xFF374151))),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            'Filter by Status',
            style: ATextStyle.medium.copyWith(
              color: Colors.white,
              fontWeight: FontWeight.bold,
            ),
          ),
          const SizedBox(height: 12),
          SingleChildScrollView(
            scrollDirection: Axis.horizontal,
            child: Row(
              children: [
                _buildFilterChip(
                  'All',
                  selectedFilter.value == null,
                  () {
                    selectedFilter.value = null;
                  },
                ),
                ...SupplementAssignmentStatus.values.map((status) => _buildFilterChip(
                  status.displayName,
                  selectedFilter.value == status,
                  () {
                    selectedFilter.value = selectedFilter.value == status ? null : status;
                  },
                )),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildFilterChip(String label, bool isSelected, VoidCallback onTap) {
    return Padding(
      padding: const EdgeInsets.only(right: 8),
      child: FilterChip(
        label: Text(
          label,
          style: ATextStyle.small.copyWith(
            color: isSelected ? Colors.black : Colors.white,
          ),
        ),
        selected: isSelected,
        onSelected: (_) => onTap(),
        backgroundColor: const Color(0xFF374151),
        selectedColor: const Color(0xFFFACC15),
        checkmarkColor: Colors.black,
        side: BorderSide(
          color: isSelected ? const Color(0xFFFACC15) : const Color(0xFF4B5563),
        ),
      ),
    );
  }

  Widget _buildSupplementsList(
    BuildContext context,
    WidgetRef ref,
    AsyncValue<List<StudentSupplementAssignment>> supplementsAsync,
    SupplementAssignmentStatus? filter,
  ) {
    return supplementsAsync.when(
      data: (assignments) {
        // Apply filter
        final filteredAssignments = filter != null
            ? assignments.where((assignment) => assignment.status == filter).toList()
            : assignments;

        if (filteredAssignments.isEmpty) {
          return Center(
            child: Column(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                const Icon(Icons.medication_outlined, color: Colors.grey, size: 64),
                const SizedBox(height: 16),
                Text(
                  filter != null 
                      ? 'No ${filter.displayName.toLowerCase()} supplements'
                      : 'No supplements assigned',
                  style: ATextStyle.medium.copyWith(color: Colors.grey),
                ),
                const SizedBox(height: 8),
                Text(
                  'Your instructor hasn\'t assigned any supplements yet',
                  style: ATextStyle.small.copyWith(color: Colors.grey),
                  textAlign: TextAlign.center,
                ),
              ],
            ),
          );
        }

        return ListView.builder(
          padding: const EdgeInsets.all(16),
          itemCount: filteredAssignments.length,
          itemBuilder: (context, index) {
            final assignment = filteredAssignments[index];
            return _buildSupplementCard(context, ref, assignment);
          },
        );
      },
      loading: () => const Center(
        child: CircularProgressIndicator(color: Color(0xFFFACC15)),
      ),
      error: (error, stack) => Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            const Icon(Icons.error_outline, color: Colors.red, size: 64),
            const SizedBox(height: 16),
            Text(
              'Error loading supplements',
              style: ATextStyle.medium.copyWith(color: Colors.red),
            ),
            const SizedBox(height: 8),
            Text(
              error.toString(),
              style: ATextStyle.small.copyWith(color: Colors.grey),
              textAlign: TextAlign.center,
            ),
            const SizedBox(height: 16),
            ElevatedButton(
              onPressed: () => ref.invalidate(studentAssignedSupplementsProvider),
              style: ElevatedButton.styleFrom(
                backgroundColor: const Color(0xFFFACC15),
                foregroundColor: Colors.black,
              ),
              child: const Text('Retry'),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildSupplementCard(
    BuildContext context,
    WidgetRef ref,
    StudentSupplementAssignment assignment,
  ) {
    final supplement = assignment.supplement;
    if (supplement == null) return const SizedBox.shrink();

    return Container(
      margin: const EdgeInsets.only(bottom: 16),
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: const Color(0xFF1F2937),
        borderRadius: BorderRadius.circular(12),
        border: Border.all(color: const Color(0xFF374151)),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Header Row
          Row(
            children: [
              // Supplement Image
              Container(
                width: 60,
                height: 60,
                decoration: BoxDecoration(
                  color: const Color(0xFF374151),
                  borderRadius: BorderRadius.circular(8),
                  image: supplement.imageUrl != null
                      ? DecorationImage(
                          image: NetworkImage(supplement.imageUrl!),
                          fit: BoxFit.cover,
                        )
                      : null,
                ),
                child: supplement.imageUrl == null
                    ? const Icon(Icons.medication, color: Colors.grey, size: 30)
                    : null,
              ),
              
              const SizedBox(width: 12),
              
              // Supplement Info
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      supplement.name,
                      style: ATextStyle.medium.copyWith(
                        color: Colors.white,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                    const SizedBox(height: 4),
                    Text(
                      supplement.brand,
                      style: ATextStyle.small.copyWith(color: const Color(0xFFFACC15)),
                    ),
                    const SizedBox(height: 4),
                    Row(
                      children: [
                        Container(
                          padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 2),
                          decoration: BoxDecoration(
                            color: _getTypeColor(supplement.type),
                            borderRadius: BorderRadius.circular(4),
                          ),
                          child: Text(
                            supplement.type.displayName,
                            style: ATextStyle.small.copyWith(color: Colors.white),
                          ),
                        ),
                        const SizedBox(width: 8),
                        Container(
                          padding: const EdgeInsets.symmetric(horizontal: 6, vertical: 2),
                          decoration: BoxDecoration(
                            color: _getStatusColor(assignment.status),
                            borderRadius: BorderRadius.circular(4),
                          ),
                          child: Text(
                            assignment.status.displayName,
                            style: ATextStyle.small.copyWith(color: Colors.white),
                          ),
                        ),
                      ],
                    ),
                  ],
                ),
              ),
              
              // Action Button
              if (assignment.status == SupplementAssignmentStatus.active)
                ElevatedButton(
                  onPressed: () => _markAsTaken(context, ref, assignment),
                  style: ElevatedButton.styleFrom(
                    backgroundColor: const Color(0xFFFACC15),
                    foregroundColor: Colors.black,
                    padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
                    shape: RoundedRectangleBorder(
                      borderRadius: BorderRadius.circular(6),
                    ),
                  ),
                  child: const Text('Mark as Taken'),
                ),
            ],
          ),
          
          const SizedBox(height: 16),
          
          // Quantity Information
          _buildQuantityInfo(assignment.quantity),

          // Instructor Notes
          if (assignment.notes != null && assignment.notes!.isNotEmpty) ...[
            const SizedBox(height: 12),
            _buildInstructorNotes(assignment.notes!),
          ],
        ],
      ),
    );
  }

  Widget _buildDosageInfo(SupplementDosage dosage) {
    return Container(
      padding: const EdgeInsets.all(12),
      decoration: BoxDecoration(
        color: const Color(0xFF374151),
        borderRadius: BorderRadius.circular(8),
      ),
      child: Row(
        children: [
          const Icon(Icons.medication_liquid, color: Color(0xFFFACC15), size: 16),
          const SizedBox(width: 8),
          Text(
            'Dosage: ',
            style: ATextStyle.small.copyWith(color: Colors.grey),
          ),
          Text(
            dosage.displayText,
            style: ATextStyle.small.copyWith(
              color: Colors.white,
              fontWeight: FontWeight.bold,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildQuantityInfo(int quantity) {
    return Container(
      padding: const EdgeInsets.all(12),
      decoration: BoxDecoration(
        color: const Color(0xFF374151),
        borderRadius: BorderRadius.circular(8),
      ),
      child: Row(
        children: [
          const Icon(Icons.numbers, color: Color(0xFFFACC15), size: 16),
          const SizedBox(width: 8),
          Text(
            'Quantity:',
            style: ATextStyle.small.copyWith(color: Colors.grey),
          ),
          const SizedBox(width: 8),
          Text(
            quantity.toString(),
            style: ATextStyle.medium.copyWith(
              color: const Color(0xFFFACC15),
              fontWeight: FontWeight.bold,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildInstructorNotes(String notes) {
    return Container(
      padding: const EdgeInsets.all(12),
      decoration: BoxDecoration(
        color: Colors.blue.withValues(alpha: 0.1),
        borderRadius: BorderRadius.circular(8),
        border: Border.all(color: Colors.blue.withValues(alpha: 0.3)),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              const Icon(Icons.note, color: Colors.blue, size: 16),
              const SizedBox(width: 8),
              Text(
                'Instructor Notes:',
                style: ATextStyle.small.copyWith(
                  color: Colors.blue,
                  fontWeight: FontWeight.bold,
                ),
              ),
            ],
          ),
          const SizedBox(height: 8),
          Text(
            notes,
            style: ATextStyle.small.copyWith(color: Colors.white),
          ),
        ],
      ),
    );
  }



  // Helper methods
  Color _getTypeColor(SupplementType type) {
    switch (type) {
      case SupplementType.protein:
        return Colors.blue;
      case SupplementType.creatine:
        return Colors.red;
      case SupplementType.vitamins:
        return Colors.green;
      case SupplementType.minerals:
        return Colors.purple;
      case SupplementType.preworkout:
        return Colors.orange;
      case SupplementType.postworkout:
        return Colors.teal;
      case SupplementType.fatburner:
        return Colors.pink;
      case SupplementType.bcaa:
        return Colors.indigo;
      case SupplementType.glutamine:
        return Colors.cyan;
      case SupplementType.omega3:
        return Colors.amber;
      case SupplementType.multivitamin:
        return Colors.lime;
      case SupplementType.other:
        return Colors.grey;
    }
  }

  Color _getStatusColor(SupplementAssignmentStatus status) {
    switch (status) {
      case SupplementAssignmentStatus.active:
        return Colors.green;
      case SupplementAssignmentStatus.paused:
        return Colors.orange;
      case SupplementAssignmentStatus.completed:
        return Colors.blue;
      case SupplementAssignmentStatus.discontinued:
        return Colors.red;
    }
  }



  void _markAsTaken(BuildContext context, WidgetRef ref, StudentSupplementAssignment assignment) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        backgroundColor: const Color(0xFF1F2937),
        title: Text(
          'Mark as Taken',
          style: ATextStyle.medium.copyWith(color: Colors.white),
        ),
        content: Text(
          'Mark "${assignment.supplement?.name}" as taken?',
          style: ATextStyle.small.copyWith(color: Colors.grey),
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: Text(
              'Cancel',
              style: ATextStyle.small.copyWith(color: Colors.grey),
            ),
          ),
          TextButton(
            onPressed: () async {
              Navigator.pop(context);

              try {
                final repository = ref.read(studentSupplementRepositoryProvider);
                await repository.markSupplementAsTaken(assignment.id);

                // Refresh the supplements list
                ref.invalidate(studentAssignedSupplementsProvider);

                if (context.mounted) {
                  ScaffoldMessenger.of(context).showSnackBar(
                    SnackBar(
                      content: Text('${assignment.supplement?.name} marked as taken'),
                      backgroundColor: Colors.green,
                    ),
                  );
                }
              } catch (e) {
                if (context.mounted) {
                  ScaffoldMessenger.of(context).showSnackBar(
                    SnackBar(
                      content: Text('Error: $e'),
                      backgroundColor: Colors.red,
                    ),
                  );
                }
              }
            },
            child: Text(
              'Mark as Taken',
              style: ATextStyle.small.copyWith(color: const Color(0xFFFACC15)),
            ),
          ),
        ],
      ),
    );
  }
}
