import 'package:flutter/material.dart';
import 'package:hooks_riverpod/hooks_riverpod.dart';
import 'package:flutter_hooks/flutter_hooks.dart';
import '../../shared/market/domain/market_models.dart';
import '../../shared/market/application/market_providers.dart';
import '../../shared/market/presentation/components/product_card.dart';
import '../../shared/market/presentation/components/category_tile.dart';
import '../../shared/market/presentation/components/brand_card.dart';
import '../../shared/market/presentation/components/promo_banner.dart';
import '../../shared/constants/app_text_style.dart';
import '../../shared/utils/app_logger.dart';

class StudentMarketPage extends HookConsumerWidget {
  const StudentMarketPage({super.key});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final scrollController = useScrollController();
    final selectedCategory = useState<ProductCategory?>(null);
    final searchController = useTextEditingController();

    // Watch providers
    final featuredProductsAsync = ref.watch(featuredProductsProvider);
    final popularProductsAsync = ref.watch(popularProductsProvider);
    final newProductsAsync = ref.watch(newProductsProvider);
    final featuredBrandsAsync = ref.watch(featuredBrandsProvider);
    final promoBannersAsync = ref.watch(promoBannersProvider);

    AppLogger.info('Loading student market page', tag: 'STUDENT_MARKET');

    return Scaffold(
      backgroundColor: const Color(0xFF111827),
      body: CustomScrollView(
        controller: scrollController,
        slivers: [
          // App Bar
          SliverAppBar(
            expandedHeight: 100,
            floating: false,
            pinned: true,
            backgroundColor: const Color(0xFF111827),
            foregroundColor: Colors.white,
            elevation: 0,
            flexibleSpace: FlexibleSpaceBar(
              title: Text(
                'FitGo Market',
                style: ATextStyle.title.copyWith(
                  color: Colors.white,
                  fontWeight: FontWeight.bold,
                ),
              ),
              centerTitle: false,
              titlePadding: const EdgeInsets.only(left: 16, bottom: 16),
            ),
            actions: [
              IconButton(
                icon: const Icon(Icons.search, color: Colors.white),
                onPressed: () => _showSearchDialog(context, searchController),
              ),
              IconButton(
                icon: const Icon(Icons.shopping_cart, color: Colors.white),
                onPressed: () => _showCartDialog(context),
              ),
            ],
          ),

          // Content
          SliverToBoxAdapter(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                // Hero Banner - Student focused
                _buildStudentHeroBanner(context),
                
                const SizedBox(height: 24),

                // Category Tiles
                _buildCategorySection(selectedCategory),

                const SizedBox(height: 24),

                // Popular Brands Section
                _buildBrandsSection(featuredBrandsAsync),

                const SizedBox(height: 24),

                // Promotional Banners
                _buildPromoBannersSection(promoBannersAsync),

                const SizedBox(height: 24),

                // Recommended for Your Goals
                _buildRecommendedForGoalsSection(featuredProductsAsync),

                const SizedBox(height: 24),

                // Popular Among Students
                _buildPopularAmongStudentsSection(popularProductsAsync),

                const SizedBox(height: 24),

                // New & Trending
                _buildNewProductsSection(newProductsAsync),

                const SizedBox(height: 32),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildStudentHeroBanner(BuildContext context) {
    return Container(
      margin: const EdgeInsets.symmetric(horizontal: 16),
      height: 200,
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(12),
        gradient: const LinearGradient(
          colors: [Color(0xFF30A958), Color(0xFF10B981)],
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
        ),
        boxShadow: [
          BoxShadow(
            color: const Color(0xFF30A958).withOpacity(0.3),
            blurRadius: 12,
            offset: const Offset(0, 6),
          ),
        ],
      ),
      child: Stack(
        children: [
          // Background pattern
          Positioned.fill(
            child: ClipRRect(
              borderRadius: BorderRadius.circular(12),
              child: Container(
                decoration: BoxDecoration(
                  image: DecorationImage(
                    image: NetworkImage(
                      'https://images.unsplash.com/photo-1593095948071-474c5cc2989d?w=800',
                    ),
                    fit: BoxFit.cover,
                    colorFilter: ColorFilter.mode(
                      Colors.black.withOpacity(0.4),
                      BlendMode.darken,
                    ),
                  ),
                ),
              ),
            ),
          ),
          // Content
          Padding(
            padding: const EdgeInsets.all(24),
            child: Row(
              children: [
                // Left side - Text content
                Expanded(
                  flex: 3,
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: [
                      Text(
                        'Fuel Your Fitness',
                        style: ATextStyle.title.copyWith(
                          color: Colors.white,
                          fontWeight: FontWeight.bold,
                          fontSize: 24,
                        ),
                      ),
                      Text(
                        'Journey!',
                        style: ATextStyle.title.copyWith(
                          color: Colors.white,
                          fontWeight: FontWeight.bold,
                          fontSize: 24,
                        ),
                      ),
                      const SizedBox(height: 8),
                      Text(
                        'Premium supplements and gear to support your training goals.',
                        style: ATextStyle.medium.copyWith(
                          color: Colors.white.withOpacity(0.9),
                          fontSize: 14,
                        ),
                      ),
                      const SizedBox(height: 16),
                      ElevatedButton(
                        onPressed: () => _exploreProducts(context),
                        style: ElevatedButton.styleFrom(
                          backgroundColor: Colors.white,
                          foregroundColor: const Color(0xFF30A958),
                          padding: const EdgeInsets.symmetric(
                            horizontal: 28,
                            vertical: 14,
                          ),
                          shape: RoundedRectangleBorder(
                            borderRadius: BorderRadius.circular(32),
                          ),
                        ),
                        child: Text(
                          'Explore Products',
                          style: ATextStyle.medium.copyWith(
                            color: const Color(0xFF30A958),
                            fontWeight: FontWeight.bold,
                          ),
                        ),
                      ),
                    ],
                  ),
                ),
                // Right side - Image placeholder
                Expanded(
                  flex: 2,
                  child: Container(
                    alignment: Alignment.center,
                    child: Icon(
                      Icons.sports_gymnastics,
                      size: 80,
                      color: Colors.white.withOpacity(0.8),
                    ),
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildCategorySection(ValueNotifier<ProductCategory?> selectedCategory) {
    return Padding(
      padding: const EdgeInsets.symmetric(horizontal: 16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            'Shop by Category',
            style: ATextStyle.large.copyWith(
              color: Colors.white,
              fontWeight: FontWeight.bold,
              fontSize: 20,
            ),
          ),
          const SizedBox(height: 16),
          SizedBox(
            height: 120,
            child: ListView.builder(
              scrollDirection: Axis.horizontal,
              itemCount: ProductCategory.values.length,
              itemBuilder: (context, index) {
                final category = ProductCategory.values[index];
                return Container(
                  width: 100,
                  margin: EdgeInsets.only(
                    right: index < ProductCategory.values.length - 1 ? 12 : 0,
                  ),
                  child: CategoryTile(
                    category: category,
                    isSelected: selectedCategory.value == category,
                    onTap: () {
                      selectedCategory.value = selectedCategory.value == category 
                          ? null 
                          : category;
                      AppLogger.userAction(
                        'Selected category: ${category.displayName}',
                        tag: 'STUDENT_MARKET',
                      );
                    },
                  ),
                );
              },
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildBrandsSection(AsyncValue<List<Brand>> brandsAsync) {
    return Padding(
      padding: const EdgeInsets.symmetric(horizontal: 16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            'Trusted Brands',
            style: ATextStyle.large.copyWith(
              color: Colors.white,
              fontWeight: FontWeight.bold,
              fontSize: 20,
            ),
          ),
          const SizedBox(height: 16),
          brandsAsync.when(
            loading: () => _buildBrandsLoadingState(),
            error: (error, stack) => _buildErrorState('Failed to load brands'),
            data: (brands) => _buildBrandsList(brands),
          ),
        ],
      ),
    );
  }

  Widget _buildBrandsList(List<Brand> brands) {
    return SizedBox(
      height: 140,
      child: ListView.builder(
        scrollDirection: Axis.horizontal,
        itemCount: brands.length,
        itemBuilder: (context, index) {
          final brand = brands[index];
          return Container(
            width: 280,
            margin: EdgeInsets.only(right: index < brands.length - 1 ? 12 : 0),
            child: BrandCard(
              brand: brand,
              onTap: () => _navigateToBrand(brand),
            ),
          );
        },
      ),
    );
  }

  Widget _buildBrandsLoadingState() {
    return SizedBox(
      height: 140,
      child: ListView.builder(
        scrollDirection: Axis.horizontal,
        itemCount: 3,
        itemBuilder: (context, index) {
          return Container(
            width: 280,
            margin: EdgeInsets.only(right: index < 2 ? 12 : 0),
            decoration: BoxDecoration(
              color: const Color(0xFF374151),
              borderRadius: BorderRadius.circular(12),
            ),
            child: const Center(
              child: CircularProgressIndicator(
                color: Color(0xFF30A958),
                strokeWidth: 2,
              ),
            ),
          );
        },
      ),
    );
  }

  Widget _buildPromoBannersSection(AsyncValue<List<PromoBanner>> bannersAsync) {
    return bannersAsync.when(
      loading: () => _buildPromoBannersLoadingState(),
      error: (error, stack) => const SizedBox.shrink(),
      data: (banners) => banners.isEmpty 
          ? const SizedBox.shrink()
          : _buildPromoBannersList(banners),
    );
  }

  Widget _buildPromoBannersList(List<PromoBanner> banners) {
    return Padding(
      padding: const EdgeInsets.symmetric(horizontal: 16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            'Special Offers',
            style: ATextStyle.large.copyWith(
              color: Colors.white,
              fontWeight: FontWeight.bold,
              fontSize: 20,
            ),
          ),
          const SizedBox(height: 16),
          SizedBox(
            height: 120,
            child: ListView.builder(
              scrollDirection: Axis.horizontal,
              itemCount: banners.length,
              itemBuilder: (context, index) {
                final banner = banners[index];
                return Container(
                  width: 300,
                  margin: EdgeInsets.only(right: index < banners.length - 1 ? 12 : 0),
                  child: PromoBannerWidget(banner: banner),
                );
              },
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildPromoBannersLoadingState() {
    return Padding(
      padding: const EdgeInsets.symmetric(horizontal: 16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            'Special Offers',
            style: ATextStyle.large.copyWith(
              color: Colors.white,
              fontWeight: FontWeight.bold,
              fontSize: 20,
            ),
          ),
          const SizedBox(height: 16),
          Container(
            height: 120,
            decoration: BoxDecoration(
              color: const Color(0xFF374151),
              borderRadius: BorderRadius.circular(12),
            ),
            child: const Center(
              child: CircularProgressIndicator(
                color: Color(0xFF30A958),
                strokeWidth: 2,
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildRecommendedForGoalsSection(AsyncValue<List<MarketProduct>> productsAsync) {
    return _buildProductSection(
      title: 'Recommended for Your Goals',
      subtitle: 'Based on Your Training Plan',
      productsAsync: productsAsync,
    );
  }

  Widget _buildPopularAmongStudentsSection(AsyncValue<List<MarketProduct>> productsAsync) {
    return _buildProductSection(
      title: 'Popular Among Students',
      subtitle: 'Top Choices by FitGo Community',
      productsAsync: productsAsync,
    );
  }

  Widget _buildNewProductsSection(AsyncValue<List<MarketProduct>> productsAsync) {
    return _buildProductSection(
      title: 'New & Trending',
      subtitle: 'Latest Arrivals',
      productsAsync: productsAsync,
    );
  }

  Widget _buildProductSection({
    required String title,
    required String subtitle,
    required AsyncValue<List<MarketProduct>> productsAsync,
  }) {
    return Padding(
      padding: const EdgeInsets.symmetric(horizontal: 16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    title,
                    style: ATextStyle.large.copyWith(
                      color: Colors.white,
                      fontWeight: FontWeight.bold,
                      fontSize: 20,
                    ),
                  ),
                  Text(
                    subtitle,
                    style: ATextStyle.small.copyWith(
                      color: const Color(0xFF9CA3AF),
                      fontSize: 12,
                    ),
                  ),
                ],
              ),
              TextButton(
                onPressed: () => _viewAllProducts(title),
                child: Text(
                  'View All',
                  style: ATextStyle.medium.copyWith(
                    color: const Color(0xFF30A958),
                    fontWeight: FontWeight.w600,
                  ),
                ),
              ),
            ],
          ),
          const SizedBox(height: 16),
          productsAsync.when(
            loading: () => _buildProductsLoadingState(),
            error: (error, stack) => _buildErrorState('Failed to load products'),
            data: (products) => _buildProductsList(products),
          ),
        ],
      ),
    );
  }

  Widget _buildProductsList(List<MarketProduct> products) {
    if (products.isEmpty) {
      return Container(
        height: 200,
        decoration: BoxDecoration(
          color: const Color(0xFF374151),
          borderRadius: BorderRadius.circular(12),
        ),
        child: Center(
          child: Text(
            'No products available',
            style: ATextStyle.medium.copyWith(color: const Color(0xFF9CA3AF)),
          ),
        ),
      );
    }

    return SizedBox(
      height: 280,
      child: ListView.builder(
        scrollDirection: Axis.horizontal,
        itemCount: products.length,
        itemBuilder: (context, index) {
          final product = products[index];
          return Container(
            width: 180,
            margin: EdgeInsets.only(right: index < products.length - 1 ? 12 : 0),
            child: ProductCard(
              product: product,
              onTap: () => _navigateToProduct(product),
              onAddToCart: () => _addToCart(product),
            ),
          );
        },
      ),
    );
  }

  Widget _buildProductsLoadingState() {
    return SizedBox(
      height: 280,
      child: ListView.builder(
        scrollDirection: Axis.horizontal,
        itemCount: 4,
        itemBuilder: (context, index) {
          return Container(
            width: 180,
            margin: EdgeInsets.only(right: index < 3 ? 12 : 0),
            decoration: BoxDecoration(
              color: const Color(0xFF374151),
              borderRadius: BorderRadius.circular(12),
            ),
            child: const Center(
              child: CircularProgressIndicator(
                color: Color(0xFF30A958),
                strokeWidth: 2,
              ),
            ),
          );
        },
      ),
    );
  }

  Widget _buildErrorState(String message) {
    return Container(
      height: 100,
      decoration: BoxDecoration(
        color: const Color(0xFF374151),
        borderRadius: BorderRadius.circular(12),
      ),
      child: Center(
        child: Text(
          message,
          style: ATextStyle.medium.copyWith(color: const Color(0xFF9CA3AF)),
        ),
      ),
    );
  }

  // Navigation and action methods
  void _exploreProducts(BuildContext context) {
    AppLogger.userAction('Explored products from hero banner', tag: 'STUDENT_MARKET');
    // Scroll to categories or show all products
  }

  void _navigateToBrand(Brand brand) {
    AppLogger.userAction('Navigated to brand: ${brand.name}', tag: 'STUDENT_MARKET');
    // TODO: Navigate to brand page
  }

  void _navigateToProduct(MarketProduct product) {
    AppLogger.userAction('Navigated to product: ${product.name}', tag: 'STUDENT_MARKET');
    // TODO: Navigate to product detail page
  }

  void _addToCart(MarketProduct product) {
    AppLogger.userAction('Added to cart: ${product.name}', tag: 'STUDENT_MARKET');
    // TODO: Add to cart functionality
  }

  void _viewAllProducts(String section) {
    AppLogger.userAction('Viewed all products in section: $section', tag: 'STUDENT_MARKET');
    // TODO: Navigate to filtered products page
  }

  void _showSearchDialog(BuildContext context, TextEditingController controller) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        backgroundColor: const Color(0xFF1F2937),
        title: Text(
          'Search Products',
          style: ATextStyle.large.copyWith(color: Colors.white),
        ),
        content: TextField(
          controller: controller,
          style: ATextStyle.medium.copyWith(color: Colors.white),
          decoration: InputDecoration(
            hintText: 'Search for products...',
            hintStyle: ATextStyle.medium.copyWith(color: const Color(0xFF9CA3AF)),
            border: OutlineInputBorder(
              borderRadius: BorderRadius.circular(8),
              borderSide: const BorderSide(color: Color(0xFF374151)),
            ),
            focusedBorder: OutlineInputBorder(
              borderRadius: BorderRadius.circular(8),
              borderSide: const BorderSide(color: Color(0xFF30A958)),
            ),
          ),
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: Text(
              'Cancel',
              style: ATextStyle.medium.copyWith(color: const Color(0xFF9CA3AF)),
            ),
          ),
          ElevatedButton(
            onPressed: () {
              Navigator.of(context).pop();
              AppLogger.userAction('Searched for: ${controller.text}', tag: 'STUDENT_MARKET');
              // TODO: Perform search
            },
            style: ElevatedButton.styleFrom(
              backgroundColor: const Color(0xFF30A958),
            ),
            child: Text(
              'Search',
              style: ATextStyle.medium.copyWith(color: Colors.white),
            ),
          ),
        ],
      ),
    );
  }

  void _showCartDialog(BuildContext context) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        backgroundColor: const Color(0xFF1F2937),
        title: Text(
          'Shopping Cart',
          style: ATextStyle.large.copyWith(color: Colors.white),
        ),
        content: Text(
          'Cart functionality coming soon!',
          style: ATextStyle.medium.copyWith(color: const Color(0xFF9CA3AF)),
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: Text(
              'OK',
              style: ATextStyle.medium.copyWith(color: const Color(0xFF30A958)),
            ),
          ),
        ],
      ),
    );
  }
}
