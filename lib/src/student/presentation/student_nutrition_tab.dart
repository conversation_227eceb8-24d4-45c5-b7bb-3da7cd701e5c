import 'package:flutter/material.dart';
import 'package:flutter_hooks/flutter_hooks.dart';
import 'package:hooks_riverpod/hooks_riverpod.dart';
import 'package:supabase_flutter/supabase_flutter.dart';
import '../application/student_nutrition_provider.dart';
import '../../instructor/domain/student_nutrition_assignment.dart';
import '../../shared/utils/app_logger.dart';

/// Student Nutrition Tab - View assigned nutrition plan
class StudentNutritionTab extends HookConsumerWidget {
  const StudentNutritionTab({super.key});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final selectedMealIndex = useState(0);
    final selectedVideoCategory = useState('Breakfast');
    
    // Get current user ID (student)
    final currentUser = Supabase.instance.client.auth.currentUser;
    if (currentUser == null) {
      AppLogger.warning('User not logged in - cannot load nutrition plan', tag: 'NUTRITION_TAB');
      return const Center(
        child: Text(
          'Please log in to view your nutrition plan',
          style: TextStyle(color: Colors.white),
        ),
      );
    }

    AppLogger.info('Loading nutrition tab for student: ${currentUser.id}', tag: 'NUTRITION_TAB');
    final nutritionAssignmentAsync = ref.watch(studentNutritionAssignmentProvider(currentUser.id));

    return Scaffold(
      backgroundColor: const Color(0xFF111827),
      body: nutritionAssignmentAsync.when(
        data: (assignment) {
          if (assignment == null) {
            AppLogger.info('No nutrition plan assigned to student', tag: 'NUTRITION_TAB');
            return _buildEmptyState();
          }
          AppLogger.success('Nutrition plan loaded: ${assignment.planName}', tag: 'NUTRITION_TAB');
          return _buildNutritionPlanView(context, assignment, selectedMealIndex, selectedVideoCategory);
        },
        loading: () {
          AppLogger.info('Loading nutrition plan...', tag: 'NUTRITION_TAB');
          return _buildLoadingState();
        },
        error: (error, stack) {
          AppLogger.error('Failed to load nutrition plan', tag: 'NUTRITION_TAB', error: error, stackTrace: stack);
          return _buildErrorState(error.toString());
        },
      ),
    );
  }

  Widget _buildNutritionPlanView(
    BuildContext context,
    StudentNutritionAssignment assignment,
    ValueNotifier<int> selectedMealIndex,
    ValueNotifier<String> selectedVideoCategory,
  ) {
    return SafeArea(
      child: Column(
        children: [
          // Header
          _buildHeader(assignment),
          
          // Meal Navigation
          if (assignment.meals.isNotEmpty) ...[
            _buildMealNavigation(assignment.meals, selectedMealIndex),
            
            // Content
            Expanded(
              child: SingleChildScrollView(
                padding: const EdgeInsets.all(16),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    // Current meal content
                    if (selectedMealIndex.value < assignment.meals.length) ...[
                      _buildCurrentMealContent(assignment.meals[selectedMealIndex.value], assignment.notes),
                      const SizedBox(height: 24),
                    ],
                    
                    // Video Guides Section
                    _buildVideoGuidesSection(selectedVideoCategory),
                  ],
                ),
              ),
            ),
          ] else ...[
            Expanded(
              child: _buildEmptyMealsState(),
            ),
          ],
        ],
      ),
    );
  }

  Widget _buildHeader(StudentNutritionAssignment assignment) {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: const BoxDecoration(
        color: Color(0xFF1F2937),
        border: Border(
          bottom: BorderSide(color: Color(0xFF374151)),
        ),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              const Icon(
                Icons.restaurant_menu,
                color: Color(0xFFFACC15),
                size: 24,
              ),
              const SizedBox(width: 12),
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      assignment.planName,
                      style: const TextStyle(
                        color: Colors.white,
                        fontSize: 20,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                    if (assignment.description != null) ...[
                      const SizedBox(height: 4),
                      Text(
                        assignment.description!,
                        style: const TextStyle(
                          color: Color(0xFF9CA3AF),
                          fontSize: 14,
                        ),
                      ),
                    ],
                  ],
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildMealNavigation(List<StudentNutritionMeal> meals, ValueNotifier<int> selectedMealIndex) {
    return Container(
      height: 60,
      padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
      child: ListView.builder(
        scrollDirection: Axis.horizontal,
        itemCount: meals.length,
        itemBuilder: (context, index) {
          final meal = meals[index];
          final isSelected = selectedMealIndex.value == index;
          
          return Container(
            margin: const EdgeInsets.only(right: 12),
            child: GestureDetector(
              onTap: () {
                AppLogger.userAction('Selected meal: ${meal.name}', tag: 'NUTRITION_TAB');
                selectedMealIndex.value = index;
              },
              child: Container(
                padding: const EdgeInsets.symmetric(horizontal: 20, vertical: 8),
                decoration: BoxDecoration(
                  color: isSelected ? const Color(0xFFFACC15) : const Color(0xFF1F2937),
                  borderRadius: BorderRadius.circular(20),
                  border: Border.all(
                    color: isSelected ? const Color(0xFFFACC15) : const Color(0xFF374151),
                  ),
                ),
                child: Center(
                  child: Text(
                    meal.name,
                    style: TextStyle(
                      color: isSelected ? Colors.black : Colors.white,
                      fontSize: 14,
                      fontWeight: isSelected ? FontWeight.w600 : FontWeight.normal,
                    ),
                  ),
                ),
              ),
            ),
          );
        },
      ),
    );
  }

  Widget _buildCurrentMealContent(StudentNutritionMeal meal, String? trainerNotes) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        // Nutrition Summary
        _buildNutritionSummary(meal),
        const SizedBox(height: 20),
        
        // Trainer Notes
        if (trainerNotes != null && trainerNotes.isNotEmpty) ...[
          _buildTrainerNotes(trainerNotes),
          const SizedBox(height: 20),
        ],
        
        // Meal Composition
        _buildMealComposition(meal),
      ],
    );
  }

  Widget _buildNutritionSummary(StudentNutritionMeal meal) {
    // Calculate totals for this meal
    final totalCalories = meal.totalCalories;
    final totalProtein = meal.totalProtein;
    final totalCarbs = meal.totalCarbs;
    final totalFats = meal.totalFats;

    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: const Color(0xFF1F2937),
        borderRadius: BorderRadius.circular(12),
        border: Border.all(color: const Color(0xFF374151)),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          const Text(
            'Nutrition Summary',
            style: TextStyle(
              color: Colors.white,
              fontSize: 18,
              fontWeight: FontWeight.bold,
            ),
          ),
          const SizedBox(height: 16),
          Row(
            children: [
              Expanded(
                child: _buildNutritionCard(
                  'Calories',
                  '${totalCalories.toInt()}',
                  'kcal',
                  const Color(0xFFEF4444),
                ),
              ),
              const SizedBox(width: 12),
              Expanded(
                child: _buildNutritionCard(
                  'Protein',
                  '${totalProtein.toInt()}',
                  'g',
                  const Color(0xFF10B981),
                ),
              ),
            ],
          ),
          const SizedBox(height: 12),
          Row(
            children: [
              Expanded(
                child: _buildNutritionCard(
                  'Carbs',
                  '${totalCarbs.toInt()}',
                  'g',
                  const Color(0xFF3B82F6),
                ),
              ),
              const SizedBox(width: 12),
              Expanded(
                child: _buildNutritionCard(
                  'Fats',
                  '${totalFats.toInt()}',
                  'g',
                  const Color(0xFFF59E0B),
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildNutritionCard(String label, String value, String unit, Color color) {
    return Container(
      padding: const EdgeInsets.all(12),
      decoration: BoxDecoration(
        color: color.withOpacity(0.1),
        borderRadius: BorderRadius.circular(8),
        border: Border.all(color: color.withOpacity(0.3)),
      ),
      child: Column(
        children: [
          Text(
            label,
            style: TextStyle(
              color: color,
              fontSize: 12,
              fontWeight: FontWeight.w500,
            ),
          ),
          const SizedBox(height: 4),
          RichText(
            text: TextSpan(
              children: [
                TextSpan(
                  text: value,
                  style: TextStyle(
                    color: color,
                    fontSize: 18,
                    fontWeight: FontWeight.bold,
                  ),
                ),
                TextSpan(
                  text: ' $unit',
                  style: TextStyle(
                    color: color.withOpacity(0.7),
                    fontSize: 12,
                    fontWeight: FontWeight.normal,
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildTrainerNotes(String notes) {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: const Color(0xFF1F2937),
        borderRadius: BorderRadius.circular(12),
        border: Border.all(color: const Color(0xFF374151)),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          const Row(
            children: [
              Icon(
                Icons.lightbulb_outline,
                color: Color(0xFFFACC15),
                size: 20,
              ),
              SizedBox(width: 8),
              Text(
                'Trainer Notes',
                style: TextStyle(
                  color: Colors.white,
                  fontSize: 16,
                  fontWeight: FontWeight.bold,
                ),
              ),
            ],
          ),
          const SizedBox(height: 12),
          Text(
            notes,
            style: const TextStyle(
              color: Color(0xFF9CA3AF),
              fontSize: 14,
              height: 1.5,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildMealComposition(StudentNutritionMeal meal) {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: const Color(0xFF1F2937),
        borderRadius: BorderRadius.circular(12),
        border: Border.all(color: const Color(0xFF374151)),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          const Text(
            'Meal Composition',
            style: TextStyle(
              color: Colors.white,
              fontSize: 16,
              fontWeight: FontWeight.bold,
            ),
          ),
          const SizedBox(height: 16),
          ...meal.items.map((item) => _buildFoodItem(item)).toList(),
        ],
      ),
    );
  }

  Widget _buildFoodItem(StudentNutritionMealItem item) {
    return Container(
      margin: const EdgeInsets.only(bottom: 12),
      padding: const EdgeInsets.all(12),
      decoration: BoxDecoration(
        color: const Color(0xFF374151).withOpacity(0.3),
        borderRadius: BorderRadius.circular(8),
      ),
      child: Row(
        children: [
          Container(
            width: 8,
            height: 8,
            decoration: const BoxDecoration(
              color: Color(0xFFFACC15),
              shape: BoxShape.circle,
            ),
          ),
          const SizedBox(width: 12),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  item.foodName,
                  style: const TextStyle(
                    color: Colors.white,
                    fontSize: 14,
                    fontWeight: FontWeight.w500,
                  ),
                ),
                const SizedBox(height: 2),
                Text(
                  '${item.quantity} ${item.unit}',
                  style: const TextStyle(
                    color: Color(0xFF9CA3AF),
                    fontSize: 12,
                  ),
                ),
              ],
            ),
          ),
          if (item.calories != null) ...[
            Text(
              '${item.calories!.toInt()} kcal',
              style: const TextStyle(
                color: Color(0xFFFACC15),
                fontSize: 12,
                fontWeight: FontWeight.w500,
              ),
            ),
          ],
        ],
      ),
    );
  }

  Widget _buildVideoGuidesSection(ValueNotifier<String> selectedVideoCategory) {
    final categories = ['Breakfast', 'Lunch', 'Dinner'];

    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: const Color(0xFF1F2937),
        borderRadius: BorderRadius.circular(12),
        border: Border.all(color: const Color(0xFF374151)),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          const Row(
            children: [
              Icon(
                Icons.play_circle_outline,
                color: Color(0xFFFACC15),
                size: 20,
              ),
              SizedBox(width: 8),
              Text(
                'Video Guides',
                style: TextStyle(
                  color: Colors.white,
                  fontSize: 16,
                  fontWeight: FontWeight.bold,
                ),
              ),
            ],
          ),
          const SizedBox(height: 16),

          // Category tabs
          SizedBox(
            height: 40,
            child: ListView.builder(
              scrollDirection: Axis.horizontal,
              itemCount: categories.length,
              itemBuilder: (context, index) {
                final category = categories[index];
                final isSelected = selectedVideoCategory.value == category;

                return Container(
                  margin: const EdgeInsets.only(right: 8),
                  child: GestureDetector(
                    onTap: () {
                      AppLogger.userAction('Selected video category: $category', tag: 'NUTRITION_TAB');
                      selectedVideoCategory.value = category;
                    },
                    child: Container(
                      padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
                      decoration: BoxDecoration(
                        color: isSelected ? const Color(0xFFFACC15).withOpacity(0.2) : Colors.transparent,
                        borderRadius: BorderRadius.circular(20),
                        border: Border.all(
                          color: isSelected ? const Color(0xFFFACC15) : const Color(0xFF374151),
                        ),
                      ),
                      child: Text(
                        category,
                        style: TextStyle(
                          color: isSelected ? const Color(0xFFFACC15) : const Color(0xFF9CA3AF),
                          fontSize: 12,
                          fontWeight: isSelected ? FontWeight.w600 : FontWeight.normal,
                        ),
                      ),
                    ),
                  ),
                );
              },
            ),
          ),

          const SizedBox(height: 16),

          // Video cards
          ...MockVideoGuides.getVideosByCategory(selectedVideoCategory.value)
              .map((video) => _buildVideoCard(video))
              .toList(),
        ],
      ),
    );
  }

  Widget _buildVideoCard(VideoGuide video) {
    return Container(
      margin: const EdgeInsets.only(bottom: 12),
      padding: const EdgeInsets.all(12),
      decoration: BoxDecoration(
        color: const Color(0xFF374151).withOpacity(0.3),
        borderRadius: BorderRadius.circular(8),
      ),
      child: Row(
        children: [
          Container(
            width: 48,
            height: 48,
            decoration: BoxDecoration(
              color: const Color(0xFFFACC15).withOpacity(0.2),
              borderRadius: BorderRadius.circular(8),
            ),
            child: const Icon(
              Icons.play_arrow,
              color: Color(0xFFFACC15),
              size: 24,
            ),
          ),
          const SizedBox(width: 12),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  video.title,
                  style: const TextStyle(
                    color: Colors.white,
                    fontSize: 14,
                    fontWeight: FontWeight.w500,
                  ),
                ),
                const SizedBox(height: 4),
                Text(
                  video.duration,
                  style: const TextStyle(
                    color: Color(0xFF9CA3AF),
                    fontSize: 12,
                  ),
                ),
              ],
            ),
          ),
          const Icon(
            Icons.arrow_forward_ios,
            color: Color(0xFF9CA3AF),
            size: 16,
          ),
        ],
      ),
    );
  }

  Widget _buildLoadingState() {
    return const Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          CircularProgressIndicator(color: Color(0xFFFACC15)),
          SizedBox(height: 16),
          Text(
            'Loading your nutrition plan...',
            style: TextStyle(
              color: Color(0xFF9CA3AF),
              fontSize: 16,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildErrorState(String error) {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          const Icon(
            Icons.error_outline,
            color: Color(0xFFEF4444),
            size: 48,
          ),
          const SizedBox(height: 16),
          const Text(
            'Failed to load nutrition plan',
            style: TextStyle(
              color: Colors.white,
              fontSize: 18,
              fontWeight: FontWeight.bold,
            ),
          ),
          const SizedBox(height: 8),
          Text(
            error,
            textAlign: TextAlign.center,
            style: const TextStyle(
              color: Color(0xFF9CA3AF),
              fontSize: 14,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildEmptyState() {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Container(
            width: 80,
            height: 80,
            decoration: BoxDecoration(
              color: const Color(0xFF1F2937),
              borderRadius: BorderRadius.circular(40),
              border: Border.all(color: const Color(0xFF374151)),
            ),
            child: const Icon(
              Icons.restaurant_menu,
              color: Color(0xFF9CA3AF),
              size: 40,
            ),
          ),
          const SizedBox(height: 24),
          const Text(
            'No Nutrition Plan Assigned',
            style: TextStyle(
              color: Colors.white,
              fontSize: 20,
              fontWeight: FontWeight.bold,
            ),
          ),
          const SizedBox(height: 8),
          const Text(
            'Your instructor hasn\'t assigned a nutrition plan yet.\nCheck back later or contact your instructor.',
            textAlign: TextAlign.center,
            style: TextStyle(
              color: Color(0xFF9CA3AF),
              fontSize: 14,
              height: 1.5,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildEmptyMealsState() {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          const Icon(
            Icons.no_meals,
            color: Color(0xFF9CA3AF),
            size: 48,
          ),
          const SizedBox(height: 16),
          const Text(
            'No Meals Available',
            style: TextStyle(
              color: Colors.white,
              fontSize: 18,
              fontWeight: FontWeight.bold,
            ),
          ),
          const SizedBox(height: 8),
          const Text(
            'Your nutrition plan doesn\'t have any meals configured yet.',
            textAlign: TextAlign.center,
            style: TextStyle(
              color: Color(0xFF9CA3AF),
              fontSize: 14,
            ),
          ),
        ],
      ),
    );
  }
}
