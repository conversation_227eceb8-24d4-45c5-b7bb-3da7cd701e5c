import 'package:flutter/material.dart';
import 'package:flutter_hooks/flutter_hooks.dart';
import 'package:hooks_riverpod/hooks_riverpod.dart';
import 'package:fitgo_app/src/theme/colors.dart';
import 'package:fitgo_app/src/shared/widgets/text_widget/text_widget.dart';
import 'package:fitgo_app/src/shared/constants/app_text_style.dart';
import 'package:fitgo_app/src/shared/extensions/string_hardcoded.dart';
import 'package:fitgo_app/src/student/domain/trainer_detail.dart';
import 'package:fitgo_app/src/student/application/trainer_detail_provider.dart';
import 'package:fitgo_app/src/student/presentation/components/trainer_image.dart';
import 'package:fitgo_app/src/student/presentation/components/stat_card.dart';

import 'package:fitgo_app/src/student/presentation/components/work_history_card.dart';
import 'package:fitgo_app/src/student/presentation/components/certification_card.dart';
import 'package:fitgo_app/src/student/presentation/components/review_card.dart';
import 'package:fitgo_app/src/student/presentation/components/reviews_modal.dart';
import 'package:fitgo_app/src/program_enrollment/presentation/program_enrollment_view.dart';
import 'package:fitgo_app/src/shared/widgets/app_drawer.dart';
import 'package:fitgo_app/src/instructor/domain/instructor_profile_models.dart'
    show FAQ;

class InstructorDetailView extends HookConsumerWidget {
  final String instructorId;

  const InstructorDetailView({super.key, required this.instructorId});

  /// Backward compatibility constructor
  const InstructorDetailView.trainer({super.key, required String trainerId})
      : instructorId = trainerId;

  /// Named constructor for backward compatibility with trainerId parameter
  const InstructorDetailView.fromTrainerId({
    super.key,
    required String trainerId,
  }) : instructorId = trainerId;

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final trainerDetailAsync = ref.watch(
      instructorDetailProvider(instructorId),
    );

    return Scaffold(
      backgroundColor: const Color(0xFF0F172A), // Dark background
      endDrawer: const AppDrawer(),
      body: SafeArea(
        child: Column(
          children: [
            // Top app bar
            _buildTopAppBar(context),

            // Main content
            Expanded(
              child: trainerDetailAsync.when(
                data: (instructor) =>
                    _buildInstructorContent(context, ref, instructor),
                loading: () => _buildLoadingState(),
                error: (error, stack) =>
                    _buildErrorState(context, error.toString()),
              ),
            ),
          ],
        ),
      ),
      floatingActionButton: trainerDetailAsync.when(
        loading: () => null,
        error: (error, stack) => null,
        data: (instructor) => _buildFloatingBookButton(context, instructor),
      ),
      floatingActionButtonLocation: FloatingActionButtonLocation.centerFloat,
    );
  }

  Widget _buildTopAppBar(BuildContext context) {
    return Padding(
      padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
      child: Row(
        children: [
          // Back button
          GestureDetector(
            onTap: () => Navigator.of(context).pop(),
            child: Container(
              width: 40,
              height: 40,
              decoration: BoxDecoration(
                color: const Color(0xFF1E293B),
                borderRadius: BorderRadius.circular(20),
              ),
              child: const Icon(
                Icons.arrow_back_ios_new,
                color: Colors.white,
                size: 18,
              ),
            ),
          ),

          const SizedBox(width: 16),

          // Fitgo logo
          Expanded(
            child: Row(
              children: [
                TextWidget(
                  'Fit',
                  style: ATextStyle.title.copyWith(
                    color: Colors.white,
                    fontSize: 24,
                    fontWeight: FontWeight.bold,
                  ),
                ),
                TextWidget(
                  'go',
                  style: ATextStyle.title.copyWith(
                    color: AColor.fitgoGreen,
                    fontSize: 24,
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ],
            ),
          ),

          // Hamburger menu
          Builder(
            builder: (context) => GestureDetector(
              onTap: () {
                debugPrint('🍔 Hamburger menu tapped in trainer detail!');
                try {
                  Scaffold.of(context).openEndDrawer();
                  debugPrint('✅ openEndDrawer() called successfully');
                } catch (e) {
                  debugPrint('❌ Error opening drawer: $e');
                }
              },
              child: Container(
                width: 40,
                height: 40,
                decoration: BoxDecoration(
                  color: const Color(0xFF1E293B),
                  borderRadius: BorderRadius.circular(20),
                ),
                child: const Icon(
                  Icons.menu,
                  color: Colors.white,
                  size: 20,
                ),
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildInstructorContent(
    BuildContext context,
    WidgetRef ref,
    InstructorDetail instructor,
  ) {
    return SingleChildScrollView(
      padding: const EdgeInsets.symmetric(horizontal: 16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          const SizedBox(height: 16),

          // Instructor profile image
          TrainerImage(imageUrl: instructor.photoUrl),

          const SizedBox(height: 20),

          // Instructor name and rating
          _buildNameAndRating(instructor),

          const SizedBox(height: 20),

          // Stats cards
          _buildStatsCards(instructor),

          const SizedBox(height: 24),

          // Bio Section
          if (instructor.bio != null && instructor.bio!.isNotEmpty) ...[
            _buildBioSection(instructor.bio!),
            const SizedBox(height: 24),
          ],

          // Work History
          _buildWorkHistory(instructor.workHistory),

          const SizedBox(height: 24),

          // Certifications
          _buildCertifications(instructor.certifications),

          const SizedBox(height: 24),

          // Client Reviews
          _buildClientReviews(instructor.reviews, context, ref),

          const SizedBox(height: 24),

          // FAQs
          _buildFAQs(instructor.faqs),

          const SizedBox(height: 32),

          // Extra bottom padding for floating action button
          const SizedBox(height: 80),
        ],
      ),
    );
  }

  Widget _buildBioSection(String bio) {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: const Color(0xFF1E293B),
        borderRadius: BorderRadius.circular(12),
        border: Border.all(color: const Color(0xFF334155), width: 1),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              const Icon(Icons.person, color: Color(0xFFFACC15), size: 20),
              const SizedBox(width: 8),
              TextWidget(
                'About',
                style: ATextStyle.medium.copyWith(
                  color: Colors.white,
                  fontWeight: FontWeight.bold,
                ),
              ),
            ],
          ),
          const SizedBox(height: 12),
          TextWidget(
            bio,
            style: ATextStyle.medium.copyWith(
              color: Colors.grey[300],
              height: 1.5,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildNameAndRating(InstructorDetail instructor) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        TextWidget(
          instructor.name,
          style: ATextStyle.title.copyWith(
            color: Colors.white,
            fontSize: 28,
            fontWeight: FontWeight.bold,
          ),
        ),
        const SizedBox(height: 8),
        // Gym affiliation
        Row(
          children: [
            const Icon(Icons.location_on, color: Colors.grey, size: 16),
            const SizedBox(width: 4),
            TextWidget(
              instructor.currentWorkplace ?? 'Fitness Center',
              style: ATextStyle.medium.copyWith(
                color: Colors.grey[400],
                fontSize: 14,
              ),
            ),
          ],
        ),
        const SizedBox(height: 8),
        // Rating
        Row(
          children: [
            const Icon(Icons.star, color: Color(0xFFFACC15), size: 20),
            const SizedBox(width: 4),
            TextWidget(
              instructor.rating.toStringAsFixed(1),
              style: ATextStyle.medium.copyWith(
                color: Colors.white,
                fontWeight: FontWeight.w600,
              ),
            ),
            const SizedBox(width: 4),
            TextWidget(
              '(${instructor.reviews.length} reviews)',
              style: ATextStyle.medium.copyWith(
                color: Colors.grey[400],
                fontSize: 14,
              ),
            ),
          ],
        ),
      ],
    );
  }

  Widget _buildStatsCards(InstructorDetail instructor) {
    return Column(
      children: [
        // Experience, Clients, and Capacity in one row
        Row(
          children: [
            Expanded(
              child: StatCard(
                title: 'Experience'.hardcoded,
                value: '${instructor.experienceYears}+ Years',
              ),
            ),
            const SizedBox(width: 12),
            Expanded(
              child: StatCard(
                title: 'Clients'.hardcoded,
                value: '${instructor.clientCount}+',
              ),
            ),
            const SizedBox(width: 12),
            Expanded(
              child: StatCard(
                title: 'Capacity'.hardcoded,
                value:
                    '${instructor.currentStudents}/${instructor.maxStudents}',
              ),
            ),
          ],
        ),
      ],
    );
  }

  Widget _buildWorkHistory(List<WorkHistory> workHistory) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        TextWidget(
          'Work History'.hardcoded,
          style: ATextStyle.large.copyWith(
            color: Colors.white,
            fontWeight: FontWeight.bold,
          ),
        ),
        const SizedBox(height: 12),
        if (workHistory.isEmpty)
          Container(
            padding: const EdgeInsets.all(16),
            decoration: BoxDecoration(
              color: const Color(0xFF1E293B),
              borderRadius: BorderRadius.circular(12),
            ),
            child: Row(
              children: [
                const Icon(Icons.work_outline, color: Colors.grey, size: 24),
                const SizedBox(width: 12),
                TextWidget(
                  'No work history available yet.'.hardcoded,
                  style: ATextStyle.medium.copyWith(color: Colors.grey),
                ),
              ],
            ),
          )
        else
          ...workHistory.map(
            (work) => Padding(
              padding: const EdgeInsets.only(bottom: 12),
              child: WorkHistoryCard(workHistory: work),
            ),
          ),
      ],
    );
  }

  Widget _buildCertifications(List<Certification> certifications) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        TextWidget(
          'Certifications'.hardcoded,
          style: ATextStyle.large.copyWith(
            color: Colors.white,
            fontWeight: FontWeight.bold,
          ),
        ),
        const SizedBox(height: 12),
        if (certifications.isEmpty)
          Container(
            padding: const EdgeInsets.all(16),
            decoration: BoxDecoration(
              color: const Color(0xFF1E293B),
              borderRadius: BorderRadius.circular(12),
            ),
            child: Row(
              children: [
                const Icon(
                  Icons.verified_outlined,
                  color: Colors.grey,
                  size: 24,
                ),
                const SizedBox(width: 12),
                TextWidget(
                  'No certifications listed yet.'.hardcoded,
                  style: ATextStyle.medium.copyWith(color: Colors.grey),
                ),
              ],
            ),
          )
        else
          ...certifications.map(
            (cert) => Padding(
              padding: const EdgeInsets.only(bottom: 12),
              child: CertificationCard(certification: cert),
            ),
          ),
      ],
    );
  }

  Widget _buildClientReviews(
    List<Review> reviews,
    BuildContext context,
    WidgetRef ref,
  ) {
    // Show only first 2 reviews
    final reviewsToShow = reviews.take(2).toList();
    final hasMoreReviews = reviews.length > 2;

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        // Header with Show All button
        Row(
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          crossAxisAlignment: CrossAxisAlignment.center,
          children: [
            TextWidget(
              'Client Reviews'.hardcoded,
              style: ATextStyle.large.copyWith(
                color: Colors.white,
                fontWeight: FontWeight.bold,
              ),
            ),
            if (hasMoreReviews)
              GestureDetector(
                onTap: () {
                  final instructorDetailAsync = ref.read(
                    instructorDetailProvider(instructorId),
                  );
                  instructorDetailAsync.whenData((instructor) {
                    showModalBottomSheet(
                      context: context,
                      isScrollControlled: true,
                      isDismissible: true,
                      enableDrag: true,
                      backgroundColor: Colors.transparent,
                      builder: (context) => ReviewsModal(
                        instructorId: instructorId,
                        instructorName: instructor.name,
                      ),
                    );
                  });
                },
                child: Container(
                  padding: const EdgeInsets.symmetric(
                    horizontal: 12,
                    vertical: 6,
                  ),
                  decoration: BoxDecoration(
                    color: AColor.fitgoGreen.withValues(alpha: 0.1),
                    borderRadius: BorderRadius.circular(16),
                    border: Border.all(
                      color: AColor.fitgoGreen.withValues(alpha: 0.3),
                      width: 1,
                    ),
                  ),
                  child: Row(
                    mainAxisSize: MainAxisSize.min,
                    children: [
                      TextWidget(
                        'Show All'.hardcoded,
                        style: ATextStyle.small.copyWith(
                          color: AColor.fitgoGreen,
                          fontWeight: FontWeight.w600,
                        ),
                      ),
                      const SizedBox(width: 4),
                      Icon(
                        Icons.arrow_forward_ios,
                        color: AColor.fitgoGreen,
                        size: 12,
                      ),
                    ],
                  ),
                ),
              ),
          ],
        ),
        const SizedBox(height: 12),
        if (reviews.isEmpty)
          Container(
            padding: const EdgeInsets.all(16),
            decoration: BoxDecoration(
              color: const Color(0xFF1E293B),
              borderRadius: BorderRadius.circular(12),
            ),
            child: Row(
              children: [
                const Icon(
                  Icons.rate_review_outlined,
                  color: Colors.grey,
                  size: 24,
                ),
                const SizedBox(width: 12),
                TextWidget(
                  'No client reviews yet.'.hardcoded,
                  style: ATextStyle.medium.copyWith(color: Colors.grey),
                ),
              ],
            ),
          )
        else
          // Show first 2 reviews
          Column(
            children: reviewsToShow
                .map(
                  (review) => Padding(
                    padding: const EdgeInsets.only(bottom: 16),
                    child: ReviewCard(review: review),
                  ),
                )
                .toList(),
          ),
      ],
    );
  }

  Widget _buildFAQs(List<FAQ> faqs) {
    if (faqs.isEmpty) {
      return const SizedBox.shrink();
    }

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        TextWidget(
          'Frequently Asked Questions'.hardcoded,
          style: ATextStyle.semiLarge.copyWith(
            color: AColor.textColor,
            fontWeight: FontWeight.bold,
          ),
        ),
        const SizedBox(height: 16),
        ...faqs.map((faq) => _buildFAQItem(faq)),
      ],
    );
  }

  Widget _buildFAQItem(FAQ faq) {
    return Container(
      margin: const EdgeInsets.only(bottom: 12),
      decoration: BoxDecoration(
        color: const Color(0xFF1F2937),
        borderRadius: BorderRadius.circular(12),
        border: Border.all(color: const Color(0xFF374151)),
      ),
      child: ExpansionTile(
        title: TextWidget(
          faq.question,
          style: ATextStyle.medium.copyWith(
            color: AColor.textColor,
            fontWeight: FontWeight.w600,
          ),
        ),
        iconColor: AColor.fitgoGreen,
        collapsedIconColor: AColor.grey,
        children: [
          Padding(
            padding: const EdgeInsets.fromLTRB(16, 0, 16, 16),
            child: TextWidget(
              faq.answer,
              style: ATextStyle.medium.copyWith(
                color: AColor.grey,
                height: 1.5,
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildFloatingBookButton(
    BuildContext context,
    InstructorDetail instructor,
  ) {
    final bool hasAvailableCapacity =
        instructor.currentStudents < instructor.maxStudents;

    return Container(
      margin: const EdgeInsets.symmetric(horizontal: 16),
      child: Row(
        children: [
          // Enrollment Button
          Expanded(
            flex: 2,
            child: SizedBox(
              height: 56,
              child: FloatingActionButton.extended(
                onPressed: hasAvailableCapacity
                    ? () {
                        Navigator.of(context).push(
                          MaterialPageRoute(
                            builder: (context) => ProgramEnrollmentView(
                              trainerId: instructor.id,
                              trainerName: instructor.name,
                            ),
                          ),
                        );
                      }
                    : null,
                backgroundColor: hasAvailableCapacity
                    ? const Color(0xFF1E293B)
                    : Colors.grey[600],
                foregroundColor:
                    hasAvailableCapacity ? AColor.fitgoGreen : Colors.grey[400],
                elevation: hasAvailableCapacity ? 8 : 0,
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(28),
                  side: BorderSide(
                    color: hasAvailableCapacity
                        ? AColor.fitgoGreen.withValues(alpha: 0.3)
                        : Colors.grey.withValues(alpha: 0.3),
                    width: 1,
                  ),
                ),
                icon: Container(
                  padding: const EdgeInsets.all(6),
                  decoration: BoxDecoration(
                    color: hasAvailableCapacity
                        ? AColor.fitgoGreen.withValues(alpha: 0.1)
                        : Colors.grey.withValues(alpha: 0.1),
                    borderRadius: BorderRadius.circular(8),
                  ),
                  child: Icon(
                    hasAvailableCapacity ? Icons.calendar_today : Icons.block,
                    color: hasAvailableCapacity
                        ? AColor.fitgoGreen
                        : Colors.grey[400],
                    size: 20,
                  ),
                ),
                label: TextWidget(
                  hasAvailableCapacity
                      ? 'Eğitmene Kayıt Ol'.hardcoded
                      : 'Kapasite Dolu'.hardcoded,
                  style: ATextStyle.medium.copyWith(
                    color: hasAvailableCapacity
                        ? AColor.fitgoGreen
                        : Colors.grey[400],
                    fontWeight: FontWeight.w600,
                    letterSpacing: 0.5,
                  ),
                ),
              ),
            ),
          ),

          const SizedBox(width: 12),

          // Pricing Section (right side)
          if ((instructor.basicPrice != null ||
                  instructor.premiumPrice != null) &&
              hasAvailableCapacity)
            Container(
              padding: const EdgeInsets.all(12),
              decoration: BoxDecoration(
                color: const Color(0xFF1E293B),
                borderRadius: BorderRadius.circular(12),
                border: Border.all(
                  color: AColor.fitgoGreen.withValues(alpha: 0.3),
                  width: 1,
                ),
              ),
              child: Column(
                mainAxisSize: MainAxisSize.min,
                children: [
                  // Basic Plan
                  if (instructor.basicPrice != null)
                    Row(
                      children: [
                        TextWidget(
                          'Basic: ',
                          style: ATextStyle.small.copyWith(
                            color: Colors.grey[400],
                            fontWeight: FontWeight.w500,
                          ),
                        ),
                        TextWidget(
                          '${instructor.basicPrice!.toInt()} TL/ay',
                          style: ATextStyle.small.copyWith(
                            color: AColor.fitgoGreen,
                            fontWeight: FontWeight.bold,
                          ),
                        ),
                      ],
                    ),

                  // Premium Plan
                  if (instructor.premiumPrice != null)
                    Row(
                      children: [
                        TextWidget(
                          'Premium: ',
                          style: ATextStyle.small.copyWith(
                            color: Colors.grey[400],
                            fontWeight: FontWeight.w500,
                          ),
                        ),
                        TextWidget(
                          '${instructor.premiumPrice!.toInt()} TL/ay',
                          style: ATextStyle.small.copyWith(
                            color: const Color(0xFFFACC15),
                            fontWeight: FontWeight.bold,
                          ),
                        ),
                      ],
                    ),
                ],
              ),
            ),
        ],
      ),
    );
  }

  Widget _buildLoadingState() {
    return const Center(
      child: CircularProgressIndicator(color: Color(0xFFFACC15)),
    );
  }

  Widget _buildErrorState(BuildContext context, String error) {
    final isNotFound = error.toLowerCase().contains('not found') ||
        error.toLowerCase().contains('no rows returned');

    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(
            isNotFound ? Icons.person_off : Icons.error_outline,
            color: isNotFound ? Colors.orange : Colors.red,
            size: 64,
          ),
          const SizedBox(height: 16),
          TextWidget(
            isNotFound
                ? 'Trainer not found'.hardcoded
                : 'Error loading trainer details'.hardcoded,
            style: ATextStyle.large.copyWith(color: Colors.white),
          ),
          const SizedBox(height: 8),
          TextWidget(
            isNotFound
                ? 'This trainer may no longer be available.'.hardcoded
                : error,
            style: ATextStyle.medium.copyWith(color: Colors.grey),
            textAlign: TextAlign.center,
          ),
          const SizedBox(height: 24),
          Row(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              ElevatedButton(
                onPressed: () => Navigator.of(context).pop(),
                style: ElevatedButton.styleFrom(
                  backgroundColor: const Color(0xFF1E293B),
                  foregroundColor: Colors.white,
                ),
                child: TextWidget(
                  'Go Back'.hardcoded,
                  style: ATextStyle.medium.copyWith(color: Colors.white),
                ),
              ),
              if (!isNotFound) ...[
                const SizedBox(width: 16),
                ElevatedButton(
                  onPressed: () {
                    // Retry loading
                    // This will trigger a rebuild and retry the provider
                  },
                  style: ElevatedButton.styleFrom(
                    backgroundColor: AColor.fitgoGreen,
                    foregroundColor: Colors.black,
                  ),
                  child: TextWidget(
                    'Retry'.hardcoded,
                    style: ATextStyle.medium.copyWith(color: Colors.black),
                  ),
                ),
              ],
            ],
          ),
        ],
      ),
    );
  }
}

/// Backward compatibility class
class TrainerDetailView extends InstructorDetailView {
  const TrainerDetailView({super.key, required String trainerId})
      : super(instructorId: trainerId);
}
