import 'package:flutter/material.dart';
import 'package:flutter_hooks/flutter_hooks.dart';
import 'package:hooks_riverpod/hooks_riverpod.dart';
import 'package:fitgo_app/src/shared/widgets/text_widget/text_widget.dart';
import 'package:fitgo_app/src/shared/constants/app_text_style.dart';
import 'package:fitgo_app/src/shared/extensions/build_context/screen_util_ext.dart';
import 'package:fitgo_app/src/theme/colors.dart';
import 'package:fitgo_app/src/shared/extensions/string_hardcoded.dart';
import 'package:fitgo_app/src/student/application/paginated_reviews_provider.dart';
import 'package:fitgo_app/src/student/presentation/components/review_card.dart';

class ReviewsModal extends HookConsumerWidget {
  final String instructorId;
  final String instructorName;

  const ReviewsModal({
    super.key,
    required this.instructorId,
    required this.instructorName,
  });

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final scrollController = useScrollController();
    final reviewsState = ref.watch(paginatedReviewsProvider(instructorId));

    // Load initial reviews when modal opens
    useEffect(() {
      WidgetsBinding.instance.addPostFrameCallback((_) {
        ref
            .read(paginatedReviewsProvider(instructorId).notifier)
            .loadInitialReviews();
      });
      return null;
    }, []);

    // Setup scroll listener for pagination
    useEffect(() {
      void onScroll() {
        if (scrollController.position.pixels >=
            scrollController.position.maxScrollExtent - 200) {
          // Load next page when user is 200px from bottom
          ref
              .read(paginatedReviewsProvider(instructorId).notifier)
              .loadNextPage();
        }
      }

      scrollController.addListener(onScroll);
      return () => scrollController.removeListener(onScroll);
    }, [scrollController]);

    return Container(
      height: context.height * 0.85,
      decoration: const BoxDecoration(
        color: Color(0xFF0F172A), // Dark background
        borderRadius: BorderRadius.only(
          topLeft: Radius.circular(20),
          topRight: Radius.circular(20),
        ),
      ),
      child: Column(
        children: [
          // Modal header
          _buildModalHeader(context, instructorName),

          // Reviews content
          Expanded(
            child: _buildReviewsContent(
              reviewsState,
              scrollController,
              ref,
              instructorId,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildModalHeader(BuildContext context, String trainerName) {
    return Container(
      padding: const EdgeInsets.all(20),
      decoration: const BoxDecoration(
        border: Border(bottom: BorderSide(color: Color(0xFF1E293B), width: 1)),
      ),
      child: Row(
        children: [
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                TextWidget(
                  'Client Reviews'.hardcoded,
                  style: ATextStyle.large.copyWith(
                    color: Colors.white,
                    fontWeight: FontWeight.bold,
                    fontSize: 20,
                  ),
                ),
                const SizedBox(height: 4),
                TextWidget(
                  trainerName,
                  style: ATextStyle.medium.copyWith(
                    color: AColor.fitgoGreen,
                    fontWeight: FontWeight.w500,
                  ),
                ),
              ],
            ),
          ),
          GestureDetector(
            onTap: () => Navigator.of(context).pop(),
            child: Container(
              padding: const EdgeInsets.all(8),
              decoration: BoxDecoration(
                color: const Color(0xFF1E293B),
                borderRadius: BorderRadius.circular(8),
              ),
              child: const Icon(Icons.close, color: Colors.white, size: 20),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildReviewsContent(
    PaginatedReviewsState reviewsState,
    ScrollController scrollController,
    WidgetRef ref,
    String trainerId,
  ) {
    if (reviewsState.reviews.isEmpty && reviewsState.isLoading) {
      return const Center(
        child: CircularProgressIndicator(color: AColor.fitgoGreen),
      );
    }

    if (reviewsState.reviews.isEmpty && reviewsState.error != null) {
      return Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            const Icon(Icons.error_outline, color: Colors.red, size: 48),
            const SizedBox(height: 16),
            TextWidget(
              'Failed to load reviews'.hardcoded,
              style: ATextStyle.medium.copyWith(color: Colors.white),
            ),
            const SizedBox(height: 8),
            TextWidget(
              reviewsState.error!,
              style: ATextStyle.small.copyWith(color: Colors.grey),
              textAlign: TextAlign.center,
            ),
            const SizedBox(height: 16),
            ElevatedButton(
              onPressed: () {
                ref
                    .read(paginatedReviewsProvider(trainerId).notifier)
                    .loadInitialReviews();
              },
              style: ElevatedButton.styleFrom(
                backgroundColor: AColor.fitgoGreen,
                foregroundColor: Colors.black,
              ),
              child: TextWidget(
                'Retry'.hardcoded,
                style: ATextStyle.medium.copyWith(
                  color: Colors.black,
                  fontWeight: FontWeight.w600,
                ),
              ),
            ),
          ],
        ),
      );
    }

    if (reviewsState.reviews.isEmpty) {
      return Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            const Icon(
              Icons.rate_review_outlined,
              color: Colors.grey,
              size: 48,
            ),
            const SizedBox(height: 16),
            TextWidget(
              'No reviews yet'.hardcoded,
              style: ATextStyle.medium.copyWith(color: Colors.white),
            ),
            const SizedBox(height: 8),
            TextWidget(
              'Be the first to leave a review!'.hardcoded,
              style: ATextStyle.small.copyWith(color: Colors.grey),
            ),
          ],
        ),
      );
    }

    return Scrollbar(
      controller: scrollController,
      thumbVisibility: true,
      child: ListView.builder(
        controller: scrollController,
        padding: const EdgeInsets.all(20),
        itemCount: reviewsState.reviews.length + (reviewsState.hasMore ? 1 : 0),
        itemBuilder: (context, index) {
          if (index == reviewsState.reviews.length) {
            // Loading indicator at bottom
            return Container(
              padding: const EdgeInsets.symmetric(vertical: 20),
              child: const Center(
                child: CircularProgressIndicator(color: AColor.fitgoGreen),
              ),
            );
          }

          final review = reviewsState.reviews[index];
          return Padding(
            padding: const EdgeInsets.only(bottom: 16),
            child: ReviewCard(review: review),
          );
        },
      ),
    );
  }
}
