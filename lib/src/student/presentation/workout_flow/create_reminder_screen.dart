import 'package:flutter/material.dart';
import 'package:flutter_hooks/flutter_hooks.dart';
import 'package:hooks_riverpod/hooks_riverpod.dart';
import '../../application/workout_reminder_provider.dart';
import '../../domain/workout_reminder.dart' as reminder_domain;
import '../../../shared/utils/app_logger.dart';

/// Screen for creating or editing workout reminders
class CreateReminderScreen extends HookConsumerWidget {
  final reminder_domain.WorkoutReminder? editingReminder;
  final reminder_domain.ReminderTime? presetTime;
  final List<int>? presetWeekdays;

  const CreateReminderScreen({
    super.key,
    this.editingReminder,
    this.presetTime,
    this.presetWeekdays,
  });

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final isEditing = editingReminder != null;
    final selectedTime = useState<TimeOfDay>(
      presetTime?.toFlutterTimeOfDay() ??
      editingReminder?.reminderTime.toFlutterTimeOfDay() ??
      const TimeOfDay(hour: 9, minute: 0)
    );
    final selectedWeekdays = useState<Set<int>>(
      Set.from(presetWeekdays ?? editingReminder?.weekdays ?? [1, 2, 3, 4, 5])
    );
    final titleController = useTextEditingController(
      text: editingReminder?.title ?? 'Workout Time!'
    );
    final messageController = useTextEditingController(
      text: editingReminder?.message ?? reminder_domain.ReminderPresets.getRandomMessage()
    );
    final isLoading = useState(false);

    return Scaffold(
      backgroundColor: const Color(0xFF111827),
      appBar: AppBar(
        backgroundColor: const Color(0xFF1F2937),
        title: Text(
          isEditing ? 'Edit Reminder' : 'Create Reminder',
          style: const TextStyle(color: Colors.white, fontWeight: FontWeight.bold),
        ),
        leading: IconButton(
          icon: const Icon(Icons.arrow_back, color: Colors.white),
          onPressed: () => Navigator.of(context).pop(),
        ),
        actions: [
          TextButton(
            onPressed: isLoading.value ? null : () => _saveReminder(
              context,
              ref,
              selectedTime.value,
              selectedWeekdays.value.toList(),
              titleController.text,
              messageController.text,
              isLoading,
            ),
            child: Text(
              isEditing ? 'Update' : 'Save',
              style: TextStyle(
                color: isLoading.value ? const Color(0xFF9CA3AF) : const Color(0xFFFACC15),
                fontWeight: FontWeight.bold,
              ),
            ),
          ),
        ],
      ),
      body: SingleChildScrollView(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Time selection
            _buildTimeSection(context, selectedTime),

            const SizedBox(height: 24),

            // Weekdays selection
            _buildWeekdaysSection(selectedWeekdays),

            const SizedBox(height: 24),

            // Title input
            _buildTitleSection(titleController),

            const SizedBox(height: 24),

            // Message input
            _buildMessageSection(messageController),

            const SizedBox(height: 24),

            // Quick presets
            if (!isEditing) _buildPresetsSection(selectedTime, selectedWeekdays),

            const SizedBox(height: 32),

            // Save button
            if (isLoading.value)
              const Center(
                child: CircularProgressIndicator(color: Color(0xFFFACC15)),
              )
            else
              SizedBox(
                width: double.infinity,
                height: 56,
                child: ElevatedButton.icon(
                  onPressed: () => _saveReminder(
                    context,
                    ref,
                    selectedTime.value,
                    selectedWeekdays.value.toList(),
                    titleController.text,
                    messageController.text,
                    isLoading,
                  ),
                  style: ElevatedButton.styleFrom(
                    backgroundColor: const Color(0xFFFACC15),
                    foregroundColor: Colors.black,
                    shape: RoundedRectangleBorder(
                      borderRadius: BorderRadius.circular(12),
                    ),
                  ),
                  icon: Icon(isEditing ? Icons.update : Icons.alarm_add, size: 20),
                  label: Text(
                    isEditing ? 'Update Reminder' : 'Create Reminder',
                    style: const TextStyle(
                      fontSize: 16,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                ),
              ),
          ],
        ),
      ),
    );
  }

  Widget _buildTimeSection(BuildContext context, ValueNotifier<TimeOfDay> selectedTime) {
    return Container(
      padding: const EdgeInsets.all(20),
      decoration: BoxDecoration(
        color: const Color(0xFF1F2937),
        borderRadius: BorderRadius.circular(16),
        border: Border.all(color: const Color(0xFF374151)),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          const Text(
            'Reminder Time',
            style: TextStyle(
              color: Colors.white,
              fontSize: 18,
              fontWeight: FontWeight.bold,
            ),
          ),
          const SizedBox(height: 16),
          GestureDetector(
            onTap: () => _selectTime(context, selectedTime),
            child: Container(
              width: double.infinity,
              padding: const EdgeInsets.all(20),
              decoration: BoxDecoration(
                color: const Color(0xFFFACC15).withOpacity(0.1),
                borderRadius: BorderRadius.circular(12),
                border: Border.all(color: const Color(0xFFFACC15)),
              ),
              child: Row(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  const Icon(
                    Icons.access_time,
                    color: Color(0xFFFACC15),
                    size: 32,
                  ),
                  const SizedBox(width: 16),
                  Text(
                    selectedTime.value.format(context),
                    style: const TextStyle(
                      color: Colors.white,
                      fontSize: 32,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                ],
              ),
            ),
          ),
          const SizedBox(height: 12),
          const Text(
            'Tap to change time',
            style: TextStyle(
              color: Color(0xFF9CA3AF),
              fontSize: 14,
            ),
            textAlign: TextAlign.center,
          ),
        ],
      ),
    );
  }

  Widget _buildWeekdaysSection(ValueNotifier<Set<int>> selectedWeekdays) {
    const weekdays = [
      {'day': 1, 'label': 'Mon'},
      {'day': 2, 'label': 'Tue'},
      {'day': 3, 'label': 'Wed'},
      {'day': 4, 'label': 'Thu'},
      {'day': 5, 'label': 'Fri'},
      {'day': 6, 'label': 'Sat'},
      {'day': 7, 'label': 'Sun'},
    ];

    return Container(
      padding: const EdgeInsets.all(20),
      decoration: BoxDecoration(
        color: const Color(0xFF1F2937),
        borderRadius: BorderRadius.circular(16),
        border: Border.all(color: const Color(0xFF374151)),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          const Text(
            'Repeat On',
            style: TextStyle(
              color: Colors.white,
              fontSize: 18,
              fontWeight: FontWeight.bold,
            ),
          ),
          const SizedBox(height: 16),
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceEvenly,
            children: weekdays.map((weekday) {
              final day = weekday['day'] as int;
              final label = weekday['label'] as String;
              final isSelected = selectedWeekdays.value.contains(day);

              return GestureDetector(
                onTap: () {
                  final newSelection = Set<int>.from(selectedWeekdays.value);
                  if (isSelected) {
                    newSelection.remove(day);
                  } else {
                    newSelection.add(day);
                  }
                  selectedWeekdays.value = newSelection;
                },
                child: Container(
                  width: 40,
                  height: 40,
                  decoration: BoxDecoration(
                    color: isSelected 
                        ? const Color(0xFFFACC15) 
                        : const Color(0xFF374151),
                    borderRadius: BorderRadius.circular(20),
                    border: Border.all(
                      color: isSelected 
                          ? const Color(0xFFFACC15) 
                          : const Color(0xFF374151),
                    ),
                  ),
                  child: Center(
                    child: Text(
                      label,
                      style: TextStyle(
                        color: isSelected ? Colors.black : const Color(0xFF9CA3AF),
                        fontSize: 12,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                  ),
                ),
              );
            }).toList(),
          ),
          const SizedBox(height: 16),
          // Quick frequency buttons
          Row(
            children: [
              Expanded(
                child: _buildFrequencyButton(
                  'Daily',
                  [1, 2, 3, 4, 5, 6, 7],
                  selectedWeekdays,
                ),
              ),
              const SizedBox(width: 8),
              Expanded(
                child: _buildFrequencyButton(
                  'Weekdays',
                  [1, 2, 3, 4, 5],
                  selectedWeekdays,
                ),
              ),
              const SizedBox(width: 8),
              Expanded(
                child: _buildFrequencyButton(
                  'Weekends',
                  [6, 7],
                  selectedWeekdays,
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildFrequencyButton(String label, List<int> days, ValueNotifier<Set<int>> selectedWeekdays) {
    final isSelected = days.every((day) => selectedWeekdays.value.contains(day)) && 
                      selectedWeekdays.value.length == days.length;

    return GestureDetector(
      onTap: () {
        selectedWeekdays.value = Set.from(days);
      },
      child: Container(
        padding: const EdgeInsets.symmetric(vertical: 8, horizontal: 12),
        decoration: BoxDecoration(
          color: isSelected 
              ? const Color(0xFFFACC15).withOpacity(0.2)
              : const Color(0xFF374151).withOpacity(0.3),
          borderRadius: BorderRadius.circular(8),
          border: Border.all(
            color: isSelected 
                ? const Color(0xFFFACC15)
                : const Color(0xFF374151),
          ),
        ),
        child: Text(
          label,
          textAlign: TextAlign.center,
          style: TextStyle(
            color: isSelected ? const Color(0xFFFACC15) : const Color(0xFF9CA3AF),
            fontSize: 12,
            fontWeight: FontWeight.w500,
          ),
        ),
      ),
    );
  }

  Widget _buildTitleSection(TextEditingController titleController) {
    return Container(
      padding: const EdgeInsets.all(20),
      decoration: BoxDecoration(
        color: const Color(0xFF1F2937),
        borderRadius: BorderRadius.circular(16),
        border: Border.all(color: const Color(0xFF374151)),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          const Text(
            'Reminder Title',
            style: TextStyle(
              color: Colors.white,
              fontSize: 18,
              fontWeight: FontWeight.bold,
            ),
          ),
          const SizedBox(height: 16),
          TextField(
            controller: titleController,
            style: const TextStyle(color: Colors.white),
            decoration: InputDecoration(
              hintText: 'e.g., Morning Workout',
              hintStyle: const TextStyle(color: Color(0xFF9CA3AF)),
              filled: true,
              fillColor: const Color(0xFF374151).withOpacity(0.3),
              border: OutlineInputBorder(
                borderRadius: BorderRadius.circular(12),
                borderSide: const BorderSide(color: Color(0xFF374151)),
              ),
              enabledBorder: OutlineInputBorder(
                borderRadius: BorderRadius.circular(12),
                borderSide: const BorderSide(color: Color(0xFF374151)),
              ),
              focusedBorder: OutlineInputBorder(
                borderRadius: BorderRadius.circular(12),
                borderSide: const BorderSide(color: Color(0xFFFACC15)),
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildMessageSection(TextEditingController messageController) {
    return Container(
      padding: const EdgeInsets.all(20),
      decoration: BoxDecoration(
        color: const Color(0xFF1F2937),
        borderRadius: BorderRadius.circular(16),
        border: Border.all(color: const Color(0xFF374151)),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          const Text(
            'Notification Message',
            style: TextStyle(
              color: Colors.white,
              fontSize: 18,
              fontWeight: FontWeight.bold,
            ),
          ),
          const SizedBox(height: 16),
          TextField(
            controller: messageController,
            style: const TextStyle(color: Colors.white),
            maxLines: 3,
            decoration: InputDecoration(
              hintText: 'e.g., Time for your workout session! 💪',
              hintStyle: const TextStyle(color: Color(0xFF9CA3AF)),
              filled: true,
              fillColor: const Color(0xFF374151).withOpacity(0.3),
              border: OutlineInputBorder(
                borderRadius: BorderRadius.circular(12),
                borderSide: const BorderSide(color: Color(0xFF374151)),
              ),
              enabledBorder: OutlineInputBorder(
                borderRadius: BorderRadius.circular(12),
                borderSide: const BorderSide(color: Color(0xFF374151)),
              ),
              focusedBorder: OutlineInputBorder(
                borderRadius: BorderRadius.circular(12),
                borderSide: const BorderSide(color: Color(0xFFFACC15)),
              ),
            ),
          ),
          const SizedBox(height: 12),
          Wrap(
            spacing: 8,
            runSpacing: 8,
            children: reminder_domain.ReminderPresets.motivationalMessages.take(3).map((message) {
              return GestureDetector(
                onTap: () => messageController.text = message,
                child: Container(
                  padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
                  decoration: BoxDecoration(
                    color: const Color(0xFF374151).withOpacity(0.3),
                    borderRadius: BorderRadius.circular(16),
                    border: Border.all(color: const Color(0xFF374151)),
                  ),
                  child: Text(
                    message.length > 30 ? '${message.substring(0, 30)}...' : message,
                    style: const TextStyle(
                      color: Color(0xFF9CA3AF),
                      fontSize: 12,
                    ),
                  ),
                ),
              );
            }).toList(),
          ),
        ],
      ),
    );
  }

  Widget _buildPresetsSection(ValueNotifier<TimeOfDay> selectedTime, ValueNotifier<Set<int>> selectedWeekdays) {
    return Container(
      padding: const EdgeInsets.all(20),
      decoration: BoxDecoration(
        color: const Color(0xFF1F2937),
        borderRadius: BorderRadius.circular(16),
        border: Border.all(color: const Color(0xFF374151)),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          const Text(
            'Quick Presets',
            style: TextStyle(
              color: Colors.white,
              fontSize: 18,
              fontWeight: FontWeight.bold,
            ),
          ),
          const SizedBox(height: 16),
          Column(
            children: [
              Row(
                children: [
                  Expanded(
                    child: _buildPresetButton(
                      'Early Bird',
                      '6:00 AM • Weekdays',
                      Icons.wb_sunny,
                      () {
                        selectedTime.value = const TimeOfDay(hour: 6, minute: 0);
                        selectedWeekdays.value = {1, 2, 3, 4, 5};
                      },
                    ),
                  ),
                  const SizedBox(width: 12),
                  Expanded(
                    child: _buildPresetButton(
                      'After Work',
                      '6:00 PM • Weekdays',
                      Icons.work_off,
                      () {
                        selectedTime.value = const TimeOfDay(hour: 18, minute: 0);
                        selectedWeekdays.value = {1, 2, 3, 4, 5};
                      },
                    ),
                  ),
                ],
              ),
              const SizedBox(height: 12),
              Row(
                children: [
                  Expanded(
                    child: _buildPresetButton(
                      'Weekend Warrior',
                      '9:00 AM • Weekends',
                      Icons.weekend,
                      () {
                        selectedTime.value = const TimeOfDay(hour: 9, minute: 0);
                        selectedWeekdays.value = {6, 7};
                      },
                    ),
                  ),
                  const SizedBox(width: 12),
                  Expanded(
                    child: _buildPresetButton(
                      'Daily Grind',
                      '7:00 AM • Every day',
                      Icons.fitness_center,
                      () {
                        selectedTime.value = const TimeOfDay(hour: 7, minute: 0);
                        selectedWeekdays.value = {1, 2, 3, 4, 5, 6, 7};
                      },
                    ),
                  ),
                ],
              ),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildPresetButton(String title, String subtitle, IconData icon, VoidCallback onTap) {
    return GestureDetector(
      onTap: onTap,
      child: Container(
        padding: const EdgeInsets.all(16),
        decoration: BoxDecoration(
          color: const Color(0xFF374151).withOpacity(0.3),
          borderRadius: BorderRadius.circular(12),
          border: Border.all(color: const Color(0xFF374151)),
        ),
        child: Column(
          children: [
            Icon(icon, color: const Color(0xFFFACC15), size: 24),
            const SizedBox(height: 8),
            Text(
              title,
              style: const TextStyle(
                color: Colors.white,
                fontSize: 14,
                fontWeight: FontWeight.w600,
              ),
              textAlign: TextAlign.center,
            ),
            const SizedBox(height: 4),
            Text(
              subtitle,
              style: const TextStyle(
                color: Color(0xFF9CA3AF),
                fontSize: 11,
              ),
              textAlign: TextAlign.center,
            ),
          ],
        ),
      ),
    );
  }

  Future<void> _selectTime(BuildContext context, ValueNotifier<TimeOfDay> selectedTime) async {
    final TimeOfDay? picked = await showTimePicker(
      context: context,
      initialTime: selectedTime.value,
      builder: (context, child) {
        return Theme(
          data: Theme.of(context).copyWith(
            colorScheme: const ColorScheme.dark(
              primary: Color(0xFFFACC15),
              onPrimary: Colors.black,
              surface: Color(0xFF1F2937),
              onSurface: Colors.white,
            ),
          ),
          child: child!,
        );
      },
    );

    if (picked != null) {
      selectedTime.value = picked;
    }
  }

  Future<void> _saveReminder(
    BuildContext context,
    WidgetRef ref,
    TimeOfDay time,
    List<int> weekdays,
    String title,
    String message,
    ValueNotifier<bool> isLoading,
  ) async {
    if (weekdays.isEmpty) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(
          content: Text('Please select at least one day'),
          backgroundColor: Color(0xFFEF4444),
        ),
      );
      return;
    }

    if (title.trim().isEmpty) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(
          content: Text('Please enter a title'),
          backgroundColor: Color(0xFFEF4444),
        ),
      );
      return;
    }

    try {
      isLoading.value = true;

      AppLogger.userAction('💾 Saving workout reminder: $title at ${time.format(context)}', tag: 'CREATE_REMINDER');

      final reminderTime = reminder_domain.ReminderTime(hour: time.hour, minute: time.minute);

      if (editingReminder != null) {
        // Update existing reminder
        final updatedReminder = editingReminder!.copyWith(
          reminderTime: reminderTime,
          weekdays: weekdays,
          title: title.trim(),
          message: message.trim(),
          updatedAt: DateTime.now(),
        );
        await ref.read(workoutReminderProvider.notifier).updateReminder(updatedReminder);
      } else {
        // Create new reminder
        await ref.read(workoutReminderProvider.notifier).createReminder(
          reminderTime: reminderTime,
          weekdays: weekdays,
          title: title.trim(),
          message: message.trim(),
        );
      }

      if (context.mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text(editingReminder != null ? 'Reminder updated!' : 'Reminder created!'),
            backgroundColor: const Color(0xFF10B981),
          ),
        );
        Navigator.of(context).pop();
      }
    } catch (e) {
      AppLogger.error('Failed to save reminder', tag: 'CREATE_REMINDER', error: e);
      if (context.mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Failed to save reminder: $e'),
            backgroundColor: const Color(0xFFEF4444),
          ),
        );
      }
    } finally {
      isLoading.value = false;
    }
  }
}
