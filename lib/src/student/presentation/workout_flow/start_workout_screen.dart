import 'dart:async';
import 'package:flutter/material.dart';
import 'package:flutter_hooks/flutter_hooks.dart';
import 'package:hooks_riverpod/hooks_riverpod.dart';
import '../../domain/student_workout_plan.dart';
import '../../domain/workout_session.dart';
import '../../application/workout_session_provider.dart';
import '../../../shared/utils/app_logger.dart';
import 'workout_complete_screen.dart';

/// Interactive workout screen where students perform exercises step by step
class StartWorkoutScreen extends HookConsumerWidget {
  final StudentWorkoutPlan workoutPlan;
  final WorkoutPlanDay selectedPlan;

  const StartWorkoutScreen({
    super.key,
    required this.workoutPlan,
    required this.selectedPlan,
  });

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final currentExerciseIndex = useState(0);
    final currentSetIndex = useState(0);
    final isRestTimerActive = useState(false);
    final restTimeRemaining = useState(0);
    final completedSets = useState<List<bool>>([]);
    final sessionState = ref.watch(workoutSessionProvider);

    // Initialize workout session on first build
    useEffect(() {
      WidgetsBinding.instance.addPostFrameCallback((_) {
        if (sessionState.currentSession == null) {
          ref.read(workoutSessionProvider.notifier).startWorkoutSession(workoutPlan, selectedPlan);
        }
      });
      return null;
    }, []);

    // Initialize completed sets tracking
    useEffect(() {
      if (selectedPlan.exercises.isNotEmpty && completedSets.value.isEmpty) {
        final currentExercise = selectedPlan.exercises[currentExerciseIndex.value];
        completedSets.value = List.filled(currentExercise.sets ?? 0, false);
      }
      return null;
    }, [currentExerciseIndex.value]);

    // Rest timer countdown
    useEffect(() {
      Timer? timer;
      if (isRestTimerActive.value && restTimeRemaining.value > 0) {
        timer = Timer.periodic(const Duration(seconds: 1), (t) {
          if (restTimeRemaining.value > 0) {
            restTimeRemaining.value--;
          } else {
            isRestTimerActive.value = false;
            t.cancel();
          }
        });
      }
      return () => timer?.cancel();
    }, [isRestTimerActive.value]);

    if (sessionState.isLoading) {
      return const Scaffold(
        backgroundColor: Color(0xFF111827),
        body: Center(
          child: CircularProgressIndicator(color: Color(0xFFFACC15)),
        ),
      );
    }

    if (selectedPlan.exercises.isEmpty) {
      return Scaffold(
        backgroundColor: const Color(0xFF111827),
        appBar: AppBar(
          backgroundColor: const Color(0xFF1F2937),
          title: const Text('Workout', style: TextStyle(color: Colors.white)),
          leading: IconButton(
            icon: const Icon(Icons.arrow_back, color: Colors.white),
            onPressed: () => Navigator.of(context).pop(),
          ),
        ),
        body: const Center(
          child: Text(
            'No exercises found in this workout plan',
            style: TextStyle(color: Colors.white, fontSize: 16),
          ),
        ),
      );
    }

    final currentExercise = selectedPlan.exercises[currentExerciseIndex.value];
    final totalExercises = selectedPlan.exercises.length;
    final progress = (currentExerciseIndex.value + 1) / totalExercises;

    return Scaffold(
      backgroundColor: const Color(0xFF111827),
      appBar: AppBar(
        backgroundColor: const Color(0xFF1F2937),
        title: Text(
          selectedPlan.name,
          style: const TextStyle(color: Colors.white, fontSize: 18, fontWeight: FontWeight.bold),
        ),
        leading: IconButton(
          icon: const Icon(Icons.close, color: Colors.white),
          onPressed: () => _showExitConfirmation(context, ref),
        ),
        bottom: PreferredSize(
          preferredSize: const Size.fromHeight(8),
          child: Container(
            margin: const EdgeInsets.symmetric(horizontal: 16),
            child: LinearProgressIndicator(
              value: progress,
              backgroundColor: const Color(0xFF374151),
              valueColor: const AlwaysStoppedAnimation<Color>(Color(0xFFFACC15)),
            ),
          ),
        ),
      ),
      body: Column(
        children: [
          // Progress indicator
          Container(
            padding: const EdgeInsets.all(16),
            child: Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Text(
                  'Exercise ${currentExerciseIndex.value + 1} of $totalExercises',
                  style: const TextStyle(
                    color: Color(0xFF9CA3AF),
                    fontSize: 14,
                    fontWeight: FontWeight.w500,
                  ),
                ),
                Text(
                  '${(progress * 100).round()}% Complete',
                  style: const TextStyle(
                    color: Color(0xFFFACC15),
                    fontSize: 14,
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ],
            ),
          ),

          // Exercise content
          Expanded(
            child: SingleChildScrollView(
              padding: const EdgeInsets.all(16),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  // Exercise image placeholder
                  Container(
                    width: double.infinity,
                    height: 200,
                    decoration: BoxDecoration(
                      color: const Color(0xFF1F2937),
                      borderRadius: BorderRadius.circular(12),
                      border: Border.all(color: const Color(0xFF374151)),
                    ),
                    child: currentExercise.imageUrl != null
                        ? ClipRRect(
                            borderRadius: BorderRadius.circular(12),
                            child: Image.network(
                              currentExercise.imageUrl!,
                              fit: BoxFit.cover,
                              errorBuilder: (context, error, stackTrace) => _buildExercisePlaceholder(),
                            ),
                          )
                        : _buildExercisePlaceholder(),
                  ),

                  const SizedBox(height: 24),

                  // Exercise name and details
                  Text(
                    currentExercise.name,
                    style: const TextStyle(
                      color: Colors.white,
                      fontSize: 24,
                      fontWeight: FontWeight.bold,
                    ),
                  ),

                  const SizedBox(height: 8),

                  if (currentExercise.description != null)
                    Text(
                      currentExercise.description!,
                      style: const TextStyle(
                        color: Color(0xFF9CA3AF),
                        fontSize: 16,
                        height: 1.5,
                      ),
                    ),

                  const SizedBox(height: 24),

                  // Exercise stats
                  Row(
                    children: [
                      _buildStatCard('Sets', '${currentExercise.sets}', Icons.repeat),
                      const SizedBox(width: 12),
                      _buildStatCard('Reps', '${currentExercise.reps}', Icons.fitness_center),
                      const SizedBox(width: 12),
                      if (currentExercise.weight != null)
                        _buildStatCard('Weight', '${currentExercise.weight}kg', Icons.monitor_weight),
                    ],
                  ),

                  const SizedBox(height: 24),

                  // Sets tracking
                  _buildSetsSection(currentExercise, completedSets, currentSetIndex),

                  const SizedBox(height: 24),

                  // Rest timer
                  if (isRestTimerActive.value)
                    _buildRestTimer(restTimeRemaining.value),

                  const SizedBox(height: 24),

                  // Exercise notes
                  if (currentExercise.notes != null)
                    Container(
                      width: double.infinity,
                      padding: const EdgeInsets.all(16),
                      decoration: BoxDecoration(
                        color: const Color(0xFF1F2937),
                        borderRadius: BorderRadius.circular(12),
                        border: Border.all(color: const Color(0xFF374151)),
                      ),
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          const Row(
                            children: [
                              Icon(Icons.lightbulb_outline, color: Color(0xFFFACC15), size: 20),
                              SizedBox(width: 8),
                              Text(
                                'Coach Notes',
                                style: TextStyle(
                                  color: Color(0xFFFACC15),
                                  fontSize: 14,
                                  fontWeight: FontWeight.bold,
                                ),
                              ),
                            ],
                          ),
                          const SizedBox(height: 8),
                          Text(
                            currentExercise.notes!,
                            style: const TextStyle(
                              color: Colors.white,
                              fontSize: 14,
                              height: 1.5,
                            ),
                          ),
                        ],
                      ),
                    ),
                ],
              ),
            ),
          ),

          // Bottom action buttons
          Container(
            padding: const EdgeInsets.all(16),
            decoration: const BoxDecoration(
              color: Color(0xFF1F2937),
              border: Border(top: BorderSide(color: Color(0xFF374151))),
            ),
            child: Column(
              children: [
                // Mark Set Done button
                if (!isRestTimerActive.value)
                  SizedBox(
                    width: double.infinity,
                    height: 56,
                    child: ElevatedButton(
                      onPressed: () => _markSetDone(
                        context,
                        ref,
                        currentExercise,
                        currentSetIndex,
                        completedSets,
                        isRestTimerActive,
                        restTimeRemaining,
                      ),
                      style: ElevatedButton.styleFrom(
                        backgroundColor: const Color(0xFFFACC15),
                        foregroundColor: Colors.black,
                        shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
                      ),
                      child: Text(
                        currentSetIndex.value < (currentExercise.sets ?? 0) - 1
                            ? 'Mark Set ${currentSetIndex.value + 1} Done'
                            : 'Complete Exercise',
                        style: const TextStyle(fontSize: 16, fontWeight: FontWeight.bold),
                      ),
                    ),
                  ),

                const SizedBox(height: 12),

                // Next Exercise button
                SizedBox(
                  width: double.infinity,
                  height: 48,
                  child: OutlinedButton(
                    onPressed: () => _nextExercise(
                      context,
                      ref,
                      currentExerciseIndex,
                      currentSetIndex,
                      completedSets,
                      totalExercises,
                    ),
                    style: OutlinedButton.styleFrom(
                      foregroundColor: Colors.white,
                      side: const BorderSide(color: Color(0xFF374151)),
                      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
                    ),
                    child: Text(
                      currentExerciseIndex.value < totalExercises - 1 ? 'Skip Exercise' : 'Finish Workout',
                      style: const TextStyle(fontSize: 14),
                    ),
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildExercisePlaceholder() {
    return const Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(Icons.fitness_center, color: Color(0xFF9CA3AF), size: 48),
          SizedBox(height: 8),
          Text(
            'Exercise Image',
            style: TextStyle(color: Color(0xFF9CA3AF), fontSize: 14),
          ),
        ],
      ),
    );
  }

  Widget _buildStatCard(String label, String value, IconData icon) {
    return Expanded(
      child: Container(
        padding: const EdgeInsets.all(16),
        decoration: BoxDecoration(
          color: const Color(0xFF1F2937),
          borderRadius: BorderRadius.circular(12),
          border: Border.all(color: const Color(0xFF374151)),
        ),
        child: Column(
          children: [
            Icon(icon, color: const Color(0xFFFACC15), size: 24),
            const SizedBox(height: 8),
            Text(
              value,
              style: const TextStyle(
                color: Colors.white,
                fontSize: 18,
                fontWeight: FontWeight.bold,
              ),
            ),
            Text(
              label,
              style: const TextStyle(
                color: Color(0xFF9CA3AF),
                fontSize: 12,
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildSetsSection(
    WorkoutExercise exercise,
    ValueNotifier<List<bool>> completedSets,
    ValueNotifier<int> currentSetIndex,
  ) {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: const Color(0xFF1F2937),
        borderRadius: BorderRadius.circular(12),
        border: Border.all(color: const Color(0xFF374151)),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          const Text(
            'Sets Progress',
            style: TextStyle(
              color: Colors.white,
              fontSize: 16,
              fontWeight: FontWeight.bold,
            ),
          ),
          const SizedBox(height: 16),
          Row(
            children: List.generate(exercise.sets ?? 0, (index) {
              final isCompleted = index < completedSets.value.length && completedSets.value[index];
              final isCurrent = index == currentSetIndex.value;

              return Expanded(
                child: Container(
                  margin: EdgeInsets.only(right: index < (exercise.sets ?? 0) - 1 ? 8 : 0),
                  height: 48,
                  decoration: BoxDecoration(
                    color: isCompleted
                        ? const Color(0xFFFACC15)
                        : isCurrent
                            ? const Color(0xFFFACC15).withOpacity(0.2)
                            : const Color(0xFF374151),
                    borderRadius: BorderRadius.circular(8),
                    border: isCurrent
                        ? Border.all(color: const Color(0xFFFACC15), width: 2)
                        : null,
                  ),
                  child: Center(
                    child: Text(
                      '${index + 1}',
                      style: TextStyle(
                        color: isCompleted ? Colors.black : Colors.white,
                        fontSize: 16,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                  ),
                ),
              );
            }),
          ),
          const SizedBox(height: 12),
          Text(
            'Set ${currentSetIndex.value + 1} of ${exercise.sets ?? 0} • ${exercise.reps ?? 0} reps',
            style: const TextStyle(
              color: Color(0xFF9CA3AF),
              fontSize: 14,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildRestTimer(int remainingSeconds) {
    final minutes = remainingSeconds ~/ 60;
    final seconds = remainingSeconds % 60;

    return Container(
      width: double.infinity,
      padding: const EdgeInsets.all(24),
      decoration: BoxDecoration(
        color: const Color(0xFF1F2937),
        borderRadius: BorderRadius.circular(12),
        border: Border.all(color: const Color(0xFFFACC15)),
      ),
      child: Column(
        children: [
          const Icon(
            Icons.timer,
            color: Color(0xFFFACC15),
            size: 32,
          ),
          const SizedBox(height: 8),
          const Text(
            'Rest Time',
            style: TextStyle(
              color: Color(0xFFFACC15),
              fontSize: 16,
              fontWeight: FontWeight.bold,
            ),
          ),
          const SizedBox(height: 8),
          Text(
            '${minutes.toString().padLeft(2, '0')}:${seconds.toString().padLeft(2, '0')}',
            style: const TextStyle(
              color: Colors.white,
              fontSize: 32,
              fontWeight: FontWeight.bold,
            ),
          ),
        ],
      ),
    );
  }

  void _markSetDone(
    BuildContext context,
    WidgetRef ref,
    WorkoutExercise exercise,
    ValueNotifier<int> currentSetIndex,
    ValueNotifier<List<bool>> completedSets,
    ValueNotifier<bool> isRestTimerActive,
    ValueNotifier<int> restTimeRemaining,
  ) {
    AppLogger.userAction('Marking set ${currentSetIndex.value + 1} as done for ${exercise.name}', tag: 'WORKOUT_FLOW');

    // Mark current set as completed
    final updatedSets = List<bool>.from(completedSets.value);
    if (currentSetIndex.value < updatedSets.length) {
      updatedSets[currentSetIndex.value] = true;
      completedSets.value = updatedSets;
    }

    // Move to next set or complete exercise
    if (currentSetIndex.value < (exercise.sets ?? 0) - 1) {
      currentSetIndex.value++;

      // Start rest timer if there's a rest time
      if (exercise.restTime != null && exercise.restTime! > 0) {
        restTimeRemaining.value = exercise.restTime!;
        isRestTimerActive.value = true;
      }
    } else {
      // Exercise completed, update session
      final exerciseResult = ExerciseSessionResult(
        exerciseId: exercise.id,
        exerciseName: exercise.name,
        setResults: List.generate(exercise.sets ?? 0, (index) => SetResult(
          setNumber: index + 1,
          reps: exercise.reps ?? 0,
          weight: exercise.weight,
          isCompleted: true,
          completedAt: DateTime.now(),
        )),
        isCompleted: true,
      );

      ref.read(workoutSessionProvider.notifier).updateExerciseResult(exerciseResult);
    }
  }

  void _nextExercise(
    BuildContext context,
    WidgetRef ref,
    ValueNotifier<int> currentExerciseIndex,
    ValueNotifier<int> currentSetIndex,
    ValueNotifier<List<bool>> completedSets,
    int totalExercises,
  ) {
    if (currentExerciseIndex.value < totalExercises - 1) {
      AppLogger.userAction('Moving to next exercise', tag: 'WORKOUT_FLOW');
      currentExerciseIndex.value++;
      currentSetIndex.value = 0;
      completedSets.value = [];
    } else {
      // Finish workout
      _finishWorkout(context, ref);
    }
  }

  void _finishWorkout(BuildContext context, WidgetRef ref) {
    AppLogger.userAction('Finishing workout', tag: 'WORKOUT_FLOW');

    Navigator.of(context).pushReplacement(
      MaterialPageRoute(
        builder: (context) => const WorkoutCompleteScreen(),
      ),
    );
  }

  void _showExitConfirmation(BuildContext context, WidgetRef ref) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        backgroundColor: const Color(0xFF1F2937),
        title: const Text(
          'Exit Workout?',
          style: TextStyle(color: Colors.white),
        ),
        content: const Text(
          'Are you sure you want to exit? Your progress will be lost.',
          style: TextStyle(color: Color(0xFF9CA3AF)),
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text(
              'Cancel',
              style: TextStyle(color: Color(0xFF9CA3AF)),
            ),
          ),
          TextButton(
            onPressed: () {
              ref.read(workoutSessionProvider.notifier).cancelWorkoutSession();
              Navigator.of(context).pop(); // Close dialog
              Navigator.of(context).pop(); // Exit workout
            },
            child: const Text(
              'Exit',
              style: TextStyle(color: Color(0xFFEF4444)),
            ),
          ),
        ],
      ),
    );
  }
}
