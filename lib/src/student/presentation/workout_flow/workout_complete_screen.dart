import 'package:flutter/material.dart';
import 'package:hooks_riverpod/hooks_riverpod.dart';
import 'package:confetti/confetti.dart';
import 'package:flutter_hooks/flutter_hooks.dart';
import '../../application/workout_session_provider.dart';
import '../../../shared/utils/app_logger.dart';

/// Congratulatory screen shown after workout completion
class WorkoutCompleteScreen extends HookConsumerWidget {
  const WorkoutCompleteScreen({super.key});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final confettiController = useRef(ConfettiController(duration: const Duration(seconds: 3)));
    final sessionState = ref.watch(workoutSessionProvider);

    // Start confetti animation on screen load
    useEffect(() {
      WidgetsBinding.instance.addPostFrameCallback((_) {
        confettiController.value.play();
      });
      return () => confettiController.value.dispose();
    }, []);

    // Complete the workout session
    useEffect(() {
      WidgetsBinding.instance.addPostFrameCallback((_) {
        if (sessionState.currentSession != null) {
          ref.read(workoutSessionProvider.notifier).completeWorkoutSession(
            notes: 'Workout completed successfully!',
          );
        }
      });
      return null;
    }, []);

    final session = sessionState.currentSession;

    return Scaffold(
      backgroundColor: const Color(0xFF111827),
      body: Stack(
        children: [
          // Confetti animation
          Align(
            alignment: Alignment.topCenter,
            child: ConfettiWidget(
              confettiController: confettiController.value,
              blastDirection: 1.5708, // radians for downward
              particleDrag: 0.05,
              emissionFrequency: 0.05,
              numberOfParticles: 50,
              gravity: 0.05,
              shouldLoop: false,
              colors: const [
                Color(0xFFFACC15),
                Color(0xFF10B981),
                Color(0xFF3B82F6),
                Color(0xFFEF4444),
                Color(0xFF8B5CF6),
              ],
            ),
          ),

          // Main content
          SafeArea(
            child: Padding(
              padding: const EdgeInsets.all(24),
              child: Column(
                children: [
                  const Spacer(),

                  // Success icon
                  Container(
                    width: 120,
                    height: 120,
                    decoration: BoxDecoration(
                      color: const Color(0xFFFACC15),
                      shape: BoxShape.circle,
                      boxShadow: [
                        BoxShadow(
                          color: const Color(0xFFFACC15).withOpacity(0.3),
                          blurRadius: 20,
                          spreadRadius: 5,
                        ),
                      ],
                    ),
                    child: const Icon(
                      Icons.check,
                      color: Colors.black,
                      size: 60,
                    ),
                  ),

                  const SizedBox(height: 32),

                  // Congratulations message
                  const Text(
                    'Great Job! 🎉',
                    style: TextStyle(
                      color: Colors.white,
                      fontSize: 32,
                      fontWeight: FontWeight.bold,
                    ),
                    textAlign: TextAlign.center,
                  ),

                  const SizedBox(height: 16),

                  const Text(
                    'Workout Completed Successfully!',
                    style: TextStyle(
                      color: Color(0xFF9CA3AF),
                      fontSize: 18,
                    ),
                    textAlign: TextAlign.center,
                  ),

                  const SizedBox(height: 40),

                  // Workout summary
                  if (session != null) _buildWorkoutSummary(session),

                  const Spacer(),

                  // Action buttons
                  Column(
                    children: [
                      // See My Progress button
                      SizedBox(
                        width: double.infinity,
                        height: 56,
                        child: ElevatedButton(
                          onPressed: () => _navigateToProgress(context),
                          style: ElevatedButton.styleFrom(
                            backgroundColor: const Color(0xFFFACC15),
                            foregroundColor: Colors.black,
                            shape: RoundedRectangleBorder(
                              borderRadius: BorderRadius.circular(12),
                            ),
                          ),
                          child: const Text(
                            'See My Progress',
                            style: TextStyle(
                              fontSize: 16,
                              fontWeight: FontWeight.bold,
                            ),
                          ),
                        ),
                      ),

                      const SizedBox(height: 16),

                      // Return to Home button
                      SizedBox(
                        width: double.infinity,
                        height: 48,
                        child: OutlinedButton(
                          onPressed: () => _returnToHome(context),
                          style: OutlinedButton.styleFrom(
                            foregroundColor: Colors.white,
                            side: const BorderSide(color: Color(0xFF374151)),
                            shape: RoundedRectangleBorder(
                              borderRadius: BorderRadius.circular(12),
                            ),
                          ),
                          child: const Text(
                            'Return to Home',
                            style: TextStyle(fontSize: 16),
                          ),
                        ),
                      ),
                    ],
                  ),

                  const SizedBox(height: 24),
                ],
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildWorkoutSummary(dynamic session) {
    // Mock summary data since session might not have all details yet
    final mockData = {
      'planName': 'Push Day Workout',
      'exercisesCompleted': 5,
      'totalSets': 15,
      'totalReps': 120,
      'duration': '45 min',
    };

    return Container(
      width: double.infinity,
      padding: const EdgeInsets.all(24),
      decoration: BoxDecoration(
        color: const Color(0xFF1F2937),
        borderRadius: BorderRadius.circular(16),
        border: Border.all(color: const Color(0xFF374151)),
      ),
      child: Column(
        children: [
          Text(
            mockData['planName'] as String,
            style: const TextStyle(
              color: Colors.white,
              fontSize: 20,
              fontWeight: FontWeight.bold,
            ),
            textAlign: TextAlign.center,
          ),

          const SizedBox(height: 24),

          // Summary stats
          Row(
            children: [
              _buildSummaryItem(
                'Exercises',
                '${mockData['exercisesCompleted']}',
                Icons.fitness_center,
              ),
              _buildSummaryItem(
                'Sets',
                '${mockData['totalSets']}',
                Icons.repeat,
              ),
            ],
          ),

          const SizedBox(height: 16),

          Row(
            children: [
              _buildSummaryItem(
                'Reps',
                '${mockData['totalReps']}',
                Icons.trending_up,
              ),
              _buildSummaryItem(
                'Duration',
                mockData['duration'] as String,
                Icons.timer,
              ),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildSummaryItem(String label, String value, IconData icon) {
    return Expanded(
      child: Column(
        children: [
          Icon(
            icon,
            color: const Color(0xFFFACC15),
            size: 24,
          ),
          const SizedBox(height: 8),
          Text(
            value,
            style: const TextStyle(
              color: Colors.white,
              fontSize: 20,
              fontWeight: FontWeight.bold,
            ),
          ),
          Text(
            label,
            style: const TextStyle(
              color: Color(0xFF9CA3AF),
              fontSize: 12,
            ),
          ),
        ],
      ),
    );
  }

  void _navigateToProgress(BuildContext context) {
    AppLogger.userAction('Navigating to progress dashboard', tag: 'WORKOUT_COMPLETE');
    
    // Navigate to progress tab
    Navigator.of(context).pushNamedAndRemoveUntil(
      '/dashboard',
      (route) => false,
      arguments: {'initialTab': 2}, // Progress tab index
    );
  }

  void _returnToHome(BuildContext context) {
    AppLogger.userAction('Returning to home', tag: 'WORKOUT_COMPLETE');
    
    // Return to dashboard
    Navigator.of(context).pushNamedAndRemoveUntil(
      '/dashboard',
      (route) => false,
    );
  }
}
