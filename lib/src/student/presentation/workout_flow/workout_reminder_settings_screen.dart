import 'package:flutter/material.dart';
import 'package:hooks_riverpod/hooks_riverpod.dart';
import '../../application/workout_reminder_provider.dart';
import '../../domain/workout_reminder.dart' as reminder_domain;
import '../../../shared/utils/app_logger.dart';
import 'create_reminder_screen.dart';

/// Workout reminder settings and management screen
class WorkoutReminderSettingsScreen extends ConsumerWidget {
  const WorkoutReminderSettingsScreen({super.key});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    AppLogger.info('🔔 Building WorkoutReminderSettingsScreen', tag: 'WORKOUT_REMINDER');

    final reminderState = ref.watch(workoutReminderProvider);
    final reminderSettingsAsync = ref.watch(reminderSettingsProvider);

    AppLogger.info('📊 Reminder state: ${reminderState.reminders.length} reminders, loading: ${reminderState.isLoading}', tag: 'WORKOUT_REMINDER');

    return Scaffold(
      backgroundColor: const Color(0xFF111827),
      appBar: AppBar(
        backgroundColor: const Color(0xFF1F2937),
        title: const Text(
          'Workout Reminders',
          style: TextStyle(color: Colors.white, fontWeight: FontWeight.bold),
        ),
        leading: IconButton(
          icon: const Icon(Icons.arrow_back, color: Colors.white),
          onPressed: () => Navigator.of(context).pop(),
        ),
        actions: [
          IconButton(
            icon: const Icon(Icons.add, color: Color(0xFFFACC15)),
            onPressed: () => _navigateToCreateReminder(context),
          ),
        ],
      ),
      body: RefreshIndicator(
        onRefresh: () async {
          ref.invalidate(workoutReminderProvider);
          ref.invalidate(reminderSettingsProvider);
        },
        color: const Color(0xFFFACC15),
        child: SingleChildScrollView(
          physics: const AlwaysScrollableScrollPhysics(),
          padding: const EdgeInsets.all(16),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // Notification permissions section
              reminderSettingsAsync.when(
                data: (settings) => _buildPermissionsSection(context, ref, settings),
                loading: () => _buildLoadingCard(),
                error: (error, stack) => _buildErrorCard(error.toString()),
              ),

              const SizedBox(height: 24),

              // Active reminders section
              _buildRemindersSection(context, ref, reminderState),

              const SizedBox(height: 24),

              // Quick setup section
              _buildQuickSetupSection(context),

              const SizedBox(height: 24),

              // Tips section
              _buildTipsSection(),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildPermissionsSection(BuildContext context, WidgetRef ref, reminder_domain.ReminderSettings settings) {
    return Container(
      padding: const EdgeInsets.all(20),
      decoration: BoxDecoration(
        color: const Color(0xFF1F2937),
        borderRadius: BorderRadius.circular(16),
        border: Border.all(color: const Color(0xFF374151)),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Icon(
                settings.permissionStatus == reminder_domain.NotificationPermissionStatus.granted
                    ? Icons.notifications_active
                    : Icons.notifications_off,
                color: settings.permissionStatus == reminder_domain.NotificationPermissionStatus.granted
                    ? const Color(0xFFFACC15)
                    : const Color(0xFF9CA3AF),
                size: 24,
              ),
              const SizedBox(width: 12),
              const Text(
                'Notifications',
                style: TextStyle(
                  color: Colors.white,
                  fontSize: 18,
                  fontWeight: FontWeight.bold,
                ),
              ),
            ],
          ),

          const SizedBox(height: 16),

          if (settings.permissionStatus != reminder_domain.NotificationPermissionStatus.granted) ...[
            const Text(
              'Enable notifications to receive workout reminders',
              style: TextStyle(
                color: Color(0xFF9CA3AF),
                fontSize: 14,
              ),
            ),
            const SizedBox(height: 16),
            SizedBox(
              width: double.infinity,
              child: ElevatedButton.icon(
                onPressed: () => _requestPermissions(ref),
                style: ElevatedButton.styleFrom(
                  backgroundColor: const Color(0xFFFACC15),
                  foregroundColor: Colors.black,
                  shape: RoundedRectangleBorder(
                    borderRadius: BorderRadius.circular(12),
                  ),
                ),
                icon: const Icon(Icons.notifications, size: 20),
                label: const Text(
                  'Enable Notifications',
                  style: TextStyle(fontWeight: FontWeight.bold),
                ),
              ),
            ),
          ] else ...[
            Row(
              children: [
                const Icon(
                  Icons.check_circle,
                  color: Color(0xFF10B981),
                  size: 20,
                ),
                const SizedBox(width: 8),
                const Text(
                  'Notifications enabled',
                  style: TextStyle(
                    color: Color(0xFF10B981),
                    fontSize: 14,
                    fontWeight: FontWeight.w500,
                  ),
                ),
              ],
            ),
            const SizedBox(height: 16),
            Row(
              children: [
                Expanded(
                  child: _buildSettingToggle(
                    'Sound',
                    settings.soundEnabled,
                    Icons.volume_up,
                    (value) => _updateSoundSetting(ref, value),
                  ),
                ),
                const SizedBox(width: 16),
                Expanded(
                  child: _buildSettingToggle(
                    'Vibration',
                    settings.vibrationEnabled,
                    Icons.vibration,
                    (value) => _updateVibrationSetting(ref, value),
                  ),
                ),
              ],
            ),
          ],
        ],
      ),
    );
  }

  Widget _buildSettingToggle(String label, bool value, IconData icon, Function(bool) onChanged) {
    return Container(
      padding: const EdgeInsets.all(12),
      decoration: BoxDecoration(
        color: const Color(0xFF374151).withOpacity(0.3),
        borderRadius: BorderRadius.circular(8),
      ),
      child: Row(
        children: [
          Icon(icon, color: const Color(0xFF9CA3AF), size: 16),
          const SizedBox(width: 8),
          Expanded(
            child: Text(
              label,
              style: const TextStyle(
                color: Colors.white,
                fontSize: 12,
              ),
            ),
          ),
          Switch(
            value: value,
            onChanged: onChanged,
            activeColor: const Color(0xFFFACC15),
            inactiveThumbColor: const Color(0xFF9CA3AF),
            inactiveTrackColor: const Color(0xFF374151),
          ),
        ],
      ),
    );
  }

  Widget _buildRemindersSection(BuildContext context, WidgetRef ref, WorkoutReminderState state) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Row(
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children: [
            const Text(
              'Your Reminders',
              style: TextStyle(
                color: Colors.white,
                fontSize: 20,
                fontWeight: FontWeight.bold,
              ),
            ),
            Text(
              '${state.reminders.length} reminders',
              style: const TextStyle(
                color: Color(0xFF9CA3AF),
                fontSize: 14,
              ),
            ),
          ],
        ),

        const SizedBox(height: 16),

        if (state.isLoading)
          _buildLoadingCard()
        else if (state.error != null)
          _buildErrorCard(state.error!)
        else if (state.reminders.isEmpty)
          _buildEmptyRemindersState(context)
        else
          ...state.reminders.map((reminder) => _buildReminderCard(context, ref, reminder)),
      ],
    );
  }

  Widget _buildReminderCard(BuildContext context, WidgetRef ref, reminder_domain.WorkoutReminder reminder) {
    return Container(
      margin: const EdgeInsets.only(bottom: 12),
      decoration: BoxDecoration(
        color: const Color(0xFF1F2937),
        borderRadius: BorderRadius.circular(12),
        border: Border.all(
          color: reminder.isEnabled ? const Color(0xFFFACC15).withOpacity(0.3) : const Color(0xFF374151),
        ),
      ),
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Row(
          children: [
            // Time and status
            Container(
              width: 60,
              height: 60,
              decoration: BoxDecoration(
                color: reminder.isEnabled 
                    ? const Color(0xFFFACC15).withOpacity(0.2)
                    : const Color(0xFF374151),
                borderRadius: BorderRadius.circular(8),
              ),
              child: Column(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  Icon(
                    reminder.isEnabled ? Icons.alarm : Icons.alarm_off,
                    color: reminder.isEnabled 
                        ? const Color(0xFFFACC15)
                        : const Color(0xFF9CA3AF),
                    size: 20,
                  ),
                  const SizedBox(height: 2),
                  Text(
                    reminder.formattedTime.split(' ')[0], // Just the time part
                    style: TextStyle(
                      color: reminder.isEnabled ? Colors.white : const Color(0xFF9CA3AF),
                      fontSize: 10,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                ],
              ),
            ),

            const SizedBox(width: 16),

            // Reminder details
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    reminder.title,
                    style: TextStyle(
                      color: reminder.isEnabled ? Colors.white : const Color(0xFF9CA3AF),
                      fontSize: 16,
                      fontWeight: FontWeight.w600,
                    ),
                  ),
                  const SizedBox(height: 4),
                  Text(
                    '${reminder.formattedTime} • ${reminder.formattedWeekdays}',
                    style: const TextStyle(
                      color: Color(0xFF9CA3AF),
                      fontSize: 12,
                    ),
                  ),
                  if (reminder.message.isNotEmpty) ...[
                    const SizedBox(height: 4),
                    Text(
                      reminder.message,
                      style: const TextStyle(
                        color: Color(0xFF9CA3AF),
                        fontSize: 11,
                      ),
                      maxLines: 1,
                      overflow: TextOverflow.ellipsis,
                    ),
                  ],
                ],
              ),
            ),

            // Toggle switch
            Switch(
              value: reminder.isEnabled,
              onChanged: (value) => _toggleReminder(ref, reminder.id),
              activeColor: const Color(0xFFFACC15),
              inactiveThumbColor: const Color(0xFF9CA3AF),
              inactiveTrackColor: const Color(0xFF374151),
            ),

            // More options
            PopupMenuButton<String>(
              icon: const Icon(Icons.more_vert, color: Color(0xFF9CA3AF)),
              color: const Color(0xFF1F2937),
              onSelected: (value) => _handleReminderAction(context, ref, reminder, value),
              itemBuilder: (context) => [
                const PopupMenuItem(
                  value: 'edit',
                  child: Row(
                    children: [
                      Icon(Icons.edit, color: Colors.white, size: 16),
                      SizedBox(width: 8),
                      Text('Edit', style: TextStyle(color: Colors.white)),
                    ],
                  ),
                ),
                const PopupMenuItem(
                  value: 'delete',
                  child: Row(
                    children: [
                      Icon(Icons.delete, color: Color(0xFFEF4444), size: 16),
                      SizedBox(width: 8),
                      Text('Delete', style: TextStyle(color: Color(0xFFEF4444))),
                    ],
                  ),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildQuickSetupSection(BuildContext context) {
    return Container(
      padding: const EdgeInsets.all(20),
      decoration: BoxDecoration(
        color: const Color(0xFF1F2937),
        borderRadius: BorderRadius.circular(16),
        border: Border.all(color: const Color(0xFF374151)),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          const Text(
            'Quick Setup',
            style: TextStyle(
              color: Colors.white,
              fontSize: 18,
              fontWeight: FontWeight.bold,
            ),
          ),
          const SizedBox(height: 16),
          Row(
            children: [
              Expanded(
                child: _buildQuickSetupButton(
                  context,
                  'Morning\n7:00 AM',
                  Icons.wb_sunny,
                  () => _createQuickReminder(context, const reminder_domain.ReminderTime(hour: 7, minute: 0), [1, 2, 3, 4, 5]),
                ),
              ),
              const SizedBox(width: 12),
              Expanded(
                child: _buildQuickSetupButton(
                  context,
                  'Evening\n6:00 PM',
                  Icons.nights_stay,
                  () => _createQuickReminder(context, const reminder_domain.ReminderTime(hour: 18, minute: 0), [1, 2, 3, 4, 5]),
                ),
              ),
              const SizedBox(width: 12),
              Expanded(
                child: _buildQuickSetupButton(
                  context,
                  'Weekend\n9:00 AM',
                  Icons.weekend,
                  () => _createQuickReminder(context, const reminder_domain.ReminderTime(hour: 9, minute: 0), [6, 7]),
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildQuickSetupButton(BuildContext context, String label, IconData icon, VoidCallback onTap) {
    return GestureDetector(
      onTap: onTap,
      child: Container(
        padding: const EdgeInsets.all(16),
        decoration: BoxDecoration(
          color: const Color(0xFF374151).withOpacity(0.3),
          borderRadius: BorderRadius.circular(12),
          border: Border.all(color: const Color(0xFF374151)),
        ),
        child: Column(
          children: [
            Icon(icon, color: const Color(0xFFFACC15), size: 24),
            const SizedBox(height: 8),
            Text(
              label,
              textAlign: TextAlign.center,
              style: const TextStyle(
                color: Colors.white,
                fontSize: 12,
                fontWeight: FontWeight.w500,
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildTipsSection() {
    return Container(
      padding: const EdgeInsets.all(20),
      decoration: BoxDecoration(
        color: const Color(0xFF1F2937),
        borderRadius: BorderRadius.circular(16),
        border: Border.all(color: const Color(0xFF374151)),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          const Row(
            children: [
              Icon(Icons.lightbulb_outline, color: Color(0xFFFACC15), size: 20),
              SizedBox(width: 8),
              Text(
                'Tips for Success',
                style: TextStyle(
                  color: Color(0xFFFACC15),
                  fontSize: 16,
                  fontWeight: FontWeight.bold,
                ),
              ),
            ],
          ),
          const SizedBox(height: 16),
          _buildTipItem('Set reminders for times when you\'re most likely to be free'),
          _buildTipItem('Start with 3-4 reminders per week and build consistency'),
          _buildTipItem('Choose different times for variety in your routine'),
          _buildTipItem('Enable sound and vibration for better notification visibility'),
        ],
      ),
    );
  }

  Widget _buildTipItem(String text) {
    return Padding(
      padding: const EdgeInsets.only(bottom: 8),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          const Text(
            '•',
            style: TextStyle(
              color: Color(0xFFFACC15),
              fontSize: 16,
              fontWeight: FontWeight.bold,
            ),
          ),
          const SizedBox(width: 8),
          Expanded(
            child: Text(
              text,
              style: const TextStyle(
                color: Color(0xFF9CA3AF),
                fontSize: 14,
                height: 1.4,
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildEmptyRemindersState(BuildContext context) {
    return Container(
      width: double.infinity,
      padding: const EdgeInsets.all(40),
      decoration: BoxDecoration(
        color: const Color(0xFF1F2937),
        borderRadius: BorderRadius.circular(16),
        border: Border.all(color: const Color(0xFF374151)),
      ),
      child: Column(
        children: [
          const Icon(
            Icons.alarm_add,
            color: Color(0xFF9CA3AF),
            size: 48,
          ),
          const SizedBox(height: 16),
          const Text(
            'No Reminders Set',
            style: TextStyle(
              color: Colors.white,
              fontSize: 18,
              fontWeight: FontWeight.bold,
            ),
          ),
          const SizedBox(height: 8),
          const Text(
            'Create your first workout reminder to stay consistent with your fitness goals!',
            style: TextStyle(
              color: Color(0xFF9CA3AF),
              fontSize: 14,
            ),
            textAlign: TextAlign.center,
          ),
          const SizedBox(height: 20),
          ElevatedButton.icon(
            onPressed: () => _navigateToCreateReminder(context),
            style: ElevatedButton.styleFrom(
              backgroundColor: const Color(0xFFFACC15),
              foregroundColor: Colors.black,
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(12),
              ),
            ),
            icon: const Icon(Icons.add, size: 20),
            label: const Text(
              'Create Reminder',
              style: TextStyle(fontWeight: FontWeight.bold),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildLoadingCard() {
    return Container(
      width: double.infinity,
      height: 120,
      decoration: BoxDecoration(
        color: const Color(0xFF1F2937),
        borderRadius: BorderRadius.circular(16),
        border: Border.all(color: const Color(0xFF374151)),
      ),
      child: const Center(
        child: CircularProgressIndicator(color: Color(0xFFFACC15)),
      ),
    );
  }

  Widget _buildErrorCard(String error) {
    return Container(
      width: double.infinity,
      padding: const EdgeInsets.all(20),
      decoration: BoxDecoration(
        color: const Color(0xFF1F2937),
        borderRadius: BorderRadius.circular(16),
        border: Border.all(color: const Color(0xFFEF4444)),
      ),
      child: Column(
        children: [
          const Icon(
            Icons.error_outline,
            color: Color(0xFFEF4444),
            size: 32,
          ),
          const SizedBox(height: 8),
          const Text(
            'Failed to load reminders',
            style: TextStyle(
              color: Colors.white,
              fontSize: 16,
              fontWeight: FontWeight.bold,
            ),
          ),
          const SizedBox(height: 4),
          Text(
            error,
            style: const TextStyle(
              color: Color(0xFF9CA3AF),
              fontSize: 12,
            ),
            textAlign: TextAlign.center,
          ),
        ],
      ),
    );
  }

  void _navigateToCreateReminder(BuildContext context) {
    AppLogger.userAction('🚀 Navigating to create reminder screen', tag: 'WORKOUT_REMINDER');

    Navigator.of(context).push(
      MaterialPageRoute(
        builder: (context) => const CreateReminderScreen(),
      ),
    );
  }

  void _createQuickReminder(BuildContext context, reminder_domain.ReminderTime time, List<int> weekdays) {
    Navigator.of(context).push(
      MaterialPageRoute(
        builder: (context) => CreateReminderScreen(
          presetTime: time,
          presetWeekdays: weekdays,
        ),
      ),
    );
  }

  void _requestPermissions(WidgetRef ref) {
    AppLogger.userAction('Requesting notification permissions', tag: 'WORKOUT_REMINDER');
    ref.read(workoutReminderProvider.notifier).requestNotificationPermissions();
  }

  void _toggleReminder(WidgetRef ref, String reminderId) {
    AppLogger.userAction('Toggling reminder: $reminderId', tag: 'WORKOUT_REMINDER');
    ref.read(workoutReminderProvider.notifier).toggleReminder(reminderId);
  }

  void _updateSoundSetting(WidgetRef ref, bool enabled) {
    AppLogger.userAction('Updating sound setting: $enabled', tag: 'WORKOUT_REMINDER');
    // TODO: Update sound setting
  }

  void _updateVibrationSetting(WidgetRef ref, bool enabled) {
    AppLogger.userAction('Updating vibration setting: $enabled', tag: 'WORKOUT_REMINDER');
    // TODO: Update vibration setting
  }

  void _handleReminderAction(BuildContext context, WidgetRef ref, reminder_domain.WorkoutReminder reminder, String action) {
    switch (action) {
      case 'edit':
        Navigator.of(context).push(
          MaterialPageRoute(
            builder: (context) => CreateReminderScreen(editingReminder: reminder),
          ),
        );
        break;
      case 'delete':
        _showDeleteConfirmation(context, ref, reminder);
        break;
    }
  }

  void _showDeleteConfirmation(BuildContext context, WidgetRef ref, reminder_domain.WorkoutReminder reminder) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        backgroundColor: const Color(0xFF1F2937),
        title: const Text(
          'Delete Reminder?',
          style: TextStyle(color: Colors.white),
        ),
        content: Text(
          'Are you sure you want to delete "${reminder.title}"?',
          style: const TextStyle(color: Color(0xFF9CA3AF)),
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text(
              'Cancel',
              style: TextStyle(color: Color(0xFF9CA3AF)),
            ),
          ),
          TextButton(
            onPressed: () {
              ref.read(workoutReminderProvider.notifier).deleteReminder(reminder.id);
              Navigator.of(context).pop();
            },
            child: const Text(
              'Delete',
              style: TextStyle(color: Color(0xFFEF4444)),
            ),
          ),
        ],
      ),
    );
  }
}
