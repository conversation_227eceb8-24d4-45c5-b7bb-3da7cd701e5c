// Domain models for student workout plans
import '../../shared/utils/app_logger.dart';

/// Student's assigned workout plan
class StudentWorkoutPlan {
  final String id;
  final String studentId;
  final String instructorId;
  final String instructorName;
  final String planName;
  final String? description;
  final String? notes; // Trainer's notes
  final List<WorkoutPlanDay> plans;
  final DateTime assignedAt;
  final DateTime? lastTrainingDate;
  final bool isActive;
  final Map<String, dynamic>? planData; // Raw plan data for completion tracking

  const StudentWorkoutPlan({
    required this.id,
    required this.studentId,
    required this.instructorId,
    required this.instructorName,
    required this.planName,
    this.description,
    this.notes,
    required this.plans,
    required this.assignedAt,
    this.lastTrainingDate,
    this.isActive = true,
    this.planData,
  });

  factory StudentWorkoutPlan.fromJson(Map<String, dynamic> json) {
    AppLogger.info('🔄 Parsing StudentWorkoutPlan from JSON', tag: 'DOMAIN');

    final instructorData = json['instructor'] as Map<String, dynamic>?;
    final instructorName = instructorData != null
        ? '${instructorData['name'] ?? ''} ${instructorData['surname'] ?? ''}'.trim()
        : 'Unknown Instructor';

    final planData = json['plan_data'] as Map<String, dynamic>?;
    final plans = <WorkoutPlanDay>[];

    AppLogger.info('📋 Plan data keys: ${planData?.keys.toList()}', tag: 'DOMAIN');

    // Parse plans from plan_data
    if (planData != null && planData['plans'] != null) {
      final plansData = planData['plans'] as List<dynamic>;
      AppLogger.success('✅ Found ${plansData.length} plans in data', tag: 'DOMAIN');

      for (int i = 0; i < plansData.length; i++) {
        final planJson = plansData[i] as Map<String, dynamic>;
        AppLogger.info('🏋️ Parsing plan ${i + 1}: ${planJson['name']}', tag: 'DOMAIN');
        plans.add(WorkoutPlanDay.fromJson(planJson, i + 1));
      }
    } else {
      AppLogger.warning('❌ No plans found in plan_data', tag: 'DOMAIN');
    }

    return StudentWorkoutPlan(
      id: json['id'] as String,
      studentId: json['student_id'] as String,
      instructorId: json['instructor_id'] as String,
      instructorName: instructorName,
      planName: json['title'] as String,
      description: json['description'] as String?,
      notes: planData?['notes'] as String?,
      plans: plans,
      assignedAt: DateTime.parse(json['assigned_at'] as String),
      lastTrainingDate: json['last_training_date'] != null 
          ? DateTime.parse(json['last_training_date'] as String)
          : null,
      isActive: json['is_active'] as bool? ?? true,
      planData: planData,
    );
  }

  /// Get days since last training
  int? get daysSinceLastTraining {
    if (lastTrainingDate == null) return null;
    return DateTime.now().difference(lastTrainingDate!).inDays;
  }

  /// Check if plan has any exercises
  bool get hasExercises {
    return plans.any((plan) => plan.exercises.isNotEmpty);
  }
}

/// Individual workout plan day (Plan 1, Plan 2, etc.)
class WorkoutPlanDay {
  final String id;
  final String name;
  final int dayNumber;
  final String? description;
  final List<WorkoutExercise> exercises;

  const WorkoutPlanDay({
    required this.id,
    required this.name,
    required this.dayNumber,
    this.description,
    required this.exercises,
  });

  factory WorkoutPlanDay.fromJson(Map<String, dynamic> json, int dayNumber) {
    AppLogger.debug('Parsing WorkoutPlanDay: ${json['name']}', tag: 'DOMAIN');

    final exercises = <WorkoutExercise>[];

    if (json['exercises'] != null) {
      final exercisesData = json['exercises'] as List<dynamic>;
      AppLogger.debug('Found ${exercisesData.length} exercises in plan', tag: 'DOMAIN');

      for (final exerciseJson in exercisesData) {
        final exercise = WorkoutExercise.fromJson(exerciseJson as Map<String, dynamic>);
        exercises.add(exercise);
        AppLogger.debug('Added exercise: ${exercise.name}', tag: 'DOMAIN');
      }
    } else {
      AppLogger.warning('No exercises found in plan: ${json['name']}', tag: 'DOMAIN');
    }

    final planDay = WorkoutPlanDay(
      id: json['id'] as String? ?? 'plan_$dayNumber',
      name: json['name'] as String? ?? 'Plan $dayNumber',
      dayNumber: dayNumber,
      description: json['description'] as String?,
      exercises: exercises,
    );

    AppLogger.debug('Created WorkoutPlanDay: ${planDay.name} with ${planDay.exercises.length} exercises', tag: 'DOMAIN');
    return planDay;
  }

  /// Get total number of exercises
  int get exerciseCount => exercises.length;

  /// Get estimated workout duration in minutes
  int get estimatedDuration {
    if (exercises.isEmpty) return 0;
    
    int totalTime = 0;
    for (final exercise in exercises) {
      // Estimate: 1 minute per set + rest time
      final setsTime = (exercise.sets ?? 1) * 1;
      final restTime = ((exercise.sets ?? 1) - 1) * ((exercise.restTime ?? 60) / 60);
      totalTime += (setsTime + restTime).round();
    }
    
    return totalTime;
  }
}

/// Individual exercise within a workout plan
class WorkoutExercise {
  final String id;
  final String exerciseId;
  final String name;
  final String? description;
  final List<String> muscleGroups;
  final String? imageUrl;
  final int? sets;
  final int? reps;
  final double? weight;
  final int? duration; // in seconds
  final int? restTime; // in seconds
  final String? notes;
  final String? equipment;
  final String? difficultyLevel;
  final int orderIndex;

  const WorkoutExercise({
    required this.id,
    required this.exerciseId,
    required this.name,
    this.description,
    this.muscleGroups = const [],
    this.imageUrl,
    this.sets,
    this.reps,
    this.weight,
    this.duration,
    this.restTime,
    this.notes,
    this.equipment,
    this.difficultyLevel,
    this.orderIndex = 0,
  });

  factory WorkoutExercise.fromJson(Map<String, dynamic> json) {
    return WorkoutExercise(
      id: json['id'] as String? ?? json['exercise_id'] as String,
      exerciseId: json['exercise_id'] as String,
      name: json['name'] as String? ?? json['exercise_name'] as String? ?? 'Unknown Exercise',
      description: json['description'] as String? ?? json['exercise_description'] as String?,
      muscleGroups: (json['muscle_groups'] as List<dynamic>?)?.cast<String>() ?? [],
      imageUrl: json['image_url'] as String?,
      sets: json['sets'] as int?,
      reps: json['reps'] as int?,
      weight: (json['weight'] as num?)?.toDouble(),
      duration: json['duration'] as int?,
      restTime: json['rest_time'] as int?,
      notes: json['notes'] as String?,
      equipment: json['equipment'] as String?,
      difficultyLevel: json['difficulty_level'] as String?,
      orderIndex: json['order_index'] as int? ?? 0,
    );
  }

  /// Get formatted sets/reps display
  String get setsRepsDisplay {
    if (sets != null && reps != null) {
      return '$sets x $reps';
    } else if (sets != null) {
      return '$sets sets';
    } else if (reps != null) {
      return '$reps reps';
    } else if (duration != null) {
      final minutes = duration! ~/ 60;
      final seconds = duration! % 60;
      if (minutes > 0) {
        return '${minutes}m ${seconds}s';
      } else {
        return '${seconds}s';
      }
    }
    return '';
  }

  /// Get formatted rest time display
  String get restTimeDisplay {
    if (restTime == null) return '';
    
    if (restTime! >= 60) {
      final minutes = restTime! ~/ 60;
      final seconds = restTime! % 60;
      if (seconds > 0) {
        return 'Rest: ${minutes}m ${seconds}s';
      } else {
        return 'Rest: ${minutes}m';
      }
    } else {
      return 'Rest: ${restTime}s';
    }
  }

  /// Get primary muscle group
  String get primaryMuscleGroup {
    if (muscleGroups.isEmpty) return 'Full Body';
    return muscleGroups.first;
  }

  /// Get formatted weight display
  String get weightDisplay {
    if (weight == null) return '';
    if (weight! % 1 == 0) {
      return '@ ${weight!.toInt()}kg';
    } else {
      return '@ ${weight}kg';
    }
  }
}
