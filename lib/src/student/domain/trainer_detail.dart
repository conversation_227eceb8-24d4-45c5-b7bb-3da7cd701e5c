import 'package:fitgo_app/src/instructor/domain/instructor_profile_models.dart';

/// Instructor detail model aligned with current database schema
/// Uses actual fields from instructors and instructor_subscription_configs tables
class InstructorDetail {
  final String id;
  final String profileId;
  final String name;
  final String photoUrl;
  final String? bio;
  final String? title;
  final double rating;
  final int experienceYears;
  final int currentStudents;
  final List<WorkHistory> workHistory;
  final List<Certification> certifications;
  final List<Review> reviews;
  final List<FAQ> faqs;
  final double? basicPrice;
  final double? premiumPrice;
  final String? currentWorkplace;
  final String approvalStatus;
  final bool isPublic;
  final String courseStatus;
  final bool canAcceptNewStudents;

  InstructorDetail({
    required this.id,
    required this.profileId,
    required this.name,
    required this.photoUrl,
    this.bio,
    this.title,
    required this.rating,
    required this.experienceYears,
    required this.currentStudents,
    required this.workHistory,
    required this.certifications,
    required this.reviews,
    required this.faqs,
    this.basicPrice,
    this.premiumPrice,
    this.currentWorkplace,
    required this.approvalStatus,
    required this.isPublic,
    this.courseStatus = 'active',
    this.canAcceptNewStudents = true,
  });

  /// Backward compatibility getters
  int get clientCount => currentStudents;
  int get maxStudents => 5; // Free tier limit
  int get availableSpots => maxStudents - currentStudents;
  bool get isAcceptingStudents => availableSpots > 0;

  factory InstructorDetail.fromJson(Map<String, dynamic> json) {
    // Get name from profiles (✅ CLEANED: Use profiles data)
    final profiles = json['profiles'] as Map<String, dynamic>?;
    final firstName = profiles?['name'] as String? ?? '';
    final lastName = profiles?['surname'] as String? ?? '';
    final fullName = '$firstName $lastName'.trim();

    // Get bio from instructors table
    final bio = json['bio'] as String?;

    // Get subscription config data
    final subscriptionConfig =
        json['instructor_subscription_configs'] as Map<String, dynamic>?;
    final approvalStatus =
        subscriptionConfig?['approval_status'] as String? ?? 'pending';

    // Get pricing from subscription config (monthly prices)
    final basicPrice =
        (subscriptionConfig?['basic_plan_monthly_price'] as num?)?.toDouble();
    final premiumPrice =
        (subscriptionConfig?['premium_plan_monthly_price'] as num?)?.toDouble();

    // Get current workplace from work history
    final workHistoryList = json['instructor_work_history'] as List<dynamic>?;
    String? currentWorkplace;
    if (workHistoryList != null && workHistoryList.isNotEmpty) {
      final currentWork = workHistoryList
          .cast<Map<String, dynamic>>()
          .firstWhere((work) => work['is_current'] == true, orElse: () => {});
      if (currentWork.isNotEmpty) {
        currentWorkplace = currentWork['company_name'] as String?;
      }
    }

    return InstructorDetail(
      id: json['id'] as String,
      profileId: json['profile_id'] as String? ?? json['id'] as String,
      name: fullName.isNotEmpty ? fullName : 'Unknown Instructor',
      photoUrl: json['photo_url'] as String? ?? '',
      bio: bio,
      title: json['title'] as String?,
      rating: (json['rating'] as num?)?.toDouble() ?? 0.0,
      experienceYears: json['experience_years'] as int? ?? 0,
      currentStudents: json['current_students'] as int? ?? 0,
      workHistory: (json['instructor_work_history'] as List<dynamic>?)
              ?.map((e) => WorkHistory.fromJson(e as Map<String, dynamic>))
              .toList() ??
          [],
      certifications: (json['instructor_certifications'] as List<dynamic>?)
              ?.map((e) => Certification.fromJson(e as Map<String, dynamic>))
              .toList() ??
          [],
      reviews: (json['instructor_reviews'] as List<dynamic>?)
              ?.map((e) => Review.fromJson(e as Map<String, dynamic>))
              .toList() ??
          [],
      faqs: (json['instructor_faqs'] as List<dynamic>?)
              ?.map(
                (e) => FAQ(
                  id: e['id']?.toString() ?? '',
                  question: e['question'] as String? ?? '',
                  answer: e['answer'] as String? ?? '',
                  order: (e['order_index'] as num?)?.toInt() ?? 0,
                  isExpanded: false,
                ),
              )
              .toList() ??
          [],
      basicPrice: basicPrice,
      premiumPrice: premiumPrice,
      currentWorkplace: currentWorkplace,
      approvalStatus: approvalStatus,
      isPublic: json['is_public'] as bool? ?? false,
      courseStatus: json['course_status'] as String? ?? 'active',
      canAcceptNewStudents: json['can_accept_new_students'] as bool? ?? true,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'profile_id': profileId,
      'name': name,
      'photo_url': photoUrl,
      'bio': bio,
      'title': title,
      'rating': rating,
      'experience_years': experienceYears,
      'current_students': currentStudents,
      'current_workplace': currentWorkplace,
      'approval_status': approvalStatus,
      'is_public': isPublic,
      'course_status': courseStatus,
      'can_accept_new_students': canAcceptNewStudents,
      'basic_price': basicPrice,
      'premium_price': premiumPrice,
      'instructor_work_history': workHistory.map((e) => e.toJson()).toList(),
      'instructor_certifications':
          certifications.map((e) => e.toJson()).toList(),
      'instructor_reviews': reviews.map((e) => e.toJson()).toList(),
      'instructor_faqs': faqs
          .map(
            (e) => {
              'id': e.id,
              'question': e.question,
              'answer': e.answer,
              'order_index': e.order,
            },
          )
          .toList(),
    };
  }
}

class WorkHistory {
  final String id;
  final String companyName;
  final String position;
  final DateTime startDate;
  final DateTime? endDate;
  final String? description;
  final String? location;
  final bool isCurrent;

  WorkHistory({
    required this.id,
    required this.companyName,
    required this.position,
    required this.startDate,
    this.endDate,
    this.description,
    this.location,
    required this.isCurrent,
  });

  factory WorkHistory.fromJson(Map<String, dynamic> json) {
    return WorkHistory(
      id: json['id'] as String,
      companyName: json['company_name'] as String,
      position: json['position'] as String,
      startDate: DateTime.parse(json['start_date'] as String),
      endDate: json['end_date'] != null
          ? DateTime.parse(json['end_date'] as String)
          : null,
      description: json['description'] as String?,
      location: json['location'] as String?,
      isCurrent: json['is_current'] as bool? ?? false,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'company_name': companyName,
      'position': position,
      'start_date': startDate.toIso8601String(),
      'end_date': endDate?.toIso8601String(),
      'description': description,
      'location': location,
      'is_current': isCurrent,
    };
  }

  /// Get formatted duration string
  String get duration {
    if (isCurrent) {
      final years = DateTime.now().difference(startDate).inDays ~/ 365;
      return years <= 1 ? '1 year' : '$years years';
    } else if (endDate != null) {
      final years = endDate!.difference(startDate).inDays ~/ 365;
      return years <= 1 ? '1 year' : '$years years';
    } else {
      return '1 year';
    }
  }

  /// Get company name for backward compatibility
  String get gym => companyName;

  /// Get position for backward compatibility
  String get role => position;
}

class Certification {
  final String id;
  final String certificationName;
  final String issuingOrganization;
  final DateTime? issueDate;
  final DateTime? expiryDate;
  final String? certificateUrl;
  final String? description;

  Certification({
    required this.id,
    required this.certificationName,
    required this.issuingOrganization,
    this.issueDate,
    this.expiryDate,
    this.certificateUrl,
    this.description,
  });

  factory Certification.fromJson(Map<String, dynamic> json) {
    return Certification(
      id: json['id'] as String,
      certificationName: json['certification_name'] as String,
      issuingOrganization: json['issuing_organization'] as String,
      issueDate: json['issue_date'] != null
          ? DateTime.parse(json['issue_date'] as String)
          : null,
      expiryDate: json['expiry_date'] != null
          ? DateTime.parse(json['expiry_date'] as String)
          : null,
      certificateUrl: json['certificate_url'] as String?,
      description: json['description'] as String?,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'certification_name': certificationName,
      'issuing_organization': issuingOrganization,
      'issue_date': issueDate?.toIso8601String(),
      'expiry_date': expiryDate?.toIso8601String(),
      'certificate_url': certificateUrl,
      'description': description,
    };
  }

  /// Get certification name for backward compatibility
  String get title => certificationName;

  /// Get issuing organization for backward compatibility
  String get organization => issuingOrganization;
}

class Review {
  final String id;
  final String clientName;
  final String avatarUrl;
  final double rating;
  final String comment;
  final DateTime createdAt;

  Review({
    required this.id,
    required this.clientName,
    required this.avatarUrl,
    required this.rating,
    required this.comment,
    required this.createdAt,
  });

  factory Review.fromJson(Map<String, dynamic> json) {
    return Review(
      id: json['id'].toString(),
      clientName: json['client_name'] as String,
      avatarUrl: json['avatar_url'] as String? ?? '',
      rating: (json['rating'] as num?)?.toDouble() ?? 0.0,
      comment: json['comment'] as String,
      createdAt: DateTime.parse(json['created_at'] as String),
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'client_name': clientName,
      'avatar_url': avatarUrl,
      'rating': rating,
      'comment': comment,
      'created_at': createdAt.toIso8601String(),
    };
  }
}
