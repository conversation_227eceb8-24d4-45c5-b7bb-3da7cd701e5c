// Domain models for workout session tracking
import '../../shared/utils/app_logger.dart';

/// Represents a completed workout session
class WorkoutSession {
  final String id;
  final String userId;
  final String workoutPlanId;
  final String workoutPlanName;
  final DateTime startTime;
  final DateTime? endTime;
  final Duration? totalDuration;
  final List<ExerciseSessionResult> exerciseResults;
  final WorkoutSessionStatus status;
  final int totalExercisesCompleted;
  final int totalSetsCompleted;
  final int totalRepsCompleted;
  final String? notes;
  final DateTime createdAt;

  const WorkoutSession({
    required this.id,
    required this.userId,
    required this.workoutPlanId,
    required this.workoutPlanName,
    required this.startTime,
    this.endTime,
    this.totalDuration,
    required this.exerciseResults,
    required this.status,
    required this.totalExercisesCompleted,
    required this.totalSetsCompleted,
    required this.totalRepsCompleted,
    this.notes,
    required this.createdAt,
  });

  /// Calculate total duration if not provided
  Duration get calculatedDuration {
    if (totalDuration != null) return totalDuration!;
    if (endTime != null) return endTime!.difference(startTime);
    return Duration.zero;
  }

  /// Check if session is completed
  bool get isCompleted => status == WorkoutSessionStatus.completed;

  /// Get formatted duration string
  String get formattedDuration {
    final duration = calculatedDuration;
    final hours = duration.inHours;
    final minutes = duration.inMinutes.remainder(60);
    final seconds = duration.inSeconds.remainder(60);
    
    if (hours > 0) {
      return '${hours}h ${minutes}m ${seconds}s';
    } else if (minutes > 0) {
      return '${minutes}m ${seconds}s';
    } else {
      return '${seconds}s';
    }
  }

  factory WorkoutSession.fromJson(Map<String, dynamic> json) {
    AppLogger.info('🔄 Parsing WorkoutSession from JSON', tag: 'WORKOUT_SESSION');
    
    return WorkoutSession(
      id: json['id'] as String,
      userId: json['user_id'] as String,
      workoutPlanId: json['workout_plan_id'] as String,
      workoutPlanName: json['workout_plan_name'] as String,
      startTime: DateTime.parse(json['start_time'] as String),
      endTime: json['end_time'] != null ? DateTime.parse(json['end_time'] as String) : null,
      totalDuration: json['total_duration'] != null 
          ? Duration(seconds: json['total_duration'] as int) 
          : null,
      exerciseResults: (json['exercise_results'] as List<dynamic>?)
          ?.map((e) => ExerciseSessionResult.fromJson(e as Map<String, dynamic>))
          .toList() ?? [],
      status: WorkoutSessionStatus.fromString(json['status'] as String),
      totalExercisesCompleted: json['total_exercises_completed'] as int? ?? 0,
      totalSetsCompleted: json['total_sets_completed'] as int? ?? 0,
      totalRepsCompleted: json['total_reps_completed'] as int? ?? 0,
      notes: json['notes'] as String?,
      createdAt: DateTime.parse(json['created_at'] as String),
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'user_id': userId,
      'workout_plan_id': workoutPlanId,
      'workout_plan_name': workoutPlanName,
      'start_time': startTime.toIso8601String(),
      'end_time': endTime?.toIso8601String(),
      'total_duration': totalDuration?.inSeconds,
      'exercise_results': exerciseResults.map((e) => e.toJson()).toList(),
      'status': status.value,
      'total_exercises_completed': totalExercisesCompleted,
      'total_sets_completed': totalSetsCompleted,
      'total_reps_completed': totalRepsCompleted,
      'notes': notes,
      'created_at': createdAt.toIso8601String(),
    };
  }
}

/// Individual exercise result within a workout session
class ExerciseSessionResult {
  final String exerciseId;
  final String exerciseName;
  final List<SetResult> setResults;
  final Duration? restTime;
  final String? notes;
  final bool isCompleted;

  const ExerciseSessionResult({
    required this.exerciseId,
    required this.exerciseName,
    required this.setResults,
    this.restTime,
    this.notes,
    required this.isCompleted,
  });

  /// Get total reps completed for this exercise
  int get totalReps => setResults.fold(0, (sum, set) => sum + set.reps);

  /// Get total sets completed
  int get completedSets => setResults.where((set) => set.isCompleted).length;

  factory ExerciseSessionResult.fromJson(Map<String, dynamic> json) {
    return ExerciseSessionResult(
      exerciseId: json['exercise_id'] as String,
      exerciseName: json['exercise_name'] as String,
      setResults: (json['set_results'] as List<dynamic>?)
          ?.map((e) => SetResult.fromJson(e as Map<String, dynamic>))
          .toList() ?? [],
      restTime: json['rest_time'] != null 
          ? Duration(seconds: json['rest_time'] as int) 
          : null,
      notes: json['notes'] as String?,
      isCompleted: json['is_completed'] as bool? ?? false,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'exercise_id': exerciseId,
      'exercise_name': exerciseName,
      'set_results': setResults.map((e) => e.toJson()).toList(),
      'rest_time': restTime?.inSeconds,
      'notes': notes,
      'is_completed': isCompleted,
    };
  }
}

/// Individual set result
class SetResult {
  final int setNumber;
  final int reps;
  final double? weight;
  final Duration? duration;
  final bool isCompleted;
  final DateTime? completedAt;

  const SetResult({
    required this.setNumber,
    required this.reps,
    this.weight,
    this.duration,
    required this.isCompleted,
    this.completedAt,
  });

  factory SetResult.fromJson(Map<String, dynamic> json) {
    return SetResult(
      setNumber: json['set_number'] as int,
      reps: json['reps'] as int,
      weight: json['weight'] as double?,
      duration: json['duration'] != null 
          ? Duration(seconds: json['duration'] as int) 
          : null,
      isCompleted: json['is_completed'] as bool? ?? false,
      completedAt: json['completed_at'] != null 
          ? DateTime.parse(json['completed_at'] as String) 
          : null,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'set_number': setNumber,
      'reps': reps,
      'weight': weight,
      'duration': duration?.inSeconds,
      'is_completed': isCompleted,
      'completed_at': completedAt?.toIso8601String(),
    };
  }
}

/// Workout session status
enum WorkoutSessionStatus {
  inProgress('in_progress'),
  completed('completed'),
  paused('paused'),
  cancelled('cancelled');

  const WorkoutSessionStatus(this.value);
  final String value;

  static WorkoutSessionStatus fromString(String value) {
    return WorkoutSessionStatus.values.firstWhere(
      (status) => status.value == value,
      orElse: () => WorkoutSessionStatus.inProgress,
    );
  }
}

/// Workout progress statistics
class WorkoutProgressStats {
  final int totalWorkouts;
  final int totalExercises;
  final int totalSets;
  final int totalReps;
  final Duration totalDuration;
  final DateTime? lastWorkoutDate;
  final int currentWeekWorkouts;
  final int currentMonthWorkouts;
  final List<DailyWorkoutCount> dailyStats;

  const WorkoutProgressStats({
    required this.totalWorkouts,
    required this.totalExercises,
    required this.totalSets,
    required this.totalReps,
    required this.totalDuration,
    this.lastWorkoutDate,
    required this.currentWeekWorkouts,
    required this.currentMonthWorkouts,
    required this.dailyStats,
  });

  factory WorkoutProgressStats.empty() {
    return const WorkoutProgressStats(
      totalWorkouts: 0,
      totalExercises: 0,
      totalSets: 0,
      totalReps: 0,
      totalDuration: Duration.zero,
      currentWeekWorkouts: 0,
      currentMonthWorkouts: 0,
      dailyStats: [],
    );
  }
}

/// Daily workout count for charts
class DailyWorkoutCount {
  final DateTime date;
  final int workoutCount;
  final Duration totalDuration;

  const DailyWorkoutCount({
    required this.date,
    required this.workoutCount,
    required this.totalDuration,
  });

  factory DailyWorkoutCount.fromJson(Map<String, dynamic> json) {
    return DailyWorkoutCount(
      date: DateTime.parse(json['date'] as String),
      workoutCount: json['workout_count'] as int,
      totalDuration: Duration(seconds: json['total_duration'] as int? ?? 0),
    );
  }
}
