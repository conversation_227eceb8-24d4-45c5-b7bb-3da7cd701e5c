import 'package:fitgo_app/src/shared/extensions/string_hardcoded.dart';

/// Course status enum for instructor visibility management
enum CourseStatus {
  active,
  hidden,
  suspended;

  String get displayName {
    switch (this) {
      case CourseStatus.active:
        return 'Active';
      case CourseStatus.hidden:
        return 'Hidden';
      case CourseStatus.suspended:
        return 'Suspended';
    }
  }

  String get description {
    switch (this) {
      case CourseStatus.active:
        return 'Course is visible to all students';
      case CourseStatus.hidden:
        return 'Course is hidden from new students';
      case CourseStatus.suspended:
        return 'Course is suspended by admin';
    }
  }

  static CourseStatus fromString(String status) {
    switch (status.toLowerCase()) {
      case 'active':
        return CourseStatus.active;
      case 'hidden':
        return CourseStatus.hidden;
      case 'suspended':
        return CourseStatus.suspended;
      default:
        return CourseStatus.active;
    }
  }
}

/// Instructor list item model aligned with current database schema
/// Uses actual fields from instructors and instructor_subscription_configs tables
class InstructorListItem {
  final String id;
  final String profileId;
  final String name;
  final String? currentWorkplace;
  final double rating;
  final int reviewCount;
  final String specialty;
  final int price; // Basic plan price for backward compatibility
  final double? basicPrice;
  final double? premiumPrice;
  final String imageUrl;
  final String? bio;
  final String? title;
  final int experienceYears;
  final int currentStudents; // From actual enrollment count
  final String approvalStatus;
  final bool isPublic;
  final String courseStatus;
  final bool canAcceptNewStudents;

  InstructorListItem({
    required this.id,
    required this.profileId,
    required this.name,
    this.currentWorkplace,
    required this.rating,
    required this.reviewCount,
    required this.specialty,
    required this.price,
    this.basicPrice,
    this.premiumPrice,
    required this.imageUrl,
    this.bio,
    this.title,
    required this.experienceYears,
    required this.currentStudents,
    required this.approvalStatus,
    required this.isPublic,
    this.courseStatus = 'active',
    this.canAcceptNewStudents = true,
  });

  /// Backward compatibility getters
  String get gym => currentWorkplace ?? 'Fitness Center';
  int get clientCount => currentStudents;
  int get maxCapacity => 5; // Free tier limit
  int get currentEnrollment => currentStudents;
  int get availableSpots => maxCapacity - currentStudents;
  bool get isAcceptingStudents => availableSpots > 0;
  List<String> get specializations => ['Fitness Training', 'Personal Training'];

  factory InstructorListItem.fromJson(Map<String, dynamic> json) {
    // Get pricing from direct fields (added by provider)
    final basicPrice = (json['basic_plan_monthly_price'] as num?)?.toDouble();
    final premiumPrice =
        (json['premium_plan_monthly_price'] as num?)?.toDouble();

    // Get name from profiles (✅ CLEANED: Use profiles data)
    final profiles = json['profiles'] as Map<String, dynamic>?;
    final firstName = profiles?['name'] as String? ?? '';
    final lastName = profiles?['surname'] as String? ?? '';
    final fullName = '$firstName $lastName'.trim();

    // Get current workplace from work history
    final workHistory = json['instructor_work_history'] as List<dynamic>?;
    String? currentWorkplace;
    if (workHistory != null && workHistory.isNotEmpty) {
      final currentWork = workHistory.firstWhere(
        (work) => work['is_current'] == true,
        orElse: () => null,
      );
      if (currentWork != null) {
        currentWorkplace = currentWork['company_name'] as String?;
      }
    }

    // Note: instructor_capacity table doesn't exist, using defaults

    return InstructorListItem(
      id: json['id'] as String,
      profileId: json['profile_id'] as String,
      name: fullName.isNotEmpty ? fullName : 'Unknown Instructor',
      currentWorkplace: currentWorkplace,
      rating: (json['rating'] as num?)?.toDouble() ?? 0.0,
      reviewCount: 0, // Will be calculated from instructor_reviews table
      specialty:
          json['title'] as String? ?? currentWorkplace ?? 'Fitness Trainer',
      price:
          basicPrice?.toInt() ?? 200, // Basic price for backward compatibility
      basicPrice: basicPrice,
      premiumPrice: premiumPrice,
      imageUrl: json['photo_url'] as String? ?? '',
      bio: json['bio'] as String?,
      title: json['title'] as String?,
      experienceYears: json['experience_years'] as int? ?? 0,
      currentStudents: json['current_students'] as int? ?? 0,
      approvalStatus: 'approved', // Only approved instructors are shown
      isPublic: json['is_public'] as bool? ?? false,
      courseStatus: json['course_status'] as String? ?? 'active',
      canAcceptNewStudents: json['can_accept_new_students'] as bool? ?? true,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'profile_id': profileId,
      'name': name,
      'current_workplace': currentWorkplace,
      'rating': rating,
      'review_count': reviewCount,
      'specialty': specialty,
      'price_per_session': price,
      'photo_url': imageUrl,
      'bio': bio,
      'title': title,
      'experience_years': experienceYears,
      'current_students': currentStudents,
      'approval_status': approvalStatus,
      'is_public': isPublic,
      'course_status': courseStatus,
      'can_accept_new_students': canAcceptNewStudents,
    };
  }
}

class InstructorListResponse {
  final List<InstructorListItem> instructors;
  final int totalCount;
  final bool hasMore;
  final int currentPage;

  InstructorListResponse({
    required this.instructors,
    required this.totalCount,
    required this.hasMore,
    required this.currentPage,
  });

  factory InstructorListResponse.fromJson(Map<String, dynamic> json) {
    return InstructorListResponse(
      instructors: (json['instructors'] as List<dynamic>)
          .map(
            (e) => InstructorListItem.fromJson(e as Map<String, dynamic>),
          )
          .toList(),
      totalCount: json['total_count'] as int,
      hasMore: json['has_more'] as bool,
      currentPage: json['current_page'] as int,
    );
  }

  /// Backward compatibility getter
  List<InstructorListItem> get trainers => instructors;
}

class TrainerListFilter {
  final String? searchQuery;
  final String? specialization;
  final double? minRating;
  final int? maxPrice;
  final String sortBy;
  final bool ascending;

  const TrainerListFilter({
    this.searchQuery,
    this.specialization,
    this.minRating,
    this.maxPrice,
    this.sortBy = 'rating',
    this.ascending = false,
  });

  // Helper getters for sort options
  static List<Map<String, dynamic>> sortOptions = [
    {
      'key': 'rating_desc',
      'label': 'Decreasing Rating'.hardcoded,
      'sortBy': 'rating',
      'ascending': false,
    },
    {
      'key': 'rating_asc',
      'label': 'Increasing Rating'.hardcoded,
      'sortBy': 'rating',
      'ascending': true,
    },
    {
      'key': 'price_desc',
      'label': 'Decreasing Price'.hardcoded,
      'sortBy': 'price_per_session',
      'ascending': false,
    },
    {
      'key': 'price_asc',
      'label': 'Increasing Price'.hardcoded,
      'sortBy': 'price_per_session',
      'ascending': true,
    },
    {
      'key': 'capacity_desc',
      'label': 'Decreasing Capacity'.hardcoded,
      'sortBy': 'available_spots',
      'ascending': false,
    },
    {
      'key': 'capacity_asc',
      'label': 'Increasing Capacity'.hardcoded,
      'sortBy': 'available_spots',
      'ascending': true,
    },
  ];

  TrainerListFilter copyWith({
    String? searchQuery,
    String? specialization,
    double? minRating,
    int? maxPrice,
    String? sortBy,
    bool? ascending,
    bool clearSearchQuery = false,
    bool clearSpecialization = false,
    bool clearMinRating = false,
    bool clearMaxPrice = false,
  }) {
    return TrainerListFilter(
      searchQuery: clearSearchQuery ? null : (searchQuery ?? this.searchQuery),
      specialization:
          clearSpecialization ? null : (specialization ?? this.specialization),
      minRating: clearMinRating ? null : (minRating ?? this.minRating),
      maxPrice: clearMaxPrice ? null : (maxPrice ?? this.maxPrice),
      sortBy: sortBy ?? this.sortBy,
      ascending: ascending ?? this.ascending,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'search_query': searchQuery,
      'specialization': specialization,
      'min_rating': minRating,
      'max_price': maxPrice,
      'sort_by': sortBy,
      'ascending': ascending,
    };
  }
}
