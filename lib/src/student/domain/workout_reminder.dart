// Domain models for workout reminder notifications
import 'package:flutter/material.dart' show TimeOfDay;
import '../../shared/utils/app_logger.dart';

/// Represents a workout reminder notification
class WorkoutReminder {
  final String id;
  final String userId;
  final ReminderTime reminderTime;
  final List<int> weekdays; // 1=Monday, 7=Sunday
  final bool isEnabled;
  final String title;
  final String message;
  final DateTime createdAt;
  final DateTime? updatedAt;

  const WorkoutReminder({
    required this.id,
    required this.userId,
    required this.reminderTime,
    required this.weekdays,
    required this.isEnabled,
    required this.title,
    required this.message,
    required this.createdAt,
    this.updatedAt,
  });

  /// Get formatted time string (e.g., "09:00 AM")
  String get formattedTime {
    final hour = reminderTime.hour;
    final minute = reminderTime.minute;
    final period = hour >= 12 ? 'PM' : 'AM';
    final displayHour = hour > 12 ? hour - 12 : (hour == 0 ? 12 : hour);
    return '${displayHour.toString().padLeft(2, '0')}:${minute.toString().padLeft(2, '0')} $period';
  }

  /// Get weekdays as readable string (e.g., "Mon, Wed, Fri")
  String get formattedWeekdays {
    const weekdayNames = ['Mon', 'Tue', 'Wed', 'Thu', 'Fri', 'Sat', 'Sun'];
    if (weekdays.length == 7) return 'Every day';
    if (weekdays.length == 5 && !weekdays.contains(6) && !weekdays.contains(7)) {
      return 'Weekdays';
    }
    if (weekdays.length == 2 && weekdays.contains(6) && weekdays.contains(7)) {
      return 'Weekends';
    }
    return weekdays.map((day) => weekdayNames[day - 1]).join(', ');
  }

  /// Check if reminder is active today
  bool get isActiveToday {
    if (!isEnabled) return false;
    final today = DateTime.now().weekday;
    return weekdays.contains(today);
  }

  factory WorkoutReminder.fromJson(Map<String, dynamic> json) {
    AppLogger.info('🔄 Parsing WorkoutReminder from JSON', tag: 'WORKOUT_REMINDER');
    
    return WorkoutReminder(
      id: json['id'] as String,
      userId: json['user_id'] as String,
      reminderTime: ReminderTime(
        hour: json['reminder_hour'] as int,
        minute: json['reminder_minute'] as int,
      ),
      weekdays: (json['weekdays'] as List<dynamic>).cast<int>(),
      isEnabled: json['is_enabled'] as bool? ?? true,
      title: json['title'] as String? ?? 'Workout Time!',
      message: json['message'] as String? ?? 'Time for your workout session!',
      createdAt: DateTime.parse(json['created_at'] as String),
      updatedAt: json['updated_at'] != null 
          ? DateTime.parse(json['updated_at'] as String) 
          : null,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'user_id': userId,
      'reminder_hour': reminderTime.hour,
      'reminder_minute': reminderTime.minute,
      'weekdays': weekdays,
      'is_enabled': isEnabled,
      'title': title,
      'message': message,
      'created_at': createdAt.toIso8601String(),
      'updated_at': updatedAt?.toIso8601String(),
    };
  }

  WorkoutReminder copyWith({
    String? id,
    String? userId,
    ReminderTime? reminderTime,
    List<int>? weekdays,
    bool? isEnabled,
    String? title,
    String? message,
    DateTime? createdAt,
    DateTime? updatedAt,
  }) {
    return WorkoutReminder(
      id: id ?? this.id,
      userId: userId ?? this.userId,
      reminderTime: reminderTime ?? this.reminderTime,
      weekdays: weekdays ?? this.weekdays,
      isEnabled: isEnabled ?? this.isEnabled,
      title: title ?? this.title,
      message: message ?? this.message,
      createdAt: createdAt ?? this.createdAt,
      updatedAt: updatedAt ?? this.updatedAt,
    );
  }
}

/// Custom time of day helper class (renamed to avoid conflict with Flutter's TimeOfDay)
class ReminderTime {
  final int hour;
  final int minute;

  const ReminderTime({required this.hour, required this.minute});

  /// Create from Flutter's TimeOfDay
  factory ReminderTime.fromFlutterTimeOfDay(TimeOfDay flutterTimeOfDay) {
    AppLogger.info('🕐 Converting Flutter TimeOfDay to ReminderTime: ${flutterTimeOfDay.hour}:${flutterTimeOfDay.minute}', tag: 'REMINDER_TIME');
    return ReminderTime(
      hour: flutterTimeOfDay.hour,
      minute: flutterTimeOfDay.minute,
    );
  }

  /// Convert to Flutter's TimeOfDay
  TimeOfDay toFlutterTimeOfDay() {
    AppLogger.info('🕐 Converting ReminderTime to Flutter TimeOfDay: $hour:$minute', tag: 'REMINDER_TIME');
    return TimeOfDay(hour: hour, minute: minute);
  }

  @override
  String toString() => '${hour.toString().padLeft(2, '0')}:${minute.toString().padLeft(2, '0')}';

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is ReminderTime && other.hour == hour && other.minute == minute;
  }

  @override
  int get hashCode => hour.hashCode ^ minute.hashCode;
}

/// Reminder frequency presets
enum ReminderFrequency {
  daily('Daily', [1, 2, 3, 4, 5, 6, 7]),
  weekdays('Weekdays', [1, 2, 3, 4, 5]),
  weekends('Weekends', [6, 7]),
  custom('Custom', []);

  const ReminderFrequency(this.label, this.weekdaysList);
  final String label;
  final List<int> weekdaysList;
}

/// Reminder preset times
class ReminderPresets {
  static const List<ReminderTime> popularTimes = [
    ReminderTime(hour: 6, minute: 0),   // 6:00 AM
    ReminderTime(hour: 7, minute: 0),   // 7:00 AM
    ReminderTime(hour: 8, minute: 0),   // 8:00 AM
    ReminderTime(hour: 9, minute: 0),   // 9:00 AM
    ReminderTime(hour: 12, minute: 0),  // 12:00 PM
    ReminderTime(hour: 17, minute: 0),  // 5:00 PM
    ReminderTime(hour: 18, minute: 0),  // 6:00 PM
    ReminderTime(hour: 19, minute: 0),  // 7:00 PM
    ReminderTime(hour: 20, minute: 0),  // 8:00 PM
  ];

  static const List<String> motivationalMessages = [
    'Time for your workout session! 💪',
    'Your body is ready for some action! 🔥',
    'Let\'s crush today\'s workout! 🚀',
    'Fitness time! Your future self will thank you! ⭐',
    'Ready to get stronger? Let\'s go! 💯',
    'Workout reminder: Consistency is key! 🎯',
    'Time to move your body and feel amazing! ✨',
    'Your workout is waiting for you! 🏋️‍♂️',
  ];

  static String getRandomMessage() {
    final random = DateTime.now().millisecondsSinceEpoch % motivationalMessages.length;
    return motivationalMessages[random];
  }
}

/// Notification permission status
enum NotificationPermissionStatus {
  granted,
  denied,
  notDetermined,
  restricted,
}

/// Reminder settings state
class ReminderSettings {
  final bool notificationsEnabled;
  final NotificationPermissionStatus permissionStatus;
  final List<WorkoutReminder> activeReminders;
  final bool soundEnabled;
  final bool vibrationEnabled;

  const ReminderSettings({
    required this.notificationsEnabled,
    required this.permissionStatus,
    required this.activeReminders,
    required this.soundEnabled,
    required this.vibrationEnabled,
  });

  factory ReminderSettings.initial() {
    return const ReminderSettings(
      notificationsEnabled: false,
      permissionStatus: NotificationPermissionStatus.notDetermined,
      activeReminders: [],
      soundEnabled: true,
      vibrationEnabled: true,
    );
  }

  ReminderSettings copyWith({
    bool? notificationsEnabled,
    NotificationPermissionStatus? permissionStatus,
    List<WorkoutReminder>? activeReminders,
    bool? soundEnabled,
    bool? vibrationEnabled,
  }) {
    return ReminderSettings(
      notificationsEnabled: notificationsEnabled ?? this.notificationsEnabled,
      permissionStatus: permissionStatus ?? this.permissionStatus,
      activeReminders: activeReminders ?? this.activeReminders,
      soundEnabled: soundEnabled ?? this.soundEnabled,
      vibrationEnabled: vibrationEnabled ?? this.vibrationEnabled,
    );
  }
}
