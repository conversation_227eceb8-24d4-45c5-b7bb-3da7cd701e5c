import 'package:hooks_riverpod/hooks_riverpod.dart';
import 'package:supabase_flutter/supabase_flutter.dart';
import '../domain/workout_reminder.dart' as reminder_domain;
import '../../shared/utils/app_logger.dart';

/// Provider for workout reminder management
final workoutReminderProvider =
    StateNotifierProvider<WorkoutReminderNotifier, WorkoutReminderState>((ref) {
      return WorkoutReminderNotifier();
    });

/// Provider for reminder settings
final reminderSettingsProvider =
    FutureProvider<reminder_domain.ReminderSettings>((ref) async {
      final notifier = ref.read(workoutReminderProvider.notifier);
      return notifier.getReminderSettings();
    });

/// Workout reminder state
class WorkoutReminderState {
  final List<reminder_domain.WorkoutReminder> reminders;
  final reminder_domain.ReminderSettings settings;
  final bool isLoading;
  final String? error;

  const WorkoutReminderState({
    this.reminders = const [],
    required this.settings,
    this.isLoading = false,
    this.error,
  });

  WorkoutReminderState copyWith({
    List<reminder_domain.WorkoutReminder>? reminders,
    reminder_domain.ReminderSettings? settings,
    bool? isLoading,
    String? error,
  }) {
    return WorkoutReminderState(
      reminders: reminders ?? this.reminders,
      settings: settings ?? this.settings,
      isLoading: isLoading ?? this.isLoading,
      error: error,
    );
  }
}

/// Workout reminder notifier
class WorkoutReminderNotifier extends StateNotifier<WorkoutReminderState> {
  WorkoutReminderNotifier()
    : super(
        WorkoutReminderState(
          settings: reminder_domain.ReminderSettings.initial(),
        ),
      ) {
    _loadReminders();
  }

  final _supabase = Supabase.instance.client;

  /// Load user's workout reminders
  Future<void> _loadReminders() async {
    try {
      state = state.copyWith(isLoading: true, error: null);

      final currentUser = _supabase.auth.currentUser;
      if (currentUser == null) {
        state = state.copyWith(isLoading: false, reminders: []);
        return;
      }

      AppLogger.info(
        '📱 Loading workout reminders for user: ${currentUser.id}',
        tag: 'WORKOUT_REMINDER',
      );

      // TODO: In real implementation, fetch from Supabase
      // For now, use mock data
      final mockReminders = _getMockReminders(currentUser.id);
      final settings = await getReminderSettings();

      state = state.copyWith(
        isLoading: false,
        reminders: mockReminders,
        settings: settings,
      );

      AppLogger.success(
        'Loaded ${mockReminders.length} workout reminders',
        tag: 'WORKOUT_REMINDER',
      );
    } catch (e, stackTrace) {
      AppLogger.error(
        'Failed to load workout reminders',
        tag: 'WORKOUT_REMINDER',
        error: e,
        stackTrace: stackTrace,
      );
      state = state.copyWith(isLoading: false, error: e.toString());
    }
  }

  /// Create a new workout reminder
  Future<void> createReminder({
    required reminder_domain.ReminderTime reminderTime,
    required List<int> weekdays,
    String? title,
    String? message,
  }) async {
    try {
      state = state.copyWith(isLoading: true, error: null);

      final currentUser = _supabase.auth.currentUser;
      if (currentUser == null) {
        throw Exception('User not authenticated');
      }

      AppLogger.userAction(
        'Creating workout reminder for ${reminderTime.toString()}',
        tag: 'WORKOUT_REMINDER',
      );

      final reminder = reminder_domain.WorkoutReminder(
        id: DateTime.now().millisecondsSinceEpoch.toString(),
        userId: currentUser.id,
        reminderTime: reminderTime,
        weekdays: weekdays,
        isEnabled: true,
        title: title ?? 'Workout Time!',
        message: message ?? reminder_domain.ReminderPresets.getRandomMessage(),
        createdAt: DateTime.now(),
      );

      // Schedule the notification
      await _scheduleWorkoutReminder(reminder);

      // TODO: Save to Supabase in real implementation
      // await _supabase.from('workout_reminders').insert(reminder.toJson());

      // Update local state
      final updatedReminders = [...state.reminders, reminder];
      state = state.copyWith(isLoading: false, reminders: updatedReminders);

      AppLogger.success(
        'Workout reminder created successfully',
        tag: 'WORKOUT_REMINDER',
      );
    } catch (e, stackTrace) {
      AppLogger.error(
        'Failed to create workout reminder',
        tag: 'WORKOUT_REMINDER',
        error: e,
        stackTrace: stackTrace,
      );
      state = state.copyWith(isLoading: false, error: e.toString());
    }
  }

  /// Update an existing reminder
  Future<void> updateReminder(
    reminder_domain.WorkoutReminder updatedReminder,
  ) async {
    try {
      state = state.copyWith(isLoading: true, error: null);

      AppLogger.userAction(
        'Updating workout reminder: ${updatedReminder.id}',
        tag: 'WORKOUT_REMINDER',
      );

      // Cancel old notification and schedule new one
      await _cancelWorkoutReminder(updatedReminder.id);
      if (updatedReminder.isEnabled) {
        await _scheduleWorkoutReminder(updatedReminder);
      }

      // TODO: Update in Supabase in real implementation
      // await _supabase.from('workout_reminders').update(updatedReminder.toJson()).eq('id', updatedReminder.id);

      // Update local state
      final updatedReminders =
          state.reminders.map((reminder) {
            return reminder.id == updatedReminder.id
                ? updatedReminder
                : reminder;
          }).toList();

      state = state.copyWith(isLoading: false, reminders: updatedReminders);

      AppLogger.success(
        'Workout reminder updated successfully',
        tag: 'WORKOUT_REMINDER',
      );
    } catch (e, stackTrace) {
      AppLogger.error(
        'Failed to update workout reminder',
        tag: 'WORKOUT_REMINDER',
        error: e,
        stackTrace: stackTrace,
      );
      state = state.copyWith(isLoading: false, error: e.toString());
    }
  }

  /// Delete a reminder
  Future<void> deleteReminder(String reminderId) async {
    try {
      state = state.copyWith(isLoading: true, error: null);

      AppLogger.userAction(
        'Deleting workout reminder: $reminderId',
        tag: 'WORKOUT_REMINDER',
      );

      // Cancel notification
      await _cancelWorkoutReminder(reminderId);

      // TODO: Delete from Supabase in real implementation
      // await _supabase.from('workout_reminders').delete().eq('id', reminderId);

      // Update local state
      final updatedReminders =
          state.reminders
              .where((reminder) => reminder.id != reminderId)
              .toList();
      state = state.copyWith(isLoading: false, reminders: updatedReminders);

      AppLogger.success(
        'Workout reminder deleted successfully',
        tag: 'WORKOUT_REMINDER',
      );
    } catch (e, stackTrace) {
      AppLogger.error(
        'Failed to delete workout reminder',
        tag: 'WORKOUT_REMINDER',
        error: e,
        stackTrace: stackTrace,
      );
      state = state.copyWith(isLoading: false, error: e.toString());
    }
  }

  /// Toggle reminder on/off
  Future<void> toggleReminder(String reminderId) async {
    final reminder = state.reminders.firstWhere((r) => r.id == reminderId);
    final updatedReminder = reminder.copyWith(
      isEnabled: !reminder.isEnabled,
      updatedAt: DateTime.now(),
    );
    await updateReminder(updatedReminder);
  }

  /// Get reminder settings
  Future<reminder_domain.ReminderSettings> getReminderSettings() async {
    try {
      AppLogger.info('📱 Getting reminder settings', tag: 'WORKOUT_REMINDER');

      // TODO: Check actual notification permissions in real implementation
      // For now, return mock settings
      return reminder_domain.ReminderSettings(
        notificationsEnabled: true,
        permissionStatus: reminder_domain.NotificationPermissionStatus.granted,
        activeReminders: state.reminders.where((r) => r.isEnabled).toList(),
        soundEnabled: true,
        vibrationEnabled: true,
      );
    } catch (e) {
      AppLogger.error(
        'Failed to get reminder settings',
        tag: 'WORKOUT_REMINDER',
        error: e,
      );
      return reminder_domain.ReminderSettings.initial();
    }
  }

  /// Request notification permissions
  Future<bool> requestNotificationPermissions() async {
    try {
      AppLogger.userAction(
        'Requesting notification permissions',
        tag: 'WORKOUT_REMINDER',
      );

      // TODO: Implement actual permission request
      // For now, simulate permission granted
      await Future.delayed(const Duration(milliseconds: 500));

      final updatedSettings = state.settings.copyWith(
        permissionStatus: reminder_domain.NotificationPermissionStatus.granted,
        notificationsEnabled: true,
      );

      state = state.copyWith(settings: updatedSettings);

      AppLogger.success(
        'Notification permissions granted',
        tag: 'WORKOUT_REMINDER',
      );
      return true;
    } catch (e) {
      AppLogger.error(
        'Failed to request notification permissions',
        tag: 'WORKOUT_REMINDER',
        error: e,
      );
      return false;
    }
  }

  /// Schedule workout reminder notification (placeholder)
  Future<void> _scheduleWorkoutReminder(
    reminder_domain.WorkoutReminder reminder,
  ) async {
    AppLogger.info(
      '📅 Scheduling workout reminder: ${reminder.formattedTime} on ${reminder.formattedWeekdays}',
      tag: 'WORKOUT_REMINDER',
    );

    // TODO: Implement actual notification scheduling
    // This would integrate with local_notifications, firebase_messaging, or OneSignal

    // Example implementation would be:
    // await _notificationService.scheduleRepeatingNotification(
    //   id: reminder.id.hashCode,
    //   title: reminder.title,
    //   body: reminder.message,
    //   scheduledDate: _getNextScheduledDate(reminder),
    //   repeatInterval: RepeatInterval.weekly,
    // );

    AppLogger.success(
      '✅ Workout reminder scheduled successfully',
      tag: 'WORKOUT_REMINDER',
    );
  }

  /// Cancel workout reminder notification (placeholder)
  Future<void> _cancelWorkoutReminder(String reminderId) async {
    AppLogger.info(
      '❌ Cancelling workout reminder: $reminderId',
      tag: 'WORKOUT_REMINDER',
    );

    // TODO: Implement actual notification cancellation
    // await _notificationService.cancel(reminderId.hashCode);

    AppLogger.success('Workout reminder cancelled', tag: 'WORKOUT_REMINDER');
  }

  /// Get mock reminders for testing
  List<reminder_domain.WorkoutReminder> _getMockReminders(String userId) {
    AppLogger.info(
      '🔄 Creating mock reminders for user: $userId',
      tag: 'WORKOUT_REMINDER',
    );
    return [
      reminder_domain.WorkoutReminder(
        id: '1',
        userId: userId,
        reminderTime: const reminder_domain.ReminderTime(hour: 7, minute: 0),
        weekdays: const [1, 2, 3, 4, 5], // Weekdays
        isEnabled: true,
        title: 'Morning Workout',
        message: 'Start your day with energy! 💪',
        createdAt: DateTime.now().subtract(const Duration(days: 7)),
      ),
      reminder_domain.WorkoutReminder(
        id: '2',
        userId: userId,
        reminderTime: const reminder_domain.ReminderTime(hour: 18, minute: 30),
        weekdays: const [6, 7], // Weekends
        isEnabled: false,
        title: 'Evening Workout',
        message: 'Time to unwind with a good workout! 🌅',
        createdAt: DateTime.now().subtract(const Duration(days: 3)),
      ),
    ];
  }

  /// Refresh reminders
  Future<void> refreshReminders() async {
    await _loadReminders();
  }
}
