import 'package:hooks_riverpod/hooks_riverpod.dart';
import 'package:supabase_flutter/supabase_flutter.dart';
import '../../instructor/domain/student_nutrition_assignment.dart';
import '../../instructor/domain/nutrition_template.dart';
import '../../shared/utils/app_logger.dart';

// Provider for student's nutrition assignment
final studentNutritionAssignmentProvider =
    FutureProvider.family<StudentNutritionAssignment?, String>((
      ref,
      studentId,
    ) async {
      final repository = ref.read(studentNutritionRepositoryProvider);
      return repository.getStudentNutritionAssignment(studentId);
    });

// Repository provider
final studentNutritionRepositoryProvider = Provider<StudentNutritionRepository>(
  (ref) {
    return StudentNutritionRepository();
  },
);

class StudentNutritionRepository {
  final SupabaseClient _supabase = Supabase.instance.client;

  /// Get student's assigned nutrition plan
  Future<StudentNutritionAssignment?> getStudentNutritionAssignment(
    String studentId,
  ) async {
    try {
      AppLogger.info(
        'Fetching nutrition assignment for student: $studentId',
        tag: 'STUDENT_NUTRITION',
      );

      final stopwatch = Stopwatch()..start();
      final response =
          await _supabase
              .from('student_nutrition_assignments')
              .select('''
            *,
            student_nutrition_meals(
              *,
              student_nutrition_meal_items(*)
            )
          ''')
              .eq('student_id', studentId)
              .eq('is_active', true)
              .maybeSingle();

      stopwatch.stop();
      AppLogger.database(
        'SELECT student_nutrition_assignments',
        'student_nutrition_assignments',
        tag: 'STUDENT_NUTRITION',
      );
      AppLogger.performance(
        'Nutrition assignment fetch',
        stopwatch.elapsed,
        tag: 'STUDENT_NUTRITION',
      );

      if (response == null) {
        AppLogger.info(
          'No nutrition assignment found for student',
          tag: 'STUDENT_NUTRITION',
        );
        return null;
      }

      final assignment = _mapToStudentNutritionAssignment(response);
      AppLogger.success(
        'Found nutrition assignment: ${assignment.planName}',
        tag: 'STUDENT_NUTRITION',
      );
      AppLogger.info(
        'Assignment has ${assignment.meals.length} meals with ${assignment.meals.fold(0, (sum, meal) => sum + meal.items.length)} total food items',
        tag: 'STUDENT_NUTRITION',
      );

      return assignment;
    } catch (e) {
      AppLogger.error(
        'Error fetching student nutrition assignment',
        tag: 'STUDENT_NUTRITION',
        error: e,
      );
      return null;
    }
  }

  /// Map database response to StudentNutritionAssignment
  StudentNutritionAssignment _mapToStudentNutritionAssignment(
    Map<String, dynamic> data,
  ) {
    final macrosData = data['macros'] as Map<String, dynamic>? ?? {};

    return StudentNutritionAssignment(
      id: data['id'],
      studentId: data['student_id'],
      instructorId: data['instructor_id'],
      templateId: data['template_id'],
      planName: data['plan_name'],
      description: data['description'],
      notes: data['notes'],
      meals:
          (data['student_nutrition_meals'] as List<dynamic>?)
              ?.map((mealData) => _mapToStudentNutritionMeal(mealData))
              .toList() ??
          [],
      macros: MacronutrientBreakdown(
        proteinPercentage: (macrosData['protein_percentage'] ?? 0.0).toDouble(),
        carbsPercentage: (macrosData['carbs_percentage'] ?? 0.0).toDouble(),
        fatsPercentage: (macrosData['fats_percentage'] ?? 0.0).toDouble(),
      ),
      assignedAt: DateTime.parse(data['assigned_at']),
      startDate:
          data['start_date'] != null
              ? DateTime.parse(data['start_date'])
              : null,
      endDate:
          data['end_date'] != null ? DateTime.parse(data['end_date']) : null,
      isActive: data['is_active'] ?? true,
      createdAt: DateTime.parse(data['created_at']),
      updatedAt: DateTime.parse(data['updated_at']),
    );
  }

  /// Map database response to StudentNutritionMeal
  StudentNutritionMeal _mapToStudentNutritionMeal(Map<String, dynamic> data) {
    return StudentNutritionMeal(
      id: data['id'],
      assignmentId: data['assignment_id'],
      name: data['name'],
      mealType: data['meal_type'],
      orderIndex: data['order_index'],
      description: data['description'],
      items:
          (data['student_nutrition_meal_items'] as List<dynamic>?)
              ?.map((itemData) => _mapToStudentNutritionMealItem(itemData))
              .toList() ??
          [],
      createdAt: DateTime.parse(data['created_at']),
      updatedAt: DateTime.parse(data['updated_at']),
    );
  }

  /// Map database response to StudentNutritionMealItem
  StudentNutritionMealItem _mapToStudentNutritionMealItem(
    Map<String, dynamic> data,
  ) {
    return StudentNutritionMealItem(
      id: data['id'],
      mealId: data['meal_id'],
      foodName: data['food_name'],
      quantity: (data['quantity'] as num).toDouble(),
      unit: data['unit'],
      notes: data['notes'],
      createdAt: DateTime.parse(data['created_at']),
      updatedAt: DateTime.parse(data['updated_at']),
      calories: (data['calories'] as num?)?.toDouble(),
      protein: (data['protein'] as num?)?.toDouble(),
      carbs: (data['carbs'] as num?)?.toDouble(),
      fats: (data['fats'] as num?)?.toDouble(),
    );
  }
}

/// Mock video guide model
class VideoGuide {
  final String id;
  final String title;
  final String duration;
  final String category;
  final String? thumbnailUrl;

  const VideoGuide({
    required this.id,
    required this.title,
    required this.duration,
    required this.category,
    this.thumbnailUrl,
  });
}

/// Mock video guides data
class MockVideoGuides {
  static const List<VideoGuide> breakfast = [
    VideoGuide(
      id: 'breakfast_1',
      title: 'Perfect Eggs Guide',
      duration: '3:00',
      category: 'Breakfast',
    ),
    VideoGuide(
      id: 'breakfast_2',
      title: 'Avocado Toast Tutorial',
      duration: '4:15',
      category: 'Breakfast',
    ),
    VideoGuide(
      id: 'breakfast_3',
      title: 'Breakfast Plating Tips',
      duration: '3:45',
      category: 'Breakfast',
    ),
  ];

  static const List<VideoGuide> lunch = [
    VideoGuide(
      id: 'lunch_1',
      title: 'Grilled Chicken Mastery',
      duration: '5:30',
      category: 'Lunch',
    ),
    VideoGuide(
      id: 'lunch_2',
      title: 'Perfect Rice Cooking',
      duration: '4:00',
      category: 'Lunch',
    ),
    VideoGuide(
      id: 'lunch_3',
      title: 'Vegetable Prep Techniques',
      duration: '6:15',
      category: 'Lunch',
    ),
  ];

  static const List<VideoGuide> dinner = [
    VideoGuide(
      id: 'dinner_1',
      title: 'Protein Portion Control',
      duration: '4:45',
      category: 'Dinner',
    ),
    VideoGuide(
      id: 'dinner_2',
      title: 'Healthy Cooking Methods',
      duration: '7:20',
      category: 'Dinner',
    ),
    VideoGuide(
      id: 'dinner_3',
      title: 'Meal Timing Tips',
      duration: '3:30',
      category: 'Dinner',
    ),
  ];

  static List<VideoGuide> getVideosByCategory(String category) {
    switch (category.toLowerCase()) {
      case 'breakfast':
        return breakfast;
      case 'lunch':
        return lunch;
      case 'dinner':
        return dinner;
      default:
        return [];
    }
  }

  static List<VideoGuide> getAllVideos() {
    return [...breakfast, ...lunch, ...dinner];
  }
}
