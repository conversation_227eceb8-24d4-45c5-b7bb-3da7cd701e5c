import 'package:hooks_riverpod/hooks_riverpod.dart';
import 'package:supabase_flutter/supabase_flutter.dart';
import '../domain/workout_session.dart';
import '../domain/student_workout_plan.dart';
import '../../shared/utils/app_logger.dart';

/// Provider for workout session management
final workoutSessionProvider =
    StateNotifierProvider<WorkoutSessionNotifier, WorkoutSessionState>((ref) {
      return WorkoutSessionNotifier();
    });

/// Provider for workout progress statistics
final workoutProgressStatsProvider = FutureProvider<WorkoutProgressStats>((
  ref,
) async {
  final notifier = ref.read(workoutSessionProvider.notifier);
  return notifier.getProgressStats();
});

/// Provider for workout session history
final workoutSessionHistoryProvider = FutureProvider<List<WorkoutSession>>((
  ref,
) async {
  final notifier = ref.read(workoutSessionProvider.notifier);
  return notifier.getSessionHistory();
});

/// Workout session state
class WorkoutSessionState {
  final WorkoutSession? currentSession;
  final bool isLoading;
  final String? error;
  final List<WorkoutSession> sessionHistory;
  final WorkoutProgressStats? progressStats;

  const WorkoutSessionState({
    this.currentSession,
    this.isLoading = false,
    this.error,
    this.sessionHistory = const [],
    this.progressStats,
  });

  WorkoutSessionState copyWith({
    WorkoutSession? currentSession,
    bool? isLoading,
    String? error,
    List<WorkoutSession>? sessionHistory,
    WorkoutProgressStats? progressStats,
  }) {
    return WorkoutSessionState(
      currentSession: currentSession ?? this.currentSession,
      isLoading: isLoading ?? this.isLoading,
      error: error,
      sessionHistory: sessionHistory ?? this.sessionHistory,
      progressStats: progressStats ?? this.progressStats,
    );
  }
}

/// Workout session notifier
class WorkoutSessionNotifier extends StateNotifier<WorkoutSessionState> {
  WorkoutSessionNotifier() : super(const WorkoutSessionState());

  final _supabase = Supabase.instance.client;

  /// Start a new workout session
  Future<void> startWorkoutSession(
    StudentWorkoutPlan workoutPlan,
    WorkoutPlanDay selectedPlan,
  ) async {
    try {
      state = state.copyWith(isLoading: true, error: null);

      final currentUser = _supabase.auth.currentUser;
      if (currentUser == null) {
        throw Exception('User not authenticated');
      }

      AppLogger.userAction(
        'Starting workout session: ${selectedPlan.name}',
        tag: 'WORKOUT_SESSION',
      );

      // Create new session
      final session = WorkoutSession(
        id: DateTime.now().millisecondsSinceEpoch.toString(),
        userId: currentUser.id,
        workoutPlanId: workoutPlan.id,
        workoutPlanName: '${workoutPlan.planName} - ${selectedPlan.name}',
        startTime: DateTime.now(),
        exerciseResults: [],
        status: WorkoutSessionStatus.inProgress,
        totalExercisesCompleted: 0,
        totalSetsCompleted: 0,
        totalRepsCompleted: 0,
        createdAt: DateTime.now(),
      );

      state = state.copyWith(currentSession: session, isLoading: false);

      AppLogger.success(
        'Workout session started successfully',
        tag: 'WORKOUT_SESSION',
      );
    } catch (e, stackTrace) {
      AppLogger.error(
        'Failed to start workout session',
        tag: 'WORKOUT_SESSION',
        error: e,
        stackTrace: stackTrace,
      );
      state = state.copyWith(isLoading: false, error: e.toString());
    }
  }

  /// Update exercise result in current session
  void updateExerciseResult(ExerciseSessionResult exerciseResult) {
    final currentSession = state.currentSession;
    if (currentSession == null) return;

    AppLogger.userAction(
      'Updating exercise result: ${exerciseResult.exerciseName}',
      tag: 'WORKOUT_SESSION',
    );

    // Update or add exercise result
    final updatedResults = List<ExerciseSessionResult>.from(
      currentSession.exerciseResults,
    );
    final existingIndex = updatedResults.indexWhere(
      (r) => r.exerciseId == exerciseResult.exerciseId,
    );

    if (existingIndex >= 0) {
      updatedResults[existingIndex] = exerciseResult;
    } else {
      updatedResults.add(exerciseResult);
    }

    // Calculate totals
    final totalExercisesCompleted =
        updatedResults.where((r) => r.isCompleted).length;
    final totalSetsCompleted = updatedResults.fold(
      0,
      (sum, r) => sum + r.completedSets,
    );
    final totalRepsCompleted = updatedResults.fold(
      0,
      (sum, r) => sum + r.totalReps,
    );

    final updatedSession = WorkoutSession(
      id: currentSession.id,
      userId: currentSession.userId,
      workoutPlanId: currentSession.workoutPlanId,
      workoutPlanName: currentSession.workoutPlanName,
      startTime: currentSession.startTime,
      endTime: currentSession.endTime,
      totalDuration: currentSession.totalDuration,
      exerciseResults: updatedResults,
      status: currentSession.status,
      totalExercisesCompleted: totalExercisesCompleted,
      totalSetsCompleted: totalSetsCompleted,
      totalRepsCompleted: totalRepsCompleted,
      notes: currentSession.notes,
      createdAt: currentSession.createdAt,
    );

    state = state.copyWith(currentSession: updatedSession);
  }

  /// Complete current workout session
  Future<void> completeWorkoutSession({String? notes}) async {
    try {
      final currentSession = state.currentSession;
      if (currentSession == null) return;

      state = state.copyWith(isLoading: true);

      AppLogger.userAction(
        'Completing workout session',
        tag: 'WORKOUT_SESSION',
      );

      final completedSession = WorkoutSession(
        id: currentSession.id,
        userId: currentSession.userId,
        workoutPlanId: currentSession.workoutPlanId,
        workoutPlanName: currentSession.workoutPlanName,
        startTime: currentSession.startTime,
        endTime: DateTime.now(),
        totalDuration: DateTime.now().difference(currentSession.startTime),
        exerciseResults: currentSession.exerciseResults,
        status: WorkoutSessionStatus.completed,
        totalExercisesCompleted: currentSession.totalExercisesCompleted,
        totalSetsCompleted: currentSession.totalSetsCompleted,
        totalRepsCompleted: currentSession.totalRepsCompleted,
        notes: notes,
        createdAt: currentSession.createdAt,
      );

      // Save to backend (placeholder)
      await _submitWorkoutSession(completedSession);

      // Update session history
      final updatedHistory = [completedSession, ...state.sessionHistory];

      state = state.copyWith(
        currentSession: null,
        isLoading: false,
        sessionHistory: updatedHistory,
      );

      AppLogger.success(
        'Workout session completed successfully',
        tag: 'WORKOUT_SESSION',
      );
    } catch (e, stackTrace) {
      AppLogger.error(
        'Failed to complete workout session',
        tag: 'WORKOUT_SESSION',
        error: e,
        stackTrace: stackTrace,
      );
      state = state.copyWith(isLoading: false, error: e.toString());
    }
  }

  /// Cancel current workout session
  void cancelWorkoutSession() {
    AppLogger.userAction('Cancelling workout session', tag: 'WORKOUT_SESSION');
    state = state.copyWith(currentSession: null);
  }

  /// Submit workout session to backend (placeholder)
  Future<void> _submitWorkoutSession(WorkoutSession session) async {
    // TODO: Implement actual backend submission
    // This is a placeholder for backend integration
    AppLogger.info(
      '📤 Submitting workout session to backend: ${session.workoutPlanName}',
      tag: 'WORKOUT_SESSION',
    );

    // Simulate API call
    await Future.delayed(const Duration(milliseconds: 500));

    // In real implementation, this would save to Supabase:
    // await _supabase.from('workout_sessions').insert(session.toJson());
  }

  /// Get workout progress statistics
  Future<WorkoutProgressStats> getProgressStats() async {
    try {
      AppLogger.info(
        '📊 Fetching workout progress statistics',
        tag: 'WORKOUT_SESSION',
      );

      // TODO: Implement actual backend fetch
      // For now, return mock data
      return _getMockProgressStats();
    } catch (e, stackTrace) {
      AppLogger.error(
        'Failed to fetch progress stats',
        tag: 'WORKOUT_SESSION',
        error: e,
        stackTrace: stackTrace,
      );
      return WorkoutProgressStats.empty();
    }
  }

  /// Get workout session history
  Future<List<WorkoutSession>> getSessionHistory() async {
    try {
      AppLogger.info(
        '📋 Fetching workout session history',
        tag: 'WORKOUT_SESSION',
      );

      // TODO: Implement actual backend fetch
      // For now, return mock data
      return _getMockSessionHistory();
    } catch (e, stackTrace) {
      AppLogger.error(
        'Failed to fetch session history',
        tag: 'WORKOUT_SESSION',
        error: e,
        stackTrace: stackTrace,
      );
      return [];
    }
  }

  /// Mock progress statistics
  WorkoutProgressStats _getMockProgressStats() {
    final now = DateTime.now();
    final dailyStats = List.generate(7, (index) {
      final date = now.subtract(Duration(days: 6 - index));
      final workoutCount = index % 3 == 0 ? 1 : (index % 2 == 0 ? 0 : 1);
      return DailyWorkoutCount(
        date: date,
        workoutCount: workoutCount,
        totalDuration: Duration(minutes: workoutCount * 45),
      );
    });

    return WorkoutProgressStats(
      totalWorkouts: 12,
      totalExercises: 156,
      totalSets: 468,
      totalReps: 2340,
      totalDuration: const Duration(hours: 9, minutes: 30),
      lastWorkoutDate: now.subtract(const Duration(days: 1)),
      currentWeekWorkouts: 3,
      currentMonthWorkouts: 12,
      dailyStats: dailyStats,
    );
  }

  /// Mock session history
  List<WorkoutSession> _getMockSessionHistory() {
    final now = DateTime.now();
    return [
      WorkoutSession(
        id: '1',
        userId: 'user1',
        workoutPlanId: 'plan1',
        workoutPlanName: 'Push Day Workout',
        startTime: now.subtract(const Duration(days: 1, hours: 2)),
        endTime: now.subtract(const Duration(days: 1, hours: 1)),
        totalDuration: const Duration(hours: 1),
        exerciseResults: [],
        status: WorkoutSessionStatus.completed,
        totalExercisesCompleted: 5,
        totalSetsCompleted: 15,
        totalRepsCompleted: 120,
        createdAt: now.subtract(const Duration(days: 1)),
      ),
      WorkoutSession(
        id: '2',
        userId: 'user1',
        workoutPlanId: 'plan2',
        workoutPlanName: 'Pull Day Workout',
        startTime: now.subtract(const Duration(days: 3, hours: 2)),
        endTime: now.subtract(const Duration(days: 3, hours: 1)),
        totalDuration: const Duration(minutes: 55),
        exerciseResults: [],
        status: WorkoutSessionStatus.completed,
        totalExercisesCompleted: 4,
        totalSetsCompleted: 12,
        totalRepsCompleted: 96,
        createdAt: now.subtract(const Duration(days: 3)),
      ),
    ];
  }
}
