import 'package:hooks_riverpod/hooks_riverpod.dart';
import 'package:supabase_flutter/supabase_flutter.dart';
import 'package:fitgo_app/src/student/domain/trainer_detail.dart';

// Domain model for paginated reviews state
class PaginatedReviewsState {
  final List<Review> reviews;
  final bool isLoading;
  final bool hasMore;
  final String? error;
  final int currentPage;

  const PaginatedReviewsState({
    this.reviews = const [],
    this.isLoading = false,
    this.hasMore = true,
    this.error,
    this.currentPage = 0,
  });

  PaginatedReviewsState copyWith({
    List<Review>? reviews,
    bool? isLoading,
    bool? hasMore,
    String? error,
    int? currentPage,
  }) {
    return PaginatedReviewsState(
      reviews: reviews ?? this.reviews,
      isLoading: isLoading ?? this.isLoading,
      hasMore: hasMore ?? this.hasMore,
      error: error ?? this.error,
      currentPage: currentPage ?? this.currentPage,
    );
  }
}

// Repository for paginated reviews following DDD pattern
class PaginatedReviewsRepository {
  final SupabaseClient _supabase = Supabase.instance.client;
  static const int _pageSize = 10;

  Future<List<Review>> getReviewsPage({
    required String trainerId,
    required int page,
  }) async {
    final startIndex = page * _pageSize;
    final endIndex = startIndex + _pageSize - 1;

    final response = await _supabase
        .from('instructor_reviews')
        .select('''
          id,
          client_name,
          avatar_url,
          rating,
          comment,
          created_at
        ''')
        .eq('instructor_id', trainerId)
        .order('created_at', ascending: false)
        .range(startIndex, endIndex);

    return (response as List<dynamic>)
        .map((item) => Review.fromJson(item as Map<String, dynamic>))
        .toList();
  }

  int get pageSize => _pageSize;
}

// Application layer - StateNotifier for business logic
class PaginatedReviewsNotifier extends StateNotifier<PaginatedReviewsState> {
  final String trainerId;
  final PaginatedReviewsRepository _repository;

  PaginatedReviewsNotifier({
    required this.trainerId,
    required PaginatedReviewsRepository repository,
  }) : _repository = repository,
       super(const PaginatedReviewsState());

  Future<void> loadInitialReviews() async {
    if (state.isLoading) return;

    state = state.copyWith(isLoading: true, error: null);

    try {
      final reviews = await _repository.getReviewsPage(
        trainerId: trainerId,
        page: 0,
      );

      state = state.copyWith(
        reviews: reviews,
        isLoading: false,
        hasMore: reviews.length == _repository.pageSize,
        currentPage: 1,
      );
    } catch (e) {
      state = state.copyWith(isLoading: false, error: e.toString());
    }
  }

  Future<void> loadNextPage() async {
    if (state.isLoading || !state.hasMore) return;

    state = state.copyWith(isLoading: true);

    try {
      final newReviews = await _repository.getReviewsPage(
        trainerId: trainerId,
        page: state.currentPage,
      );

      final allReviews = [...state.reviews, ...newReviews];

      state = state.copyWith(
        reviews: allReviews,
        isLoading: false,
        hasMore: newReviews.length == _repository.pageSize,
        currentPage: state.currentPage + 1,
      );
    } catch (e) {
      state = state.copyWith(isLoading: false, error: e.toString());
    }
  }

  void reset() {
    state = const PaginatedReviewsState();
  }
}

// Provider layer following established patterns
final paginatedReviewsRepositoryProvider = Provider<PaginatedReviewsRepository>(
  (ref) {
    return PaginatedReviewsRepository();
  },
);

final paginatedReviewsProvider = StateNotifierProvider.family<
  PaginatedReviewsNotifier,
  PaginatedReviewsState,
  String
>((ref, trainerId) {
  final repository = ref.read(paginatedReviewsRepositoryProvider);
  return PaginatedReviewsNotifier(trainerId: trainerId, repository: repository);
});
