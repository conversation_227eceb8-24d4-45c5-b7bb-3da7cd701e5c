import 'package:fitgo_app/core/local_storage/hive_helper.dart';
import 'package:fitgo_app/core/local_storage/storage_key.dart';
import 'package:fitgo_app/src/shared/utils/app_logger.dart';
import 'package:supabase_flutter/supabase_flutter.dart';

enum StudentWorkflowState {
  needsCourseSelection,
  needsProfileForm,
  waitingForPlan,
  hasActivePlan,
}

class StudentWorkflowService {
  final SupabaseClient _supabase;
  final HiveHelper _hiveHelper;

  StudentWorkflowService({
    required SupabaseClient supabase,
    required HiveHelper hiveHelper,
  })  : _supabase = supabase,
        _hiveHelper = hiveHelper;

  /// Get current student workflow state
  Future<StudentWorkflowState> getCurrentWorkflowState() async {
    try {
      final currentUser = _supabase.auth.currentUser;
      if (currentUser == null) {
        return StudentWorkflowState.needsCourseSelection;
      }

      AppLogger.info(
        'Getting workflow state for user: ${currentUser.id}',
        tag: 'STUDENT_WORKFLOW',
      );

      // First check if we have a stored state for persistence
      final storedState = getStoredWorkflowState();
      if (storedState != null) {
        AppLogger.info(
          'Found stored workflow state: $storedState',
          tag: 'STUDENT_WORKFLOW',
        );

        // Validate the stored state against current database state
        final isValidState = await _validateStoredState(
          storedState,
          currentUser.id,
        );
        if (isValidState) {
          AppLogger.info(
            'Stored state is valid, using: $storedState',
            tag: 'STUDENT_WORKFLOW',
          );
          return storedState;
        } else {
          AppLogger.info(
            'Stored state is invalid, recalculating...',
            tag: 'STUDENT_WORKFLOW',
          );
        }
      }

      // Calculate fresh state from database
      return await _calculateFreshWorkflowState(currentUser.id);
    } catch (e) {
      AppLogger.error(
        'Error getting workflow state',
        tag: 'STUDENT_WORKFLOW',
        error: e,
      );
      return StudentWorkflowState.needsCourseSelection;
    }
  }

  /// Calculate fresh workflow state from database
  Future<StudentWorkflowState> _calculateFreshWorkflowState(
    String userId,
  ) async {
    // Check payment status
    final hasPayment = await _checkPaymentStatus(userId);
    AppLogger.info('Payment status: $hasPayment', tag: 'STUDENT_WORKFLOW');

    if (!hasPayment) {
      await _saveWorkflowState(StudentWorkflowState.needsCourseSelection);
      return StudentWorkflowState.needsCourseSelection;
    }

    // Check profile form status
    final hasProfileForm = await _checkProfileFormStatus(userId);
    AppLogger.info(
      'Profile form status: $hasProfileForm',
      tag: 'STUDENT_WORKFLOW',
    );

    if (!hasProfileForm) {
      // Save state to local storage
      await _saveWorkflowState(StudentWorkflowState.needsProfileForm);
      return StudentWorkflowState.needsProfileForm;
    }

    // Check plan assignment status
    final hasPlan = await _checkPlanAssignmentStatus(userId);
    AppLogger.info('Plan assignment status: $hasPlan', tag: 'STUDENT_WORKFLOW');

    if (!hasPlan) {
      await _saveWorkflowState(StudentWorkflowState.waitingForPlan);
      return StudentWorkflowState.waitingForPlan;
    }

    await _saveWorkflowState(StudentWorkflowState.hasActivePlan);
    return StudentWorkflowState.hasActivePlan;
  }

  /// Validate if stored state is still accurate
  Future<bool> _validateStoredState(
    StudentWorkflowState storedState,
    String userId,
  ) async {
    try {
      switch (storedState) {
        case StudentWorkflowState.needsCourseSelection:
          // Always recalculate for course selection state
          return false;

        case StudentWorkflowState.needsProfileForm:
          // Check if payment is still valid and form is still not completed
          final hasPayment = await _checkPaymentStatus(userId);
          final hasProfileForm = await _checkProfileFormStatus(userId);
          return hasPayment && !hasProfileForm;

        case StudentWorkflowState.waitingForPlan:
          // Check if payment and form are completed but no plan assigned
          final hasPayment = await _checkPaymentStatus(userId);
          final hasProfileForm = await _checkProfileFormStatus(userId);
          final hasPlan = await _checkPlanAssignmentStatus(userId);
          return hasPayment && hasProfileForm && !hasPlan;

        case StudentWorkflowState.hasActivePlan:
          // Check if all conditions are met
          final hasPayment = await _checkPaymentStatus(userId);
          final hasProfileForm = await _checkProfileFormStatus(userId);
          final hasPlan = await _checkPlanAssignmentStatus(userId);
          return hasPayment && hasProfileForm && hasPlan;
      }
    } catch (e) {
      AppLogger.error(
        'Error validating stored state',
        tag: 'STUDENT_WORKFLOW',
        error: e,
      );
      return false;
    }
  }

  /// Check if user has completed payment
  Future<bool> _checkPaymentStatus(String userId) async {
    try {
      // Check if user has active enrollment
      final enrollmentResponse = await _supabase
          .from('enrollments')
          .select('id')
          .eq('student_id', userId)
          .eq('is_active', true)
          .limit(1);

      if (enrollmentResponse.isEmpty) {
        AppLogger.info(
          'Payment check: No active enrollment found',
          tag: 'STUDENT_WORKFLOW',
        );
        return false;
      }

      final enrollmentId = enrollmentResponse.first['id'] as String;

      // Check if there's a completed payment for this enrollment
      final paymentResponse = await _supabase
          .from('payment_transactions')
          .select('id, payment_status')
          .eq('relationship_id', enrollmentId)
          .eq('payment_status', 'completed')
          .limit(1);

      AppLogger.info(
        'Payment check: ${paymentResponse.length} completed payments found for enrollment $enrollmentId',
        tag: 'STUDENT_WORKFLOW',
      );

      return paymentResponse.isNotEmpty;
    } catch (e) {
      AppLogger.error(
        'Error checking payment status',
        tag: 'STUDENT_WORKFLOW',
        error: e,
      );
      return false;
    }
  }

  /// Check if user has completed profile form
  Future<bool> _checkProfileFormStatus(String userId) async {
    try {
      // Check if user has filled the profile form in user_profiles table
      final response = await _supabase
          .from('user_profiles')
          .select('id, fitness_goals, activity_level')
          .eq('user_id', userId)
          .not('fitness_goals', 'is', null)
          .not('activity_level', 'is', null)
          .limit(1);

      AppLogger.info(
        'Profile form check for user $userId: ${response.length} completed forms found',
        tag: 'STUDENT_WORKFLOW',
      );

      return response.isNotEmpty;
    } catch (e) {
      AppLogger.error(
        'Error checking profile form status',
        tag: 'STUDENT_WORKFLOW',
        error: e,
      );
      return false;
    }
  }

  /// Check if user has assigned plan
  Future<bool> _checkPlanAssignmentStatus(String userId) async {
    try {
      // Check for workout plans
      final workoutResponse = await _supabase
          .from('student_workout_plans')
          .select('id')
          .eq('student_id', userId)
          .limit(1);

      if (workoutResponse.isNotEmpty) {
        return true;
      }

      // Check for nutrition plans
      final nutritionResponse = await _supabase
          .from('student_nutrition_plans')
          .select('id')
          .eq('student_id', userId)
          .limit(1);

      return nutritionResponse.isNotEmpty;
    } catch (e) {
      AppLogger.error(
        'Error checking plan assignment status',
        tag: 'STUDENT_WORKFLOW',
        error: e,
      );
      return false;
    }
  }

  /// Save workflow state to local storage
  Future<void> _saveWorkflowState(StudentWorkflowState state) async {
    try {
      await _hiveHelper.writeString(
        key: StorageKey.studentWorkflowState,
        value: state.name,
      );
      AppLogger.info(
        'Workflow state saved: ${state.name}',
        tag: 'STUDENT_WORKFLOW',
      );
    } catch (e) {
      AppLogger.error(
        'Error saving workflow state',
        tag: 'STUDENT_WORKFLOW',
        error: e,
      );
    }
  }

  /// Get workflow state from local storage
  StudentWorkflowState? getStoredWorkflowState() {
    try {
      final stateName = _hiveHelper.readString(
        key: StorageKey.studentWorkflowState,
      );

      if (stateName != null) {
        return StudentWorkflowState.values.firstWhere(
          (state) => state.name == stateName,
          orElse: () => StudentWorkflowState.needsCourseSelection,
        );
      }

      return null;
    } catch (e) {
      AppLogger.error(
        'Error getting stored workflow state',
        tag: 'STUDENT_WORKFLOW',
        error: e,
      );
      return null;
    }
  }

  /// Clear workflow state (for logout)
  Future<void> clearWorkflowState() async {
    try {
      await _hiveHelper.delete(key: StorageKey.studentWorkflowState);
      AppLogger.info('Workflow state cleared', tag: 'STUDENT_WORKFLOW');
    } catch (e) {
      AppLogger.error(
        'Error clearing workflow state',
        tag: 'STUDENT_WORKFLOW',
        error: e,
      );
    }
  }

  /// Mark payment as completed
  Future<void> markPaymentCompleted() async {
    await _saveWorkflowState(StudentWorkflowState.needsProfileForm);
  }

  /// Mark profile form as completed
  Future<void> markProfileFormCompleted() async {
    await _saveWorkflowState(StudentWorkflowState.waitingForPlan);
  }

  /// Mark plan as assigned
  Future<void> markPlanAssigned() async {
    await _saveWorkflowState(StudentWorkflowState.hasActivePlan);
  }

  /// Force refresh workflow state (for debugging)
  Future<StudentWorkflowState> forceRefreshWorkflowState() async {
    try {
      final currentUser = _supabase.auth.currentUser;
      if (currentUser == null) {
        return StudentWorkflowState.needsCourseSelection;
      }

      AppLogger.info(
        'Force refreshing workflow state for user: ${currentUser.id}',
        tag: 'STUDENT_WORKFLOW',
      );

      // Clear stored state
      await clearWorkflowState();

      // Calculate fresh state from database
      return await _calculateFreshWorkflowState(currentUser.id);
    } catch (e) {
      AppLogger.error(
        'Error force refreshing workflow state',
        tag: 'STUDENT_WORKFLOW',
        error: e,
      );
      return StudentWorkflowState.needsCourseSelection;
    }
  }
}
