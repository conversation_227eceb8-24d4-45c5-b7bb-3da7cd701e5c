import 'package:fitgo_app/core/local_storage/hive_provider.dart';
import 'package:fitgo_app/src/shared/providers/supabase_provider.dart';
import 'package:fitgo_app/src/student/application/student_workflow_service.dart';
import 'package:hooks_riverpod/hooks_riverpod.dart';

/// Provider for student workflow service
final studentWorkflowServiceProvider = Provider<StudentWorkflowService>((ref) {
  final supabaseService = ref.watch(supabaseServiceProvider);
  final hiveHelper = ref.watch(hiveProvider);

  return StudentWorkflowService(
    supabase: supabaseService.client,
    hiveHelper: hiveHelper,
  );
});

/// Provider for getting current workflow state
final studentWorkflowStateProvider = FutureProvider<StudentWorkflowState>((
  ref,
) async {
  final service = ref.watch(studentWorkflowServiceProvider);
  return await service.getCurrentWorkflowState();
});
