import 'package:hooks_riverpod/hooks_riverpod.dart';
import 'package:supabase_flutter/supabase_flutter.dart';
import '../../instructor/domain/supplement_models.dart';
import '../../shared/utils/app_logger.dart';

// Student supplement repository
final studentSupplementRepositoryProvider = Provider<StudentSupplementRepository>((ref) {
  return StudentSupplementRepository();
});

// Student's assigned supplements provider
final studentAssignedSupplementsProvider = FutureProvider<List<StudentSupplementAssignment>>((ref) async {
  final repository = ref.read(studentSupplementRepositoryProvider);
  final currentUser = Supabase.instance.client.auth.currentUser;
  
  if (currentUser == null) {
    throw Exception('User not logged in');
  }
  
  return repository.getStudentAssignedSupplements(currentUser.id);
});

// Student supplement repository
class StudentSupplementRepository {
  final SupabaseClient _supabase = Supabase.instance.client;

  /// Get student's assigned supplements
  Future<List<StudentSupplementAssignment>> getStudentAssignedSupplements(String studentId) async {
    try {
      final response = await _supabase
          .from('student_supplement_assignments')
          .select('''
            *,
            supplements (
              id,
              name,
              brand,
              description,
              type,
              category,
              image_url,
              benefits,
              ingredients,
              warnings
            )
          ''')
          .eq('student_id', studentId)
          .order('assigned_at', ascending: false);

      AppLogger.success(
        'Loaded ${(response as List).length} supplement assignments for student',
        tag: 'STUDENT_SUPPLEMENT_REPO',
      );

      return (response as List)
          .map((json) => StudentSupplementAssignment.fromJson(json))
          .toList();
    } catch (e) {
      AppLogger.error('Failed to fetch student supplements: $e', tag: 'STUDENT_SUPPLEMENT_REPO');
      rethrow;
    }
  }

  /// Mark supplement as taken
  Future<void> markSupplementAsTaken(String assignmentId) async {
    try {
      await _supabase
          .from('student_supplement_assignments')
          .update({
            'last_taken_at': DateTime.now().toIso8601String(),
          })
          .eq('id', assignmentId);

      AppLogger.success(
        'Marked supplement as taken: $assignmentId',
        tag: 'STUDENT_SUPPLEMENT_REPO',
      );
    } catch (e) {
      AppLogger.error('Failed to mark supplement as taken: $e', tag: 'STUDENT_SUPPLEMENT_REPO');
      rethrow;
    }
  }

  /// Get supplement intake history
  Future<List<SupplementIntakeRecord>> getSupplementIntakeHistory(String studentId) async {
    try {
      // This would require a separate table for tracking intake history
      // For now, we'll return empty list
      return [];
    } catch (e) {
      AppLogger.error('Failed to fetch supplement intake history: $e', tag: 'STUDENT_SUPPLEMENT_REPO');
      rethrow;
    }
  }
}

// Supplement intake record model
class SupplementIntakeRecord {
  final String id;
  final String assignmentId;
  final DateTime takenAt;
  final String? notes;

  const SupplementIntakeRecord({
    required this.id,
    required this.assignmentId,
    required this.takenAt,
    this.notes,
  });

  factory SupplementIntakeRecord.fromJson(Map<String, dynamic> json) {
    return SupplementIntakeRecord(
      id: json['id'] as String,
      assignmentId: json['assignment_id'] as String,
      takenAt: DateTime.parse(json['taken_at'] as String),
      notes: json['notes'] as String?,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'assignment_id': assignmentId,
      'taken_at': takenAt.toIso8601String(),
      'notes': notes,
    };
  }
}
