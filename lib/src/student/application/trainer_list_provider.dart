import 'package:hooks_riverpod/hooks_riverpod.dart';
import 'package:supabase_flutter/supabase_flutter.dart';
import 'package:fitgo_app/src/student/domain/trainer_list.dart';
import 'package:flutter/foundation.dart';

// Constants for pagination
const int kTrainersPerPage = 10;

// State class for instructor list with pagination
class InstructorListState {
  final List<InstructorListItem> instructors;
  final bool isLoading;
  final bool isLoadingMore;
  final String? error;
  final bool hasMore;
  final int currentPage;
  final TrainerListFilter filter;

  const InstructorListState({
    this.instructors = const [],
    this.isLoading = false,
    this.isLoadingMore = false,
    this.error,
    this.hasMore = true,
    this.currentPage = 0,
    this.filter = const TrainerListFilter(),
  });

  InstructorListState copyWith({
    List<InstructorListItem>? instructors,
    bool? isLoading,
    bool? isLoadingMore,
    String? error,
    bool? hasMore,
    int? currentPage,
    TrainerListFilter? filter,
  }) {
    return InstructorListState(
      instructors: instructors ?? this.instructors,
      isLoading: isLoading ?? this.isLoading,
      isLoadingMore: isLoadingMore ?? this.isLoadingMore,
      error: error ?? this.error,
      hasMore: hasMore ?? this.hasMore,
      currentPage: currentPage ?? this.currentPage,
      filter: filter ?? this.filter,
    );
  }

  /// Backward compatibility getter
  List<InstructorListItem> get trainers => instructors;
}

// Provider for instructor list state
final instructorListProvider =
    StateNotifierProvider<InstructorListNotifier, InstructorListState>((ref) {
  return InstructorListNotifier(ref.read(instructorListRepositoryProvider));
});

// Backward compatibility provider
final trainerListProvider = instructorListProvider;

// Repository provider
final instructorListRepositoryProvider = Provider<InstructorListRepository>((
  ref,
) {
  return InstructorListRepository();
});

// Backward compatibility provider
final trainerListRepositoryProvider = instructorListRepositoryProvider;

class InstructorListNotifier extends StateNotifier<InstructorListState> {
  final InstructorListRepository _repository;

  InstructorListNotifier(this._repository)
      : super(const InstructorListState()) {
    loadTrainers();
  }

  Future<void> loadTrainers({bool refresh = false}) async {
    if (refresh) {
      state = state.copyWith(
        instructors: [],
        currentPage: 0,
        hasMore: true,
        error: null,
      );
    }

    if (state.isLoading || state.isLoadingMore) return;

    state = state.copyWith(
      isLoading: refresh || state.instructors.isEmpty,
      error: null,
    );

    try {
      final pageToLoad = refresh ? 0 : state.currentPage;
      final response = await _repository.getInstructors(
        page: pageToLoad,
        filter: state.filter,
      );

      state = state.copyWith(
        instructors: refresh
            ? response.instructors
            : [...state.instructors, ...response.instructors],
        hasMore: response.hasMore,
        currentPage: pageToLoad,
        isLoading: false,
      );
    } catch (e) {
      state = state.copyWith(error: e.toString(), isLoading: false);
    }
  }

  Future<void> loadMoreTrainers() async {
    if (!state.hasMore || state.isLoading || state.isLoadingMore) return;

    state = state.copyWith(isLoadingMore: true, error: null);

    try {
      final nextPage = state.currentPage + 1;
      final response = await _repository.getInstructors(
        page: nextPage,
        filter: state.filter,
      );

      state = state.copyWith(
        instructors: [...state.instructors, ...response.instructors],
        hasMore: response.hasMore,
        currentPage: nextPage,
        isLoadingMore: false,
      );
    } catch (e) {
      state = state.copyWith(error: e.toString(), isLoadingMore: false);
    }
  }

  Future<void> applyFilter(TrainerListFilter filter) async {
    state = state.copyWith(filter: filter);
    await loadTrainers(refresh: true);
  }

  void clearError() {
    state = state.copyWith(error: null);
  }
}

class InstructorListRepository {
  final SupabaseClient _supabase = Supabase.instance.client;

  Future<InstructorListResponse> getInstructors({
    int page = 0,
    TrainerListFilter filter = const TrainerListFilter(),
  }) async {
    try {
      final startIndex = page * kTrainersPerPage;
      final endIndex = startIndex + kTrainersPerPage - 1;

      // Build query with filters - only show public instructors
      var query = _supabase.from('instructors').select('''
            id,
            profile_id,
            photo_url,
            bio,
            experience_years,
            rating,
            title,
            is_public,
            profiles!instructors_profile_id_fkey(
              name,
              surname
            ),
            instructor_work_history!instructor_work_history_instructor_id_fkey(
              company_name,
              role,
              is_current
            )
          ''').eq('is_public', true);

      // Apply filters (✅ CLEANED: Search in profiles.name)
      if (filter.searchQuery != null && filter.searchQuery!.isNotEmpty) {
        query = query.ilike('profiles.name', '%${filter.searchQuery}%');
      }

      // Note: Specialization filtering removed as specializations column doesn't exist
      // TODO: Implement specialization filtering using a separate table or instructor_specializations junction table

      if (filter.minRating != null) {
        query = query.gte('rating', filter.minRating!);
      }

      if (filter.maxPrice != null) {
        query = query.lte('price_per_session', filter.maxPrice!);
      }

      // Apply sorting
      final sortedQuery = query.order(
        filter.sortBy,
        ascending: filter.ascending,
      );

      // Apply pagination
      final response = await sortedQuery.range(startIndex, endIndex);

      // Get total count for pagination - only public and approved instructors
      final countResponse = await _supabase.from('instructors').select('''
            id,
            profile_id
          ''').eq('is_public', true);

      // Get profile IDs for count query
      final countInstructorIds = (countResponse as List<dynamic>)
          .map((instructor) => instructor['profile_id'] as String)
          .toList();

      // Fetch subscription configs to count approved instructors
      final countConfigs = await _fetchSubscriptionConfigs(countInstructorIds);
      final totalCount =
          countConfigs.length; // Only approved configs are returned

      // Fetch subscription configs for pricing - use profile_id as foreign key
      final instructorIds = (response as List<dynamic>)
          .map((instructor) => instructor['profile_id'] as String)
          .toList();

      // Fetch actual enrollment counts
      final enrollmentCounts = <String, int>{};
      if (instructorIds.isNotEmpty) {
        final enrollmentResponse = await _supabase
            .from('enrollments')
            .select('instructor_id')
            .eq('is_active', true)
            .inFilter('instructor_id', instructorIds);

        for (final enrollment in enrollmentResponse) {
          final instructorId = enrollment['instructor_id'] as String;
          enrollmentCounts[instructorId] =
              (enrollmentCounts[instructorId] ?? 0) + 1;
        }
      }
      final subscriptionConfigs = await _fetchSubscriptionConfigs(
        instructorIds,
      );

      // Filter instructors to only include those with approved subscription configs
      final instructors = (response as List<dynamic>).where((item) {
        final json = item as Map<String, dynamic>;
        final profileId = json['profile_id'] as String;
        final config = subscriptionConfigs[profileId];
        final isApproved = config != null; // Only approved configs are fetched
        return isApproved; // Only show approved instructors
      }).map((item) {
        final json = item as Map<String, dynamic>;
        final profileId = json['profile_id'] as String;
        final config = subscriptionConfigs[profileId];
        // Add pricing info to json
        if (config != null) {
          json['basic_plan_monthly_price'] = config['basic_plan_monthly_price'];
          json['premium_plan_monthly_price'] =
              config['premium_plan_monthly_price'];
        }
        // Add actual enrollment count (use instructor id for enrollments)
        final instructorId = json['id'] as String;
        json['current_students'] = enrollmentCounts[instructorId] ?? 0;
        return InstructorListItem.fromJson(json);
      }).toList();

      final hasMore = (startIndex + instructors.length) < totalCount;

      return InstructorListResponse(
        instructors: instructors,
        totalCount: totalCount,
        hasMore: hasMore,
        currentPage: page,
      );
    } on PostgrestException catch (e) {
      throw Exception('Failed to fetch trainers: ${e.message}');
    } catch (e) {
      throw Exception('Unexpected error: $e');
    }
  }

  /// Fetch subscription configs for multiple instructors
  Future<Map<String, Map<String, dynamic>>> _fetchSubscriptionConfigs(
    List<String> instructorIds,
  ) async {
    if (instructorIds.isEmpty) return {};

    try {
      // For now, query each instructor individually to avoid inFilter issues
      final List<Map<String, dynamic>> response = [];
      for (final instructorId in instructorIds) {
        final configResponse = await _supabase
            .from('instructor_subscription_configs')
            .select(
              'instructor_id, desired_monthly_earnings, basic_plan_monthly_price, premium_plan_monthly_price, approval_status',
            )
            .eq('instructor_id', instructorId)
            .eq('approval_status', 'approved')
            .maybeSingle();

        if (configResponse != null) {
          response.add(configResponse);
        }
      }

      final configs = <String, Map<String, dynamic>>{};

      for (final config in response) {
        final instructorId = config['instructor_id'] as String;

        // Get pricing from individual columns (new structure)
        double basicPrice = 200.0; // Default fallback
        double premiumPrice = 300.0; // Default fallback

        // Get pricing from new column structure
        if (config['basic_plan_monthly_price'] != null) {
          basicPrice =
              double.tryParse(config['basic_plan_monthly_price'].toString()) ??
                  200.0;
        }

        if (config['premium_plan_monthly_price'] != null) {
          premiumPrice = double.tryParse(
                  config['premium_plan_monthly_price'].toString()) ??
              300.0;
        }

        configs[instructorId] = {
          'basic_plan_monthly_price': basicPrice,
          'premium_plan_monthly_price': premiumPrice,
        };
      }
      return configs;
    } catch (e) {
      debugPrint('Error fetching subscription configs: $e');
      return {};
    }
  }

  // Note: Sample instructor creation method removed as it used deprecated fields
}

/// Backward compatibility alias
typedef TrainerListRepository = InstructorListRepository;
typedef TrainerListState = InstructorListState;
typedef TrainerListNotifier = InstructorListNotifier;
typedef TrainerListResponse = InstructorListResponse;
