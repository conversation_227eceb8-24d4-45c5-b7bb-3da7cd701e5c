import 'package:hooks_riverpod/hooks_riverpod.dart';
import 'package:supabase_flutter/supabase_flutter.dart';
import '../domain/student_workout_plan.dart';
import '../../shared/utils/app_logger.dart';

// Repository provider
final studentWorkoutRepositoryProvider = Provider<StudentWorkoutRepository>((
  ref,
) {
  return StudentWorkoutRepository(Supabase.instance.client);
});

// Student workout plan provider
final studentWorkoutPlanProvider =
    FutureProvider.family<StudentWorkoutPlan?, String>((ref, studentId) async {
      final repository = ref.read(studentWorkoutRepositoryProvider);
      return repository.getStudentWorkoutPlan(studentId);
    });

// Repository class
class StudentWorkoutRepository {
  final SupabaseClient _supabase;

  StudentWorkoutRepository(this._supabase);

  /// Get student's assigned workout plan
  Future<StudentWorkoutPlan?> getStudentWorkoutPlan(String studentId) async {
    try {
      AppLogger.workout(
        'Fetching workout plan for student: $studentId',
        tag: 'STUDENT',
      );

      // Get the student's workout assignment
      final stopwatch = Stopwatch()..start();
      final response =
          await _supabase
              .from('student_workout_plans')
              .select('''
            *,
            instructor:profiles!instructor_id(
              id,
              name,
              surname
            )
          ''')
              .eq('student_id', studentId)
              .eq('is_active', true)
              .maybeSingle();

      stopwatch.stop();
      AppLogger.database(
        'SELECT student_workout_plans',
        'student_workout_plans',
        tag: 'STUDENT',
      );
      AppLogger.performance(
        'Workout plan fetch',
        stopwatch.elapsed,
        tag: 'STUDENT',
      );

      if (response == null) {
        AppLogger.info('No workout plan found for student', tag: 'STUDENT');
        return null;
      }

      final planTitle = response['title'] as String;
      AppLogger.success('Found workout plan: $planTitle', tag: 'STUDENT');

      // Debug the raw response
      AppLogger.info(
        '📄 Raw response keys: ${response.keys.toList()}',
        tag: 'STUDENT',
      );
      AppLogger.info(
        '📋 Plan data type: ${response['plan_data'].runtimeType}',
        tag: 'STUDENT',
      );

      final workoutPlan = StudentWorkoutPlan.fromJson(response);
      AppLogger.info(
        'Parsed ${workoutPlan.plans.length} workout plans with ${workoutPlan.plans.fold(0, (sum, plan) => sum + plan.exercises.length)} total exercises',
        tag: 'STUDENT',
      );

      return workoutPlan;
    } catch (e) {
      AppLogger.error(
        'Error fetching student workout plan',
        tag: 'STUDENT',
        error: e,
      );
      throw Exception('Failed to fetch workout plan: $e');
    }
  }

  /// Mark exercise as completed
  Future<void> markExerciseCompleted({
    required String planId,
    required String exerciseId,
    required DateTime completedAt,
  }) async {
    try {
      AppLogger.workout(
        'Marking exercise $exerciseId as completed',
        tag: 'STUDENT',
      );

      // Update exercise completion in plan_data
      final response =
          await _supabase
              .from('student_workout_plans')
              .select('plan_data')
              .eq('id', planId)
              .single();

      final planData = Map<String, dynamic>.from(response['plan_data'] ?? {});

      // Add completion tracking
      if (!planData.containsKey('completions')) {
        planData['completions'] = <String, dynamic>{};
      }

      planData['completions'][exerciseId] = {
        'completed_at': completedAt.toIso8601String(),
        'date': DateTime.now().toIso8601String().split('T')[0], // YYYY-MM-DD
      };

      await _supabase
          .from('student_workout_plans')
          .update({
            'plan_data': planData,
            'updated_at': DateTime.now().toIso8601String(),
          })
          .eq('id', planId);

      AppLogger.success(
        'Exercise completion recorded for $exerciseId',
        tag: 'STUDENT',
      );
      AppLogger.database(
        'UPDATE student_workout_plans',
        'student_workout_plans',
        tag: 'STUDENT',
      );
    } catch (e) {
      AppLogger.error(
        'Error marking exercise completed',
        tag: 'STUDENT',
        error: e,
      );
      throw Exception('Failed to mark exercise completed: $e');
    }
  }

  /// Get exercise completion status
  bool isExerciseCompleted({
    required Map<String, dynamic>? planData,
    required String exerciseId,
    DateTime? forDate,
  }) {
    if (planData == null) return false;

    final completions = planData['completions'] as Map<String, dynamic>?;
    if (completions == null) return false;

    final exerciseCompletion = completions[exerciseId] as Map<String, dynamic>?;
    if (exerciseCompletion == null) return false;

    // If checking for specific date
    if (forDate != null) {
      final completionDate = exerciseCompletion['date'] as String?;
      final targetDate = forDate.toIso8601String().split('T')[0];
      return completionDate == targetDate;
    }

    // Check if completed today
    final completionDate = exerciseCompletion['date'] as String?;
    final today = DateTime.now().toIso8601String().split('T')[0];
    return completionDate == today;
  }
}
