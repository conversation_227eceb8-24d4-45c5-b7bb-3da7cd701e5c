import 'package:hooks_riverpod/hooks_riverpod.dart';
import 'package:supabase_flutter/supabase_flutter.dart';
import 'package:fitgo_app/src/student/domain/trainer_detail.dart';
import 'package:flutter/foundation.dart';

// Provider for fetching instructor details
final instructorDetailProvider =
    FutureProvider.family<InstructorDetail, String>((ref, instructorId) async {
  return ref
      .read(instructorDetailRepositoryProvider)
      .getInstructorDetail(instructorId);
});

// Repository provider
final instructorDetailRepositoryProvider = Provider<InstructorDetailRepository>(
  (ref) {
    return InstructorDetailRepository();
  },
);

class InstructorDetailRepository {
  final SupabaseClient _supabase = Supabase.instance.client;

  Future<InstructorDetail> getInstructorDetail(String instructorId) async {
    try {
      debugPrint('🔍 Fetching instructor detail for ID: $instructorId');
      // Fetch instructor basic info (✅ CLEANED: Use profiles for name)
      final instructorResponse = await _supabase.from('instructors').select('''
            id,
            profile_id,
            photo_url,
            bio,
            rating,
            experience_years,
            title,
            is_public,
            profiles!instructors_profile_id_fkey(
              name,
              surname
            )
          ''').eq('id', instructorId).maybeSingle();

      if (instructorResponse == null) {
        throw Exception('Instructor not found with ID: $instructorId');
      }

      // Fetch work history
      final workHistoryResponse = await _supabase
          .from('instructor_work_history')
          .select('company_name, role, start_year, end_year, is_current')
          .eq('instructor_id', instructorId)
          .order('created_at', ascending: false);

      // Fetch certifications
      final certificationsResponse = await _supabase
          .from('instructor_certifications')
          .select('name, issuer, year')
          .eq('instructor_id', instructorId)
          .order('created_at', ascending: false);

      // Fetch actual enrollment count
      final enrollmentCountResponse = await _supabase
          .from('enrollments')
          .select('id')
          .eq('instructor_id', instructorId)
          .eq('is_active', true);

      final actualCurrentStudents = enrollmentCountResponse.length;

      // Get profile_id from instructor response for subscription config
      final profileId = instructorResponse['profile_id'] as String;

      // Fetch subscription config for pricing (only if approved)
      final subscriptionConfigResponse = await _supabase
          .from('instructor_subscription_configs')
          .select(
            'desired_monthly_earnings, basic_plan_monthly_price, premium_plan_monthly_price, approval_status',
          )
          .eq('instructor_id', profileId)
          .eq('approval_status', 'approved') // Only approved instructors
          .maybeSingle();

      // Fetch reviews
      final reviewsResponse = await _supabase
          .from('instructor_reviews')
          .select('''
            id,
            client_name,
            avatar_url,
            rating,
            comment,
            created_at
          ''')
          .eq('instructor_id', instructorId)
          .order('created_at', ascending: false)
          .limit(10); // Limit to latest 10 reviews

      // Fetch FAQs
      final faqsResponse = await _supabase
          .from('instructor_faqs')
          .select('id, question, answer, order_index')
          .eq('instructor_id', instructorId)
          .order('order_index', ascending: true);

      // Parse work history with new schema
      final workHistory = (workHistoryResponse as List<dynamic>).map((item) {
        final workItem = item as Map<String, dynamic>;
        return WorkHistory(
          id: workItem['id'] as String? ?? '',
          companyName: workItem['company_name'] as String? ?? 'Unknown Company',
          position: workItem['role'] as String? ?? 'Trainer',
          startDate:
              DateTime.tryParse(workItem['start_year']?.toString() ?? '') ??
                  DateTime.now(),
          endDate: workItem['end_year'] != null
              ? DateTime.tryParse(workItem['end_year'].toString())
              : null,
          isCurrent: workItem['is_current'] as bool? ?? false,
        );
      }).toList();

      // Parse certifications with new schema
      final certifications = (certificationsResponse as List<dynamic>)
          .map(
            (item) => Certification(
              id: item['id'] as String? ?? '',
              certificationName: item['name'] as String? ?? '',
              issuingOrganization: item['issuer'] as String? ?? '',
            ),
          )
          .toList();

      // Parse reviews
      final reviews = (reviewsResponse as List<dynamic>)
          .map((item) => Review.fromJson(item as Map<String, dynamic>))
          .toList();

      // Parse FAQs
      final faqs = (faqsResponse as List<dynamic>).map((item) {
        return {
          'id': item['id']?.toString() ?? '',
          'question': item['question'] as String? ?? '',
          'answer': item['answer'] as String? ?? '',
          'order_index': (item['order_index'] as num?)?.toInt() ?? 0,
        };
      }).toList();

      // Add pricing information from subscription config (new column structure)
      double? basicPrice;
      double? premiumPrice;

      if (subscriptionConfigResponse != null) {
        // Get pricing from individual columns
        basicPrice =
            (subscriptionConfigResponse['basic_plan_monthly_price'] as num?)
                ?.toDouble();
        premiumPrice =
            (subscriptionConfigResponse['premium_plan_monthly_price'] as num?)
                ?.toDouble();
      }

      // Combine all data
      final instructorData = Map<String, dynamic>.from(instructorResponse);
      instructorData['instructor_work_history'] =
          workHistory.map((e) => e.toJson()).toList();
      instructorData['instructor_certifications'] =
          certifications.map((e) => e.toJson()).toList();
      instructorData['instructor_reviews'] =
          reviews.map((e) => e.toJson()).toList();
      instructorData['instructor_faqs'] = faqs;
      instructorData['basic_monthly_price'] = basicPrice;
      instructorData['premium_monthly_price'] = premiumPrice;
      instructorData['current_students'] =
          actualCurrentStudents; // Use real enrollment count

      // Add subscription config data
      if (subscriptionConfigResponse != null) {
        instructorData['instructor_subscription_configs'] =
            subscriptionConfigResponse;
      }

      final instructorDetail = InstructorDetail.fromJson(instructorData);

      return instructorDetail;
    } catch (e) {
      debugPrint('Error fetching instructor details: $e');
      rethrow;
    }
  }

  // Create mock instructor detail for testing
  InstructorDetail _createMockInstructorDetail(String instructorId) {
    return InstructorDetail.fromJson({
      'id': instructorId,
      'profiles': {'name': 'Michael', 'surname': 'Anderson'},
      'photo_url':
          'https://images.unsplash.com/photo-1571019613454-1cb2f99b2d8b?w=400',
      'rating': 4.8,
      'experience_years': 8,
      'client_count': 200,
      'is_public': true,
      'instructor_capacity': {
        'max_students': 5,
        'current_students': 3,
        'available_spots': 2,
        'is_accepting_students': true,
      },
      'instructor_subscription_configs': {
        'bio':
            'Experienced fitness instructor specializing in strength training and weight loss.',
        'specializations': [
          'Weight Loss',
          'Strength Training',
          'HIIT',
          'Nutrition',
        ],
        'basic_plan_price': 299.99,
        'premium_plan_price': 449.99,
        'approval_status': 'approved',
      },
      'instructor_work_history': [
        {
          'id': '1',
          'company_name': 'MacFit Istanbul',
          'position': 'Gym Owner',
          'start_date': '2021-01-01',
          'end_date': null,
          'is_current': true,
        },
        {
          'id': '2',
          'company_name': 'Fit Gym Istanbul',
          'position': 'Instructor',
          'start_date': '2020-01-01',
          'end_date': '2020-12-31',
          'is_current': false,
        },
      ],
      'instructor_certifications': [
        {
          'id': '1',
          'certification_name': 'NASM Certified Personal Trainer',
          'issuing_organization': 'National Academy of Sports Medicine',
          'issue_date': '2020-01-01',
        },
        {
          'id': '2',
          'certification_name': 'Precision Nutrition Level 1',
          'issuing_organization': 'Precision Nutrition',
          'issue_date': '2021-01-01',
        },
      ],
      'instructor_reviews': [
        {
          'id': '1',
          'rating': 5,
          'title': 'Excellent Trainer',
          'comment':
              'Michael is an inspirational instructor! His expertise in strength training helped me achieve my fitness goals.',
          'created_at': DateTime.now().toIso8601String(),
        },
      ],
      'instructor_faqs': [
        {
          'id': '1',
          'question': 'What is your training philosophy?',
          'answer':
              'I believe in sustainable, progressive training that builds both physical and mental strength.',
          'order_index': 0,
        },
      ],
    });
  }

  // Note: Sample instructor data creation method removed as it used deprecated fields
}
