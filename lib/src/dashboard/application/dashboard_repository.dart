import 'package:flutter/material.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:supabase_flutter/supabase_flutter.dart';
import 'package:fitgo_app/src/dashboard/domain/dashboard_models.dart';
import 'package:fitgo_app/src/theme/colors.dart';
import 'package:fitgo_app/src/shared/utils/app_logger.dart';

class DashboardRepository {
  final SupabaseClient _supabase = Supabase.instance.client;
  static const String _onboardingStatusKey = 'onboarding_status';

  // Get default dashboard tiles
  List<DashboardTile> getDefaultDashboardTiles() {
    return [
      const DashboardTile(
        id: 'reinforcement_plan',
        title: 'Reinforcement Plan',
        subtitle: 'A personalized nutrition guide.',
        icon: Icons.restaurant_menu,
        iconColor: AColor.fitgoGreen,
        route: '/reinforcement-plan',
      ),
      const DashboardTile(
        id: 'training_notes',
        title: 'Training Notes',
        subtitle: 'Save your progress',
        icon: Icons.note_alt,
        iconColor: AColor.fitgoGreen,
        route: '/training-notes',
      ),
      const DashboardTile(
        id: 'weight_record',
        title: 'Weight Record Book',
        subtitle: 'Track your removal statistics.',
        icon: Icons.monitor_weight,
        iconColor: AColor.fitgoGreen,
        route: '/weight-record',
      ),
      const DashboardTile(
        id: 'education_videos',
        title: 'Education Videos',
        subtitle: 'Learn professionalism.',
        icon: Icons.play_circle_filled,
        iconColor: AColor.fitgoGreen,
        route: '/education-videos',
      ),
      const DashboardTile(
        id: 'healthy_recipes',
        title: 'Healthy Recipes',
        subtitle: 'Nutritious meal ideas.',
        icon: Icons.local_dining,
        iconColor: AColor.fitgoGreen,
        route: '/healthy-recipes',
      ),
      const DashboardTile(
        id: 'my_questions',
        title: 'My Questions',
        subtitle: 'Get expert answers.',
        icon: Icons.help_center,
        iconColor: AColor.fitgoGreen,
        route: '/my-questions',
        hasNotification: true,
        notificationCount: 2,
      ),
      const DashboardTile(
        id: 'feedback',
        title: 'Feedback',
        subtitle: 'Track your progress with expert feedback.',
        icon: Icons.feedback,
        iconColor: AColor.fitgoGreen,
        route: '/feedback',
      ),
      const DashboardTile(
        id: 'shopping_list',
        title: 'Shopping List',
        subtitle: 'Nutritious meal ideas.',
        icon: Icons.shopping_cart,
        iconColor: AColor.fitgoGreen,
        route: '/shopping-list',
      ),
      const DashboardTile(
        id: 'market',
        title: 'Market',
        subtitle: 'Plan your shopping.',
        icon: Icons.store,
        iconColor: AColor.fitgoGreen,
        route: '/market',
      ),
      const DashboardTile(
        id: 'workout_reminders',
        title: 'Workout Reminders',
        subtitle: 'Set workout notifications.',
        icon: Icons.alarm,
        iconColor: AColor.fitgoGreen,
        route: '/workout-reminders',
      ),
    ];
  }

  // Get user profile for dashboard
  Future<DashboardUserProfile?> getUserProfile() async {
    try {
      final user = _supabase.auth.currentUser;
      if (user == null) {
        // Return mock user for development
        return const DashboardUserProfile(
          userId: 'mock_user',
          name: 'Orkun Hacılar',
          email: '<EMAIL>',
          subscriptionStatus: SubscriptionStatus.premium,
        );
      }

      // Try to get profile from Supabase
      final response =
          await _supabase
              .from('user_profiles')
              .select('*')
              .eq('user_id', user.id)
              .maybeSingle();

      if (response != null) {
        return DashboardUserProfile(
          userId: user.id,
          name:
              response['full_name'] as String? ??
              user.email?.split('@')[0] ??
              'User',
          email: user.email,
          avatarUrl: response['avatar_url'] as String?,
          subscriptionStatus: _parseSubscriptionStatus(
            response['subscription_status'] as String?,
          ),
        );
      } else {
        // Fallback to user metadata
        return DashboardUserProfile(
          userId: user.id,
          name:
              user.userMetadata?['full_name'] as String? ??
              user.email?.split('@')[0] ??
              'User',
          email: user.email,
          subscriptionStatus:
              SubscriptionStatus.premium, // Default for new users
        );
      }
    } catch (e) {
      // Return mock user on error
      return const DashboardUserProfile(
        userId: 'mock_user',
        name: 'Orkun Hacılar',
        email: '<EMAIL>',
        subscriptionStatus: SubscriptionStatus.premium,
      );
    }
  }

  // Parse subscription status from string
  SubscriptionStatus _parseSubscriptionStatus(String? status) {
    switch (status?.toLowerCase()) {
      case 'premium':
        return SubscriptionStatus.premium;
      case 'expired':
        return SubscriptionStatus.expired;
      default:
        return SubscriptionStatus.free;
    }
  }

  // Get onboarding status
  Future<OnboardingStatus> getOnboardingStatus() async {
    try {
      // Always check database first for accurate status
      AppLogger.info('🔍 Getting onboarding status from database...', tag: 'ONBOARDING');
      final user = _supabase.auth.currentUser;
      if (user != null) {
        // Check payment status from enrollments (CLEAN ARCHITECTURE)
        final enrollmentResponse = await _supabase
            .from('enrollments')
            .select('is_active')
            .eq('student_id', user.id)
            .eq('is_active', true)
            .maybeSingle();

        final hasCompletedPayment = enrollmentResponse != null &&
            enrollmentResponse['is_active'] == true;

        // Check profile completion from user_profiles
        final profileResponse = await _supabase
            .from('user_profiles')
            .select('id')
            .eq('user_id', user.id)
            .maybeSingle();

        final hasCompletedProfileForm = profileResponse != null;

        // Check if user has assigned plan
        final hasAssignedPlan = await _checkUserHasAssignedPlan(user.id);

        AppLogger.info('🔍 Dashboard repository onboarding status:', tag: 'ONBOARDING');
        AppLogger.info('  - User ID: ${user.id}', tag: 'ONBOARDING');
        AppLogger.info('  - hasCompletedPayment: $hasCompletedPayment', tag: 'ONBOARDING');
        AppLogger.info('  - hasCompletedProfileForm: $hasCompletedProfileForm', tag: 'ONBOARDING');
        AppLogger.info('  - hasAssignedPlan: $hasAssignedPlan', tag: 'ONBOARDING');

        final status = OnboardingStatus(
          hasCompletedPayment: hasCompletedPayment,
          hasCompletedProfileForm: hasCompletedProfileForm,
          hasSeenDashboard: false, // Always false, will be set when dashboard is seen
          hasAssignedPlan: hasAssignedPlan,
        );

        // Save to local storage for caching
        await saveOnboardingStatus(status);
        return status;
      }

      return const OnboardingStatus();
    } catch (e) {
      return const OnboardingStatus();
    }
  }

  // Save onboarding status
  Future<void> saveOnboardingStatus(OnboardingStatus status) async {
    try {
      final prefs = await SharedPreferences.getInstance();
      await prefs.setBool('has_completed_payment', status.hasCompletedPayment);
      await prefs.setBool(
        'has_completed_profile_form',
        status.hasCompletedProfileForm,
      );
      await prefs.setBool('has_seen_dashboard', status.hasSeenDashboard);
      await prefs.setBool('has_assigned_plan', status.hasAssignedPlan);
    } catch (e) {
      // Handle error silently
    }
  }

  // Mark payment as completed
  Future<void> markPaymentCompleted() async {
    final currentStatus = await getOnboardingStatus();
    final updatedStatus = currentStatus.copyWith(hasCompletedPayment: true);
    await saveOnboardingStatus(updatedStatus);
  }

  // Mark profile form as completed
  Future<void> markProfileFormCompleted() async {
    final currentStatus = await getOnboardingStatus();
    final updatedStatus = currentStatus.copyWith(hasCompletedProfileForm: true);
    await saveOnboardingStatus(updatedStatus);
  }

  // Mark dashboard as seen
  Future<void> markDashboardSeen() async {
    final currentStatus = await getOnboardingStatus();
    final updatedStatus = currentStatus.copyWith(hasSeenDashboard: true);
    await saveOnboardingStatus(updatedStatus);
  }

  // Mark plan as assigned
  Future<void> markPlanAssigned() async {
    final currentStatus = await getOnboardingStatus();
    final updatedStatus = currentStatus.copyWith(hasAssignedPlan: true);
    await saveOnboardingStatus(updatedStatus);
  }

  // Check if user has assigned plan from instructor
  Future<bool> _checkUserHasAssignedPlan(String userId) async {
    try {
      AppLogger.info('🔍 Checking assigned plans for user: $userId', tag: 'PLAN_CHECK');

      // Check if user has any active workout plan
      final workoutPlan =
          await _supabase
              .from('student_workout_plans')
              .select('id')
              .eq('student_id', userId)
              .eq('is_active', true)
              .maybeSingle();

      AppLogger.debug('💪 Workout plan check result: $workoutPlan', tag: 'PLAN_CHECK');

      if (workoutPlan != null) {
        AppLogger.success('✅ User has active workout plan', tag: 'PLAN_CHECK');
        return true;
      }

      // Check if user has any active nutrition plan
      final nutritionPlan =
          await _supabase
              .from('student_nutrition_assignments')
              .select('id')
              .eq('student_id', userId)
              .eq('is_active', true)
              .maybeSingle();

      AppLogger.debug('🥗 Nutrition plan check result: $nutritionPlan', tag: 'PLAN_CHECK');

      final hasNutritionPlan = nutritionPlan != null;
      AppLogger.info('📊 Final plan assignment status: $hasNutritionPlan', tag: 'PLAN_CHECK');

      return hasNutritionPlan;
    } catch (e) {
      AppLogger.error('❌ Error checking assigned plan for $userId', tag: 'PLAN_CHECK', error: e);
      return false;
    }
  }

  // Update tile notification status
  List<DashboardTile> updateTileNotification(
    List<DashboardTile> tiles,
    String tileId, {
    bool? hasNotification,
    int? notificationCount,
  }) {
    return tiles.map((tile) {
      if (tile.id == tileId) {
        return tile.copyWith(
          hasNotification: hasNotification,
          notificationCount: notificationCount,
        );
      }
      return tile;
    }).toList();
  }

  // Get current user ID
  String? getCurrentUserId() {
    return _supabase.auth.currentUser?.id;
  }

  // Check if user is authenticated
  bool isUserAuthenticated() {
    return _supabase.auth.currentUser != null;
  }
}
