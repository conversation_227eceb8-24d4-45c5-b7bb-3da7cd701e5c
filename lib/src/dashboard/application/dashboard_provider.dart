import 'package:hooks_riverpod/hooks_riverpod.dart';
import 'package:fitgo_app/src/dashboard/domain/dashboard_models.dart';
import 'package:fitgo_app/src/dashboard/application/dashboard_repository.dart';

// Repository provider
final dashboardRepositoryProvider = Provider<DashboardRepository>((ref) {
  return DashboardRepository();
});

// Main dashboard state notifier
class DashboardNotifier extends StateNotifier<DashboardState> {
  final DashboardRepository _repository;

  DashboardNotifier({
    required DashboardRepository repository,
  }) : _repository = repository, super(const DashboardState());

  // Initialize dashboard
  Future<void> initializeDashboard() async {
    state = state.copyWith(isLoading: true, clearError: true);

    try {
      // Load user profile
      final userProfile = await _repository.getUserProfile();
      
      // Load dashboard tiles
      final tiles = _repository.getDefaultDashboardTiles();

      // Mark dashboard as seen
      await _repository.markDashboardSeen();

      state = state.copyWith(
        userProfile: userProfile,
        tiles: tiles,
        isLoading: false,
      );
    } catch (e) {
      state = state.copyWith(
        isLoading: false,
        error: 'Failed to load dashboard: ${e.toString()}',
      );
    }
  }

  // Change bottom navigation tab
  void changeTab(BottomNavTab tab) {
    state = state.copyWith(currentTab: tab);
  }

  // Update tile notification
  void updateTileNotification(String tileId, {bool? hasNotification, int? notificationCount}) {
    final updatedTiles = _repository.updateTileNotification(
      state.tiles,
      tileId,
      hasNotification: hasNotification,
      notificationCount: notificationCount,
    );

    state = state.copyWith(tiles: updatedTiles);
  }

  // Clear notification for tile
  void clearTileNotification(String tileId) {
    updateTileNotification(tileId, hasNotification: false, notificationCount: null);
  }

  // Refresh dashboard data
  Future<void> refreshDashboard() async {
    await initializeDashboard();
  }

  // Clear error
  void clearError() {
    state = state.copyWith(clearError: true);
  }

  // Get tile by ID
  DashboardTile? getTileById(String tileId) {
    try {
      return state.tiles.firstWhere((tile) => tile.id == tileId);
    } catch (e) {
      return null;
    }
  }
}

// Main dashboard provider
final dashboardProvider = StateNotifierProvider<DashboardNotifier, DashboardState>(
  (ref) {
    final repository = ref.read(dashboardRepositoryProvider);
    return DashboardNotifier(repository: repository);
  },
);

// Convenience providers
final dashboardUserProfileProvider = Provider<DashboardUserProfile?>((ref) {
  return ref.watch(dashboardProvider).userProfile;
});

final dashboardTilesProvider = Provider<List<DashboardTile>>((ref) {
  return ref.watch(dashboardProvider).tiles;
});

final currentBottomNavTabProvider = Provider<BottomNavTab>((ref) {
  return ref.watch(dashboardProvider).currentTab;
});

final isDashboardLoadingProvider = Provider<bool>((ref) {
  return ref.watch(dashboardProvider).isLoading;
});

// Onboarding status provider
final onboardingStatusProvider = FutureProvider<OnboardingStatus>((ref) async {
  final repository = ref.read(dashboardRepositoryProvider);
  return await repository.getOnboardingStatus();
});

// Check if user should see dashboard
final shouldShowDashboardProvider = FutureProvider<bool>((ref) async {
  final onboardingStatus = await ref.read(onboardingStatusProvider.future);
  return onboardingStatus.shouldShowDashboard;
});

// Onboarding completion notifier
class OnboardingNotifier extends StateNotifier<OnboardingStatus> {
  final DashboardRepository _repository;

  OnboardingNotifier({
    required DashboardRepository repository,
  }) : _repository = repository, super(const OnboardingStatus());

  // Initialize onboarding status
  Future<void> initializeOnboardingStatus() async {
    final status = await _repository.getOnboardingStatus();
    state = status;
  }

  // Mark payment completed
  Future<void> markPaymentCompleted() async {
    await _repository.markPaymentCompleted();
    state = state.copyWith(hasCompletedPayment: true);
  }

  // Mark profile form completed
  Future<void> markProfileFormCompleted() async {
    await _repository.markProfileFormCompleted();
    state = state.copyWith(hasCompletedProfileForm: true);
  }

  // Mark dashboard seen
  Future<void> markDashboardSeen() async {
    await _repository.markDashboardSeen();
    state = state.copyWith(hasSeenDashboard: true);
  }
}

// Onboarding state provider
final onboardingNotifierProvider = StateNotifierProvider<OnboardingNotifier, OnboardingStatus>(
  (ref) {
    final repository = ref.read(dashboardRepositoryProvider);
    return OnboardingNotifier(repository: repository);
  },
);

// Tile notification providers
final tileNotificationsProvider = Provider<Map<String, int>>((ref) {
  final tiles = ref.watch(dashboardTilesProvider);
  final notifications = <String, int>{};
  
  for (final tile in tiles) {
    if (tile.hasNotification && tile.notificationCount != null) {
      notifications[tile.id] = tile.notificationCount!;
    }
  }
  
  return notifications;
});

// Total notification count
final totalNotificationCountProvider = Provider<int>((ref) {
  final notifications = ref.watch(tileNotificationsProvider);
  return notifications.values.fold(0, (sum, count) => sum + count);
});
