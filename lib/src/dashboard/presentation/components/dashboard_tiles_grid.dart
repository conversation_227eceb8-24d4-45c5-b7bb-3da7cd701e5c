import 'package:flutter/material.dart';
import 'package:fitgo_app/src/shared/widgets/text_widget/text_widget.dart';
import 'package:fitgo_app/src/shared/constants/app_text_style.dart';
import 'package:fitgo_app/src/theme/colors.dart';
import 'package:fitgo_app/src/dashboard/domain/dashboard_models.dart';

class DashboardTilesGrid extends StatelessWidget {
  final List<DashboardTile> tiles;
  final Function(DashboardTile) onTileTap;

  const DashboardTilesGrid({
    super.key,
    required this.tiles,
    required this.onTileTap,
  });

  @override
  Widget build(BuildContext context) {
    return GridView.builder(
      shrinkWrap: true,
      physics: const NeverScrollableScrollPhysics(),
      gridDelegate: const SliverGridDelegateWithFixedCrossAxisCount(
        crossAxisCount: 2,
        crossAxisSpacing: 12,
        mainAxisSpacing: 12,
        childAspectRatio: 1.1,
      ),
      itemCount: tiles.length,
      itemBuilder: (context, index) {
        final tile = tiles[index];
        return _buildDashboardTile(tile);
      },
    );
  }

  Widget _buildDashboardTile(DashboardTile tile) {
    return GestureDetector(
      onTap: tile.isEnabled ? () => onTileTap(tile) : null,
      child: AnimatedContainer(
        duration: const Duration(milliseconds: 200),
        decoration: BoxDecoration(
          color: tile.isEnabled 
              ? const Color(0xFF1E293B)
              : const Color(0xFF1E293B).withOpacity(0.5),
          borderRadius: BorderRadius.circular(16),
          border: Border.all(
            color: tile.isEnabled 
                ? const Color(0xFF334155)
                : const Color(0xFF334155).withOpacity(0.5),
            width: 1,
          ),
          boxShadow: tile.isEnabled ? [
            BoxShadow(
              color: Colors.black.withOpacity(0.1),
              blurRadius: 8,
              offset: const Offset(0, 2),
            ),
          ] : [],
        ),
        child: Stack(
          children: [
            // Main tile content
            Padding(
              padding: const EdgeInsets.all(16),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  // Icon
                  Container(
                    width: 48,
                    height: 48,
                    decoration: BoxDecoration(
                      color: (tile.iconColor ?? AColor.fitgoGreen).withOpacity(0.1),
                      borderRadius: BorderRadius.circular(12),
                    ),
                    child: Icon(
                      tile.icon,
                      color: tile.isEnabled 
                          ? (tile.iconColor ?? AColor.fitgoGreen)
                          : (tile.iconColor ?? AColor.fitgoGreen).withOpacity(0.5),
                      size: 24,
                    ),
                  ),
                  const SizedBox(height: 12),
                  
                  // Title
                  TextWidget(
                    tile.title,
                    style: ATextStyle.medium.copyWith(
                      color: tile.isEnabled ? Colors.white : Colors.white54,
                      fontWeight: FontWeight.w600,
                    ),
                    maxLines: 2,
                    overflow: TextOverflow.ellipsis,
                  ),
                  const SizedBox(height: 4),
                  
                  // Subtitle
                  Expanded(
                    child: TextWidget(
                      tile.subtitle,
                      style: ATextStyle.small.copyWith(
                        color: tile.isEnabled ? Colors.white70 : Colors.white38,
                        height: 1.3,
                      ),
                      maxLines: 3,
                      overflow: TextOverflow.ellipsis,
                    ),
                  ),
                ],
              ),
            ),
            
            // Notification badge
            if (tile.hasNotification && tile.isEnabled)
              Positioned(
                top: 8,
                right: 8,
                child: _buildNotificationBadge(tile.notificationCount),
              ),
            
            // Disabled overlay
            if (!tile.isEnabled)
              Positioned.fill(
                child: Container(
                  decoration: BoxDecoration(
                    color: Colors.black.withOpacity(0.3),
                    borderRadius: BorderRadius.circular(16),
                  ),
                  child: const Center(
                    child: Icon(
                      Icons.lock,
                      color: Colors.white54,
                      size: 32,
                    ),
                  ),
                ),
              ),
            
            // Tap effect overlay
            if (tile.isEnabled)
              Positioned.fill(
                child: Material(
                  color: Colors.transparent,
                  borderRadius: BorderRadius.circular(16),
                  child: InkWell(
                    borderRadius: BorderRadius.circular(16),
                    onTap: () => onTileTap(tile),
                    splashColor: AColor.fitgoGreen.withOpacity(0.1),
                    highlightColor: AColor.fitgoGreen.withOpacity(0.05),
                  ),
                ),
              ),
          ],
        ),
      ),
    );
  }

  Widget _buildNotificationBadge(int? count) {
    return Container(
      padding: EdgeInsets.all(count != null && count > 9 ? 6 : 8),
      decoration: BoxDecoration(
        color: Colors.red,
        shape: BoxShape.circle,
        border: Border.all(
          color: const Color(0xFF1E293B),
          width: 2,
        ),
      ),
      child: count != null
          ? TextWidget(
              count > 99 ? '99+' : count.toString(),
              style: ATextStyle.small.copyWith(
                color: Colors.white,
                fontWeight: FontWeight.bold,
                fontSize: count > 9 ? 10 : 12,
              ),
            )
          : const SizedBox(
              width: 8,
              height: 8,
            ),
    );
  }
}
