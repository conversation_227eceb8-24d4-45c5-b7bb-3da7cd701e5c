import 'package:flutter/material.dart';
import 'package:fitgo_app/src/shared/widgets/text_widget/text_widget.dart';
import 'package:fitgo_app/src/shared/constants/app_text_style.dart';
import 'package:fitgo_app/src/theme/colors.dart';
import 'package:fitgo_app/src/dashboard/domain/dashboard_models.dart';

class DashboardHeader extends StatelessWidget {
  final DashboardUserProfile? userProfile;
  final VoidCallback onMenuTap;

  const DashboardHeader({
    super.key,
    required this.userProfile,
    required this.onMenuTap,
  });

  @override
  Widget build(BuildContext context) {
    return Container(
      padding: const EdgeInsets.fromLTRB(16, 16, 16, 8),
      decoration: BoxDecoration(
        color: const Color(0xFF1E293B),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withOpacity(0.1),
            blurRadius: 8,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Row(
        children: [
          // User avatar
          _buildUserAvatar(),
          const SizedBox(width: 12),
          
          // User info
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                TextWidget(
                  userProfile?.name ?? 'Kullanıcı',
                  style: ATextStyle.large.copyWith(
                    color: Colors.white,
                    fontWeight: FontWeight.bold,
                  ),
                ),
                const SizedBox(height: 2),
                Row(
                  children: [
                    if (userProfile?.subscriptionStatus.isPremium == true) ...[
                      Icon(
                        Icons.workspace_premium,
                        color: AColor.fitgoGreen,
                        size: 16,
                      ),
                      const SizedBox(width: 4),
                    ],
                    Flexible(
                      child: TextWidget(
                        userProfile?.subscriptionStatus.displayName ?? 'Free User',
                        style: ATextStyle.medium.copyWith(
                          color: userProfile?.subscriptionStatus.isPremium == true
                              ? AColor.fitgoGreen
                              : Colors.white70,
                          fontWeight: FontWeight.w500,
                        ),
                      ),
                    ),
                  ],
                ),
              ],
            ),
          ),
          
          // Menu button
          IconButton(
            onPressed: onMenuTap,
            icon: Icon(
              Icons.menu,
              color: Colors.white,
              size: 24,
            ),
            style: IconButton.styleFrom(
              backgroundColor: Colors.white.withOpacity(0.1),
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(8),
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildUserAvatar() {
    return Container(
      width: 56,
      height: 56,
      decoration: BoxDecoration(
        shape: BoxShape.circle,
        border: Border.all(
          color: AColor.fitgoGreen,
          width: 2,
        ),
      ),
      child: ClipOval(
        child: userProfile?.hasAvatar == true
            ? Image.network(
                userProfile!.avatarUrl!,
                fit: BoxFit.cover,
                errorBuilder: (context, error, stackTrace) {
                  return _buildInitialsAvatar();
                },
              )
            : _buildInitialsAvatar(),
      ),
    );
  }

  Widget _buildInitialsAvatar() {
    return Container(
      decoration: BoxDecoration(
        shape: BoxShape.circle,
        gradient: LinearGradient(
          colors: [
            AColor.fitgoGreen.withOpacity(0.8),
            AColor.fitgoGreen,
          ],
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
        ),
      ),
      child: Center(
        child: TextWidget(
          userProfile?.initials ?? 'U',
          style: ATextStyle.large.copyWith(
            color: Colors.white,
            fontWeight: FontWeight.bold,
          ),
        ),
      ),
    );
  }
}
