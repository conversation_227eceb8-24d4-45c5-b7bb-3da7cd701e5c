import 'package:flutter/material.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:fitgo_app/src/dashboard/domain/dashboard_models.dart';

class DashboardBottomNavigationBar extends StatelessWidget {
  final BottomNavTab currentTab;
  final Function(BottomNavTab) onTabChanged;

  const DashboardBottomNavigationBar({
    super.key,
    required this.currentTab,
    required this.onTabChanged,
  });

  @override
  Widget build(BuildContext context) {
    return Container(
      decoration: BoxDecoration(
        color: const Color(0xFF1F2937), // Dark navy background like in image
        boxShadow: [
          BoxShadow(
            color: Colors.black.withOpacity(0.2),
            blurRadius: 12,
            offset: const Offset(0, -4),
          ),
        ],
      ),
      child: SafeArea(
        child: Container(
          height: 75, // Slightly taller for better proportions
          padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
          child: Row(
            mainAxisAlignment: MainAxisAlignment.spaceAround,
            children: BottomNavTab.values.map((tab) {
              return _buildNavItem(tab);
            }).toList(),
          ),
        ),
      ),
    );
  }

  Widget _buildNavItem(BottomNavTab tab) {
    final isSelected = currentTab == tab;

    return Expanded(
      child: GestureDetector(
        onTap: () => onTabChanged(tab),
        behavior: HitTestBehavior.opaque,
        child: AnimatedContainer(
          duration: const Duration(milliseconds: 250),
          curve: Curves.easeInOut,
          padding: const EdgeInsets.symmetric(vertical: 8, horizontal: 6),
          child: Column(
            mainAxisSize: MainAxisSize.min,
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              // Icon with green background when selected
              AnimatedContainer(
                duration: const Duration(milliseconds: 250),
                curve: Curves.easeInOut,
                width: isSelected ? 28 : 24,
                height: isSelected ? 28 : 24,
                decoration: BoxDecoration(
                  color: isSelected
                      ? const Color(0xFF10B981) // Bright green like in image
                      : Colors.transparent,
                  borderRadius: BorderRadius.circular(6),
                ),
                child: Padding(
                  padding: const EdgeInsets.all(4),
                  child: SvgPicture.asset(
                    tab.iconPath,
                    width: isSelected ? 16 : 14,
                    height: isSelected ? 16 : 14,
                    colorFilter: ColorFilter.mode(
                      isSelected
                          ? Colors.white // White icon on green background
                          : const Color(0xFF9CA3AF), // Gray when not selected
                      BlendMode.srcIn,
                    ),
                  ),
                ),
              ),

              const SizedBox(height: 4),

              // Label with green color when selected
              AnimatedDefaultTextStyle(
                duration: const Duration(milliseconds: 250),
                curve: Curves.easeInOut,
                style: TextStyle(
                  color: isSelected
                      ? const Color(0xFF10B981) // Bright green like in image
                      : const Color(0xFF9CA3AF), // Gray when not selected
                  fontWeight: isSelected ? FontWeight.w600 : FontWeight.w500,
                  fontSize: 11,
                ),
                child: Text(tab.label),
              ),
            ],
          ),
        ),
      ),
    );
  }
}
