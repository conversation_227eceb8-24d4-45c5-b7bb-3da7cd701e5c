import 'package:fitgo_app/src/shared/utils/dt_util/dt_format.dart';
import 'package:flutter/material.dart';
import 'package:intl/intl.dart';
import 'package:hooks_riverpod/hooks_riverpod.dart';

// Create a locale provider to access the current app locale
final localeProvider = StateProvider<Locale>((ref) {
  // Default to English - this will be overridden by the app's locale
  return const Locale('en');
});

// Initialize this in your main.dart before runApp
void initLocaleProvider(WidgetRef ref, Locale locale) {
  ref.read(localeProvider.notifier).state = locale;
}

class DTUtil {
  static DateTime? stringToDT(
    String? stringDateTime, {
    DTFormat format = DTFormat.json,
    bool utc = false,
  }) =>
      stringDateTime == null
          ? null
          : DateFormat(format.key).parse(stringDateTime, utc).toLocal();

  static String dtToString(
    DateTime? date, {
    DTFormat format = DTFormat.json,
    Locale? locale,
  }) {
    if (date == null) {
      return '';
    }

    // Use the provided locale or get it from the provider
    final languageCode = locale?.languageCode ?? Intl.getCurrentLocale();
    return DateFormat(format.key, languageCode).format(date);
  }

  // Alternative method that requires a WidgetRef
  static String dtToStringWithRef(
    WidgetRef ref,
    DateTime? date, {
    DTFormat format = DTFormat.json,
  }) {
    if (date == null) {
      return '';
    }
    final currentLocale = ref.watch(localeProvider);
    return DateFormat(format.key, currentLocale.languageCode).format(date);
  }

  /// Parse user input date in DD/MM/YYYY format to DateTime
  static DateTime? parseUserInputDate(String? dateString) {
    if (dateString == null || dateString.trim().isEmpty) {
      return null;
    }

    try {
      return DateFormat(DTFormat.userInput.key).parse(dateString.trim());
    } catch (e) {
      return null;
    }
  }

  /// Format DateTime to user input format DD/MM/YYYY
  static String formatToUserInput(DateTime? date) {
    if (date == null) {
      return '';
    }
    return DateFormat(DTFormat.userInput.key).format(date);
  }
}
