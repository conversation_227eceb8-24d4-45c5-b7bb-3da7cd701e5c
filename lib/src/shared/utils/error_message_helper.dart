import 'package:fitgo_app/src/auth/domain/user_exception.dart';
import 'package:supabase_flutter/supabase_flutter.dart';

/// Helper class to convert technical errors to user-friendly messages
class ErrorMessageHelper {
  /// Convert any error to a user-friendly message
  static String getUserFriendlyMessage(dynamic error) {
    // Handle UserException (our custom exceptions)
    if (error is UserException) {
      return _getUserExceptionMessage(error.type);
    }
    
    // Handle Supabase AuthException
    if (error is AuthException) {
      return _getAuthExceptionMessage(error);
    }
    
    // Handle AuthRetryableFetchException (don't expose technical details)
    if (error.toString().contains('AuthRetryableFetchException')) {
      return 'Bağlantı sorunu yaşanıyor. Lütfen internet bağlantınızı kontrol edin ve tekrar deneyin.';
    }
    
    // Handle network/connection errors
    if (error.toString().toLowerCase().contains('network') ||
        error.toString().toLowerCase().contains('connection') ||
        error.toString().toLowerCase().contains('timeout')) {
      return 'İnternet bağlantısı sorunu. Lütfen bağlantınızı kontrol edin.';
    }
    
    // Handle server errors
    if (error.toString().contains('500') || 
        error.toString().toLowerCase().contains('server')) {
      return 'Sunucu hatası. Lütfen daha sonra tekrar deneyin.';
    }
    
    // Default fallback message
    return 'Bir hata oluştu. Lütfen tekrar deneyin.';
  }
  
  /// Get user-friendly message for UserException types
  static String _getUserExceptionMessage(UserExceptionType type) {
    switch (type) {
      case UserExceptionType.phoneExist:
        return 'Bu telefon numarası zaten kullanılıyor.';
      case UserExceptionType.emailExist:
        return 'Bu e-posta adresi zaten kullanılıyor.';
      case UserExceptionType.userExist:
        return 'Bu kullanıcı zaten mevcut.';
      case UserExceptionType.validationFailed:
        return 'Girilen bilgiler geçersiz. Lütfen kontrol edin.';
      case UserExceptionType.wrongCredential:
        return 'E-posta veya şifre hatalı.';
      case UserExceptionType.notFound:
        return 'Kullanıcı bulunamadı.';
      case UserExceptionType.toManyAttempts:
        return 'Çok fazla deneme yapıldı. Lütfen daha sonra tekrar deneyin.';
      case UserExceptionType.unexpected:
        return 'Beklenmeyen bir hata oluştu.';
    }
  }
  
  /// Get user-friendly message for Supabase AuthException
  static String _getAuthExceptionMessage(AuthException exception) {
    switch (exception.code) {
      case 'invalid_credentials':
        return 'E-posta veya şifre hatalı.';
      case 'email_not_confirmed':
        return 'E-posta adresinizi doğrulamanız gerekiyor.';
      case 'user_not_found':
        return 'Bu e-posta adresi ile kayıtlı kullanıcı bulunamadı.';
      case 'weak_password':
        return 'Şifre çok zayıf. Daha güçlü bir şifre seçin.';
      case 'email_address_invalid':
        return 'Geçersiz e-posta adresi.';
      case 'password_too_short':
        return 'Şifre en az 6 karakter olmalıdır.';
      case 'signup_disabled':
        return 'Kayıt işlemi şu anda devre dışı.';
      case 'email_address_not_authorized':
        return 'Bu e-posta adresi ile kayıt yapılamıyor.';
      case 'too_many_requests':
        return 'Çok fazla deneme yapıldı. Lütfen daha sonra tekrar deneyin.';
      case 'captcha_failed':
        return 'Güvenlik doğrulaması başarısız. Tekrar deneyin.';
      case 'email_exists':
        return 'Bu e-posta adresi zaten kullanılıyor.';
      case 'phone_exists':
        return 'Bu telefon numarası zaten kullanılıyor.';
      case 'user_already_exists':
        return 'Bu kullanıcı zaten mevcut.';
      case 'validation_failed':
        return 'Girilen bilgiler geçersiz. Lütfen kontrol edin.';
      default:
        // Don't expose technical error codes to users
        if (exception.statusCode == '429') {
          return 'Çok fazla deneme yapıldı. Lütfen daha sonra tekrar deneyin.';
        } else if (exception.statusCode == '500') {
          return 'Sunucu hatası. Lütfen daha sonra tekrar deneyin.';
        } else if (exception.statusCode == '403') {
          return 'Bu işlem için yetkiniz bulunmuyor.';
        }
        return 'Giriş yapılırken bir hata oluştu. Lütfen tekrar deneyin.';
    }
  }
  
  /// Get user-friendly message for registration errors
  static String getRegistrationErrorMessage(dynamic error) {
    final message = getUserFriendlyMessage(error);
    
    // Add specific context for registration
    if (message.contains('E-posta veya şifre hatalı')) {
      return 'Kayıt sırasında bir hata oluştu. Lütfen bilgilerinizi kontrol edin.';
    }
    
    return message;
  }
  
  /// Get user-friendly message for login errors
  static String getLoginErrorMessage(dynamic error) {
    return getUserFriendlyMessage(error);
  }
}
