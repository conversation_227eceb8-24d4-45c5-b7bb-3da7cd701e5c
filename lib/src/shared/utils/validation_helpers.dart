/// Utility class for common validation functions
class ValidationHelpers {
  ValidationHelpers._(); // Private constructor to prevent instantiation

  /// Validate email format
  static bool isValidEmail(String email) {
    if (email.isEmpty) return false;
    final emailRegex = RegExp(
      r'^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$',
    );
    return emailRegex.hasMatch(email);
  }

  /// Validate Turkish phone number format
  static bool isValidTurkishPhone(String phone) {
    if (phone.isEmpty) return false;
    
    // Remove spaces, dashes, and parentheses
    final cleanPhone = phone.replaceAll(RegExp(r'[\s\-\(\)]'), '');
    
    // Check for Turkish phone number patterns
    final patterns = [
      RegExp(r'^\+905[0-9]{9}$'), // +905XXXXXXXXX
      RegExp(r'^05[0-9]{9}$'),    // 05XXXXXXXXX
      RegExp(r'^5[0-9]{9}$'),     // 5XXXXXXXXX
    ];
    
    return patterns.any((pattern) => pattern.hasMatch(cleanPhone));
  }

  /// Validate password strength
  static bool isStrongPassword(String password) {
    if (password.length < 8) return false;
    
    // Check for at least one uppercase letter
    if (!RegExp(r'[A-Z]').hasMatch(password)) return false;
    
    // Check for at least one lowercase letter
    if (!RegExp(r'[a-z]').hasMatch(password)) return false;
    
    // Check for at least one digit
    if (!RegExp(r'[0-9]').hasMatch(password)) return false;
    
    // Check for at least one special character
    if (!RegExp(r'[!@#$%^&*(),.?":{}|<>]').hasMatch(password)) return false;
    
    return true;
  }

  /// Validate name format
  static bool isValidName(String name) {
    if (name.isEmpty || name.length < 2) return false;
    if (name.length > 50) return false;
    
    // Allow letters, spaces, hyphens, and Turkish characters
    final nameRegex = RegExp(r"^[A-Za-zÀ-ÖØ-öø-ÿÇçĞğİıÖöŞşÜü\s'-]+$");
    return nameRegex.hasMatch(name);
  }

  /// Validate age range
  static bool isValidAge(int age) {
    return age >= 18 && age <= 100;
  }

  /// Validate price value
  static bool isValidPrice(double price) {
    if (price <= 0) return false;
    if (price.isInfinite || price.isNaN) return false;
    return true;
  }

  /// Validate experience years
  static bool isValidExperienceYears(int years) {
    return years >= 0 && years <= 50;
  }

  /// Validate Turkish ID number
  static bool isValidTurkishId(String id) {
    if (id.length != 11) return false;
    if (id.startsWith('0')) return false;
    if (!RegExp(r'^[0-9]+$').hasMatch(id)) return false;
    
    // Turkish ID checksum validation
    final digits = id.split('').map(int.parse).toList();
    
    // Calculate first checksum
    int sum1 = 0;
    int sum2 = 0;
    for (int i = 0; i < 9; i++) {
      if (i % 2 == 0) {
        sum1 += digits[i];
      } else {
        sum2 += digits[i];
      }
    }
    
    final check1 = (sum1 * 7 - sum2) % 10;
    if (check1 != digits[9]) return false;
    
    // Calculate second checksum
    final totalSum = digits.take(10).reduce((a, b) => a + b);
    final check2 = totalSum % 10;
    if (check2 != digits[10]) return false;
    
    return true;
  }

  /// Validate URL format
  static bool isValidUrl(String url) {
    if (url.isEmpty) return false;
    try {
      final uri = Uri.parse(url);
      return uri.hasScheme && (uri.scheme == 'http' || uri.scheme == 'https');
    } catch (e) {
      return false;
    }
  }

  /// Validate date format (DD/MM/YYYY)
  static bool isValidDateFormat(String date) {
    if (date.isEmpty) return false;
    final dateRegex = RegExp(r'^(\d{2})\/(\d{2})\/(\d{4})$');
    if (!dateRegex.hasMatch(date)) return false;
    
    try {
      final parts = date.split('/');
      final day = int.parse(parts[0]);
      final month = int.parse(parts[1]);
      final year = int.parse(parts[2]);
      
      if (month < 1 || month > 12) return false;
      if (day < 1 || day > 31) return false;
      if (year < 1900 || year > DateTime.now().year) return false;
      
      // Check if date is valid
      final dateTime = DateTime(year, month, day);
      return dateTime.day == day && 
             dateTime.month == month && 
             dateTime.year == year;
    } catch (e) {
      return false;
    }
  }

  /// Validate postal code format
  static bool isValidPostalCode(String postalCode) {
    if (postalCode.isEmpty) return false;
    // Turkish postal codes are 5 digits
    return RegExp(r'^[0-9]{5}$').hasMatch(postalCode);
  }

  /// Validate weight (in kg)
  static bool isValidWeight(double weight) {
    return weight > 0 && weight <= 500; // Reasonable weight range
  }

  /// Validate height (in cm)
  static bool isValidHeight(double height) {
    return height > 0 && height <= 300; // Reasonable height range
  }

  /// Validate BMI value
  static bool isValidBMI(double bmi) {
    return bmi > 0 && bmi <= 100; // Reasonable BMI range
  }

  /// Calculate BMI from weight and height
  static double calculateBMI(double weightKg, double heightCm) {
    if (heightCm <= 0) return 0;
    final heightM = heightCm / 100;
    return weightKg / (heightM * heightM);
  }

  /// Get BMI category
  static String getBMICategory(double bmi) {
    if (bmi < 18.5) return 'Underweight';
    if (bmi < 25) return 'Normal';
    if (bmi < 30) return 'Overweight';
    return 'Obese';
  }

  /// Validate credit card number (basic Luhn algorithm)
  static bool isValidCreditCard(String cardNumber) {
    if (cardNumber.isEmpty) return false;
    
    // Remove spaces and dashes
    final cleanNumber = cardNumber.replaceAll(RegExp(r'[\s\-]'), '');
    
    // Check if all digits
    if (!RegExp(r'^[0-9]+$').hasMatch(cleanNumber)) return false;
    
    // Check length (13-19 digits for most cards)
    if (cleanNumber.length < 13 || cleanNumber.length > 19) return false;
    
    // Luhn algorithm
    int sum = 0;
    bool alternate = false;
    
    for (int i = cleanNumber.length - 1; i >= 0; i--) {
      int digit = int.parse(cleanNumber[i]);
      
      if (alternate) {
        digit *= 2;
        if (digit > 9) {
          digit = (digit % 10) + 1;
        }
      }
      
      sum += digit;
      alternate = !alternate;
    }
    
    return sum % 10 == 0;
  }

  /// Validate CVV code
  static bool isValidCVV(String cvv) {
    if (cvv.isEmpty) return false;
    return RegExp(r'^[0-9]{3,4}$').hasMatch(cvv);
  }

  /// Validate expiry date (MM/YY format)
  static bool isValidExpiryDate(String expiryDate) {
    if (expiryDate.isEmpty) return false;
    
    final regex = RegExp(r'^(0[1-9]|1[0-2])\/([0-9]{2})$');
    if (!regex.hasMatch(expiryDate)) return false;
    
    try {
      final parts = expiryDate.split('/');
      final month = int.parse(parts[0]);
      final year = int.parse('20${parts[1]}');
      final now = DateTime.now();
      final expiry = DateTime(year, month + 1, 0); // Last day of expiry month
      
      return expiry.isAfter(now);
    } catch (e) {
      return false;
    }
  }
}
