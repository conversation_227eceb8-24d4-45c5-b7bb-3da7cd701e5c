import 'dart:developer' as developer;
import 'package:flutter/foundation.dart';

/// Enhanced logging utility for FitGo app
class AppLogger {
  static const String _appName = 'FitGo';
  
  // ANSI color codes for terminal output
  static const String _reset = '\x1B[0m';
  static const String _red = '\x1B[31m';
  static const String _green = '\x1B[32m';
  static const String _yellow = '\x1B[33m';
  static const String _blue = '\x1B[34m';
  static const String _magenta = '\x1B[35m';
  static const String _cyan = '\x1B[36m';
  static const String _white = '\x1B[37m';
  static const String _bold = '\x1B[1m';

  /// Log info message
  static void info(String message, {String? tag}) {
    _log('INFO', message, _blue, tag: tag);
  }

  /// Log success message
  static void success(String message, {String? tag}) {
    _log('SUCCESS', message, _green, tag: tag);
  }

  /// Log warning message
  static void warning(String message, {String? tag}) {
    _log('WARNING', message, _yellow, tag: tag);
  }

  /// Log error message
  static void error(String message, {String? tag, Object? error, StackTrace? stackTrace}) {
    _log('ERROR', message, _red, tag: tag);
    if (error != null) {
      _log('ERROR', 'Exception: $error', _red, tag: tag);
    }
    if (stackTrace != null && kDebugMode) {
      _log('ERROR', 'Stack trace: $stackTrace', _red, tag: tag);
    }
  }

  /// Log debug message (only in debug mode)
  static void debug(String message, {String? tag}) {
    if (kDebugMode) {
      _log('DEBUG', message, _cyan, tag: tag);
    }
  }

  /// Log network request
  static void network(String method, String url, {int? statusCode, String? tag}) {
    final status = statusCode != null ? ' [$statusCode]' : '';
    _log('NETWORK', '$method $url$status', _magenta, tag: tag);
  }

  /// Log database operation
  static void database(String operation, String table, {String? tag}) {
    _log('DATABASE', '$operation on $table', _cyan, tag: tag);
  }

  /// Log navigation
  static void navigation(String from, String to, {String? tag}) {
    _log('NAVIGATION', '$from → $to', _blue, tag: tag);
  }

  /// Log user action
  static void userAction(String action, {String? tag}) {
    _log('USER', action, _green, tag: tag);
  }

  /// Log authentication events
  static void auth(String event, {String? tag}) {
    _log('AUTH', event, _yellow, tag: tag);
  }

  /// Log workout/exercise events
  static void workout(String event, {String? tag}) {
    _log('WORKOUT', event, _magenta, tag: tag);
  }

  /// Log provider state changes
  static void provider(String provider, String state, {String? tag}) {
    _log('PROVIDER', '$provider: $state', _cyan, tag: tag);
  }

  /// Internal logging method
  static void _log(String level, String message, String color, {String? tag}) {
    final timestamp = DateTime.now().toIso8601String().substring(11, 23); // HH:mm:ss.SSS
    final tagStr = tag != null ? '[$tag] ' : '';
    final formattedMessage = '$color$_bold[$_appName]$_reset $color[$level]$_reset $tagStr$message';
    
    if (kDebugMode) {
      // Use developer.log for better integration with Flutter DevTools
      developer.log(
        message,
        time: DateTime.now(),
        level: _getLevelValue(level),
        name: '$_appName${tag != null ? '.$tag' : ''}',
      );
      
      // Also print to console with colors for terminal visibility
      print('$timestamp $formattedMessage');
    }
  }

  /// Get numeric level value for developer.log
  static int _getLevelValue(String level) {
    switch (level) {
      case 'DEBUG':
        return 500;
      case 'INFO':
        return 800;
      case 'SUCCESS':
        return 900;
      case 'WARNING':
        return 1000;
      case 'ERROR':
        return 1200;
      case 'NETWORK':
      case 'DATABASE':
      case 'NAVIGATION':
      case 'USER':
      case 'AUTH':
      case 'WORKOUT':
      case 'PROVIDER':
        return 700;
      default:
        return 800;
    }
  }

  /// Log section separator for better readability
  static void separator({String? title}) {
    if (kDebugMode) {
      final line = '=' * 60;
      if (title != null) {
        final padding = (60 - title.length - 2) ~/ 2;
        final paddedTitle = '=' * padding + ' $title ' + '=' * padding;
        print('$_bold$_white$paddedTitle$_reset');
      } else {
        print('$_bold$_white$line$_reset');
      }
    }
  }

  /// Log app startup
  static void appStartup(String message) {
    separator(title: 'APP STARTUP');
    success(message, tag: 'STARTUP');
  }

  /// Log app shutdown
  static void appShutdown(String message) {
    warning(message, tag: 'SHUTDOWN');
    separator();
  }

  /// Log feature usage
  static void feature(String featureName, String action, {Map<String, dynamic>? data}) {
    final dataStr = data != null ? ' | Data: $data' : '';
    info('$featureName: $action$dataStr', tag: 'FEATURE');
  }

  /// Log performance metrics
  static void performance(String operation, Duration duration, {String? tag}) {
    final ms = duration.inMilliseconds;
    final color = ms > 1000 ? _red : ms > 500 ? _yellow : _green;
    _log('PERFORMANCE', '$operation took ${ms}ms', color, tag: tag);
  }

  /// Log API calls with detailed info
  static void api(String method, String endpoint, {
    int? statusCode,
    Duration? duration,
    Map<String, dynamic>? requestData,
    Map<String, dynamic>? responseData,
    String? error,
  }) {
    final status = statusCode != null ? ' [$statusCode]' : '';
    final time = duration != null ? ' (${duration.inMilliseconds}ms)' : '';
    
    network('$method $endpoint$status$time', '', tag: 'API');
    
    if (requestData != null && kDebugMode) {
      debug('Request: $requestData', tag: 'API');
    }
    
    if (responseData != null && kDebugMode) {
      debug('Response: $responseData', tag: 'API');
    }
    
    if (error != null) {
      AppLogger.error('API Error: $error', tag: 'API');
    }
  }
}
