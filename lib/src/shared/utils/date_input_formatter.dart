import 'package:flutter/services.dart';

/// Input formatter for birth date in DD/MM/YYYY format
/// Automatically adds slashes after day and month
class DateInputFormatter extends TextInputFormatter {
  @override
  TextEditingValue formatEditUpdate(
    TextEditingValue oldValue,
    TextEditingValue newValue,
  ) {
    // Remove all non-digit characters
    final text = newValue.text.replaceAll(RegExp(r'[^\d]'), '');
    final buffer = StringBuffer();
    
    // Limit to 8 digits (DDMMYYYY)
    final limitedText = text.length > 8 ? text.substring(0, 8) : text;
    
    for (int i = 0; i < limitedText.length; i++) {
      // Add slash after day (2 digits)
      if (i == 2) {
        buffer.write('/');
      }
      // Add slash after month (4 digits total including first slash)
      else if (i == 4) {
        buffer.write('/');
      }
      buffer.write(limitedText[i]);
    }
    
    final formatted = buffer.toString();
    
    // Calculate cursor position
    int cursorPosition = formatted.length;
    
    // If user is deleting and cursor would be on a slash, move it back
    if (newValue.selection.baseOffset < oldValue.selection.baseOffset) {
      // User is deleting
      if (formatted.endsWith('/') && formatted.length > 1) {
        cursorPosition = formatted.length - 1;
      }
    }
    
    return TextEditingValue(
      text: formatted,
      selection: TextSelection.collapsed(offset: cursorPosition),
    );
  }
}
