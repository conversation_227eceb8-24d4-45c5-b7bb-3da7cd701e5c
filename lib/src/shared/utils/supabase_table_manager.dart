import 'package:supabase_flutter/supabase_flutter.dart';
import 'package:flutter/foundation.dart';

/// Utility class for managing Supabase tables
class SupabaseTableManager {
  static final SupabaseClient _client = Supabase.instance.client;

  /// Check if a table exists in the database
  static Future<bool> tableExists(String tableName) async {
    try {
      // Try to query the table with a limit of 0 to check existence
      await _client.from(tableName).select('*').limit(0);
      return true;
    } catch (e) {
      debugPrint('Table $tableName does not exist: $e');
      return false;
    }
  }

  /// Create instructor_faqs table if it doesn't exist
  static Future<void> ensureInstructorFaqsTable() async {
    try {
      final exists = await tableExists('instructor_faqs');
      if (exists) {
        debugPrint('✅ instructor_faqs table already exists');
        return;
      }

      debugPrint('🔄 Creating instructor_faqs table...');

      // Try to create the table using RPC call
      try {
        final result = await _client.rpc('create_instructor_faqs_table');
        debugPrint('✅ RPC result: $result');
        debugPrint('✅ instructor_faqs table created successfully');
      } catch (rpcError) {
        debugPrint('❌ RPC call failed: $rpcError');

        // RPC doesn't exist, need manual intervention
        throw Exception(
          'instructor_faqs table does not exist and cannot be created automatically. '
          'Please run the SQL migration manually in Supabase Dashboard:\n'
          '1. Go to Supabase Dashboard > SQL Editor\n'
          '2. Run the SQL from database_schema/create_instructor_faqs_rpc.sql\n'
          '3. Then run the SQL from database_schema/migration_002_instructor_faqs.sql',
        );
      }
    } catch (e) {
      debugPrint('❌ Error ensuring instructor_faqs table: $e');
      rethrow;
    }
  }

  /// List all tables in the database
  static Future<List<String>> listTables() async {
    try {
      final response = await _client
          .from('information_schema.tables')
          .select('table_name')
          .eq('table_schema', 'public');

      return (response as List)
          .map((table) => table['table_name'] as String)
          .toList();
    } catch (e) {
      debugPrint('❌ Error listing tables: $e');
      return [];
    }
  }

  /// Check multiple tables existence
  static Future<Map<String, bool>> checkTablesExistence(
    List<String> tableNames,
  ) async {
    final results = <String, bool>{};

    for (final tableName in tableNames) {
      results[tableName] = await tableExists(tableName);
    }

    return results;
  }

  /// Verify all required tables for instructor profile
  static Future<Map<String, bool>> verifyInstructorProfileTables() async {
    const requiredTables = [
      'profiles',
      'instructors',
      'instructor_work_history',
      'instructor_certifications',
      'instructor_faqs',
      'instructor_reviews',
      'instructor_subscription_configs',
    ];

    return await checkTablesExistence(requiredTables);
  }
}
