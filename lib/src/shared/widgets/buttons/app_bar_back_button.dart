import 'package:fitgo_app/src/shared/extensions/build_context/navigator_ext.dart';
import 'package:fitgo_app/src/theme/colors.dart';
import 'package:flutter/material.dart';

class AppBarBackButton extends StatelessWidget {
  const AppBarBackButton({
    super.key,
    this.onTap,
    this.color = AColor.textColor,
    this.padding,
    this.size = 20,
  });

  final VoidCallback? onTap;
  final Color color;
  final EdgeInsets? padding;
  final double? size;

  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      onTap: onTap ?? () => context.pop(),
      child: Align(
        alignment: Alignment.topLeft,
        child: Icon(Icons.arrow_back_ios_new_rounded, color: color, size: size),
      ),
    );
  }
}
