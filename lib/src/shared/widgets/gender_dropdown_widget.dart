import 'package:flutter/material.dart';
import 'package:fitgo_app/src/shared/enums/gender.dart';
import 'package:fitgo_app/src/theme/colors.dart';

/// Dropdown-style gender selection widget that matches CustomTextFormField design
class GenderDropdownWidget extends StatelessWidget {
  const GenderDropdownWidget({
    super.key,
    required this.selectedGender,
    required this.onGenderChanged,
    this.headerText = 'Gender',
    this.validator,
  });

  final Gender? selectedGender;
  final ValueChanged<Gender?> onGenderChanged;
  final String headerText;
  final String? Function(Gender?)? validator;

  @override
  Widget build(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        // Header text
        Padding(
          padding: const EdgeInsets.only(bottom: 8.0),
          child: Text(
            headerText,
            style: const TextStyle(
              fontSize: 16,
              fontWeight: FontWeight.w500,
              color: Colors.white,
            ),
          ),
        ),

        // Dropdown field
        Container(
          width: double.infinity,
          decoration: BoxDecoration(
            color: Colors.white,
            borderRadius: BorderRadius.circular(12),
            border: Border.all(color: const Color(0xFFE5E7EB), width: 1),
          ),
          child: DropdownButton<Gender>(
            value: selectedGender,
            onChanged: onGenderChanged,
            hint: const Padding(
              padding: EdgeInsets.symmetric(horizontal: 16, vertical: 12),
              child: Text(
                'Select gender',
                style: TextStyle(color: Color(0xFF6B7280), fontSize: 16),
              ),
            ),
            selectedItemBuilder: (BuildContext context) {
              return Gender.values.map<Widget>((Gender gender) {
                return Padding(
                  padding: const EdgeInsets.symmetric(
                    horizontal: 16,
                    vertical: 12,
                  ),
                  child: Row(
                    children: [
                      Icon(gender.icon, size: 18, color: AColor.fitgoGreen),
                      const SizedBox(width: 12),
                      Text(
                        gender.getDisplayName(context),
                        style: const TextStyle(
                          color: Color(0xFF111827),
                          fontSize: 16,
                        ),
                      ),
                    ],
                  ),
                );
              }).toList();
            },
            style: const TextStyle(color: Color(0xFF111827), fontSize: 16),
            dropdownColor: Colors.white,
            icon: const Padding(
              padding: EdgeInsets.only(right: 16),
              child: Icon(Icons.keyboard_arrow_down, color: Color(0xFF6B7280)),
            ),
            isExpanded: true,
            underline: const SizedBox.shrink(),
            borderRadius: BorderRadius.circular(12),
            menuMaxHeight: 200,
            alignment: AlignmentDirectional.topStart,
            items:
                Gender.values.map((gender) {
                  final isSelected = selectedGender == gender;
                  return DropdownMenuItem<Gender>(
                    value: gender,
                    child: Container(
                      width: double.infinity,
                      margin: const EdgeInsets.symmetric(horizontal: 8),
                      padding: const EdgeInsets.symmetric(
                        horizontal: 12,
                        vertical: 8,
                      ),
                      decoration: BoxDecoration(
                        color:
                            isSelected
                                ? AColor.fitgoGreen.withValues(alpha: 0.1)
                                : Colors.transparent,
                        borderRadius: BorderRadius.circular(8),
                      ),
                      child: Row(
                        children: [
                          Icon(
                            gender.icon,
                            size: 18,
                            color:
                                isSelected
                                    ? AColor.fitgoGreen
                                    : const Color(0xFF6B7280),
                          ),
                          const SizedBox(width: 12),
                          Text(
                            gender.getDisplayName(context),
                            style: TextStyle(
                              color:
                                  isSelected
                                      ? AColor.fitgoGreen
                                      : const Color(0xFF111827),
                              fontSize: 16,
                              fontWeight:
                                  isSelected
                                      ? FontWeight.w600
                                      : FontWeight.w400,
                            ),
                          ),
                        ],
                      ),
                    ),
                  );
                }).toList(),
          ),
        ),

        // Validation error message
        if (validator != null)
          Builder(
            builder: (context) {
              final errorMessage = validator!(selectedGender);
              if (errorMessage != null) {
                return Padding(
                  padding: const EdgeInsets.only(top: 8.0),
                  child: Text(
                    errorMessage,
                    style: const TextStyle(color: Colors.red, fontSize: 12),
                  ),
                );
              }
              return const SizedBox.shrink();
            },
          ),
      ],
    );
  }
}

/// Compact inline gender dropdown that can be used in forms
class InlineGenderDropdown extends StatelessWidget {
  const InlineGenderDropdown({
    super.key,
    required this.selectedGender,
    required this.onGenderChanged,
    this.hint = 'Select gender',
  });

  final Gender? selectedGender;
  final ValueChanged<Gender?> onGenderChanged;
  final String hint;

  @override
  Widget build(BuildContext context) {
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 4),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(8),
        border: Border.all(color: const Color(0xFFE5E7EB), width: 1),
      ),
      child: DropdownButton<Gender>(
        value: selectedGender,
        onChanged: onGenderChanged,
        hint: Text(
          hint,
          style: const TextStyle(color: Color(0xFF6B7280), fontSize: 14),
        ),
        style: const TextStyle(color: Color(0xFF111827), fontSize: 14),
        dropdownColor: Colors.white,
        underline: const SizedBox.shrink(),
        icon: const Icon(
          Icons.keyboard_arrow_down,
          color: Color(0xFF6B7280),
          size: 18,
        ),
        isExpanded: false,
        menuMaxHeight: 150,
        borderRadius: BorderRadius.circular(8),
        alignment: AlignmentDirectional.topStart,
        items:
            Gender.values.map((gender) {
              final isSelected = selectedGender == gender;
              return DropdownMenuItem<Gender>(
                value: gender,
                child: Container(
                  margin: const EdgeInsets.symmetric(horizontal: 4),
                  padding: const EdgeInsets.symmetric(
                    horizontal: 8,
                    vertical: 4,
                  ),
                  decoration: BoxDecoration(
                    color:
                        isSelected
                            ? AColor.fitgoGreen.withValues(alpha: 0.1)
                            : Colors.transparent,
                    borderRadius: BorderRadius.circular(6),
                  ),
                  child: Row(
                    mainAxisSize: MainAxisSize.min,
                    children: [
                      Icon(
                        gender.icon,
                        size: 16,
                        color:
                            isSelected
                                ? AColor.fitgoGreen
                                : const Color(0xFF6B7280),
                      ),
                      const SizedBox(width: 8),
                      Text(
                        gender.getDisplayName(context),
                        style: TextStyle(
                          color:
                              isSelected
                                  ? AColor.fitgoGreen
                                  : const Color(0xFF111827),
                          fontSize: 14,
                          fontWeight:
                              isSelected ? FontWeight.w600 : FontWeight.w400,
                        ),
                      ),
                    ],
                  ),
                ),
              );
            }).toList(),
      ),
    );
  }
}
