import 'package:flutter/material.dart';
import 'package:fitgo_app/src/shared/enums/gender.dart';
import 'package:fitgo_app/src/theme/colors.dart';

/// Gender selection widget with radio buttons
class GenderSelectionWidget extends StatelessWidget {
  const GenderSelectionWidget({
    super.key,
    required this.selectedGender,
    required this.onGenderChanged,
    this.headerText = 'Cinsiyet',
    this.isRequired = false,
    this.validator,
  });

  final Gender? selectedGender;
  final ValueChanged<Gender?> onGenderChanged;
  final String headerText;
  final bool isRequired;
  final String? Function(Gender?)? validator;

  @override
  Widget build(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        // Header text
        Padding(
          padding: const EdgeInsets.only(bottom: 8.0),
          child: Row(
            children: [
              Text(
                headerText,
                style: const TextStyle(
                  fontSize: 16,
                  fontWeight: FontWeight.w500,
                  color: Colors.white,
                ),
              ),
              if (isRequired)
                const Text(
                  ' *',
                  style: TextStyle(
                    color: Colors.red,
                    fontSize: 16,
                    fontWeight: FontWeight.w500,
                  ),
                ),
            ],
          ),
        ),

        // Gender options
        Container(
          decoration: BoxDecoration(
            color: const Color(0xFF1F2937),
            borderRadius: BorderRadius.circular(12),
            border: Border.all(color: const Color(0xFF374151), width: 1),
          ),
          child: Column(
            children:
                Gender.values.map((gender) {
                  final isSelected = selectedGender == gender;

                  return InkWell(
                    onTap: () => onGenderChanged(gender),
                    borderRadius: BorderRadius.circular(12),
                    child: Container(
                      padding: const EdgeInsets.symmetric(
                        horizontal: 16,
                        vertical: 12,
                      ),
                      decoration: BoxDecoration(
                        color:
                            isSelected
                                ? AColor.fitgoGreen.withOpacity(0.1)
                                : Colors.transparent,
                        borderRadius: BorderRadius.circular(12),
                      ),
                      child: Row(
                        children: [
                          // Radio button
                          Container(
                            width: 20,
                            height: 20,
                            decoration: BoxDecoration(
                              shape: BoxShape.circle,
                              border: Border.all(
                                color:
                                    isSelected
                                        ? AColor.fitgoGreen
                                        : const Color(0xFF6B7280),
                                width: 2,
                              ),
                              color:
                                  isSelected
                                      ? AColor.fitgoGreen
                                      : Colors.transparent,
                            ),
                            child:
                                isSelected
                                    ? const Icon(
                                      Icons.check,
                                      size: 12,
                                      color: Colors.white,
                                    )
                                    : null,
                          ),

                          const SizedBox(width: 12),

                          // Gender icon
                          Icon(
                            gender.icon,
                            size: 20,
                            color:
                                isSelected
                                    ? AColor.fitgoGreen
                                    : const Color(0xFF9CA3AF),
                          ),

                          const SizedBox(width: 12),

                          // Gender text
                          Expanded(
                            child: Text(
                              gender.getDisplayName(context),
                              style: TextStyle(
                                fontSize: 16,
                                fontWeight:
                                    isSelected
                                        ? FontWeight.w600
                                        : FontWeight.w400,
                                color:
                                    isSelected
                                        ? AColor.fitgoGreen
                                        : Colors.white,
                              ),
                            ),
                          ),
                        ],
                      ),
                    ),
                  );
                }).toList(),
          ),
        ),

        // Validation error message
        if (validator != null)
          Builder(
            builder: (context) {
              final errorMessage = validator!(selectedGender);
              if (errorMessage != null) {
                return Padding(
                  padding: const EdgeInsets.only(top: 8.0),
                  child: Text(
                    errorMessage,
                    style: const TextStyle(color: Colors.red, fontSize: 12),
                  ),
                );
              }
              return const SizedBox.shrink();
            },
          ),
      ],
    );
  }
}
