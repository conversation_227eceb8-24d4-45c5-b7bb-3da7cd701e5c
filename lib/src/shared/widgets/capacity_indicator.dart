import 'package:flutter/material.dart';

/// Widget to display instructor capacity status
class CapacityIndicator extends StatelessWidget {
  final int currentStudents;
  final int maxStudents;
  final bool showProgressBar;
  final bool showText;
  final double? width;
  final double? height;

  const CapacityIndicator({
    super.key,
    required this.currentStudents,
    required this.maxStudents,
    this.showProgressBar = true,
    this.showText = true,
    this.width,
    this.height,
  });

  @override
  Widget build(BuildContext context) {
    final percentage = maxStudents > 0 ? currentStudents / maxStudents : 0.0;
    final isNearlyFull = percentage > 0.8;
    final isFull = currentStudents >= maxStudents;

    Color getStatusColor() {
      if (isFull) return Colors.red;
      if (isNearlyFull) return Colors.orange;
      return const Color(0xFF10B981); // Green
    }

    return Container(
      width: width,
      height: height,
      child: Column(
        mainAxisSize: MainAxisSize.min,
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          if (showText) ...[
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Text(
                  'Capacity',
                  style: TextStyle(
                    color: Colors.grey[400],
                    fontSize: 12,
                    fontWeight: FontWeight.w500,
                  ),
                ),
                Text(
                  '$currentStudents/$maxStudents',
                  style: TextStyle(
                    color: getStatusColor(),
                    fontSize: 12,
                    fontWeight: FontWeight.w600,
                  ),
                ),
              ],
            ),
            const SizedBox(height: 4),
          ],

          if (showProgressBar) ...[
            Container(
              height: 6,
              decoration: BoxDecoration(
                color: Colors.grey[800],
                borderRadius: BorderRadius.circular(3),
              ),
              child: ClipRRect(
                borderRadius: BorderRadius.circular(3),
                child: LinearProgressIndicator(
                  value: percentage,
                  backgroundColor: Colors.transparent,
                  valueColor: AlwaysStoppedAnimation<Color>(getStatusColor()),
                ),
              ),
            ),
          ],
        ],
      ),
    );
  }
}

/// Large capacity status widget for detail pages
class CapacityStatusCard extends StatelessWidget {
  final int currentStudents;
  final int maxStudents;
  final String title;

  const CapacityStatusCard({
    super.key,
    required this.currentStudents,
    required this.maxStudents,
    this.title = 'Student Capacity',
  });

  @override
  Widget build(BuildContext context) {
    final percentage = maxStudents > 0 ? currentStudents / maxStudents : 0.0;
    final isNearlyFull = percentage > 0.8;
    final isFull = currentStudents >= maxStudents;
    final remaining = maxStudents - currentStudents;

    Color getStatusColor() {
      if (isFull) return Colors.red;
      if (isNearlyFull) return Colors.orange;
      return const Color(0xFF10B981); // Green
    }

    String getStatusMessage() {
      if (isFull) return 'This instructor has reached maximum capacity';
      if (isNearlyFull) return 'Only $remaining spots remaining - enroll soon!';
      return '$remaining spots available for new students';
    }

    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: const Color(0xFF1F2937),
        borderRadius: BorderRadius.circular(12),
        border: Border.all(color: getStatusColor().withOpacity(0.2), width: 1),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Icon(Icons.people, color: getStatusColor(), size: 20),
              const SizedBox(width: 8),
              Text(
                title,
                style: const TextStyle(
                  color: Colors.white,
                  fontSize: 16,
                  fontWeight: FontWeight.w600,
                ),
              ),
            ],
          ),

          const SizedBox(height: 12),

          // Progress bar
          Container(
            height: 8,
            decoration: BoxDecoration(
              color: Colors.grey[800],
              borderRadius: BorderRadius.circular(4),
            ),
            child: ClipRRect(
              borderRadius: BorderRadius.circular(4),
              child: LinearProgressIndicator(
                value: percentage,
                backgroundColor: Colors.transparent,
                valueColor: AlwaysStoppedAnimation<Color>(getStatusColor()),
              ),
            ),
          ),

          const SizedBox(height: 8),

          // Numbers
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Text(
                '$currentStudents students enrolled',
                style: TextStyle(color: Colors.grey[400], fontSize: 14),
              ),
              Text(
                'Max: $maxStudents',
                style: TextStyle(color: Colors.grey[400], fontSize: 14),
              ),
            ],
          ),

          const SizedBox(height: 8),

          // Status message
          Text(
            getStatusMessage(),
            style: TextStyle(
              color: getStatusColor(),
              fontSize: 13,
              fontWeight: FontWeight.w500,
            ),
          ),
        ],
      ),
    );
  }
}
