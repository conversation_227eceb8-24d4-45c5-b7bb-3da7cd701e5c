import 'package:fitgo_app/src/shared/extensions/build_context/screen_util_ext.dart';
import 'package:flutter/material.dart';
import 'package:hooks_riverpod/hooks_riverpod.dart';
import 'package:go_router/go_router.dart';
import 'package:fitgo_app/src/theme/colors.dart';
import 'package:fitgo_app/src/shared/constants/app_text_style.dart';
import 'package:fitgo_app/src/shared/widgets/text_widget/text_widget.dart';
import 'package:fitgo_app/src/shared/extensions/string_hardcoded.dart';
import 'package:fitgo_app/src/auth/application/auth_provider.dart';
import 'package:fitgo_app/src/shared/providers/supabase_provider.dart';

class AppDrawer extends ConsumerWidget {
  const AppDrawer({super.key});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final supabaseService = ref.watch(supabaseServiceProvider);
    final user = supabaseService.client.auth.currentUser;

    return SizedBox(
      width: context.width * 0.75,
      child: Drawer(
        backgroundColor: const Color(0xFF1F2937),
        child: Safe<PERSON><PERSON>(
          child: Column(
            children: [
              // Header with user profile
              Container(
                padding: const EdgeInsets.all(16),
                child: Column(
                  children: [
                    // Language selector
                    Row(
                      mainAxisAlignment: MainAxisAlignment.end,
                      children: [
                        Container(
                          padding: const EdgeInsets.symmetric(
                            horizontal: 12,
                            vertical: 6,
                          ),
                          decoration: BoxDecoration(
                            color: const Color(0xFF374151),
                            borderRadius: BorderRadius.circular(16),
                          ),
                          child: TextWidget(
                            'EN',
                            style: ATextStyle.small.copyWith(
                              color: AColor.textColor,
                              fontWeight: FontWeight.w600,
                            ),
                          ),
                        ),
                      ],
                    ),
                    // User avatar
                    Container(
                      width: 80,
                      height: 80,
                      decoration: BoxDecoration(
                        shape: BoxShape.circle,
                        border: Border.all(color: AColor.fitgoGreen, width: 3),
                        image:
                            user?.userMetadata?['avatar_url'] != null
                                ? DecorationImage(
                                  image: NetworkImage(
                                    user!.userMetadata!['avatar_url'],
                                  ),
                                  fit: BoxFit.cover,
                                )
                                : null,
                      ),
                      child:
                          user?.userMetadata?['avatar_url'] == null
                              ? const Icon(
                                Icons.person,
                                size: 40,
                                color: AColor.textColor,
                              )
                              : null,
                    ),
                  ],
                ),
              ),
              // Menu items
              Expanded(
                child: ListView(
                  padding: const EdgeInsets.symmetric(horizontal: 8),
                  children: [
                    _buildMenuItem(
                      icon: Icons.person_outline,
                      title: 'Profile'.hardcoded,
                      onTap: () {
                        context.pop();
                        // Navigate to profile page
                      },
                    ),
                    _buildDivider(),
                    _buildMenuItem(
                      icon: Icons.star_outline,
                      title: 'Membership'.hardcoded,
                      onTap: () {
                        context.pop();
                        // Navigate to membership page
                      },
                    ),
                    _buildDivider(),
                    _buildMenuItem(
                      icon: Icons.notifications_outlined,
                      title: 'Notifications'.hardcoded,
                      onTap: () {
                        context.pop();
                        // Navigate to notifications page
                      },
                    ),
                    _buildDivider(),
                    _buildMenuItem(
                      icon: Icons.security_outlined,
                      title: 'Privacy and Security'.hardcoded,
                      onTap: () {
                        context.pop();
                        // Navigate to privacy page
                      },
                    ),
                    _buildDivider(),
                    _buildMenuItem(
                      icon: Icons.logout_outlined,
                      title: 'Sign Out'.hardcoded,
                      onTap: () => _showLogoutDialog(context, ref),
                      isDestructive: true,
                    ),
                  ],
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildDivider() {
    return Container(
      height: 1,
      margin: const EdgeInsets.symmetric(horizontal: 16),
      color: const Color(0xFF374151),
    );
  }

  Widget _buildMenuItem({
    required IconData icon,
    required String title,
    required VoidCallback onTap,
    bool isDestructive = false,
  }) {
    return ListTile(
      leading: Icon(
        icon,
        color: isDestructive ? Colors.red : AColor.textColor,
        size: 24,
      ),
      title: TextWidget(
        title,
        style: ATextStyle.medium.copyWith(
          color: isDestructive ? Colors.red : AColor.textColor,
          fontWeight: FontWeight.w500,
        ),
      ),
      onTap: onTap,
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
      tileColor: AColor.transparent,
      hoverColor: const Color(0xFF374151),
      contentPadding: const EdgeInsets.symmetric(horizontal: 16),
    );
  }

  void _showLogoutDialog(BuildContext context, WidgetRef ref) {
    showDialog(
      context: context,
      builder: (BuildContext context) {
        return AlertDialog(
          backgroundColor: const Color(0xFF1F2937),
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(16),
          ),
          title: TextWidget(
            'Sign Out'.hardcoded,
            style: ATextStyle.large.copyWith(
              color: AColor.textColor,
              fontWeight: FontWeight.bold,
            ),
          ),
          content: TextWidget(
            'Are you sure you want to sign out?'.hardcoded,
            style: ATextStyle.medium.copyWith(color: AColor.grey),
          ),
          actions: [
            TextButton(
              onPressed: () => context.pop(),
              child: TextWidget(
                'Cancel'.hardcoded,
                style: ATextStyle.medium.copyWith(color: AColor.textColor),
              ),
            ),
            TextButton(
              onPressed: () async {
                try {
                  // Sign out first
                  await ref.read(authNotifierProvider.notifier).signOut();

                  // Close dialog and drawer, then navigate
                  if (context.mounted) {
                    context.pop(); // Close dialog
                    context.pop(); // Close drawer
                    context.go('/auth');
                  }
                } catch (e) {
                  // Close dialog and drawer on error
                  if (context.mounted) {
                    context.pop(); // Close dialog
                    context.pop(); // Close drawer
                    ScaffoldMessenger.of(context).showSnackBar(
                      SnackBar(
                        content: Text('Logout failed: $e'),
                        backgroundColor: Colors.red,
                      ),
                    );
                  }
                }
              },
              child: TextWidget(
                'Sign Out'.hardcoded,
                style: ATextStyle.medium.copyWith(
                  color: Colors.red,
                  fontWeight: FontWeight.w600,
                ),
              ),
            ),
          ],
        );
      },
    );
  }
}
