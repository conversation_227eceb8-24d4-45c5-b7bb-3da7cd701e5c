import 'package:fitgo_app/src/auth/application/auth_provider.dart';
import 'package:flutter/material.dart';
import 'package:hooks_riverpod/hooks_riverpod.dart';
import 'package:fitgo_app/src/shared/extensions/build_context/screen_util_ext.dart';
import 'package:fitgo_app/src/shared/constants/app_path.dart';
import 'package:fitgo_app/src/shared/widgets/image/asset_image.dart';
import 'package:fitgo_app/src/theme/colors.dart';

/// A common scaffold for onboarding, landing and auth screens
/// with a background image, safe area, padding and flexible layout.
/// Automatically shows a half-width top-left image whenever
/// `isRegisterOpenProvider` is true.
class OnboardingScaffold extends ConsumerWidget {
  /// The main content of the screen.
  final Widget child;

  /// Optional widget displayed at the top (e.g. back button, language selector).
  final Widget? topBar;

  /// Optional widget displayed beneath [child].
  final Widget? bottomBar;

  /// Persistent bottom sheet (e.g. login form).
  final Widget? sheet;

  /// Full-screen background.
  final String backgroundImagePath;

  const OnboardingScaffold({
    required this.child,
    this.topBar,
    this.bottomBar,
    this.sheet,
    this.backgroundImagePath = APath.onboardingBackground,
    super.key,
  });

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final isRegisterOpen = ref.watch(isRegisterOpenProvider);
    final statusBar = context.paddingTop;

    return PopScope(
      canPop: false,
      child: Scaffold(
        backgroundColor: AColor.onboardingBackgroundColor,
        bottomSheet: sheet,
        body: Stack(
          clipBehavior: Clip.none,
          children: [
            if (!isRegisterOpen)
              Positioned.fill(
                child: AImage(imgPath: backgroundImagePath, fit: BoxFit.cover),
              ),

            if (isRegisterOpen)
              Positioned(
                top: statusBar + 24,
                left: 24,
                child: SizedBox(
                  width: context.width * 0.3,
                  child: AImage(imgPath: APath.appLogo, fit: BoxFit.contain),
                ),
              ),

            SafeArea(
              child: Padding(
                padding: const EdgeInsets.all(24.0),
                child: Column(
                  children: [
                    if (topBar != null) topBar!,
                    Expanded(child: child),
                    if (bottomBar != null) bottomBar!,
                  ],
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }
}
