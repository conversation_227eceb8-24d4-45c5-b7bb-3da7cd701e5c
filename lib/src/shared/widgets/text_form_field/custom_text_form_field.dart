import 'dart:async';

import 'package:fitgo_app/src/shared/constants/app_fonts.dart';
import 'package:fitgo_app/src/shared/constants/app_text_style.dart';
import 'package:fitgo_app/src/shared/enums/regex_type.dart';
import 'package:fitgo_app/src/shared/utils/date_input_formatter.dart';
import 'package:fitgo_app/src/shared/widgets/text_widget/text_widget.dart';
import 'package:fitgo_app/src/theme/colors.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';

class CustomTextFormField extends StatefulWidget {
  const CustomTextFormField({
    super.key,
    this.controller,
    this.labelText,
    this.hintText,
    this.errorText,
    this.onChanged,
    this.validator,
    this.obscureText = false,
    this.enabled = true,
    this.prefixIcon,
    this.suffixIcon,
    this.keyboardType,
    this.textInputAction,
    this.maxLines = 1,
    this.minLines,
    this.debounceDuration,
    this.fillcolor = AColor.white,
    this.readOnly = false,
    this.onTap,
    this.style,
    this.contentPadding,
    this.headerText,
    this.headerTextStyle,
    this.borderColor,
    this.inputFormatters,
    this.regexType,
    this.focusNode,
    this.autovalidateMode,
  });

  final TextEditingController? controller;
  final String? labelText;
  final String? hintText;
  final String? errorText;
  final ValueChanged<String>? onChanged;
  final String? Function(String?)? validator;
  final bool obscureText;
  final bool enabled;
  final Widget? prefixIcon;
  final Widget? suffixIcon;
  final TextInputType? keyboardType;
  final TextInputAction? textInputAction;
  final int? maxLines;
  final int? minLines;
  final Duration? debounceDuration;
  final Color? fillcolor;
  final bool readOnly;
  final VoidCallback? onTap;
  final TextStyle? style;
  final EdgeInsets? contentPadding;
  final String? headerText;
  final TextStyle? headerTextStyle;
  final FocusNode? focusNode;
  final Color? borderColor;
  final List<TextInputFormatter>? inputFormatters;
  final RegexType? regexType;
  final AutovalidateMode? autovalidateMode;

  @override
  State<CustomTextFormField> createState() => _CustomTextFormFieldState();
}

class _CustomTextFormFieldState extends State<CustomTextFormField> {
  Timer? _debounceTimer;

  @override
  Widget build(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      spacing: 8,
      children: [
        if (widget.headerText != null)
          TextWidget(
            widget.headerText!,
            style:
                widget.headerTextStyle ??
                ATextStyle.medium.copyWith(fontFamily: AppFonts.poppins),
          ),
        TextFormField(
          focusNode: widget.focusNode,
          controller: widget.controller,
          readOnly: widget.readOnly,
          onTap: widget.onTap,
          onChanged: (value) {
            if (widget.debounceDuration != null) {
              _debounceTimer?.cancel();
              _debounceTimer = Timer(widget.debounceDuration!, () {
                widget.onChanged?.call(value);
              });
            } else {
              widget.onChanged?.call(value);
            }
          },
          validator: widget.validator,
          obscureText: widget.obscureText,
          enabled: widget.enabled,
          keyboardType: widget.keyboardType,
          textInputAction: widget.textInputAction,
          maxLines: widget.maxLines,
          minLines: widget.minLines,
          style:
              widget.style ??
              ATextStyle.medium.copyWith(
                fontFamily: AppFonts.poppins,
                color: AColor.black,
              ),
          cursorColor: AColor.black,
          inputFormatters: _inputFormatters,
          autovalidateMode: widget.autovalidateMode,
          decoration: InputDecoration(
            isDense: true, // Makes the field more compact
            contentPadding: widget.contentPadding ?? const EdgeInsets.all(12),
            labelText: widget.labelText,
            hintText: widget.hintText,
            hintStyle: ATextStyle.medium.copyWith(
              fontFamily: AppFonts.poppins,
              color: AColor.black,
            ),
            errorText: widget.errorText,
            prefixIcon: widget.prefixIcon,
            prefixIconConstraints: const BoxConstraints(
              minWidth: 40,
              minHeight: 20,
            ),
            suffixIcon: widget.suffixIcon,
            suffixIconConstraints: const BoxConstraints(
              minWidth: 40,
              minHeight: 20,
            ),
            floatingLabelStyle: const TextStyle(color: Colors.black),
            border: OutlineInputBorder(borderRadius: BorderRadius.circular(15)),
            enabledBorder: OutlineInputBorder(
              borderRadius: BorderRadius.circular(15),
              borderSide: BorderSide(
                color: widget.borderColor ?? AColor.fitgoColor3,
              ),
            ),
            focusedBorder: OutlineInputBorder(
              borderRadius: BorderRadius.circular(15),
            ),
            errorBorder: OutlineInputBorder(
              borderRadius: BorderRadius.circular(15),
              borderSide: const BorderSide(color: AColor.red),
            ),
            focusedErrorBorder: OutlineInputBorder(
              borderRadius: BorderRadius.circular(15),
              borderSide: const BorderSide(color: AColor.red),
            ),
            filled: true,
            fillColor: widget.fillcolor,
          ),
        ),
      ],
    );
  }

  List<TextInputFormatter> get _inputFormatters {
    // Eğer kullanıcı tarafından inputFormatters verilmişse onu kullan
    if (widget.inputFormatters != null) {
      return widget.inputFormatters!;
    }
    // regexType'a göre varsayılan inputFormatters üret
    if (widget.regexType != null) {
      switch (widget.regexType!) {
        case RegexType.phone:
          // Telefon: rakam, artı, boşluk ve tire
          return [FilteringTextInputFormatter.allow(RegExp(r'[0-9+\s-]'))];

        case RegexType.id:
          // Sadece rakam
          return [FilteringTextInputFormatter.allow(RegExp(r'[0-9]'))];

        case RegexType.birthDate:
          // Birth date: DD/MM/YYYY format with automatic slash insertion
          return [
            DateInputFormatter(),
            LengthLimitingTextInputFormatter(10), // DD/MM/YYYY = 10 characters
          ];
        // Diğer regexType'lar için gerekiyorsa eklemeler yapabilirsiniz.
        default:
          return [];
      }
    }
    return [];
  }
}
