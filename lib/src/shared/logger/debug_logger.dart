import 'package:fitgo_app/src/shared/logger/logger.dart';

void networkLog({
  required String repository,
  required dynamic message,
  dynamic error,
  StackTrace? stackTrace,
}) {
  loggerNoMethod.e('REPOSITORY: $repository');
  if (error != null && stackTrace != null) {
    logger.e('$message\nError: $error\nStackTrace: $stackTrace');
  } else if (error != null) {
    logger.e('$message\nError: $error');
  } else {
    logger.e(message);
  }
}

void dioLog(dynamic message) {
  if (env.enableDioLogs) {
    loggerNoMethod.w(message);
  }
}

void dioDataLog(dynamic message) {
  if (env.enableDioLogs) {
    loggerData.w(message);
  }
}

void appExceptionLog(dynamic message, dynamic error, StackTrace? stackTrace) {
  logger.e('APP EXCEPTION:');
  if (error != null && stackTrace != null) {
    logger.e('$message\nError: $error\nStackTrace: $stackTrace');
  } else if (error != null) {
    logger.e('$message\nError: $error');
  } else {
    logger.e(message);
  }
}

void dataLog(dynamic message) {
  loggerData.w(message);
}

void navigationLog(dynamic message) {
  loggerData.v(message);
}

// prints error logs
void loge(dynamic message) {
  logger.e(message);
}

// prints error logs
void logi(dynamic message) {
  logger.i(message);
}
