import 'package:fitgo_app/core/environment/environment_interface.dart';
import 'package:logger/logger.dart';
import 'package:fitgo_app/core/environment/environment.dart'; // Import the environment
import 'package:hooks_riverpod/hooks_riverpod.dart';

// Create a global reference for environment
// This will be initialized in your app's startup
late final IEnvironment env;

// Initialize this in your main.dart before runApp
void initLogger(WidgetRef ref) {
  env = ref.read(environmentProvider);
}

class MyFilter extends LogFilter {
  @override
  bool shouldLog(LogEvent event) {
    return env.isDev || env.enableDioLogs;
  }
}

Logger logger = Logger(filter: MyFilter());

Logger loggerNoMethod = Logger(
  filter: MyFilter(),
  printer: PrettyPrinter(methodCount: 0),
);

Logger loggerData = Logger(
  filter: MyFilter(),
  printer: PrettyPrinter(methodCount: 0, printEmojis: false),
);
