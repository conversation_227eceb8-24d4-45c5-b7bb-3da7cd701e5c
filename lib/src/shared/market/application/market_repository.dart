import 'package:hooks_riverpod/hooks_riverpod.dart';
import '../domain/market_models.dart';

/// Provider for market repository
final marketRepositoryProvider = Provider<MarketRepository>((ref) {
  return MarketRepository();
});

/// Repository for FitGo Market data
class MarketRepository {
  /// Get all market products
  Future<List<MarketProduct>> getProducts({
    ProductCategory? category,
    bool popularOnly = false,
    bool newOnly = false,
    String? searchQuery,
    String? brandId,
  }) async {
    try {
      // Simulate network delay
      await Future.delayed(const Duration(milliseconds: 500));
      
      var products = MockMarketData.getProducts();
      
      // Apply filters
      if (category != null) {
        products = products.where((p) => p.category == category).toList();
      }
      
      if (popularOnly) {
        products = products.where((p) => p.isPopular).toList();
      }
      
      if (newOnly) {
        products = products.where((p) => p.isNew).toList();
      }

      if (brandId != null) {
        products = products.where((p) => p.brand.id == brandId).toList();
      }

      if (searchQuery != null && searchQuery.isNotEmpty) {
        final query = searchQuery.toLowerCase();
        products = products.where((p) => 
          p.name.toLowerCase().contains(query) ||
          p.description.toLowerCase().contains(query) ||
          p.brand.name.toLowerCase().contains(query) ||
          p.tags.any((tag) => tag.toLowerCase().contains(query))
        ).toList();
      }
      
      return products;
    } catch (e) {
      throw Exception('Failed to load products: $e');
    }
  }

  /// Get featured products for homepage
  Future<List<MarketProduct>> getFeaturedProducts() async {
    try {
      await Future.delayed(const Duration(milliseconds: 300));
      final products = MockMarketData.getProducts();
      return products.where((p) => p.isPopular || p.isNew).take(6).toList();
    } catch (e) {
      throw Exception('Failed to load featured products: $e');
    }
  }

  /// Get products by category
  Future<List<MarketProduct>> getProductsByCategory(ProductCategory category) async {
    return getProducts(category: category);
  }

  /// Get popular products
  Future<List<MarketProduct>> getPopularProducts() async {
    return getProducts(popularOnly: true);
  }

  /// Get new products
  Future<List<MarketProduct>> getNewProducts() async {
    return getProducts(newOnly: true);
  }

  /// Search products
  Future<List<MarketProduct>> searchProducts(String query) async {
    return getProducts(searchQuery: query);
  }

  /// Get product by ID
  Future<MarketProduct?> getProductById(String id) async {
    try {
      await Future.delayed(const Duration(milliseconds: 200));
      final products = MockMarketData.getProducts();
      return products.firstWhere(
        (p) => p.id == id,
        orElse: () => throw Exception('Product not found'),
      );
    } catch (e) {
      return null;
    }
  }

  /// Get all brands
  Future<List<Brand>> getBrands() async {
    try {
      await Future.delayed(const Duration(milliseconds: 200));
      return MockMarketData.getBrands();
    } catch (e) {
      throw Exception('Failed to load brands: $e');
    }
  }

  /// Get featured brands
  Future<List<Brand>> getFeaturedBrands() async {
    try {
      final brands = await getBrands();
      return brands.where((b) => b.isFeatured).toList();
    } catch (e) {
      throw Exception('Failed to load featured brands: $e');
    }
  }

  /// Get promotional banners
  Future<List<PromoBanner>> getPromoBanners() async {
    try {
      await Future.delayed(const Duration(milliseconds: 200));
      return MockMarketData.getPromoBanners();
    } catch (e) {
      throw Exception('Failed to load promo banners: $e');
    }
  }

  /// Get products by brand
  Future<List<MarketProduct>> getProductsByBrand(String brandId) async {
    return getProducts(brandId: brandId);
  }

  /// Get recommended products for user
  Future<List<MarketProduct>> getRecommendedProducts({
    String? userId,
    ProductCategory? preferredCategory,
  }) async {
    try {
      await Future.delayed(const Duration(milliseconds: 400));
      
      // For now, return a mix of popular and new products
      // In a real app, this would use ML/AI recommendations
      final products = MockMarketData.getProducts();
      
      var recommended = <MarketProduct>[];
      
      // Add popular products
      recommended.addAll(products.where((p) => p.isPopular).take(3));
      
      // Add new products
      recommended.addAll(products.where((p) => p.isNew).take(2));
      
      // Add products from preferred category if specified
      if (preferredCategory != null) {
        recommended.addAll(
          products.where((p) => p.category == preferredCategory).take(2)
        );
      }
      
      // Remove duplicates and limit to 6 items
      final uniqueRecommended = recommended.toSet().toList();
      return uniqueRecommended.take(6).toList();
    } catch (e) {
      throw Exception('Failed to load recommended products: $e');
    }
  }
}
