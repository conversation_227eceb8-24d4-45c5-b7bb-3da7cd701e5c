import 'package:hooks_riverpod/hooks_riverpod.dart';
import '../domain/market_models.dart';
import 'market_repository.dart';

/// Provider for featured market products
final featuredProductsProvider = FutureProvider<List<MarketProduct>>((ref) async {
  final repository = ref.read(marketRepositoryProvider);
  return repository.getFeaturedProducts();
});

/// Provider for all market products
final allProductsProvider = FutureProvider<List<MarketProduct>>((ref) async {
  final repository = ref.read(marketRepositoryProvider);
  return repository.getProducts();
});

/// Provider for products by category
final productsByCategoryProvider = FutureProvider.family<List<MarketProduct>, ProductCategory>((ref, category) async {
  final repository = ref.read(marketRepositoryProvider);
  return repository.getProductsByCategory(category);
});

/// Provider for popular products
final popularProductsProvider = FutureProvider<List<MarketProduct>>((ref) async {
  final repository = ref.read(marketRepositoryProvider);
  return repository.getPopularProducts();
});

/// Provider for new products
final newProductsProvider = FutureProvider<List<MarketProduct>>((ref) async {
  final repository = ref.read(marketRepositoryProvider);
  return repository.getNewProducts();
});

/// Provider for product search
final productSearchProvider = FutureProvider.family<List<MarketProduct>, String>((ref, query) async {
  final repository = ref.read(marketRepositoryProvider);
  return repository.searchProducts(query);
});

/// Provider for product by ID
final productByIdProvider = FutureProvider.family<MarketProduct?, String>((ref, id) async {
  final repository = ref.read(marketRepositoryProvider);
  return repository.getProductById(id);
});

/// Provider for all brands
final brandsProvider = FutureProvider<List<Brand>>((ref) async {
  final repository = ref.read(marketRepositoryProvider);
  return repository.getBrands();
});

/// Provider for featured brands
final featuredBrandsProvider = FutureProvider<List<Brand>>((ref) async {
  final repository = ref.read(marketRepositoryProvider);
  return repository.getFeaturedBrands();
});

/// Provider for promotional banners
final promoBannersProvider = FutureProvider<List<PromoBanner>>((ref) async {
  final repository = ref.read(marketRepositoryProvider);
  return repository.getPromoBanners();
});

/// Provider for products by brand
final productsByBrandProvider = FutureProvider.family<List<MarketProduct>, String>((ref, brandId) async {
  final repository = ref.read(marketRepositoryProvider);
  return repository.getProductsByBrand(brandId);
});

/// Provider for recommended products
final recommendedProductsProvider = FutureProvider.family<List<MarketProduct>, RecommendationParams>((ref, params) async {
  final repository = ref.read(marketRepositoryProvider);
  return repository.getRecommendedProducts(
    userId: params.userId,
    preferredCategory: params.preferredCategory,
  );
});

/// Parameters for product recommendations
class RecommendationParams {
  final String? userId;
  final ProductCategory? preferredCategory;

  const RecommendationParams({
    this.userId,
    this.preferredCategory,
  });

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is RecommendationParams &&
          runtimeType == other.runtimeType &&
          userId == other.userId &&
          preferredCategory == other.preferredCategory;

  @override
  int get hashCode => userId.hashCode ^ preferredCategory.hashCode;
}

/// State notifier for market filters and search
class MarketFilterNotifier extends StateNotifier<MarketFilterState> {
  MarketFilterNotifier() : super(const MarketFilterState());

  void setCategory(ProductCategory? category) {
    state = state.copyWith(selectedCategory: category);
  }

  void setSearchQuery(String query) {
    state = state.copyWith(searchQuery: query);
  }

  void setBrand(String? brandId) {
    state = state.copyWith(selectedBrandId: brandId);
  }

  void togglePopularOnly() {
    state = state.copyWith(popularOnly: !state.popularOnly);
  }

  void toggleNewOnly() {
    state = state.copyWith(newOnly: !state.newOnly);
  }

  void clearFilters() {
    state = const MarketFilterState();
  }
}

/// Provider for market filter state
final marketFilterProvider = StateNotifierProvider<MarketFilterNotifier, MarketFilterState>((ref) {
  return MarketFilterNotifier();
});

/// Provider for filtered products based on current filter state
final filteredProductsProvider = FutureProvider<List<MarketProduct>>((ref) async {
  final repository = ref.read(marketRepositoryProvider);
  final filterState = ref.watch(marketFilterProvider);

  return repository.getProducts(
    category: filterState.selectedCategory,
    popularOnly: filterState.popularOnly,
    newOnly: filterState.newOnly,
    searchQuery: filterState.searchQuery.isNotEmpty ? filterState.searchQuery : null,
    brandId: filterState.selectedBrandId,
  );
});

/// Market filter state
class MarketFilterState {
  final ProductCategory? selectedCategory;
  final String searchQuery;
  final String? selectedBrandId;
  final bool popularOnly;
  final bool newOnly;

  const MarketFilterState({
    this.selectedCategory,
    this.searchQuery = '',
    this.selectedBrandId,
    this.popularOnly = false,
    this.newOnly = false,
  });

  MarketFilterState copyWith({
    ProductCategory? selectedCategory,
    String? searchQuery,
    String? selectedBrandId,
    bool? popularOnly,
    bool? newOnly,
  }) {
    return MarketFilterState(
      selectedCategory: selectedCategory,
      searchQuery: searchQuery ?? this.searchQuery,
      selectedBrandId: selectedBrandId,
      popularOnly: popularOnly ?? this.popularOnly,
      newOnly: newOnly ?? this.newOnly,
    );
  }

  bool get hasActiveFilters =>
      selectedCategory != null ||
      searchQuery.isNotEmpty ||
      selectedBrandId != null ||
      popularOnly ||
      newOnly;
}
