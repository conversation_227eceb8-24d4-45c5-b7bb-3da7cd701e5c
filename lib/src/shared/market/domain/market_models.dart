import 'package:flutter/material.dart';

/// Product categories for FitGo Market
enum ProductCategory {
  clothing('Clothing', Icons.checkroom, Color(0xFF8B5CF6)),
  equipment('Equipment', Icons.fitness_center, Color(0xFF10B981)),
  nutrition('Nutrition', Icons.restaurant, Color(0xFFF59E0B)),
  supplements('Supplements', Icons.medication, Color(0xFFEF4444)),
  accessories('Accessories', Icons.watch, Color(0xFF6366F1));

  const ProductCategory(this.displayName, this.icon, this.color);
  final String displayName;
  final IconData icon;
  final Color color;
}

/// Brand information
class Brand {
  final String id;
  final String name;
  final String logoUrl;
  final String description;
  final bool isFeatured;

  const Brand({
    required this.id,
    required this.name,
    required this.logoUrl,
    required this.description,
    this.isFeatured = false,
  });
}

/// Market product model
class MarketProduct {
  final String id;
  final String name;
  final String description;
  final String imageUrl;
  final double price;
  final double? originalPrice;
  final String currency;
  final ProductCategory category;
  final Brand brand;
  final List<String> benefits;
  final double rating;
  final int reviewCount;
  final bool isPopular;
  final bool isNew;
  final String? discountText;
  final List<String> tags;
  final bool inStock;

  const MarketProduct({
    required this.id,
    required this.name,
    required this.description,
    required this.imageUrl,
    required this.price,
    this.originalPrice,
    this.currency = 'TL',
    required this.category,
    required this.brand,
    this.benefits = const [],
    this.rating = 0.0,
    this.reviewCount = 0,
    this.isPopular = false,
    this.isNew = false,
    this.discountText,
    this.tags = const [],
    this.inStock = true,
  });

  String get formattedPrice => '${price.toStringAsFixed(0)} $currency';
  String get formattedOriginalPrice => originalPrice != null 
      ? '${originalPrice!.toStringAsFixed(0)} $currency' 
      : '';
  
  bool get hasDiscount => originalPrice != null && originalPrice! > price;
  double get discountPercentage => hasDiscount 
      ? ((originalPrice! - price) / originalPrice! * 100) 
      : 0.0;
}

/// Promotional banner model
class PromoBanner {
  final String id;
  final String title;
  final String subtitle;
  final String imageUrl;
  final String? ctaText;
  final Color backgroundColor;
  final Color textColor;
  final VoidCallback? onTap;

  const PromoBanner({
    required this.id,
    required this.title,
    required this.subtitle,
    required this.imageUrl,
    this.ctaText,
    this.backgroundColor = const Color(0xFF30A958),
    this.textColor = Colors.white,
    this.onTap,
  });
}

/// Mock data for FitGo Market
class MockMarketData {
  static List<Brand> getBrands() {
    return [
      const Brand(
        id: '1',
        name: 'Nike',
        logoUrl: 'https://images.unsplash.com/photo-1542291026-7eec264c27ff?w=200',
        description: 'Just Do It',
        isFeatured: true,
      ),
      const Brand(
        id: '2',
        name: 'Ocean Nutritions',
        logoUrl: 'https://images.unsplash.com/photo-1593095948071-474c5cc2989d?w=200',
        description: 'Premium Sports Nutrition',
        isFeatured: true,
      ),
      const Brand(
        id: '3',
        name: 'Under Armour',
        logoUrl: 'https://images.unsplash.com/photo-1571019613454-1cb2f99b2d8b?w=200',
        description: 'I Will',
        isFeatured: true,
      ),
      const Brand(
        id: '4',
        name: 'Adidas',
        logoUrl: 'https://images.unsplash.com/photo-1556906781-9a412961c28c?w=200',
        description: 'Impossible is Nothing',
        isFeatured: false,
      ),
    ];
  }

  static List<MarketProduct> getProducts() {
    final brands = getBrands();
    return [
      MarketProduct(
        id: '1',
        name: 'Premium Whey Protein',
        description: 'High-quality whey protein for muscle building and recovery',
        imageUrl: 'https://images.unsplash.com/photo-1593095948071-474c5cc2989d?w=400',
        price: 299,
        originalPrice: 399,
        category: ProductCategory.supplements,
        brand: brands[1], // Ocean Nutritions
        benefits: ['25g Protein', 'Fast Absorption', 'Great Taste'],
        rating: 4.8,
        reviewCount: 1250,
        isPopular: true,
        discountText: '25% OFF',
        tags: ['protein', 'muscle building', 'recovery'],
      ),
      MarketProduct(
        id: '2',
        name: 'Nike Air Max Training',
        description: 'Professional training shoes for all workout types',
        imageUrl: 'https://images.unsplash.com/photo-1542291026-7eec264c27ff?w=400',
        price: 899,
        originalPrice: 1199,
        category: ProductCategory.clothing,
        brand: brands[0], // Nike
        benefits: ['Air Max Technology', 'Durable', 'Comfortable'],
        rating: 4.6,
        reviewCount: 890,
        isPopular: true,
        discountText: '25% OFF',
        tags: ['shoes', 'training', 'nike'],
      ),
      MarketProduct(
        id: '3',
        name: 'Adjustable Dumbbells Set',
        description: 'Space-saving adjustable dumbbells for home workouts',
        imageUrl: 'https://images.unsplash.com/photo-1571019613454-1cb2f99b2d8b?w=400',
        price: 1299,
        category: ProductCategory.equipment,
        brand: brands[2], // Under Armour
        benefits: ['5-50kg Range', 'Space Saving', 'Quick Adjustment'],
        rating: 4.7,
        reviewCount: 456,
        isNew: true,
        tags: ['dumbbells', 'home gym', 'adjustable'],
      ),
      MarketProduct(
        id: '4',
        name: 'BCAA Energy Drink',
        description: 'Essential amino acids for workout energy and recovery',
        imageUrl: 'https://images.unsplash.com/photo-1544367567-0f2fcb009e0b?w=400',
        price: 149,
        originalPrice: 199,
        category: ProductCategory.supplements,
        brand: brands[1], // Ocean Nutritions
        benefits: ['Energy Boost', 'Muscle Recovery', 'Zero Sugar'],
        rating: 4.5,
        reviewCount: 678,
        discountText: '25% OFF',
        tags: ['bcaa', 'energy', 'recovery'],
      ),
      MarketProduct(
        id: '5',
        name: 'Resistance Bands Set',
        description: 'Complete resistance training system for all fitness levels',
        imageUrl: 'https://images.unsplash.com/photo-1571019613454-1cb2f99b2d8b?w=400',
        price: 199,
        category: ProductCategory.equipment,
        brand: brands[2], // Under Armour
        benefits: ['5 Resistance Levels', 'Portable', 'Full Body Workout'],
        rating: 4.4,
        reviewCount: 234,
        isNew: true,
        tags: ['resistance', 'bands', 'portable'],
      ),
      MarketProduct(
        id: '6',
        name: 'Fitness Tracker Watch',
        description: 'Advanced fitness tracking with heart rate monitoring',
        imageUrl: 'https://images.unsplash.com/photo-1523275335684-37898b6baf30?w=400',
        price: 799,
        originalPrice: 999,
        category: ProductCategory.accessories,
        brand: brands[3], // Adidas
        benefits: ['Heart Rate Monitor', 'GPS Tracking', '7-Day Battery'],
        rating: 4.3,
        reviewCount: 567,
        discountText: '20% OFF',
        tags: ['fitness tracker', 'watch', 'monitoring'],
      ),
    ];
  }

  static List<PromoBanner> getPromoBanners() {
    return [
      const PromoBanner(
        id: '1',
        title: 'Summer Fit Sale',
        subtitle: 'Up to 30% Off on All Equipment',
        imageUrl: 'https://images.unsplash.com/photo-1571019613454-1cb2f99b2d8b?w=800',
        ctaText: 'Shop Now',
        backgroundColor: Color(0xFF30A958),
      ),
      const PromoBanner(
        id: '2',
        title: 'Buy 2 Get 1 Free',
        subtitle: 'On All Supplements',
        imageUrl: 'https://images.unsplash.com/photo-1593095948071-474c5cc2989d?w=800',
        ctaText: 'Get Deal',
        backgroundColor: Color(0xFFF59E0B),
      ),
    ];
  }
}
