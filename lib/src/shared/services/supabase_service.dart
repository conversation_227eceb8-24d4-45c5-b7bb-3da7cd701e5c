import 'package:supabase_flutter/supabase_flutter.dart';
import 'package:fitgo_app/core/environment/environment_interface.dart';

/// Abstract interface for Supabase service
abstract interface class ISupabaseService {
  SupabaseClient get client;
  Future<void> initialize();
  bool get isInitialized;
}

/// Concrete implementation of Supabase service
class SupabaseService implements ISupabaseService {
  SupabaseService(this._environment);

  final IEnvironment _environment;
  bool _isInitialized = false;

  @override
  SupabaseClient get client => Supabase.instance.client;

  @override
  bool get isInitialized => _isInitialized;

  @override
  Future<void> initialize() async {
    if (_isInitialized) return;

    await Supabase.initialize(
      url: _environment.supabaseUrl,
      anonKey: _environment.supabaseAnonKey,
      authOptions: const FlutterAuthClientOptions(
        authFlowType: AuthFlowType.pkce,
      ),
    );

    _isInitialized = true;
  }
}
