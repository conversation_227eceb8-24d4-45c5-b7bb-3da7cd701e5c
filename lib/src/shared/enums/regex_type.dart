enum RegexType {
  // A simple email regex that allows letters, numbers, and special characters.
  eMail(r"^[a-zA-Z0-9.a-zA-Z0-9.!#$%&'*+-/=?^_`{|}~]+@[a-zA-Z0-9]+\.[a-zA-Z]+"),

  // A simple password regex that allows letters, numbers, and special characters.
  password(r'^.{6,20}$'),

  // A simple OTP regex that allows numbers.
  otp(r'^[0-9]{6}$'),

  // This regex allows alphanumeric characters, spaces, and hyphens (adjust as needed).
  postalCode(r'^[A-Za-z0-9\s-]+$'),

  // A simple name regex that allows letters (including accented), spaces, apostrophes, and hyphens.
  name(r"^[A-Za-zÀ-ÖØ-öø-ÿ\s'-]{2,}$"),

  // A phone number regex that allows an optional leading plus, numbers, spaces, and hyphens.
  phone(r'^\+?[0-9\s-]{7,15}$'),

  // Regex for ID: Only numbers, no length restriction
  id(r'^[0-9]+$'),

  // Regex for web address http/https validation
  webAddress(r'^(https?:\/\/)?([\da-z\.-]+)\.([a-z\.]{2,6})([\/\w \.-]*)*\/?$'),

  // Regex for birth date in DD/MM/YYYY format
  birthDate(r'^(\d{2})\/(\d{2})\/(\d{4})$');

  const RegexType(this.pattern);
  final String pattern;
  RegExp get regex => RegExp(pattern);
}
