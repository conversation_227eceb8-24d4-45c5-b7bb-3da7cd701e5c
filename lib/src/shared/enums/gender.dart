import 'package:flutter/material.dart';

/// Gender enumeration with Turkish localization
enum Gender {
  female,
  male,
  preferNotToSay;

  /// Turkish display names for gender options
  String get displayNameTurkish {
    switch (this) {
      case Gender.female:
        return '<PERSON>ız';
      case Gender.male:
        return '<PERSON><PERSON><PERSON>';
      case Gender.preferNotToSay:
        return 'Belirtmek İstemiyorum';
    }
  }

  /// English display names for gender options
  String get displayNameEnglish {
    switch (this) {
      case Gender.female:
        return 'Female';
      case Gender.male:
        return 'Male';
      case Gender.preferNotToSay:
        return 'Prefer not to say';
    }
  }

  /// Database value mapping to match Supabase schema
  String get databaseValue {
    switch (this) {
      case Gender.female:
        return 'female';
      case Gender.male:
        return 'male';
      case Gender.preferNotToSay:
        return 'other';
    }
  }

  /// Create Gender from database value
  static Gender? fromDatabaseValue(String? value) {
    if (value == null) return null;

    switch (value.toLowerCase()) {
      case 'female':
        return Gender.female;
      case 'male':
        return Gender.male;
      case 'other':
        return Gender.preferNotToSay;
      default:
        return null;
    }
  }

  /// Get localized display name based on context
  String getDisplayName(BuildContext context) {
    // For now, return English names for UI consistency
    return displayNameEnglish;
  }
}

/// Extension for additional Gender functionality
extension GenderExtension on Gender {
  /// Icon representation for each gender
  IconData get icon {
    switch (this) {
      case Gender.female:
        return Icons.female;
      case Gender.male:
        return Icons.male;
      case Gender.preferNotToSay:
        return Icons.person;
    }
  }

  /// Color representation for each gender (optional styling)
  Color get color {
    switch (this) {
      case Gender.female:
        return Colors.pink.shade300;
      case Gender.male:
        return Colors.blue.shade300;
      case Gender.preferNotToSay:
        return Colors.grey.shade400;
    }
  }
}
