import 'package:fitgo_app/src/shared/extensions/string_hardcoded.dart';

class AppConstants {
  static const String appName = 'Onboarding App';

  static const String landingTitle = 'Hoş Geldiniz';
  static const String landingDescription =
      'Lütfen devam etmek için kullanıcı tipinizi seçin';
  static const String studentButtonText = 'Öğrenci Olarak Devam Et';
  static const String teacherButtonText = 'Eğitmen Olarak Devam Et';

  static List<Map<String, String>> studentOnboardingItems = [
    {
      'imgPath': 'assets/images/student_1.png',
      'title': 'Dersleri Keşfet'.hardcoded,
      'desc':
          'Binlerce ders arasından ilgi alanlarınıza uygun olanları keşfedin.'
              .hardcoded,
    },
    {
      'imgPath': 'assets/images/student_2.png',
      'title': '<PERSON><PERSON> Hızınızda Öğrenin'.hardcoded,
      'desc':
          'İstediğ<PERSON>z zaman, istediğ<PERSON>z yerden kendi hızınızda öğrenme deneyimi.'
              .hardcoded,
    },
    {
      'imgPath': 'assets/images/student_3.png',
      'title': 'Başarılarınızı Takip Edin'.hardcoded,
      'desc':
          'İlerlemenizi ve öğrenme başarılarınızı detaylı analizlerle takip edin.'
              .hardcoded,
    },
  ];

  static List<Map<String, String>> teacherOnboardingItems = [
    {
      'imgPath': 'assets/images/teacher_1.png',
      'title': 'İçerik Oluşturun'.hardcoded,
      'desc':
          'Kullanıcı dostu araçlarla etkileyici dersler oluşturun.'.hardcoded,
    },
    {
      'imgPath': 'assets/images/teacher_2.png',
      'title': 'Öğrencilerle Bağlantı Kurun'.hardcoded,
      'desc':
          'Dünyanın her yerinden öğrencilere ulaşın ve onlarla etkileşime geçin.'
              .hardcoded,
    },
    {
      'imgPath': 'assets/images/teacher_3.png',
      'title': 'Gelir Elde Edin'.hardcoded,
      'desc':
          'Bilgi ve deneyimlerinizi paylaşarak pasif gelir elde edin.'
              .hardcoded,
    },
  ];

  static String nextButtonText = 'İleri'.hardcoded;
  static String skipButtonText = 'Atla'.hardcoded;
  static String doneButtonText = 'Başla'.hardcoded;
}
