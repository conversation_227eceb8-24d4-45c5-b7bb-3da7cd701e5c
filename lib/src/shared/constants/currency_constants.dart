/// Currency constants and utilities for the application
class CurrencyConstants {
  // Default currency
  static const String defaultCurrency = 'TL';
  static const String defaultCurrencySymbol = '₺';
  
  // Supported currencies
  static const Map<String, String> supportedCurrencies = {
    'TL': '₺',   // Turkish Lira
    'USD': '\$', // US Dollar
    'EUR': '€',  // Euro
  };
  
  // Currency display names
  static const Map<String, String> currencyNames = {
    'TL': 'Türk Lirası',
    'USD': 'US Dollar',
    'EUR': 'Euro',
  };
  
  /// Get currency symbol for given currency code
  static String getCurrencySymbol(String currencyCode) {
    return supportedCurrencies[currencyCode] ?? defaultCurrencySymbol;
  }
  
  /// Get currency name for given currency code
  static String getCurrencyName(String currencyCode) {
    return currencyNames[currencyCode] ?? currencyCode;
  }
  
  /// Format price with currency symbol
  static String formatPrice(double price, {String? currencyCode}) {
    final currency = currencyCode ?? defaultCurrency;
    final symbol = getCurrencySymbol(currency);
    
    // For Turkish Lira, show without decimals if it's a whole number
    if (currency == 'TL' && price == price.roundToDouble()) {
      return '${price.toStringAsFixed(0)}$symbol';
    }
    
    return '${price.toStringAsFixed(2)}$symbol';
  }
  
  /// Format price with currency symbol at the beginning
  static String formatPriceWithPrefix(double price, {String? currencyCode}) {
    final currency = currencyCode ?? defaultCurrency;
    final symbol = getCurrencySymbol(currency);
    
    // For Turkish Lira, show without decimals if it's a whole number
    if (currency == 'TL' && price == price.roundToDouble()) {
      return '$symbol${price.toStringAsFixed(0)}';
    }
    
    return '$symbol${price.toStringAsFixed(2)}';
  }
  
  /// Check if currency is supported
  static bool isCurrencySupported(String currencyCode) {
    return supportedCurrencies.containsKey(currencyCode);
  }
}

/// Extension for easy currency formatting
extension CurrencyFormatting on double {
  /// Format as currency with default settings
  String get asCurrency => CurrencyConstants.formatPrice(this);
  
  /// Format as currency with specific currency code
  String asCurrencyWith(String currencyCode) => 
      CurrencyConstants.formatPrice(this, currencyCode: currencyCode);
  
  /// Format as currency with symbol prefix
  String get asCurrencyPrefix => CurrencyConstants.formatPriceWithPrefix(this);
  
  /// Format as currency with symbol prefix and specific currency
  String asCurrencyPrefixWith(String currencyCode) => 
      CurrencyConstants.formatPriceWithPrefix(this, currencyCode: currencyCode);
}
