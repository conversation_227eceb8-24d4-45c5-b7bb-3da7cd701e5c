import 'package:hooks_riverpod/hooks_riverpod.dart';
import 'package:fitgo_app/src/shared/providers/supabase_provider.dart';
import 'package:fitgo_app/src/shared/providers/auth_provider.dart';
import 'package:fitgo_app/core/local_storage/hive_provider.dart';
import 'package:fitgo_app/core/services/deep_link_service.dart';
import 'package:fitgo_app/core/router/app_router.dart';
import 'package:fitgo_app/src/shared/logger/logger.dart';
import 'package:fitgo_app/core/environment/environment.dart';

/// Represents the app startup state
enum AppStartupState { loading, completed, error }

/// Provider that handles app initialization
final appStartupProvider = FutureProvider<AppStartupState>((ref) async {
  try {
    // Initialize logger environment first
    env = ref.read(environmentProvider);

    // Initialize Hive storage first (faster)
    final hiveHelper = ref.read(hiveProvider);
    await hiveHelper.initialize();

    // Initialize Supabase (with timeout)
    await ref
        .read(initializedSupabaseServiceProvider.future)
        .timeout(const Duration(seconds: 10));

    // Initialize auth state listener
    ref.read(authStateListenerProvider);

    // Initialize deep link service
    final deepLinkService = ref.read(deepLinkServiceProvider);
    final router = ref.read(appRouterProvider);
    await deepLinkService.initialize(router);

    // Add a small delay to ensure everything is ready
    await Future.delayed(const Duration(milliseconds: 500));

    return AppStartupState.completed;
  } catch (error, stackTrace) {
    // Log the error
    // ignore: avoid_print
    print('App startup error: $error\nStack trace: $stackTrace');
    return AppStartupState.error;
  }
});

/// Provider for checking if app is initialized
final isAppInitializedProvider = Provider<bool>((ref) {
  final startup = ref.watch(appStartupProvider);
  return startup.when(
    data: (state) => state == AppStartupState.completed,
    loading: () => false,
    error: (_, __) => false,
  );
});
