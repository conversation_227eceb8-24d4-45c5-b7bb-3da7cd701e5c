import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:supabase_flutter/supabase_flutter.dart';
import '../domain/student_nutrition_assignment.dart';
import '../domain/nutrition_template.dart';
import '../../shared/utils/app_logger.dart';

// Repository provider
final studentNutritionAssignmentRepositoryProvider = Provider<StudentNutritionAssignmentRepository>((ref) {
  return StudentNutritionAssignmentRepository();
});

// State notifier for managing nutrition assignments
final studentNutritionAssignmentNotifierProvider = StateNotifierProvider<StudentNutritionAssignmentNotifier, AsyncValue<List<StudentNutritionAssignment>>>((ref) {
  final repository = ref.read(studentNutritionAssignmentRepositoryProvider);
  return StudentNutritionAssignmentNotifier(repository);
});

class StudentNutritionAssignmentNotifier extends StateNotifier<AsyncValue<List<StudentNutritionAssignment>>> {
  final StudentNutritionAssignmentRepository _repository;

  StudentNutritionAssignmentNotifier(this._repository) : super(const AsyncValue.loading());

  /// Assign nutrition template to student
  Future<StudentNutritionAssignment?> assignNutritionTemplate({
    required String templateId,
    required String studentId,
    required String instructorId,
    String? notes,
  }) async {
    try {
      AppLogger.info('Assigning nutrition template $templateId to student $studentId', tag: 'NUTRITION_ASSIGNMENT');
      
      final assignment = await _repository.assignNutritionTemplate(
        templateId: templateId,
        studentId: studentId,
        instructorId: instructorId,
        notes: notes,
      );

      AppLogger.success('Nutrition template assigned successfully', tag: 'NUTRITION_ASSIGNMENT');
      return assignment;
    } catch (e) {
      AppLogger.error('Failed to assign nutrition template', tag: 'NUTRITION_ASSIGNMENT', error: e);
      return null;
    }
  }

  /// Create custom nutrition plan for student
  Future<StudentNutritionAssignment?> createCustomNutritionPlan({
    required String studentId,
    required String instructorId,
    required String planName,
    String? description,
    String? notes,
    required List<StudentNutritionMeal> meals,
    required MacronutrientBreakdown macros,
  }) async {
    try {
      AppLogger.info('Creating custom nutrition plan for student $studentId', tag: 'NUTRITION_ASSIGNMENT');
      
      final assignment = await _repository.createCustomNutritionPlan(
        studentId: studentId,
        instructorId: instructorId,
        planName: planName,
        description: description,
        notes: notes,
        meals: meals,
        macros: macros,
      );

      AppLogger.success('Custom nutrition plan created successfully', tag: 'NUTRITION_ASSIGNMENT');
      return assignment;
    } catch (e) {
      AppLogger.error('Failed to create custom nutrition plan', tag: 'NUTRITION_ASSIGNMENT', error: e);
      return null;
    }
  }

  /// Get student's current nutrition assignment
  Future<StudentNutritionAssignment?> getStudentNutritionAssignment(String studentId) async {
    try {
      AppLogger.info('Fetching nutrition assignment for student $studentId', tag: 'NUTRITION_ASSIGNMENT');
      
      final assignment = await _repository.getStudentNutritionAssignment(studentId);
      
      if (assignment != null) {
        AppLogger.success('Found nutrition assignment: ${assignment.planName}', tag: 'NUTRITION_ASSIGNMENT');
      } else {
        AppLogger.info('No nutrition assignment found for student', tag: 'NUTRITION_ASSIGNMENT');
      }
      
      return assignment;
    } catch (e) {
      AppLogger.error('Failed to fetch nutrition assignment', tag: 'NUTRITION_ASSIGNMENT', error: e);
      return null;
    }
  }
}

class StudentNutritionAssignmentRepository {
  final SupabaseClient _supabase = Supabase.instance.client;

  /// Assign nutrition template to student
  Future<StudentNutritionAssignment> assignNutritionTemplate({
    required String templateId,
    required String studentId,
    required String instructorId,
    String? notes,
  }) async {
    try {
      AppLogger.database('INSERT student_nutrition_assignments', 'student_nutrition_assignments', tag: 'NUTRITION_ASSIGNMENT');
      
      // First, get the template data
      final templateResponse = await _supabase
          .from('nutrition_templates')
          .select('''
            *,
            nutrition_template_meals(
              *,
              nutrition_template_meal_items(*)
            )
          ''')
          .eq('id', templateId)
          .single();

      final template = _mapToNutritionTemplate(templateResponse);
      
      // Create assignment from template
      final assignmentData = {
        'student_id': studentId,
        'instructor_id': instructorId,
        'template_id': templateId,
        'plan_name': template.name,
        'description': template.description,
        'notes': notes,
        'macros': {
          'protein_percentage': template.macros.proteinPercentage,
          'carbs_percentage': template.macros.carbsPercentage,
          'fats_percentage': template.macros.fatsPercentage,
        },
        'assigned_at': DateTime.now().toIso8601String(),
        'is_active': true,
        'created_at': DateTime.now().toIso8601String(),
        'updated_at': DateTime.now().toIso8601String(),
      };

      // Insert assignment
      final assignmentResponse = await _supabase
          .from('student_nutrition_assignments')
          .insert(assignmentData)
          .select()
          .single();

      final assignmentId = assignmentResponse['id'];

      // Copy meals from template to assignment
      await _copyMealsFromTemplate(assignmentId, template.meals);

      // Fetch complete assignment
      return await getStudentNutritionAssignment(studentId) ??
             _mapToStudentNutritionAssignment(assignmentResponse);
    } catch (e) {
      AppLogger.error('Error assigning nutrition template', tag: 'NUTRITION_ASSIGNMENT', error: e);
      rethrow;
    }
  }

  /// Create custom nutrition plan for student
  Future<StudentNutritionAssignment> createCustomNutritionPlan({
    required String studentId,
    required String instructorId,
    required String planName,
    String? description,
    String? notes,
    required List<StudentNutritionMeal> meals,
    required MacronutrientBreakdown macros,
  }) async {
    try {
      AppLogger.database('INSERT student_nutrition_assignments (custom)', 'student_nutrition_assignments', tag: 'NUTRITION_ASSIGNMENT');
      
      final assignmentData = {
        'student_id': studentId,
        'instructor_id': instructorId,
        'template_id': null, // Custom plan
        'plan_name': planName,
        'description': description,
        'notes': notes,
        'macros': {
          'protein_percentage': macros.proteinPercentage,
          'carbs_percentage': macros.carbsPercentage,
          'fats_percentage': macros.fatsPercentage,
        },
        'assigned_at': DateTime.now().toIso8601String(),
        'is_active': true,
        'created_at': DateTime.now().toIso8601String(),
        'updated_at': DateTime.now().toIso8601String(),
      };

      // Insert assignment
      final assignmentResponse = await _supabase
          .from('student_nutrition_assignments')
          .insert(assignmentData)
          .select()
          .single();

      final assignmentId = assignmentResponse['id'];

      // Create meals for assignment
      await _createMealsForAssignment(assignmentId, meals);

      // Fetch complete assignment
      return await getStudentNutritionAssignment(studentId) ??
             _mapToStudentNutritionAssignment(assignmentResponse);
    } catch (e) {
      AppLogger.error('Error creating custom nutrition plan', tag: 'NUTRITION_ASSIGNMENT', error: e);
      rethrow;
    }
  }

  /// Get student's current nutrition assignment
  Future<StudentNutritionAssignment?> getStudentNutritionAssignment(String studentId) async {
    try {
      final response = await _supabase
          .from('student_nutrition_assignments')
          .select('''
            *,
            student_nutrition_meals(
              *,
              student_nutrition_meal_items(*)
            )
          ''')
          .eq('student_id', studentId)
          .eq('is_active', true)
          .maybeSingle();

      if (response == null) return null;

      return _mapToStudentNutritionAssignment(response);
    } catch (e) {
      AppLogger.error('Error fetching student nutrition assignment', tag: 'NUTRITION_ASSIGNMENT', error: e);
      return null;
    }
  }

  /// Copy meals from template to assignment
  Future<void> _copyMealsFromTemplate(String assignmentId, List<NutritionTemplateMeal> templateMeals) async {
    for (final templateMeal in templateMeals) {
      final mealData = {
        'assignment_id': assignmentId,
        'name': templateMeal.name,
        'meal_type': templateMeal.mealType,
        'order_index': templateMeal.orderIndex,
        'description': templateMeal.description,
        'created_at': DateTime.now().toIso8601String(),
        'updated_at': DateTime.now().toIso8601String(),
      };

      final mealResponse = await _supabase
          .from('student_nutrition_meals')
          .insert(mealData)
          .select()
          .single();

      final mealId = mealResponse['id'];

      // Copy meal items
      for (final templateItem in templateMeal.items) {
        final itemData = {
          'meal_id': mealId,
          'food_name': templateItem.foodName,
          'quantity': templateItem.quantity,
          'unit': templateItem.unit,
          'notes': templateItem.notes,
          'calories': templateItem.calories,
          'protein': templateItem.protein,
          'carbs': templateItem.carbs,
          'fats': templateItem.fats,
          'created_at': DateTime.now().toIso8601String(),
          'updated_at': DateTime.now().toIso8601String(),
        };

        await _supabase
            .from('student_nutrition_meal_items')
            .insert(itemData);
      }
    }
  }

  /// Create meals for assignment
  Future<void> _createMealsForAssignment(String assignmentId, List<StudentNutritionMeal> meals) async {
    for (final meal in meals) {
      final mealData = {
        'assignment_id': assignmentId,
        'name': meal.name,
        'meal_type': meal.mealType,
        'order_index': meal.orderIndex,
        'description': meal.description,
        'created_at': DateTime.now().toIso8601String(),
        'updated_at': DateTime.now().toIso8601String(),
      };

      final mealResponse = await _supabase
          .from('student_nutrition_meals')
          .insert(mealData)
          .select()
          .single();

      final mealId = mealResponse['id'];

      // Create meal items
      for (final item in meal.items) {
        final itemData = {
          'meal_id': mealId,
          'food_name': item.foodName,
          'quantity': item.quantity,
          'unit': item.unit,
          'notes': item.notes,
          'calories': item.calories,
          'protein': item.protein,
          'carbs': item.carbs,
          'fats': item.fats,
          'created_at': DateTime.now().toIso8601String(),
          'updated_at': DateTime.now().toIso8601String(),
        };

        await _supabase
            .from('student_nutrition_meal_items')
            .insert(itemData);
      }
    }
  }

  /// Map database response to NutritionTemplate
  NutritionTemplate _mapToNutritionTemplate(Map<String, dynamic> data) {
    final macrosData = data['macros'] as Map<String, dynamic>? ?? {};
    
    return NutritionTemplate(
      id: data['id'],
      instructorId: data['instructor_id'],
      name: data['name'],
      description: data['description'],
      tags: List<String>.from(data['tags'] ?? []),
      macros: MacronutrientBreakdown(
        proteinPercentage: (macrosData['protein_percentage'] ?? 0.0).toDouble(),
        carbsPercentage: (macrosData['carbs_percentage'] ?? 0.0).toDouble(),
        fatsPercentage: (macrosData['fats_percentage'] ?? 0.0).toDouble(),
      ),
      createdAt: DateTime.parse(data['created_at']),
      updatedAt: DateTime.parse(data['updated_at']),
      meals: (data['nutrition_template_meals'] as List<dynamic>?)
              ?.map((mealData) => _mapToNutritionTemplateMeal(mealData))
              .toList() ??
          [],
    );
  }

  /// Map database response to NutritionTemplateMeal
  NutritionTemplateMeal _mapToNutritionTemplateMeal(Map<String, dynamic> data) {
    return NutritionTemplateMeal(
      id: data['id'],
      templateId: data['template_id'],
      name: data['name'],
      mealType: data['meal_type'],
      orderIndex: data['order_index'],
      description: data['description'],
      createdAt: DateTime.parse(data['created_at']),
      updatedAt: DateTime.parse(data['updated_at']),
      items: (data['nutrition_template_meal_items'] as List<dynamic>?)
              ?.map((itemData) => _mapToNutritionTemplateMealItem(itemData))
              .toList() ??
          [],
    );
  }

  /// Map database response to NutritionTemplateMealItem
  NutritionTemplateMealItem _mapToNutritionTemplateMealItem(Map<String, dynamic> data) {
    return NutritionTemplateMealItem(
      id: data['id'],
      mealId: data['meal_id'],
      foodName: data['food_name'],
      quantity: (data['quantity'] as num).toDouble(),
      unit: data['unit'],
      notes: data['notes'],
      createdAt: DateTime.parse(data['created_at']),
      updatedAt: DateTime.parse(data['updated_at']),
      calories: (data['calories'] as num?)?.toDouble(),
      protein: (data['protein'] as num?)?.toDouble(),
      carbs: (data['carbs'] as num?)?.toDouble(),
      fats: (data['fats'] as num?)?.toDouble(),
    );
  }

  /// Map database response to StudentNutritionAssignment
  StudentNutritionAssignment _mapToStudentNutritionAssignment(Map<String, dynamic> data) {
    final macrosData = data['macros'] as Map<String, dynamic>? ?? {};
    
    return StudentNutritionAssignment(
      id: data['id'],
      studentId: data['student_id'],
      instructorId: data['instructor_id'],
      templateId: data['template_id'],
      planName: data['plan_name'],
      description: data['description'],
      notes: data['notes'],
      meals: (data['student_nutrition_meals'] as List<dynamic>?)
              ?.map((mealData) => _mapToStudentNutritionMeal(mealData))
              .toList() ??
          [],
      macros: MacronutrientBreakdown(
        proteinPercentage: (macrosData['protein_percentage'] ?? 0.0).toDouble(),
        carbsPercentage: (macrosData['carbs_percentage'] ?? 0.0).toDouble(),
        fatsPercentage: (macrosData['fats_percentage'] ?? 0.0).toDouble(),
      ),
      assignedAt: DateTime.parse(data['assigned_at']),
      startDate: data['start_date'] != null ? DateTime.parse(data['start_date']) : null,
      endDate: data['end_date'] != null ? DateTime.parse(data['end_date']) : null,
      isActive: data['is_active'] ?? true,
      createdAt: DateTime.parse(data['created_at']),
      updatedAt: DateTime.parse(data['updated_at']),
    );
  }

  /// Map database response to StudentNutritionMeal
  StudentNutritionMeal _mapToStudentNutritionMeal(Map<String, dynamic> data) {
    return StudentNutritionMeal(
      id: data['id'],
      assignmentId: data['assignment_id'],
      name: data['name'],
      mealType: data['meal_type'],
      orderIndex: data['order_index'],
      description: data['description'],
      items: (data['student_nutrition_meal_items'] as List<dynamic>?)
              ?.map((itemData) => _mapToStudentNutritionMealItem(itemData))
              .toList() ??
          [],
      createdAt: DateTime.parse(data['created_at']),
      updatedAt: DateTime.parse(data['updated_at']),
    );
  }

  /// Map database response to StudentNutritionMealItem
  StudentNutritionMealItem _mapToStudentNutritionMealItem(Map<String, dynamic> data) {
    return StudentNutritionMealItem(
      id: data['id'],
      mealId: data['meal_id'],
      foodName: data['food_name'],
      quantity: (data['quantity'] as num).toDouble(),
      unit: data['unit'],
      notes: data['notes'],
      createdAt: DateTime.parse(data['created_at']),
      updatedAt: DateTime.parse(data['updated_at']),
      calories: (data['calories'] as num?)?.toDouble(),
      protein: (data['protein'] as num?)?.toDouble(),
      carbs: (data['carbs'] as num?)?.toDouble(),
      fats: (data['fats'] as num?)?.toDouble(),
    );
  }
}
