import 'package:supabase_flutter/supabase_flutter.dart';
import 'package:fitgo_app/src/shared/utils/app_logger.dart';
import 'package:fitgo_app/src/instructor/domain/capacity_upgrade_models.dart';

/// Service for managing instructor capacity upgrades
class CapacityUpgradeService {
  final SupabaseClient _supabase = Supabase.instance.client;

  /// Get current capacity status for instructor
  Future<InstructorCapacityStatus> getCapacityStatus(
    String instructorId,
  ) async {
    try {
      AppLogger.info(
        '📊 Getting capacity status for instructor: $instructorId',
        tag: 'CAPACITY_UPGRADE',
      );

      final response =
          await _supabase
              .from('instructors')
              .select('max_students, current_students')
              .eq('id', instructorId)
              .single();

      final maxCapacity = response['max_students'] as int? ?? 5;
      final usedCapacity = response['current_students'] as int? ?? 0;
      final utilizationPercentage =
          maxCapacity > 0 ? (usedCapacity / maxCapacity * 100) : 0.0;

      // Check if instructor can upgrade (business rules)
      final canUpgrade = await _canInstructorUpgrade(instructorId);
      final upgradeRestriction =
          canUpgrade ? null : await _getUpgradeRestriction(instructorId);

      return InstructorCapacityStatus(
        currentCapacity: maxCapacity,
        maxCapacity: maxCapacity,
        usedCapacity: usedCapacity,
        utilizationPercentage: utilizationPercentage,
        canUpgrade: canUpgrade,
        upgradeRestriction: upgradeRestriction,
        currentPlan: 'Basic', // TODO: Get from database
      );
    } catch (e) {
      AppLogger.error(
        '❌ Error getting capacity status: $e',
        tag: 'CAPACITY_UPGRADE',
      );
      rethrow;
    }
  }

  /// Check if instructor can upgrade capacity
  Future<bool> _canInstructorUpgrade(String instructorId) async {
    try {
      // Check if instructor has any pending upgrade transactions
      final pendingUpgrades =
          await _supabase
              .from('capacity_upgrade_transactions')
              .select('id')
              .eq('instructor_id', instructorId)
              .eq('status', 'pending')
              .count();

      if (pendingUpgrades.count > 0) {
        return false; // Has pending upgrades
      }

      // Check if instructor has reached maximum allowed capacity
      final instructorResponse =
          await _supabase
              .from('instructors')
              .select('max_students')
              .eq('id', instructorId)
              .single();

      final currentMaxCapacity =
          instructorResponse['max_students'] as int? ?? 5;

      // Business rule: Maximum 500 students per instructor
      return currentMaxCapacity < 500;
    } catch (e) {
      AppLogger.error(
        '❌ Error checking upgrade eligibility: $e',
        tag: 'CAPACITY_UPGRADE',
      );
      return false;
    }
  }

  /// Get upgrade restriction reason
  Future<String> _getUpgradeRestriction(String instructorId) async {
    try {
      // Check for pending transactions
      final pendingCount =
          await _supabase
              .from('capacity_upgrade_transactions')
              .select('id')
              .eq('instructor_id', instructorId)
              .eq('status', 'pending')
              .count();

      if (pendingCount.count > 0) {
        return 'Bekleyen kapasite yükseltme işleminiz var';
      }

      // Check current capacity
      final instructorResponse =
          await _supabase
              .from('instructors')
              .select('max_students')
              .eq('id', instructorId)
              .single();

      final currentMaxCapacity =
          instructorResponse['max_students'] as int? ?? 5;

      if (currentMaxCapacity >= 500) {
        return 'Maksimum kapasite limitine ulaştınız';
      }

      return 'Kapasite yükseltme şu anda mevcut değil';
    } catch (e) {
      return 'Kapasite durumu kontrol edilemiyor';
    }
  }

  /// Get recommended packages for instructor
  Future<List<CapacityUpgradePackage>> getRecommendedPackages(
    String instructorId,
  ) async {
    try {
      final status = await getCapacityStatus(instructorId);
      return CapacityUpgradePackages.getRecommendedPackages(
        status.usedCapacity,
      );
    } catch (e) {
      AppLogger.error(
        '❌ Error getting recommended packages: $e',
        tag: 'CAPACITY_UPGRADE',
      );
      return CapacityUpgradePackages.packages;
    }
  }

  /// Process capacity upgrade purchase
  Future<CapacityUpgradeResult> purchaseCapacityUpgrade({
    required String instructorId,
    required String packageId,
    required String paymentMethod,
  }) async {
    try {
      AppLogger.userAction(
        '💳 Processing capacity upgrade purchase',
        tag: 'CAPACITY_UPGRADE',
      );

      // Get package details
      final package = CapacityUpgradePackages.getPackageById(packageId);
      if (package == null) {
        return CapacityUpgradeResult.failure('Geçersiz paket seçimi');
      }

      // Check if instructor can upgrade
      final canUpgrade = await _canInstructorUpgrade(instructorId);
      if (!canUpgrade) {
        final restriction = await _getUpgradeRestriction(instructorId);
        return CapacityUpgradeResult.failure(restriction);
      }

      // Create upgrade transaction
      final transactionResponse =
          await _supabase
              .from('capacity_upgrade_transactions')
              .insert({
                'instructor_id': instructorId,
                'package_id': packageId,
                'package_name': package.name,
                'additional_capacity': package.additionalCapacity,
                'amount': package.price,
                'status': 'pending',
                'payment_method': paymentMethod,
                'created_at': DateTime.now().toIso8601String(),
              })
              .select('id')
              .single();

      final transactionId = transactionResponse['id'] as String;

      // Simulate payment processing (in real app, integrate with payment gateway)
      await Future.delayed(const Duration(seconds: 2));

      // Update transaction status to completed
      await _supabase
          .from('capacity_upgrade_transactions')
          .update({
            'status': 'completed',
            'completed_at': DateTime.now().toIso8601String(),
            'transaction_id': 'TXN_${DateTime.now().millisecondsSinceEpoch}',
          })
          .eq('id', transactionId);

      // Update instructor capacity
      final currentCapacityResponse =
          await _supabase
              .from('instructors')
              .select('max_students')
              .eq('id', instructorId)
              .single();

      final currentCapacity =
          currentCapacityResponse['max_students'] as int? ?? 50;
      final newCapacity = currentCapacity + package.additionalCapacity;

      await _supabase
          .from('instructors')
          .update({'max_students': newCapacity})
          .eq('id', instructorId);

      AppLogger.success(
        '✅ Capacity upgrade completed successfully',
        tag: 'CAPACITY_UPGRADE',
      );

      return CapacityUpgradeResult.success(
        transactionId: transactionId,
        newCapacity: newCapacity,
      );
    } catch (e) {
      AppLogger.error(
        '❌ Error processing capacity upgrade: $e',
        tag: 'CAPACITY_UPGRADE',
      );
      return CapacityUpgradeResult.failure(
        'Kapasite yükseltme işlemi başarısız: ${e.toString()}',
      );
    }
  }

  /// Get upgrade transaction history
  Future<List<CapacityUpgradeTransaction>> getUpgradeHistory(
    String instructorId,
  ) async {
    try {
      final response = await _supabase
          .from('capacity_upgrade_transactions')
          .select('*')
          .eq('instructor_id', instructorId)
          .order('created_at', ascending: false);

      return (response as List<dynamic>)
          .map(
            (item) => CapacityUpgradeTransaction(
              id: item['id'] as String,
              instructorId: item['instructor_id'] as String,
              packageId: item['package_id'] as String,
              packageName: item['package_name'] as String,
              additionalCapacity: item['additional_capacity'] as int,
              amount: (item['amount'] as num).toDouble(),
              status: item['status'] as String,
              createdAt: DateTime.parse(item['created_at'] as String),
              completedAt:
                  item['completed_at'] != null
                      ? DateTime.parse(item['completed_at'] as String)
                      : null,
              paymentMethod: item['payment_method'] as String?,
              transactionId: item['transaction_id'] as String?,
            ),
          )
          .toList();
    } catch (e) {
      AppLogger.error(
        '❌ Error getting upgrade history: $e',
        tag: 'CAPACITY_UPGRADE',
      );
      return [];
    }
  }
}
