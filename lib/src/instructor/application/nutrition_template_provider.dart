import 'package:hooks_riverpod/hooks_riverpod.dart';
import 'package:supabase_flutter/supabase_flutter.dart';
import '../domain/nutrition_template.dart';
import '../data/nutrition_template_repository.dart';
import 'instructor_provider.dart';

// Repository provider
final nutritionTemplateRepositoryProvider =
    Provider<NutritionTemplateRepository>((ref) {
      return NutritionTemplateRepository();
    });

// Nutrition templates provider
final nutritionTemplatesProvider = FutureProvider<List<NutritionTemplate>>((
  ref,
) async {
  final instructorId = ref.watch(currentInstructorIdProvider);
  if (instructorId == null) return [];

  final repository = ref.read(nutritionTemplateRepositoryProvider);
  return repository.getNutritionTemplates(instructorId: instructorId);
});

// State notifier for managing nutrition template operations
final nutritionTemplateNotifierProvider = StateNotifierProvider<
  NutritionTemplateNotifier,
  AsyncValue<List<NutritionTemplate>>
>((ref) {
  final repository = ref.read(nutritionTemplateRepositoryProvider);
  return NutritionTemplateNotifier(repository, ref);
});

class NutritionTemplateNotifier
    extends StateNotifier<AsyncValue<List<NutritionTemplate>>> {
  final NutritionTemplateRepository _repository;
  final Ref _ref;

  NutritionTemplateNotifier(this._repository, this._ref)
    : super(const AsyncValue.loading()) {
    loadTemplates();
  }

  Future<void> loadTemplates() async {
    state = const AsyncValue.loading();
    try {
      final instructorId = _ref.read(currentInstructorIdProvider);
      if (instructorId == null) {
        state = const AsyncValue.data([]);
        return;
      }

      final templates = await _repository.getNutritionTemplates(
        instructorId: instructorId,
      );
      state = AsyncValue.data(templates);
    } catch (error, stackTrace) {
      state = AsyncValue.error(error, stackTrace);
    }
  }

  // Create a new nutrition template
  Future<NutritionTemplate?> createNutritionTemplate(
    NutritionTemplate template,
  ) async {
    try {
      print(
        '🔄 Provider: Creating template with ${template.meals.length} meals',
      );
      final createdTemplate = await _repository.createNutritionTemplate(
        template,
      );

      // Reload templates to get updated list
      print('🔄 Provider: Reloading templates after creation');
      await loadTemplates();
      print('✅ Provider: Templates reloaded, notifying listeners');

      return createdTemplate;
    } catch (e) {
      state = AsyncValue.error(e, StackTrace.current);
      return null;
    }
  }

  // Update a nutrition template
  Future<NutritionTemplate?> updateNutritionTemplate(
    NutritionTemplate template,
  ) async {
    try {
      final updatedTemplate = await _repository.updateNutritionTemplate(
        template,
      );

      // Reload templates to get updated list
      await loadTemplates();

      return updatedTemplate;
    } catch (e) {
      state = AsyncValue.error(e, StackTrace.current);
      return null;
    }
  }

  // Delete a nutrition template
  Future<bool> deleteNutritionTemplate(String templateId) async {
    try {
      await _repository.deleteNutritionTemplate(templateId);

      // Reload templates to get updated list
      await loadTemplates();

      return true;
    } catch (e) {
      state = AsyncValue.error(e, StackTrace.current);
      return false;
    }
  }
}
