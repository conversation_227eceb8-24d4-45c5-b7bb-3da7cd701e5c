import 'package:supabase_flutter/supabase_flutter.dart';
import 'package:fitgo_app/src/shared/utils/app_logger.dart';

/// Service for managing instructor capacity using new instructor_capacity table
class InstructorCapacityService {
  final SupabaseClient _supabase = Supabase.instance.client;

  /// Check if instructor has available capacity
  Future<bool> hasAvailableCapacity(String instructorId) async {
    try {
      AppLogger.info(
        '🔍 Checking capacity for instructor: $instructorId',
        tag: 'CAPACITY',
      );

      final response =
          await _supabase
              .from('instructor_capacity')
              .select('max_students, current_students, is_accepting_students')
              .eq('instructor_id', instructorId)
              .single();

      final maxStudents = response['max_students'] as int? ?? 5;
      final currentStudents = response['current_students'] as int? ?? 0;
      final isAcceptingStudents =
          response['is_accepting_students'] as bool? ?? true;

      final hasCapacity = currentStudents < maxStudents && isAcceptingStudents;

      AppLogger.info(
        '📊 Capacity check: $currentStudents/$maxStudents - Available: $hasCapacity',
        tag: 'CAPACITY',
      );

      return hasCapacity;
    } catch (e) {
      AppLogger.error(
        '❌ Error checking instructor capacity: $e',
        tag: 'CAPACITY',
      );
      return false;
    }
  }

  /// Get instructor capacity info
  Future<InstructorCapacityInfo> getCapacityInfo(String instructorId) async {
    try {
      final response =
          await _supabase
              .from('instructor_capacity')
              .select(
                'max_students, current_students, available_spots, is_accepting_students',
              )
              .eq('instructor_id', instructorId)
              .single();

      final maxStudents = response['max_students'] as int? ?? 5;
      final currentStudents = response['current_students'] as int? ?? 0;

      return InstructorCapacityInfo(
        maxStudents: maxStudents,
        currentStudents: currentStudents,
      );
    } catch (e) {
      AppLogger.error('❌ Error getting capacity info: $e', tag: 'CAPACITY');
      return const InstructorCapacityInfo(maxStudents: 5, currentStudents: 0);
    }
  }

  /// Increment student count when new student enrolls
  Future<bool> incrementStudentCount(String instructorId) async {
    try {
      AppLogger.userAction(
        '➕ Incrementing student count for instructor: $instructorId',
        tag: 'CAPACITY',
      );

      // First check if there's capacity
      final hasCapacity = await hasAvailableCapacity(instructorId);
      if (!hasCapacity) {
        AppLogger.warning(
          '⚠️ Instructor at full capacity, cannot enroll new student',
          tag: 'CAPACITY',
        );
        return false;
      }

      // Increment current_students count
      await _supabase.rpc(
        'increment_instructor_students',
        params: {'instructor_id': instructorId},
      );

      AppLogger.success(
        '✅ Student count incremented successfully',
        tag: 'CAPACITY',
      );
      return true;
    } catch (e) {
      AppLogger.error(
        '❌ Error incrementing student count: $e',
        tag: 'CAPACITY',
      );
      return false;
    }
  }

  /// Decrement student count when student unenrolls
  Future<void> decrementStudentCount(String instructorId) async {
    try {
      AppLogger.userAction(
        '➖ Decrementing student count for instructor: $instructorId',
        tag: 'CAPACITY',
      );

      await _supabase.rpc(
        'decrement_instructor_students',
        params: {'instructor_id': instructorId},
      );

      AppLogger.success(
        '✅ Student count decremented successfully',
        tag: 'CAPACITY',
      );
    } catch (e) {
      AppLogger.error(
        '❌ Error decrementing student count: $e',
        tag: 'CAPACITY',
      );
    }
  }

  /// Update instructor max capacity
  Future<void> updateMaxCapacity(
    String instructorId,
    int newMaxCapacity,
  ) async {
    try {
      AppLogger.userAction(
        '📝 Updating max capacity for instructor: $instructorId to $newMaxCapacity',
        tag: 'CAPACITY',
      );

      await _supabase
          .from('instructor_capacity')
          .update({'max_students': newMaxCapacity})
          .eq('instructor_id', instructorId);

      AppLogger.success('✅ Max capacity updated successfully', tag: 'CAPACITY');
    } catch (e) {
      AppLogger.error('❌ Error updating max capacity: $e', tag: 'CAPACITY');
      rethrow;
    }
  }

  /// Validate enrollment attempt
  Future<EnrollmentValidationResult> validateEnrollment(
    String instructorId,
  ) async {
    try {
      final capacityInfo = await getCapacityInfo(instructorId);

      if (capacityInfo.isFull) {
        return EnrollmentValidationResult(
          canEnroll: false,
          reason:
              'Instructor has reached maximum capacity (${capacityInfo.maxStudents} students)',
          capacityInfo: capacityInfo,
        );
      }

      if (capacityInfo.isNearlyFull) {
        return EnrollmentValidationResult(
          canEnroll: true,
          reason: 'Only ${capacityInfo.remainingCapacity} spots remaining',
          capacityInfo: capacityInfo,
          isWarning: true,
        );
      }

      return EnrollmentValidationResult(
        canEnroll: true,
        reason: '${capacityInfo.remainingCapacity} spots available',
        capacityInfo: capacityInfo,
      );
    } catch (e) {
      AppLogger.error('❌ Error validating enrollment: $e', tag: 'CAPACITY');
      return EnrollmentValidationResult(
        canEnroll: false,
        reason: 'Error checking instructor availability',
        capacityInfo: const InstructorCapacityInfo(
          maxStudents: 5,
          currentStudents: 0,
        ),
      );
    }
  }
}

/// Instructor capacity information
class InstructorCapacityInfo {
  final int maxStudents;
  final int currentStudents;

  const InstructorCapacityInfo({
    required this.maxStudents,
    required this.currentStudents,
  });

  /// Check if instructor has available capacity
  bool get hasAvailableCapacity => currentStudents < maxStudents;

  /// Get remaining capacity
  int get remainingCapacity => maxStudents - currentStudents;

  /// Get capacity percentage (0.0 to 1.0)
  double get capacityPercentage =>
      maxStudents > 0 ? currentStudents / maxStudents : 0.0;

  /// Get capacity status text
  String get capacityStatus => '$currentStudents/$maxStudents students';

  /// Check if capacity is nearly full (>80%)
  bool get isNearlyFull => capacityPercentage > 0.8;

  /// Check if capacity is full
  bool get isFull => currentStudents >= maxStudents;
}

/// Enrollment validation result
class EnrollmentValidationResult {
  final bool canEnroll;
  final String reason;
  final InstructorCapacityInfo capacityInfo;
  final bool isWarning;

  const EnrollmentValidationResult({
    required this.canEnroll,
    required this.reason,
    required this.capacityInfo,
    this.isWarning = false,
  });
}
