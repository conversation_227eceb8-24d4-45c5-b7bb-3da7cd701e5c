import 'package:hooks_riverpod/hooks_riverpod.dart';
import 'package:flutter/material.dart';
import 'dart:io';
import '../domain/instructor_profile_models.dart';
import 'instructor_profile_repository.dart';
import 'instructor_provider.dart';
import 'instructor_registration_service.dart';
import '../presentation/components/profile_edit_dialogs.dart';
import '../../profile_form/application/profile_provider.dart';

/// Provider for instructor profile data
final instructorProfileDataProvider = FutureProvider<InstructorProfileData>((
  ref,
) async {
  final instructorId = ref.watch(currentInstructorIdProvider);
  if (instructorId == null) {
    throw Exception('No instructor ID available');
  }

  final repository = ref.read(instructorProfileRepositoryProvider);
  return repository.getInstructorProfile(instructorId);
});

/// Provider for instructor profile state management
final instructorProfileNotifierProvider =
    StateNotifierProvider<InstructorProfileNotifier, InstructorProfileState>((
  ref,
) {
  return InstructorProfileNotifier(ref);
});

/// State class for instructor profile
class InstructorProfileState {
  final InstructorProfileData? profileData;
  final bool isLoading;
  final String? error;
  final List<FAQ> expandedFAQs;
  final bool isEditing;
  final ProfileSection? editingSection;

  const InstructorProfileState({
    this.profileData,
    this.isLoading = false,
    this.error,
    this.expandedFAQs = const [],
    this.isEditing = false,
    this.editingSection,
  });

  InstructorProfileState copyWith({
    InstructorProfileData? profileData,
    bool? isLoading,
    String? error,
    List<FAQ>? expandedFAQs,
    bool? isEditing,
    ProfileSection? editingSection,
  }) {
    return InstructorProfileState(
      profileData: profileData ?? this.profileData,
      isLoading: isLoading ?? this.isLoading,
      error: error,
      expandedFAQs: expandedFAQs ?? this.expandedFAQs,
      isEditing: isEditing ?? this.isEditing,
      editingSection: editingSection,
    );
  }
}

/// State notifier for instructor profile
class InstructorProfileNotifier extends StateNotifier<InstructorProfileState> {
  final Ref _ref;

  InstructorProfileNotifier(this._ref) : super(const InstructorProfileState());

  /// Initialize profile data
  Future<void> initializeProfile() async {
    if (state.isLoading) return;

    state = state.copyWith(isLoading: true, error: null);

    try {
      final instructorId = _ref.read(currentInstructorIdProvider);
      if (instructorId == null) {
        throw Exception('No instructor ID available');
      }

      // Ensure instructor record exists before loading profile
      final registrationService = _ref.read(
        instructorRegistrationServiceProvider,
      );
      await registrationService.ensureInstructorRecord(instructorId);

      final repository = _ref.read(instructorProfileRepositoryProvider);
      final profileData = await repository.getInstructorProfile(instructorId);

      state = state.copyWith(
        profileData: profileData,
        isLoading: false,
        expandedFAQs: profileData.faqs,
      );
    } catch (e) {
      state = state.copyWith(isLoading: false, error: e.toString());
    }
  }

  /// Refresh profile data
  Future<void> refreshProfile() async {
    await initializeProfile();
  }

  /// Toggle FAQ expansion
  void toggleFAQ(String faqId) {
    final updatedFAQs = state.expandedFAQs.map((faq) {
      if (faq.id == faqId) {
        return faq.copyWith(isExpanded: !faq.isExpanded);
      }
      return faq;
    }).toList();

    state = state.copyWith(expandedFAQs: updatedFAQs);
  }

  /// Start editing a section
  void startEditing(ProfileSection section) {
    state = state.copyWith(isEditing: true, editingSection: section);
  }

  /// Stop editing
  void stopEditing() {
    state = state.copyWith(isEditing: false, editingSection: null);
  }

  /// Handle profile edit actions
  Future<void> handleEditAction(
    ProfileEditAction action, {
    Map<String, dynamic>? data,
  }) async {
    try {
      final instructorId = _ref.read(currentInstructorIdProvider);
      if (instructorId == null) return;

      final repository = _ref.read(instructorProfileRepositoryProvider);

      switch (action) {
        case ProfileEditAction.editBasicInfo:
          if (data?['basicInfo'] != null) {
            final basicInfo = data!['basicInfo'] as InstructorBasicInfo;
            await repository.updateBasicInfo(instructorId, basicInfo);
            // Immediately update state without full refresh for better UX
            _updateBasicInfoInState(basicInfo);
          }
          break;
        case ProfileEditAction.addWorkExperience:
          if (data?['workExperience'] != null) {
            final workExperience = data!['workExperience'] as WorkExperience;
            await repository.addWorkExperience(instructorId, workExperience);
            _addWorkExperienceToState(workExperience);
          }
          break;
        case ProfileEditAction.editWorkExperience:
          if (data?['workExperience'] != null) {
            final workExperience = data!['workExperience'] as WorkExperience;
            await repository.updateWorkExperience(instructorId, workExperience);
            _updateWorkExperienceInState(workExperience);
          }
          break;
        case ProfileEditAction.deleteWorkExperience:
          if (data?['workExperienceId'] != null) {
            final workExperienceId = data!['workExperienceId'] as String;
            await repository.deleteWorkExperience(workExperienceId);
            _removeWorkExperienceFromState(workExperienceId);
          }
          break;
        case ProfileEditAction.addCertification:
          if (data?['certification'] != null) {
            final certification = data!['certification'] as Certification;
            await repository.addCertification(instructorId, certification);
            _addCertificationToState(certification);
          }
          break;
        case ProfileEditAction.editCertification:
          if (data?['certification'] != null) {
            final certification = data!['certification'] as Certification;
            await repository.updateCertification(instructorId, certification);
            _updateCertificationInState(certification);
          }
          break;
        case ProfileEditAction.deleteCertification:
          if (data?['certificationId'] != null) {
            final certificationId = data!['certificationId'] as String;
            await repository.deleteCertification(certificationId);
            _removeCertificationFromState(certificationId);
          }
          break;
        case ProfileEditAction.addFAQ:
          if (data?['faq'] != null) {
            final faq = data!['faq'] as FAQ;
            await repository.addFAQ(instructorId, faq);
            _addFAQToState(faq);
          }
          break;
        case ProfileEditAction.editFAQ:
          if (data?['faq'] != null) {
            final faq = data!['faq'] as FAQ;
            await repository.updateFAQ(instructorId, faq);
            _updateFAQInState(faq);
          }
          break;
        case ProfileEditAction.deleteFAQ:
          if (data?['faqId'] != null) {
            final faqId = data!['faqId'] as String;
            await repository.deleteFAQ(faqId);
            _deleteFAQFromState(faqId);
          }
          break;
        case ProfileEditAction.deleteReview:
          if (data?['reviewId'] != null) {
            await repository.deleteReview(data!['reviewId'] as String);
            await refreshProfile();
          }
          break;
        case ProfileEditAction.manageSubscription:
          // Handle subscription management
          break;
        default:
          break;
      }
    } catch (e) {
      state = state.copyWith(error: e.toString());
    }
  }

  /// Navigate to subscription management
  void navigateToSubscriptionManagement(BuildContext context) {
    // TODO: Implement navigation to subscription management
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(
        content: Text('Opening subscription management...'),
        backgroundColor: Color(0xFFFACC15),
      ),
    );
  }

  // Optimized state update methods to avoid full profile refresh
  void _updateBasicInfoInState(InstructorBasicInfo basicInfo) {
    if (state.profileData != null) {
      final updatedProfileData = InstructorProfileData(
        basicInfo: basicInfo,
        workHistory: state.profileData!.workHistory,
        certifications: state.profileData!.certifications,
        reviews: state.profileData!.reviews,
        faqs: state.profileData!.faqs,
        subscriptionInfo: state.profileData!.subscriptionInfo,
      );

      state = state.copyWith(profileData: updatedProfileData);
    }
  }

  void _addWorkExperienceToState(WorkExperience workExperience) {
    if (state.profileData != null) {
      final updatedWorkHistory = [
        ...state.profileData!.workHistory,
        workExperience,
      ];
      final updatedProfileData = InstructorProfileData(
        basicInfo: state.profileData!.basicInfo,
        workHistory: updatedWorkHistory,
        certifications: state.profileData!.certifications,
        reviews: state.profileData!.reviews,
        faqs: state.profileData!.faqs,
        subscriptionInfo: state.profileData!.subscriptionInfo,
      );
      state = state.copyWith(profileData: updatedProfileData);
    }
  }

  void _addFAQToState(FAQ faq) {
    if (state.profileData != null) {
      final updatedFAQs = [...state.profileData!.faqs, faq];

      final updatedProfileData = InstructorProfileData(
        basicInfo: state.profileData!.basicInfo,
        workHistory: state.profileData!.workHistory,
        certifications: state.profileData!.certifications,
        reviews: state.profileData!.reviews,
        faqs: updatedFAQs,
        subscriptionInfo: state.profileData!.subscriptionInfo,
      );

      // Also update expandedFAQs for immediate UI update
      final updatedExpandedFAQs = [...state.expandedFAQs, faq];

      state = state.copyWith(
        profileData: updatedProfileData,
        expandedFAQs: updatedExpandedFAQs,
      );
    }
  }

  void _updateFAQInState(FAQ updatedFAQ) {
    if (state.profileData != null) {
      final updatedFAQs = state.profileData!.faqs
          .map((faq) => faq.id == updatedFAQ.id ? updatedFAQ : faq)
          .toList();

      final updatedProfileData = InstructorProfileData(
        basicInfo: state.profileData!.basicInfo,
        workHistory: state.profileData!.workHistory,
        certifications: state.profileData!.certifications,
        reviews: state.profileData!.reviews,
        faqs: updatedFAQs,
        subscriptionInfo: state.profileData!.subscriptionInfo,
      );

      // Also update expandedFAQs for immediate UI update
      final updatedExpandedFAQs = state.expandedFAQs
          .map((faq) => faq.id == updatedFAQ.id ? updatedFAQ : faq)
          .toList();

      state = state.copyWith(
        profileData: updatedProfileData,
        expandedFAQs: updatedExpandedFAQs,
      );
    }
  }

  void _deleteFAQFromState(String faqId) {
    if (state.profileData != null) {
      final updatedFAQs =
          state.profileData!.faqs.where((faq) => faq.id != faqId).toList();

      final updatedProfileData = InstructorProfileData(
        basicInfo: state.profileData!.basicInfo,
        workHistory: state.profileData!.workHistory,
        certifications: state.profileData!.certifications,
        reviews: state.profileData!.reviews,
        faqs: updatedFAQs,
        subscriptionInfo: state.profileData!.subscriptionInfo,
      );

      // Also update expandedFAQs for immediate UI update
      final updatedExpandedFAQs =
          state.expandedFAQs.where((faq) => faq.id != faqId).toList();

      state = state.copyWith(
        profileData: updatedProfileData,
        expandedFAQs: updatedExpandedFAQs,
      );
    }
  }

  void _updateWorkExperienceInState(WorkExperience workExperience) {
    if (state.profileData != null) {
      final updatedWorkHistory = state.profileData!.workHistory
          .map((exp) => exp.id == workExperience.id ? workExperience : exp)
          .toList();
      final updatedProfileData = InstructorProfileData(
        basicInfo: state.profileData!.basicInfo,
        workHistory: updatedWorkHistory,
        certifications: state.profileData!.certifications,
        reviews: state.profileData!.reviews,
        faqs: state.profileData!.faqs,
        subscriptionInfo: state.profileData!.subscriptionInfo,
      );
      state = state.copyWith(profileData: updatedProfileData);
    }
  }

  void _removeWorkExperienceFromState(String workExperienceId) {
    if (state.profileData != null) {
      final updatedWorkHistory = state.profileData!.workHistory
          .where((exp) => exp.id != workExperienceId)
          .toList();
      final updatedProfileData = InstructorProfileData(
        basicInfo: state.profileData!.basicInfo,
        workHistory: updatedWorkHistory,
        certifications: state.profileData!.certifications,
        reviews: state.profileData!.reviews,
        faqs: state.profileData!.faqs,
        subscriptionInfo: state.profileData!.subscriptionInfo,
      );
      state = state.copyWith(profileData: updatedProfileData);
    }
  }

  void _addCertificationToState(Certification certification) {
    if (state.profileData != null) {
      final updatedCertifications = [
        ...state.profileData!.certifications,
        certification,
      ];
      final updatedProfileData = InstructorProfileData(
        basicInfo: state.profileData!.basicInfo,
        workHistory: state.profileData!.workHistory,
        certifications: updatedCertifications,
        reviews: state.profileData!.reviews,
        faqs: state.profileData!.faqs,
        subscriptionInfo: state.profileData!.subscriptionInfo,
      );
      state = state.copyWith(profileData: updatedProfileData);
    }
  }

  void _updateCertificationInState(Certification certification) {
    if (state.profileData != null) {
      final updatedCertifications = state.profileData!.certifications
          .map((cert) => cert.id == certification.id ? certification : cert)
          .toList();
      final updatedProfileData = InstructorProfileData(
        basicInfo: state.profileData!.basicInfo,
        workHistory: state.profileData!.workHistory,
        certifications: updatedCertifications,
        reviews: state.profileData!.reviews,
        faqs: state.profileData!.faqs,
        subscriptionInfo: state.profileData!.subscriptionInfo,
      );
      state = state.copyWith(profileData: updatedProfileData);
    }
  }

  void _removeCertificationFromState(String certificationId) {
    if (state.profileData != null) {
      final updatedCertifications = state.profileData!.certifications
          .where((cert) => cert.id != certificationId)
          .toList();
      final updatedProfileData = InstructorProfileData(
        basicInfo: state.profileData!.basicInfo,
        workHistory: state.profileData!.workHistory,
        certifications: updatedCertifications,
        reviews: state.profileData!.reviews,
        faqs: state.profileData!.faqs,
        subscriptionInfo: state.profileData!.subscriptionInfo,
      );
      state = state.copyWith(profileData: updatedProfileData);
    }
  }

  /// Upload profile photo
  Future<void> uploadProfilePhoto(File imageFile) async {
    try {
      state = state.copyWith(isLoading: true, error: null);

      final instructorId = _ref.read(currentInstructorIdProvider);
      if (instructorId == null) {
        throw Exception('No instructor ID available');
      }

      // For now, we'll use the existing profile repository to upload the photo
      // This is a simplified implementation - in a real app you'd want a dedicated method
      final profileRepository = _ref.read(profileRepositoryProvider);

      // Upload the image and get the URL
      final photoUrl = await profileRepository.uploadAvatarPhoto(
        imageFile: imageFile,
        userId: instructorId,
      );

      // Immediately update the state with the new photo URL
      if (state.profileData != null) {
        final updatedBasicInfo = state.profileData!.basicInfo.copyWith(
          profilePictureUrl: photoUrl,
        );

        _updateBasicInfoInState(updatedBasicInfo);
      }

      state = state.copyWith(isLoading: false);

      // Update the basic info with the new photo URL
      if (state.profileData?.basicInfo != null) {
        final updatedBasicInfo = InstructorBasicInfo(
          id: state.profileData!.basicInfo.id,
          name: state.profileData!.basicInfo.name,
          title: state.profileData!.basicInfo.title,
          bio: state.profileData!.basicInfo.bio, // Keep existing bio
          profilePictureUrl: photoUrl,
          averageRating: state.profileData!.basicInfo.averageRating,
          totalReviews: state.profileData!.basicInfo.totalReviews,
          experienceYears: state.profileData!.basicInfo.experienceYears,
          totalStudents: state.profileData!.basicInfo.totalStudents,
          primaryCertification: null, // Remove certification badge
          canEdit: state.profileData!.basicInfo.canEdit,
          maxStudents: state.profileData!.basicInfo.maxStudents,
          currentStudents: state.profileData!.basicInfo.currentStudents,
        );

        final repository = _ref.read(instructorProfileRepositoryProvider);
        await repository.updateBasicInfo(instructorId, updatedBasicInfo);
        await refreshProfile();
      }

      state = state.copyWith(isLoading: false);
    } catch (e) {
      state = state.copyWith(
        isLoading: false,
        error: 'Failed to upload profile photo: $e',
      );
      debugPrint('Error uploading profile photo: $e');
    }
  }

  /// Show edit dialog
  void showEditDialog(
    BuildContext context,
    ProfileEditAction action, {
    dynamic data,
  }) {
    switch (action) {
      case ProfileEditAction.editBasicInfo:
        if (state.profileData?.basicInfo != null) {
          showDialog(
            context: context,
            builder: (context) => BasicInfoEditDialog(
              basicInfo: state.profileData!.basicInfo,
            ),
          );
        }
        break;
      case ProfileEditAction.addWorkExperience:
        showDialog(
          context: context,
          builder: (context) => const WorkExperienceEditDialog(),
        );
        break;
      case ProfileEditAction.editWorkExperience:
        if (data is WorkExperience) {
          showDialog(
            context: context,
            builder: (context) => WorkExperienceEditDialog(experience: data),
          );
        }
        break;
      case ProfileEditAction.addCertification:
        showDialog(
          context: context,
          builder: (context) => const CertificationEditDialog(),
        );
        break;
      case ProfileEditAction.editCertification:
        if (data is Certification) {
          showDialog(
            context: context,
            builder: (context) => CertificationEditDialog(certification: data),
          );
        }
        break;
      case ProfileEditAction.addFAQ:
        showDialog(
          context: context,
          builder: (context) => const FAQEditDialog(),
        );
        break;
      case ProfileEditAction.editFAQ:
        if (data is FAQ) {
          showDialog(
            context: context,
            builder: (context) => FAQEditDialog(faq: data),
          );
        }
        break;
      default:
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Opening ${action.name} editor...'),
            backgroundColor: const Color(0xFFFACC15),
          ),
        );
        break;
    }
  }

  /// Clear error state
  void clearError() {
    state = state.copyWith(error: null);
  }
}

/// Provider for instructor badges - now watches state for real-time updates
final instructorBadgesProvider = Provider<List<InstructorBadge>>((ref) {
  final profileState = ref.watch(instructorProfileNotifierProvider);
  final profileData = profileState.profileData;

  if (profileData == null) return [];

  final basicInfo = profileData.basicInfo;

  return [
    InstructorBadge(
      label: 'Experience',
      value: '${basicInfo.experienceYears} years',
      icon: '🏆',
      type: BadgeType.experience,
    ),
    InstructorBadge(
      label: 'Students',
      value: '${basicInfo.totalStudents}',
      icon: '👥',
      type: BadgeType.students,
    ),
  ];
});

/// Provider for profile completion percentage
final profileCompletionProvider = Provider<double>((ref) {
  final profileData = ref.watch(instructorProfileDataProvider).value;
  if (profileData == null) return 0.0;

  int completedSections = 0;
  int totalSections = 6;

  // Basic info is always completed if we have data
  completedSections++;

  // Work history
  if (profileData.workHistory.isNotEmpty) completedSections++;

  // Certifications
  if (profileData.certifications.isNotEmpty) completedSections++;

  // Reviews (not controlled by instructor but counts for completion)
  if (profileData.reviews.isNotEmpty) completedSections++;

  // FAQs
  if (profileData.faqs.isNotEmpty) completedSections++;

  // Subscription
  if (profileData.subscriptionInfo != null) completedSections++;

  return completedSections / totalSections;
});

/// Provider for review statistics
final reviewStatsProvider = Provider<Map<String, dynamic>>((ref) {
  final profileData = ref.watch(instructorProfileDataProvider).value;
  if (profileData == null) return {};

  final reviews = profileData.reviews;
  if (reviews.isEmpty) return {};

  // Calculate rating distribution
  final ratingCounts = <int, int>{};
  for (int i = 1; i <= 5; i++) {
    ratingCounts[i] = 0;
  }

  for (final review in reviews) {
    final rating = review.rating.round();
    ratingCounts[rating] = (ratingCounts[rating] ?? 0) + 1;
  }

  return {
    'total': reviews.length,
    'average': profileData.basicInfo.averageRating,
    'distribution': ratingCounts,
    'recent': reviews.take(3).toList(),
  };
});

/// Provider for empty state messages
final emptyStateMessagesProvider = Provider<Map<ProfileSection, String>>((ref) {
  return {
    ProfileSection.workHistory:
        'Add your work experience to showcase your professional background',
    ProfileSection.certifications:
        'Add certifications to build trust with potential clients',
    ProfileSection.reviews:
        'Client reviews will appear here once you start training students',
    ProfileSection.faqs:
        'Add frequently asked questions to help potential clients learn more about you',
  };
});
