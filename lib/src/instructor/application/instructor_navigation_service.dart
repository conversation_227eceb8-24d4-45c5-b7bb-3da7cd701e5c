import 'package:hooks_riverpod/hooks_riverpod.dart';
import 'package:supabase_flutter/supabase_flutter.dart';
import 'package:go_router/go_router.dart';
import 'package:flutter/material.dart';
import '../presentation/student_profile_detail_screen.dart';
import '../../feedback/presentation/instructor/feedback_queue_screen.dart';

/// Provider for instructor navigation service
final instructorNavigationServiceProvider =
    Provider<InstructorNavigationService>((ref) {
  return InstructorNavigationService();
});

/// Service for handling instructor-specific navigation logic
class InstructorNavigationService {
  final SupabaseClient _supabase = Supabase.instance.client;

  /// Check if current user is instructor and navigate accordingly
  Future<void> handleUserNavigation(BuildContext context) async {
    try {
      final user = _supabase.auth.currentUser;
      if (user == null) {
        // No user logged in, go to auth
        context.go('/auth');
        return;
      }

      // Check user role from profiles table
      final isInstructor = await _checkIfUserIsInstructor(user.id);

      if (isInstructor) {
        // User is instructor, navigate to instructor homepage
        context.go('/instructor-main');
      } else {
        // User is student, navigate to student dashboard
        context.go('/dashboard');
      }
    } catch (e) {
      // Error checking role, default to auth page
      context.go('/auth');
    }
  }

  /// Check if user is instructor from database
  Future<bool> _checkIfUserIsInstructor(String userId) async {
    try {
      // First check profiles table
      final profileResponse = await _supabase
          .from('profiles')
          .select('role')
          .eq('id', userId)
          .maybeSingle();

      if (profileResponse != null) {
        return profileResponse['role'] == 'instructor';
      }

      // Fallback: check instructors table
      final instructorResponse = await _supabase
          .from('instructors')
          .select('id')
          .eq('id', userId)
          .maybeSingle();

      return instructorResponse != null;
    } catch (e) {
      return false;
    }
  }

  /// Navigate to instructor homepage after successful registration/login
  Future<void> navigateToInstructorHomepage(BuildContext context) async {
    context.go('/instructor-main');
  }

  /// Navigate to specific instructor task view
  void navigateToTaskView(BuildContext context, String taskType) {
    switch (taskType.toLowerCase()) {
      case 'feedback':
        Navigator.of(context).push(
          MaterialPageRoute(
            builder: (context) => const FeedbackQueueScreen(),
          ),
        );
        break;
      default:
        // TODO: Implement navigation for other task types
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Opening $taskType tasks...'),
            backgroundColor: const Color(0xFFFACC15),
          ),
        );
        break;
    }
  }

  /// Navigate to student profile view
  void navigateToStudentProfile(BuildContext context, String studentId) {
    Navigator.of(context).push(
      MaterialPageRoute(
        builder: (context) => StudentProfileDetailScreen(studentId: studentId),
      ),
    );
  }

  /// Navigate to alert action view
  void navigateToAlertAction(
      BuildContext context, String alertType, String studentId) {
    // TODO: Implement navigation to alert action views
    // For now, just show a snackbar
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text('Handling $alertType for student: $studentId'),
        backgroundColor: const Color(0xFFFACC15),
      ),
    );
  }

  /// Check if user should see instructor homepage on app launch
  Future<bool> shouldShowInstructorHomepage() async {
    try {
      final user = _supabase.auth.currentUser;
      if (user == null) return false;

      return await _checkIfUserIsInstructor(user.id);
    } catch (e) {
      return false;
    }
  }

  /// Handle first-time instructor setup
  Future<void> handleFirstTimeInstructorSetup(
      BuildContext context, String instructorId) async {
    try {
      // Check if instructor has completed initial setup
      final setupResponse = await _supabase
          .from('instructors')
          .select('setup_completed')
          .eq('id', instructorId)
          .maybeSingle();

      if (setupResponse != null && setupResponse['setup_completed'] == true) {
        // Setup already completed, go to homepage
        navigateToInstructorHomepage(context);
      } else {
        // First time setup needed
        // TODO: Navigate to instructor onboarding/setup flow
        navigateToInstructorHomepage(context);
      }
    } catch (e) {
      // Error checking setup, default to homepage
      navigateToInstructorHomepage(context);
    }
  }

  /// Create initial tasks for new student enrollment
  Future<void> createTasksForNewStudentEnrollment({
    required String instructorId,
    required String studentId,
    required String programType,
  }) async {
    try {
      final tasks = [
        {
          'instructor_id': instructorId,
          'student_id': studentId,
          'task_type': 'workout',
          'title': 'Create Workout Plan',
          'description': 'Design initial workout plan for new student',
          'status': 'pending',
          'priority': 'high',
          'created_at': DateTime.now().toIso8601String(),
        },
        {
          'instructor_id': instructorId,
          'student_id': studentId,
          'task_type': 'cardio',
          'title': 'Create Cardio Plan',
          'description': 'Design cardio routine for new student',
          'status': 'pending',
          'priority': 'medium',
          'created_at': DateTime.now().toIso8601String(),
        },
        {
          'instructor_id': instructorId,
          'student_id': studentId,
          'task_type': 'nutrition',
          'title': 'Create Nutrition Plan',
          'description': 'Design nutrition plan for new student',
          'status': 'pending',
          'priority': 'high',
          'created_at': DateTime.now().toIso8601String(),
        },
      ];

      // Add supplements task for premium students
      if (programType.toLowerCase().contains('premium')) {
        tasks.add({
          'instructor_id': instructorId,
          'student_id': studentId,
          'task_type': 'supplement',
          'title': 'Create Supplements Plan',
          'description': 'Recommend supplements for new premium student',
          'status': 'pending',
          'priority': 'low',
          'created_at': DateTime.now().toIso8601String(),
        });
      }

      // Insert tasks into database
      await _supabase.from('instructor_tasks').insert(tasks);
    } catch (e) {
      // Log error but don't throw - tasks can be created manually
      print('Error creating tasks for new student: $e');
    }
  }
}
