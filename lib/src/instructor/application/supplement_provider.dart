import 'package:hooks_riverpod/hooks_riverpod.dart';
import '../domain/supplement_models.dart';
import '../data/supplement_repository.dart';
import '../../shared/utils/app_logger.dart';

// Repository provider
final supplementRepositoryProvider = Provider<SupplementRepository>((ref) {
  return SupplementRepository();
});

// Supplements list provider
final supplementsProvider = FutureProvider.family<List<Supplement>, SupplementFilters>((ref, filters) async {
  final repository = ref.read(supplementRepositoryProvider);
  return repository.getSupplements(
    type: filters.type,
    category: filters.category,
    searchQuery: filters.searchQuery,
  );
});

// Student supplements provider
final studentSupplementsProvider = FutureProvider.family<List<StudentSupplementAssignment>, StudentSupplementParams>((ref, params) async {
  final repository = ref.read(supplementRepositoryProvider);
  return repository.getStudentSupplements(params.studentId, params.instructorId);
});

// Supplement assignment notifier
final supplementAssignmentNotifierProvider = StateNotifierProvider<SupplementAssignmentNotifier, SupplementAssignmentState>((ref) {
  return SupplementAssignmentNotifier(ref.read(supplementRepositoryProvider));
});

// Supplement assignment state
class SupplementAssignmentState {
  final bool isLoading;
  final String? error;
  final List<StudentSupplementAssignment> assignments;

  const SupplementAssignmentState({
    this.isLoading = false,
    this.error,
    this.assignments = const [],
  });

  SupplementAssignmentState copyWith({
    bool? isLoading,
    String? error,
    List<StudentSupplementAssignment>? assignments,
  }) {
    return SupplementAssignmentState(
      isLoading: isLoading ?? this.isLoading,
      error: error,
      assignments: assignments ?? this.assignments,
    );
  }
}

// Supplement assignment notifier
class SupplementAssignmentNotifier extends StateNotifier<SupplementAssignmentState> {
  final SupplementRepository _repository;

  SupplementAssignmentNotifier(this._repository) : super(const SupplementAssignmentState());

  /// Assign supplement to student
  Future<StudentSupplementAssignment?> assignSupplement({
    required String studentId,
    required String instructorId,
    required String supplementId,
    required int quantity,
    String? notes,
  }) async {
    state = state.copyWith(isLoading: true, error: null);

    try {
      final assignment = await _repository.assignSupplementToStudent(
        studentId: studentId,
        instructorId: instructorId,
        supplementId: supplementId,
        quantity: quantity,
        notes: notes,
      );

      // Update assignments list
      final updatedAssignments = [...state.assignments, assignment];
      state = state.copyWith(
        isLoading: false,
        assignments: updatedAssignments,
      );

      AppLogger.success(
        'Supplement assigned successfully: ${assignment.supplement?.name}',
        tag: 'SUPPLEMENT_PROVIDER',
      );

      return assignment;
    } catch (e) {
      state = state.copyWith(
        isLoading: false,
        error: 'Failed to assign supplement: $e',
      );
      
      AppLogger.error(
        'Failed to assign supplement: $e',
        tag: 'SUPPLEMENT_PROVIDER',
      );
      
      return null;
    }
  }

  /// Update supplement assignment
  Future<StudentSupplementAssignment?> updateAssignment({
    required String assignmentId,
    int? quantity,
    String? notes,
    SupplementAssignmentStatus? status,
  }) async {
    state = state.copyWith(isLoading: true, error: null);

    try {
      final updatedAssignment = await _repository.updateSupplementAssignment(
        assignmentId: assignmentId,
        quantity: quantity,
        notes: notes,
        status: status,
      );

      // Update assignments list
      final updatedAssignments = state.assignments.map((assignment) {
        return assignment.id == assignmentId ? updatedAssignment : assignment;
      }).toList();

      state = state.copyWith(
        isLoading: false,
        assignments: updatedAssignments,
      );

      AppLogger.success(
        'Supplement assignment updated: $assignmentId',
        tag: 'SUPPLEMENT_PROVIDER',
      );

      return updatedAssignment;
    } catch (e) {
      state = state.copyWith(
        isLoading: false,
        error: 'Failed to update assignment: $e',
      );
      
      AppLogger.error(
        'Failed to update assignment: $e',
        tag: 'SUPPLEMENT_PROVIDER',
      );
      
      return null;
    }
  }

  /// Remove supplement assignment
  Future<bool> removeAssignment(String assignmentId) async {
    state = state.copyWith(isLoading: true, error: null);

    try {
      await _repository.removeSupplementAssignment(assignmentId);

      // Remove from assignments list
      final updatedAssignments = state.assignments
          .where((assignment) => assignment.id != assignmentId)
          .toList();

      state = state.copyWith(
        isLoading: false,
        assignments: updatedAssignments,
      );

      AppLogger.success(
        'Supplement assignment removed: $assignmentId',
        tag: 'SUPPLEMENT_PROVIDER',
      );

      return true;
    } catch (e) {
      state = state.copyWith(
        isLoading: false,
        error: 'Failed to remove assignment: $e',
      );
      
      AppLogger.error(
        'Failed to remove assignment: $e',
        tag: 'SUPPLEMENT_PROVIDER',
      );
      
      return false;
    }
  }

  /// Load student assignments
  Future<void> loadStudentAssignments(String studentId, String instructorId) async {
    state = state.copyWith(isLoading: true, error: null);

    try {
      final assignments = await _repository.getStudentSupplements(studentId, instructorId);
      
      state = state.copyWith(
        isLoading: false,
        assignments: assignments,
      );

      AppLogger.success(
        'Loaded ${assignments.length} supplement assignments',
        tag: 'SUPPLEMENT_PROVIDER',
      );
    } catch (e) {
      state = state.copyWith(
        isLoading: false,
        error: 'Failed to load assignments: $e',
      );
      
      AppLogger.error(
        'Failed to load assignments: $e',
        tag: 'SUPPLEMENT_PROVIDER',
      );
    }
  }

  /// Create custom supplement
  Future<Supplement?> createCustomSupplement({
    required String name,
    required String brand,
    required String description,
    required SupplementType type,
    required SupplementCategory category,
    String? imageUrl,
    List<String>? benefits,
    List<String>? ingredients,
    String? warnings,
  }) async {
    try {
      final supplement = await _repository.createCustomSupplement(
        name: name,
        brand: brand,
        description: description,
        type: type,
        category: category,
        imageUrl: imageUrl,
        benefits: benefits,
        ingredients: ingredients,
        warnings: warnings,
      );

      AppLogger.success(
        'Custom supplement created: $name',
        tag: 'SUPPLEMENT_PROVIDER',
      );

      return supplement;
    } catch (e) {
      AppLogger.error(
        'Failed to create custom supplement: $e',
        tag: 'SUPPLEMENT_PROVIDER',
      );
      
      return null;
    }
  }
}

// Helper classes
class SupplementFilters {
  final SupplementType? type;
  final SupplementCategory? category;
  final String? searchQuery;

  const SupplementFilters({
    this.type,
    this.category,
    this.searchQuery,
  });

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is SupplementFilters &&
          runtimeType == other.runtimeType &&
          type == other.type &&
          category == other.category &&
          searchQuery == other.searchQuery;

  @override
  int get hashCode => type.hashCode ^ category.hashCode ^ searchQuery.hashCode;
}

class StudentSupplementParams {
  final String studentId;
  final String instructorId;

  const StudentSupplementParams({
    required this.studentId,
    required this.instructorId,
  });

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is StudentSupplementParams &&
          runtimeType == other.runtimeType &&
          studentId == other.studentId &&
          instructorId == other.instructorId;

  @override
  int get hashCode => studentId.hashCode ^ instructorId.hashCode;
}
