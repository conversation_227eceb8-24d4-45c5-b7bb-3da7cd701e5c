import 'package:hooks_riverpod/hooks_riverpod.dart';
import 'package:supabase_flutter/supabase_flutter.dart';
import 'package:flutter/foundation.dart';
import '../domain/subscription_plan_models.dart';

/// Exception thrown when instructor profile is incomplete
class ProfileIncompleteException implements Exception {
  final String message;
  final List<String> missingItems;

  const ProfileIncompleteException(this.message, this.missingItems);

  @override
  String toString() => message;
}

/// Provider for subscription plan repository
final subscriptionPlanRepositoryProvider = Provider<SubscriptionPlanRepository>(
  (ref) {
    return SubscriptionPlanRepository();
  },
);

/// Repository for instructor subscription plan management
class SubscriptionPlanRepository {
  final SupabaseClient _supabase = Supabase.instance.client;

  /// Get instructor's subscription configuration
  Future<InstructorSubscriptionConfig> getInstructorSubscriptionConfig(
    String instructorId,
  ) async {
    try {
      // Get base configuration including approval_status
      final configResponse = await _supabase
          .from('instructor_subscription_configs')
          .select('*, approval_status, approved_by, approved_at')
          .eq('instructor_id', instructorId)
          .maybeSingle();

      if (configResponse == null) {
        // Return default configuration for new instructors
        return _getDefaultConfiguration();
      }

      return _parseConfigurationFromDatabase(configResponse);
    } catch (e) {
      // Return mock data for development
      return _getMockConfiguration();
    }
  }

  /// Save instructor's subscription configuration
  Future<void> saveInstructorSubscriptionConfig(
    String instructorId,
    InstructorSubscriptionConfig config,
  ) async {
    try {
      // Save main configuration using new column structure
      await _supabase.from('instructor_subscription_configs').upsert({
        'instructor_id': instructorId,
        'desired_monthly_earnings': config.baseMonthlyPrice,
        'submission_status': config.submissionStatus.name,
        'last_submitted_at': config.lastSubmittedAt?.toIso8601String(),
        'admin_feedback': config.adminFeedback,
        'updated_at': DateTime.now().toIso8601String(),
      });

      // Calculate and update all plan prices using the database function
      await _supabase.rpc(
        'calculate_plan_prices_from_earnings',
        params: {
          'instructor_uuid': instructorId,
          'desired_earnings': config.baseMonthlyPrice,
        },
      );

      // Save discount codes
      for (final discountCode in config.discountCodes) {
        await _saveDiscountCode(instructorId, discountCode);
      }
    } catch (e) {
      throw Exception('Failed to save subscription configuration: $e');
    }
  }

  /// Submit configuration for admin review
  Future<void> submitForReview(
    String instructorId,
    InstructorSubscriptionConfig config,
  ) async {
    try {
      // First save the config so it exists for profile readiness check
      final updatedConfig = config.copyWith(
        submissionStatus: SubmissionStatus.submitted,
        lastSubmittedAt: DateTime.now(),
      );

      await saveInstructorSubscriptionConfig(instructorId, updatedConfig);

      // Then check if profile is ready using database function
      final profileReadyResponse = await _supabase.rpc(
        'is_instructor_profile_ready',
        params: {'instructor_id': instructorId},
      );

      final isProfileReady = profileReadyResponse as bool? ?? false;

      if (!isProfileReady) {
        throw ProfileIncompleteException(
          'Profil bilgilerinizi tamamlamanız gerekiyor: Temel bilgiler, iş deneyimi, sertifikalar ve fiyatlandırma ayarlarını kontrol edin.',
          ['Profil eksik'],
        );
      }

      // Create approval application instead of making instructor public directly
      await _createApprovalApplication(instructorId);

      // Notify admin (could be implemented with notifications table)
      await _notifyAdminOfSubmission(instructorId);
    } catch (e) {
      if (e is ProfileIncompleteException) {
        rethrow; // Re-throw profile incomplete exceptions as-is
      }
      throw Exception('Failed to submit for review: $e');
    }
  }

  /// Create a new discount code
  Future<DiscountCode> createDiscountCode(
    String instructorId,
    DiscountCode discountCode,
  ) async {
    try {
      // Get current config
      final currentConfig = await getInstructorSubscriptionConfig(instructorId);

      // Create new discount code with unique ID
      final newDiscountCode = discountCode.copyWith(
        id: DateTime.now().millisecondsSinceEpoch.toString(),
      );

      // Add to existing discount codes
      final updatedDiscountCodes = [
        ...currentConfig.discountCodes,
        newDiscountCode,
      ];

      // Serialize discount codes to JSONB
      final discountCodesJson = updatedDiscountCodes
          .map(
            (code) => {
              'id': code.id,
              'code': code.code,
              'discountPercentage': code.discountPercentage,
              'expiryDate': code.expiryDate.toIso8601String(),
              'isActive': code.isActive,
              'usageLimit': code.usageLimit,
              'usageCount': code.usageCount,
              'applicablePlans':
                  code.applicablePlans.map((p) => p.name).toList(),
            },
          )
          .toList();

      // Check if config exists
      final existingConfig = await _supabase
          .from('instructor_subscription_configs')
          .select('id')
          .eq('instructor_id', instructorId)
          .maybeSingle();

      if (existingConfig != null) {
        // Update existing config
        await _supabase.from('instructor_subscription_configs').update({
          'discount_codes': discountCodesJson,
          'updated_at': DateTime.now().toIso8601String(),
        }).eq('instructor_id', instructorId);
      } else {
        // Insert new config
        await _supabase.from('instructor_subscription_configs').insert({
          'instructor_id': instructorId,
          'discount_codes': discountCodesJson,
          'desired_monthly_earnings': 0,
          'submission_status': 'draft',
          'created_at': DateTime.now().toIso8601String(),
          'updated_at': DateTime.now().toIso8601String(),
        });
      }

      return newDiscountCode;
    } catch (e) {
      throw Exception('Failed to create discount code: $e');
    }
  }

  /// Delete a discount code
  Future<void> deleteDiscountCode(String discountCodeId) async {
    try {
      // Get current instructor ID from context
      final instructorId = _supabase.auth.currentUser?.id;
      if (instructorId == null) {
        throw Exception('No authenticated instructor found');
      }

      // Get current config
      final currentConfig = await getInstructorSubscriptionConfig(instructorId);

      // Remove the discount code
      final updatedDiscountCodes = currentConfig.discountCodes
          .where((code) => code.id != discountCodeId)
          .toList();

      // Serialize discount codes to JSONB
      final discountCodesJson = updatedDiscountCodes
          .map(
            (code) => {
              'id': code.id,
              'code': code.code,
              'discountPercentage': code.discountPercentage,
              'expiryDate': code.expiryDate.toIso8601String(),
              'isActive': code.isActive,
              'usageLimit': code.usageLimit,
              'usageCount': code.usageCount,
              'applicablePlans':
                  code.applicablePlans.map((p) => p.name).toList(),
            },
          )
          .toList();

      // Update the config in database
      await _supabase.from('instructor_subscription_configs').update({
        'discount_codes': discountCodesJson,
        'updated_at': DateTime.now().toIso8601String(),
      }).eq('instructor_id', instructorId);
    } catch (e) {
      throw Exception('Failed to delete discount code: $e');
    }
  }

  /// Validate subscription configuration
  ValidationResult validateConfiguration(InstructorSubscriptionConfig config) {
    final errors = <String>[];
    final warnings = <String>[];

    // Validate base price
    if (config.baseMonthlyPrice <= 0) {
      errors.add('Base monthly price must be greater than 0');
    }
    if (config.baseMonthlyPrice < 10) {
      warnings.add(
        'Base monthly price seems low. Consider pricing competitively.',
      );
    }
    if (config.baseMonthlyPrice > 200) {
      warnings.add(
        'Base monthly price seems high. This might limit your client base.',
      );
    }

    // Validate plan pricing
    for (final duration in PlanDuration.values) {
      final basicPricing = config.basicPlanPricing[duration];
      final premiumPricing = config.premiumPlanPricing[duration];

      if (basicPricing == null) {
        errors.add('Basic plan pricing for ${duration.displayName} is missing');
      }
      if (premiumPricing == null) {
        errors.add(
          'Premium plan pricing for ${duration.displayName} is missing',
        );
      }

      if (basicPricing != null && premiumPricing != null) {
        if (premiumPricing.monthlyPrice <= basicPricing.monthlyPrice) {
          errors.add(
            'Premium plan must be more expensive than Basic plan for ${duration.displayName}',
          );
        }
      }
    }

    // Validate discount codes
    for (final discountCode in config.discountCodes) {
      if (discountCode.code.length < 3) {
        errors.add('Discount code "${discountCode.code}" is too short');
      }
      if (discountCode.discountPercentage <= 0 ||
          discountCode.discountPercentage > 50) {
        errors.add(
          'Discount percentage for "${discountCode.code}" must be between 1% and 50%',
        );
      }
      if (discountCode.expiryDate.isBefore(DateTime.now())) {
        warnings.add(
          'Discount code "${discountCode.code}" has already expired',
        );
      }
    }

    return ValidationResult(
      isValid: errors.isEmpty,
      errors: errors,
      warnings: warnings,
    );
  }

  /// Calculate pricing for a plan based on trainer earnings and duration
  /// The basePrice parameter represents trainer's desired net earnings for BASIC plan
  PlanPricing calculatePlanPricing({
    required double basePrice, // Trainer's net earnings for basic plan
    required PlanDuration duration,
    required PlanType planType,
    double? customMultiplier,
  }) {
    // Commission rates
    const double appCommissionRate = 0.30; // 30% app commission

    // Calculate trainer earnings based on plan type
    // basePrice is what trainer wants to earn from BASIC plan
    // Premium plan earns 1.5x more for trainer
    final trainerEarnings = planType == PlanType.premium
        ? basePrice * 1.5 // Premium: 1.5x trainer earnings
        : basePrice; // Basic: trainer's desired earnings

    // Calculate total price including app commission
    // Formula: Total Price = Trainer Earnings / (1 - Commission Rate)
    final totalMonthlyPrice = trainerEarnings / (1 - appCommissionRate);

    // Apply duration discount
    final discountedMonthlyPrice =
        totalMonthlyPrice * duration.discountMultiplier;
    final totalPrice = discountedMonthlyPrice * duration.months;

    return PlanPricing(
      monthlyPrice: discountedMonthlyPrice,
      totalPrice: totalPrice,
      duration: duration,
      features: PlanFeatures.getFeaturesForPlan(planType),
      isCustomPrice: customMultiplier != null,
    );
  }

  /// Private helper methods
  InstructorSubscriptionConfig _getDefaultConfiguration() {
    const basePrice = 30.00; // Default trainer earnings

    final basicPricing = <PlanDuration, PlanPricing>{};
    final premiumPricing = <PlanDuration, PlanPricing>{};

    for (final duration in PlanDuration.values) {
      basicPricing[duration] = calculatePlanPricing(
        basePrice: basePrice,
        duration: duration,
        planType: PlanType.basic,
      );

      premiumPricing[duration] = calculatePlanPricing(
        basePrice: basePrice,
        duration: duration,
        planType: PlanType.premium,
      );
    }

    return InstructorSubscriptionConfig(
      baseMonthlyPrice: basePrice,
      basicPlanPricing: basicPricing,
      premiumPlanPricing: premiumPricing,
      discountCodes: [],
    );
  }

  InstructorSubscriptionConfig _getMockConfiguration() {
    const basePrice = 30.00; // Default trainer earnings

    final basicPricing = <PlanDuration, PlanPricing>{};
    final premiumPricing = <PlanDuration, PlanPricing>{};

    for (final duration in PlanDuration.values) {
      basicPricing[duration] = calculatePlanPricing(
        basePrice: basePrice,
        duration: duration,
        planType: PlanType.basic,
      );

      premiumPricing[duration] = calculatePlanPricing(
        basePrice: basePrice,
        duration: duration,
        planType: PlanType.premium,
      );
    }

    return InstructorSubscriptionConfig(
      baseMonthlyPrice: basePrice,
      basicPlanPricing: basicPricing,
      premiumPlanPricing: premiumPricing,
      discountCodes: [
        DiscountCode(
          id: '1',
          code: 'SUMMER2024',
          discountPercentage: 20.0,
          expiryDate: DateTime.now().add(const Duration(days: 90)),
          usageCount: 5,
          usageLimit: 100,
        ),
        DiscountCode(
          id: '2',
          code: 'NEWCLIENT',
          discountPercentage: 15.0,
          expiryDate: DateTime.now().add(const Duration(days: 365)),
          usageCount: 12,
          usageLimit: 50,
        ),
      ],
      submissionStatus: SubmissionStatus.draft,
    );
  }

  InstructorSubscriptionConfig _parseConfigurationFromDatabase(
    Map<String, dynamic> configData,
  ) {
    // Get desired monthly earnings (new column structure)
    final desiredEarnings =
        (configData['desired_monthly_earnings'] as num?)?.toDouble() ?? 0.0;

    // Parse pricing from individual columns instead of JSON
    final basicPlanPricing = <PlanDuration, PlanPricing>{};
    final premiumPlanPricing = <PlanDuration, PlanPricing>{};

    // Basic plan pricing from columns
    final basicMonthlyPrice =
        (configData['basic_plan_monthly_price'] as num?)?.toDouble() ?? 0.0;
    final basic6MonthPrice =
        (configData['basic_plan_6month_price'] as num?)?.toDouble() ?? 0.0;
    final basicYearlyPrice =
        (configData['basic_plan_yearly_price'] as num?)?.toDouble() ?? 0.0;

    // Premium plan pricing from columns
    final premiumMonthlyPrice =
        (configData['premium_plan_monthly_price'] as num?)?.toDouble() ?? 0.0;
    final premium6MonthPrice =
        (configData['premium_plan_6month_price'] as num?)?.toDouble() ?? 0.0;
    final premiumYearlyPrice =
        (configData['premium_plan_yearly_price'] as num?)?.toDouble() ?? 0.0;

    // Create basic plan pricing objects
    basicPlanPricing[PlanDuration.monthly] = PlanPricing(
      monthlyPrice: basicMonthlyPrice,
      totalPrice: basicMonthlyPrice,
      duration: PlanDuration.monthly,
      features: PlanFeatures.getFeaturesForPlan(PlanType.basic),
      isCustomPrice: basicMonthlyPrice > 0,
    );

    basicPlanPricing[PlanDuration.sixMonth] = PlanPricing(
      monthlyPrice: basic6MonthPrice / 6,
      totalPrice: basic6MonthPrice,
      duration: PlanDuration.sixMonth,
      features: PlanFeatures.getFeaturesForPlan(PlanType.basic),
      isCustomPrice: basic6MonthPrice > 0,
    );

    basicPlanPricing[PlanDuration.yearly] = PlanPricing(
      monthlyPrice: basicYearlyPrice / 12,
      totalPrice: basicYearlyPrice,
      duration: PlanDuration.yearly,
      features: PlanFeatures.getFeaturesForPlan(PlanType.basic),
      isCustomPrice: basicYearlyPrice > 0,
    );

    // Create premium plan pricing objects
    premiumPlanPricing[PlanDuration.monthly] = PlanPricing(
      monthlyPrice: premiumMonthlyPrice,
      totalPrice: premiumMonthlyPrice,
      duration: PlanDuration.monthly,
      features: PlanFeatures.getFeaturesForPlan(PlanType.premium),
      isCustomPrice: premiumMonthlyPrice > 0,
    );

    premiumPlanPricing[PlanDuration.sixMonth] = PlanPricing(
      monthlyPrice: premium6MonthPrice / 6,
      totalPrice: premium6MonthPrice,
      duration: PlanDuration.sixMonth,
      features: PlanFeatures.getFeaturesForPlan(PlanType.premium),
      isCustomPrice: premium6MonthPrice > 0,
    );

    premiumPlanPricing[PlanDuration.yearly] = PlanPricing(
      monthlyPrice: premiumYearlyPrice / 12,
      totalPrice: premiumYearlyPrice,
      duration: PlanDuration.yearly,
      features: PlanFeatures.getFeaturesForPlan(PlanType.premium),
      isCustomPrice: premiumYearlyPrice > 0,
    );

    // Parse discount codes from JSONB
    final discountCodesData =
        configData['discount_codes'] as List<dynamic>? ?? [];
    final discountCodes = discountCodesData.map((codeData) {
      final data = codeData as Map<String, dynamic>;
      return DiscountCode(
        id: data['id'] as String,
        code: data['code'] as String,
        discountPercentage: (data['discountPercentage'] as num).toDouble(),
        expiryDate: DateTime.parse(data['expiryDate'] as String),
        isActive: data['isActive'] as bool? ?? true,
        usageLimit: data['usageLimit'] as int? ?? 100,
        usageCount: data['usageCount'] as int? ?? 0,
        applicablePlans: (data['applicablePlans'] as List<dynamic>?)
                ?.map(
                  (p) => PlanType.values.firstWhere((type) => type.name == p),
                )
                .toList() ??
            [PlanType.basic, PlanType.premium],
      );
    }).toList();

    return InstructorSubscriptionConfig(
      baseMonthlyPrice: desiredEarnings,
      basicPlanPricing: basicPlanPricing,
      premiumPlanPricing: premiumPlanPricing,
      discountCodes: discountCodes,
      submissionStatus: _determineSubmissionStatus(configData),
      lastSubmittedAt: configData['last_submitted_at'] != null
          ? DateTime.parse(configData['last_submitted_at'] as String)
          : null,
      adminFeedback: configData['admin_feedback'] as String?,
    );
  }

  /// Determine submission status based on approval_status and submission_status
  SubmissionStatus _determineSubmissionStatus(Map<String, dynamic> configData) {
    final approvalStatus = configData['approval_status'] as String?;
    final submissionStatus = configData['submission_status'] as String?;

    // Priority: approval_status overrides submission_status
    if (approvalStatus != null) {
      switch (approvalStatus) {
        case 'approved':
          return SubmissionStatus.approved;
        case 'rejected':
          return SubmissionStatus.rejected;
        case 'pending':
        case 'under_review':
          return SubmissionStatus.underReview;
        default:
          break; // Fall through to submission_status check
      }
    }

    // Fallback to submission_status if approval_status is not decisive
    if (submissionStatus != null) {
      switch (submissionStatus) {
        case 'submitted':
          return SubmissionStatus.submitted;
        case 'approved':
          return SubmissionStatus.approved;
        case 'rejected':
          return SubmissionStatus.rejected;
        case 'needs_revision':
          return SubmissionStatus.needsRevision;
        case 'draft':
        default:
          return SubmissionStatus.draft;
      }
    }

    return SubmissionStatus.draft;
  }

  Future<void> _saveDiscountCode(
    String instructorId,
    DiscountCode discountCode,
  ) async {
    // Implementation for saving individual discount codes
  }

  /// Create approval application for instructor
  Future<void> _createApprovalApplication(String instructorId) async {
    try {
      // Update instructor_subscription_configs with approval status
      await _supabase.from('instructor_subscription_configs').update({
        'approval_status': 'pending',
        'application_submitted_at': DateTime.now().toIso8601String(),
        'updated_at': DateTime.now().toIso8601String(),
      }).eq('instructor_id', instructorId);

      debugPrint(
        '✅ Approval application created in instructor_subscription_configs',
      );
    } catch (e) {
      throw Exception('Failed to create approval application: $e');
    }
  }

  Future<void> _notifyAdminOfSubmission(String instructorId) async {
    try {
      // Check if admin_notifications table exists
      final tableExists = await _supabase
          .from('information_schema.tables')
          .select('table_name')
          .eq('table_schema', 'public')
          .eq('table_name', 'admin_notifications')
          .maybeSingle();

      if (tableExists != null) {
        // Create admin notification
        await _supabase.from('admin_notifications').insert({
          'type': 'instructor_submission',
          'instructor_id': instructorId,
          'message': 'New instructor submitted profile for review',
          'is_read': false,
          'created_at': DateTime.now().toIso8601String(),
        });
        debugPrint('✅ Admin notification created');
      } else {
        debugPrint(
          '⚠️ Admin notifications table does not exist, skipping notification',
        );
      }
    } catch (e) {
      // Notification failure shouldn't block the submission
      debugPrint('Failed to notify admin: $e');
    }
  }
}
