import 'package:hooks_riverpod/hooks_riverpod.dart';
import 'package:supabase_flutter/supabase_flutter.dart';
import 'package:flutter/foundation.dart';
import '../domain/instructor_profile_models.dart';
import '../../shared/utils/supabase_table_manager.dart';

/// Provider for instructor profile repository
final instructorProfileRepositoryProvider =
    Provider<InstructorProfileRepository>((ref) {
  return InstructorProfileRepository();
});

/// Repository for instructor profile data operations
class InstructorProfileRepository {
  final SupabaseClient _supabase = Supabase.instance.client;

  /// Get complete instructor profile data
  Future<InstructorProfileData> getInstructorProfile(
    String instructorId,
  ) async {
    try {
      // Get basic info
      final basicInfo = await _getBasicInfo(instructorId);

      // Get work history
      final workHistory = await _getWorkHistory(instructorId);

      // Get certifications
      final certifications = await _getCertifications(instructorId);

      // Get reviews
      final reviews = await _getReviews(instructorId);

      // Get FAQs
      final faqs = await _getFAQs(instructorId);

      // Get subscription info
      final subscriptionInfo = await _getSubscriptionInfo(instructorId);

      return InstructorProfileData(
        basicInfo: basicInfo,
        workHistory: workHistory,
        certifications: certifications,
        reviews: reviews,
        faqs: faqs,
        subscriptionInfo: subscriptionInfo,
      );
    } catch (e) {
      debugPrint('❌ Error getting instructor profile: $e');
      rethrow;
    }
  }

  /// Get basic instructor information (✅ CLEANED: Use profiles for name)
  Future<InstructorBasicInfo> _getBasicInfo(String instructorId) async {
    try {
      final response = await _supabase
          .from('instructors')
          .select(
            'id, title, bio, photo_url, experience_years, rating, profiles!instructors_profile_id_fkey(name, surname)',
          )
          .eq('id', instructorId)
          .single();

      // Get reviews count and average rating
      final reviewsResponse = await _supabase
          .from('instructor_reviews')
          .select('rating')
          .eq('instructor_id', instructorId);

      double averageRating = 0.0;
      int totalReviews = reviewsResponse.length;
      if (totalReviews > 0) {
        final ratings = reviewsResponse.map((r) => r['rating'] as num).toList();
        averageRating = ratings.reduce((a, b) => a + b) / ratings.length;
      }

      // Get student count through enrollments (CLEAN ARCHITECTURE)
      final studentCountResponse = await _supabase
          .from('enrollments')
          .select('student_id')
          .eq('instructor_id', instructorId)
          .eq('is_active', true);

      final totalStudents = studentCountResponse.length;

      // Get name from profiles (✅ CLEANED: Use profiles data)
      final profiles = response['profiles'] as Map<String, dynamic>?;
      final name = profiles?['name'] as String? ?? '';
      final surname = profiles?['surname'] as String? ?? '';
      final fullName = '$name $surname'.trim();

      return InstructorBasicInfo(
        id: instructorId,
        name: fullName.isNotEmpty ? fullName : 'Enter your name',
        title: response['title'] as String? ?? 'Enter your title',
        bio: response['bio'] as String?,
        profilePictureUrl: response['photo_url'] as String?,
        averageRating: averageRating,
        totalReviews: totalReviews,
        experienceYears: response['experience_years'] as int? ?? 0,
        totalStudents: totalStudents,
        primaryCertification: null, // Remove default certification
        canEdit: true,
        currentStudents:
            totalStudents, // Fix: Pass totalStudents as currentStudents for capacity display
      );
    } catch (e) {
      debugPrint('❌ Error getting basic info: $e');
      rethrow;
    }
  }

  /// Get work history
  Future<List<WorkExperience>> _getWorkHistory(String instructorId) async {
    try {
      final response = await _supabase
          .from('instructor_work_history')
          .select(
            'id, company_name, role, start_year, end_year, description, is_current',
          )
          .eq('instructor_id', instructorId)
          .order('id', ascending: false);

      return response.map<WorkExperience>((work) {
        return WorkExperience(
          id: work['id'].toString(),
          companyName: work['company_name'] as String? ?? 'Unknown Company',
          role: work['role'] as String? ?? 'Unknown Role',
          startYear: work['start_year'] as String? ?? '',
          endYear: work['end_year'] as String?,
          description: work['description'] as String? ?? '',
          isCurrent: work['is_current'] as bool? ?? false,
        );
      }).toList();
    } catch (e) {
      debugPrint('❌ Error getting work history: $e');
      return []; // Return empty list if no work history
    }
  }

  /// Get certifications
  Future<List<Certification>> _getCertifications(String instructorId) async {
    try {
      final response = await _supabase
          .from('instructor_certifications')
          .select('id, name, issuer, year')
          .eq('instructor_id', instructorId)
          .order('id', ascending: false);

      return response.map<Certification>((cert) {
        return Certification(
          id: cert['id'].toString(),
          name: cert['name'] as String? ?? 'Unknown Certification',
          issuer: cert['issuer'] as String? ?? 'Unknown Organization',
          year: cert['year'] as String? ?? '',
          isVerified: false, // is_verified column removed from schema
        );
      }).toList();
    } catch (e) {
      debugPrint('❌ Error getting certifications: $e');
      return []; // Return empty list if no certifications
    }
  }

  /// Get client reviews
  Future<List<ClientReview>> _getReviews(String instructorId) async {
    try {
      final response = await _supabase
          .from('instructor_reviews')
          .select(
            'id, instructor_id, client_name, avatar_url, rating, comment, created_at',
          )
          .eq('instructor_id', instructorId)
          .order('created_at', ascending: false)
          .limit(10);

      return response.map<ClientReview>((review) {
        return ClientReview(
          id: review['id'].toString(),
          clientName: review['client_name'] as String? ?? 'Anonymous',
          clientAvatarUrl: review['avatar_url'] as String?,
          rating: (review['rating'] as num).toDouble(),
          comment: review['comment'] as String? ?? '',
          createdAt: DateTime.parse(review['created_at'] as String),
          canModerate: true,
        );
      }).toList();
    } catch (e) {
      debugPrint('❌ Error getting reviews: $e');
      return []; // Return empty list if no reviews
    }
  }

  /// Get FAQs
  Future<List<FAQ>> _getFAQs(String instructorId) async {
    try {
      debugPrint('🔄 Getting FAQs for instructor: $instructorId');

      // Check if table exists first
      final tableExists = await SupabaseTableManager.tableExists(
        'instructor_faqs',
      );
      if (!tableExists) {
        debugPrint(
          '⚠️ instructor_faqs table does not exist, attempting to create...',
        );
        await SupabaseTableManager.ensureInstructorFaqsTable();
      }

      final response = await _supabase
          .from('instructor_faqs')
          .select('*')
          .eq('instructor_id', instructorId)
          .order('order_index', ascending: true);

      debugPrint('✅ Found ${response.length} FAQs');

      return response.map<FAQ>((faq) {
        return FAQ(
          id: faq['id'].toString(),
          question: faq['question'] as String,
          answer: faq['answer'] as String,
          order: (faq['order_index'] as num?)?.toInt() ?? 0,
          isExpanded: false, // Default to collapsed
        );
      }).toList();
    } catch (e) {
      debugPrint('❌ Error getting FAQs: $e');
      return []; // Return empty list if error
    }
  }

  /// Get subscription information
  Future<SubscriptionInfo?> _getSubscriptionInfo(String instructorId) async {
    try {
      // Check if instructor has submitted subscription configuration
      final response = await _supabase
          .from('instructor_subscription_configs')
          .select(
            'submission_status, approval_status, desired_monthly_earnings, last_submitted_at, approved_at',
          )
          .eq('instructor_id', instructorId)
          .maybeSingle();

      if (response == null) {
        return null; // No subscription configuration yet
      }

      final status = _determineDisplayStatus(response);
      final desiredEarnings =
          (response['desired_monthly_earnings'] as num?)?.toDouble() ?? 0.0;
      final lastSubmitted = response['last_submitted_at'] as String?;
      final approvedAt = response['approved_at'] as String?;

      return SubscriptionInfo(
        planName: _getStatusDisplayName(status),
        status: status,
        expiresAt: approvedAt != null
            ? DateTime.parse(approvedAt)
            : (lastSubmitted != null ? DateTime.parse(lastSubmitted) : null),
        autoRenew: status == 'approved',
        monthlyPrice: desiredEarnings,
      );
    } catch (e) {
      debugPrint('❌ Error getting subscription info: $e');
      return null; // Return null if no subscription data
    }
  }

  /// Determine display status based on approval_status and submission_status
  String _determineDisplayStatus(Map<String, dynamic> response) {
    final approvalStatus = response['approval_status'] as String?;
    final submissionStatus = response['submission_status'] as String?;

    // Priority: approval_status overrides submission_status
    if (approvalStatus != null) {
      switch (approvalStatus) {
        case 'approved':
          return 'approved';
        case 'rejected':
          return 'rejected';
        case 'pending':
        case 'under_review':
          return 'under_review';
        default:
          break; // Fall through to submission_status check
      }
    }

    // Fallback to submission_status if approval_status is not decisive
    return submissionStatus ?? 'draft';
  }

  /// Get display name for subscription status
  String _getStatusDisplayName(String status) {
    switch (status) {
      case 'draft':
        return 'Setup Required';
      case 'submitted':
        return 'Under Review';
      case 'under_review':
        return 'Under Review';
      case 'approved':
        return 'Professional Trainer';
      case 'rejected':
        return 'Needs Updates';
      default:
        return 'Setup Required';
    }
  }

  /// Update basic info
  Future<void> updateBasicInfo(
    String instructorId,
    InstructorBasicInfo basicInfo,
  ) async {
    try {
      debugPrint('🔄 Updating basic info for instructor: $instructorId');
      debugPrint('📝 Data: ${basicInfo.name}, ${basicInfo.title}');

      // Split name into name and surname if needed
      final nameParts = basicInfo.name.split(' ');
      final firstName = nameParts.isNotEmpty ? nameParts[0] : basicInfo.name;
      final lastName =
          nameParts.length > 1 ? nameParts.sublist(1).join(' ') : '';

      // Update profiles table (✅ CLEANED: Single source of truth)
      await _supabase.from('profiles').update({
        'name': firstName,
        'surname': lastName.isNotEmpty ? lastName : null,
        'updated_at': DateTime.now().toIso8601String(),
      }).eq('id', instructorId);

      // Update instructor-specific data (✅ CLEANED: Only existing columns)
      await _supabase.from('instructors').update({
        'bio': basicInfo.bio,
        'photo_url': basicInfo.profilePictureUrl,
        'experience_years': basicInfo.experienceYears,
        'title': basicInfo.title,
        'updated_at': DateTime.now().toIso8601String(),
      }).eq('id', instructorId);

      debugPrint('✅ Basic info updated successfully');
    } catch (e) {
      debugPrint('❌ Error updating basic info: $e');
      rethrow;
    }
  }

  /// Add work experience
  Future<void> addWorkExperience(
    String instructorId,
    WorkExperience experience,
  ) async {
    try {
      debugPrint('🔄 Adding work experience for instructor: $instructorId');
      debugPrint(
        '📝 Company: ${experience.companyName}, Role: ${experience.role}',
      );

      // No need for duration formatting - using separate fields now

      await _supabase.from('instructor_work_history').insert({
        'instructor_id': instructorId,
        'company_name': experience.companyName,
        'role': experience.role,
        'start_year': experience.startYear,
        'end_year': experience.isCurrent ? null : experience.endYear,
        'description': experience.description,
        'is_current': experience.isCurrent,
        'created_at': DateTime.now().toIso8601String(),
      });

      debugPrint('✅ Work experience added successfully');
    } catch (e) {
      debugPrint('❌ Error adding work experience: $e');
      rethrow;
    }
  }

  /// Update work experience
  Future<void> updateWorkExperience(
    String instructorId,
    WorkExperience experience,
  ) async {
    try {
      debugPrint('🔄 Updating work experience: ${experience.id}');

      await _supabase.from('instructor_work_history').update({
        'company_name': experience.companyName,
        'role': experience.role,
        'start_year': experience.startYear,
        'end_year': experience.isCurrent ? null : experience.endYear,
        'description': experience.description,
        'is_current': experience.isCurrent,
      }).eq('id', experience.id);

      debugPrint('✅ Work experience updated successfully');
    } catch (e) {
      debugPrint('❌ Error updating work experience: $e');
      rethrow;
    }
  }

  /// Delete work experience
  Future<void> deleteWorkExperience(String workExperienceId) async {
    try {
      debugPrint('🔄 Deleting work experience: $workExperienceId');

      await _supabase
          .from('instructor_work_history')
          .delete()
          .eq('id', workExperienceId);

      debugPrint('✅ Work experience deleted successfully');
    } catch (e) {
      debugPrint('❌ Error deleting work experience: $e');
      rethrow;
    }
  }

  /// Add certification
  Future<void> addCertification(
    String instructorId,
    Certification certification,
  ) async {
    try {
      debugPrint('🔄 Adding certification for instructor: $instructorId');
      debugPrint(
        '📝 Certification: ${certification.name} from ${certification.issuer}',
      );

      await _supabase.from('instructor_certifications').insert({
        'instructor_id': instructorId,
        'name': certification.name,
        'issuer': certification.issuer,
        'year': certification.year,
        'created_at': DateTime.now().toIso8601String(),
      });

      debugPrint('✅ Certification added successfully');
    } catch (e) {
      debugPrint('❌ Error adding certification: $e');
      rethrow;
    }
  }

  /// Update certification
  Future<void> updateCertification(
    String instructorId,
    Certification certification,
  ) async {
    try {
      debugPrint('🔄 Updating certification: ${certification.id}');

      await _supabase.from('instructor_certifications').update({
        'name': certification.name,
        'issuer': certification.issuer,
        'year': certification.year,
      }).eq('id', certification.id);

      debugPrint('✅ Certification updated successfully');
    } catch (e) {
      debugPrint('❌ Error updating certification: $e');
      rethrow;
    }
  }

  /// Delete certification
  Future<void> deleteCertification(String certificationId) async {
    try {
      debugPrint('🔄 Deleting certification: $certificationId');

      await _supabase
          .from('instructor_certifications')
          .delete()
          .eq('id', certificationId);

      debugPrint('✅ Certification deleted successfully');
    } catch (e) {
      debugPrint('❌ Error deleting certification: $e');
      rethrow;
    }
  }

  /// Add FAQ
  Future<void> addFAQ(String instructorId, FAQ faq) async {
    try {
      debugPrint('🔄 Adding FAQ for instructor: $instructorId');
      debugPrint('📝 Question: ${faq.question}');

      // Check if table exists first
      final tableExists = await SupabaseTableManager.tableExists(
        'instructor_faqs',
      );
      if (!tableExists) {
        debugPrint(
          '⚠️ instructor_faqs table does not exist, attempting to create...',
        );
        await SupabaseTableManager.ensureInstructorFaqsTable();
      }

      await _supabase.from('instructor_faqs').insert({
        'instructor_id': instructorId,
        'question': faq.question,
        'answer': faq.answer,
        'order_index': faq.order,
      });

      debugPrint('✅ FAQ added successfully');
    } catch (e) {
      debugPrint('❌ Error adding FAQ: $e');
      throw Exception('Failed to add FAQ: $e');
    }
  }

  /// Update FAQ
  Future<void> updateFAQ(String instructorId, FAQ faq) async {
    try {
      debugPrint('🔄 Updating FAQ: ${faq.id}');

      await _supabase
          .from('instructor_faqs')
          .update({
            'question': faq.question,
            'answer': faq.answer,
            'order_index': faq.order,
          })
          .eq('id', faq.id)
          .eq('instructor_id', instructorId);

      debugPrint('✅ FAQ updated successfully');
    } catch (e) {
      debugPrint('❌ Error updating FAQ: $e');
      throw Exception('Failed to update FAQ: $e');
    }
  }

  /// Delete FAQ
  Future<void> deleteFAQ(String faqId) async {
    try {
      debugPrint('🔄 Deleting FAQ: $faqId');

      await _supabase.from('instructor_faqs').delete().eq('id', faqId);

      debugPrint('✅ FAQ deleted successfully');
    } catch (e) {
      debugPrint('❌ Error deleting FAQ: $e');
      throw Exception('Failed to delete FAQ: $e');
    }
  }

  /// Delete review (moderation)
  Future<void> deleteReview(String reviewId) async {
    await _supabase.from('instructor_reviews').delete().eq('id', reviewId);
  }

  /// Check if instructor profile is complete enough for subscription setup
  Future<ProfileCompletionStatus> checkProfileCompletion(
    String instructorId,
  ) async {
    try {
      final profileData = await getInstructorProfile(instructorId);

      final issues = <String>[];

      // Check basic info completeness
      if (profileData.basicInfo.name == 'Enter your name' ||
          profileData.basicInfo.name.trim().isEmpty) {
        issues.add('Adınızı ve soyadınızı ekleyin');
      }

      if (profileData.basicInfo.title == 'Enter your title' ||
          profileData.basicInfo.title.trim().isEmpty) {
        issues.add('Uzmanlık alanınızı belirtin');
      }

      if (profileData.basicInfo.experienceYears == 0) {
        issues.add('Deneyim yılınızı girin');
      }

      // Check work history
      if (profileData.workHistory.isEmpty) {
        issues.add('En az bir iş deneyimi ekleyin');
      }

      // Check certifications
      if (profileData.certifications.isEmpty) {
        issues.add('En az bir sertifika ekleyin');
      }

      final isComplete = issues.isEmpty;
      final completionPercentage = _calculateCompletionPercentage(profileData);

      return ProfileCompletionStatus(
        isComplete: isComplete,
        completionPercentage: completionPercentage,
        missingItems: issues,
      );
    } catch (e) {
      debugPrint('❌ Error checking profile completion: $e');
      return ProfileCompletionStatus(
        isComplete: false,
        completionPercentage: 0.0,
        missingItems: ['Profil bilgileri yüklenirken hata oluştu'],
      );
    }
  }

  /// Calculate profile completion percentage
  double _calculateCompletionPercentage(InstructorProfileData profileData) {
    int completedSections = 0;
    int totalSections =
        5; // Basic info, title, experience, work history, certifications

    // Basic info name
    if (profileData.basicInfo.name != 'Enter your name' &&
        profileData.basicInfo.name.trim().isNotEmpty) {
      completedSections++;
    }

    // Basic info title
    if (profileData.basicInfo.title != 'Enter your title' &&
        profileData.basicInfo.title.trim().isNotEmpty) {
      completedSections++;
    }

    // Experience years
    if (profileData.basicInfo.experienceYears > 0) {
      completedSections++;
    }

    // Work history
    if (profileData.workHistory.isNotEmpty) {
      completedSections++;
    }

    // Certifications
    if (profileData.certifications.isNotEmpty) {
      completedSections++;
    }

    return (completedSections / totalSections) * 100;
  }

  // ==========================================
  // WEB ADMIN PANEL API METHODS
  // ==========================================

  /// Get all instructor subscription submissions for admin panel
  /// Returns list of submissions with instructor basic info
  Future<List<Map<String, dynamic>>> getInstructorSubmissionsForAdmin() async {
    try {
      final response = await _supabase
          .from('instructor_subscription_configs')
          .select('''
            id,
            instructor_id,
            base_monthly_price,
            submission_status,
            last_submitted_at,
            admin_feedback,
            created_at,
            instructors!inner(
              id,
              photo_url,
              experience_years,
              rating,
              title,
              is_public
            )
          ''')
          .neq('submission_status', 'draft')
          .order('last_submitted_at', ascending: false);

      return response;
    } catch (e) {
      throw Exception('Failed to fetch instructor submissions: $e');
    }
  }

  /// Get detailed instructor profile for admin review
  /// Includes work history, certifications, and subscription config
  Future<Map<String, dynamic>> getInstructorDetailForAdmin(
    String instructorId,
  ) async {
    try {
      // Get instructor basic info (✅ CLEANED: Include profiles data)
      final instructorResponse = await _supabase
          .from('instructors')
          .select(
            'id, profile_id, photo_url, bio, rating, experience_years, title, is_public, profiles!instructors_profile_id_fkey(name, surname, email, phone)',
          )
          .eq('id', instructorId)
          .single();

      // Get work history
      final workHistoryResponse = await _supabase
          .from('instructor_work_history')
          .select('*')
          .eq('instructor_id', instructorId)
          .order('start_year', ascending: false);

      // Get certifications
      final certificationsResponse = await _supabase
          .from('instructor_certifications')
          .select('*')
          .eq('instructor_id', instructorId)
          .order('year', ascending: false);

      // Get subscription config
      final subscriptionResponse = await _supabase
          .from('instructor_subscription_configs')
          .select('*')
          .eq('instructor_id', instructorId)
          .maybeSingle();

      // Get reviews
      final reviewsResponse = await _supabase
          .from('instructor_reviews')
          .select('*')
          .eq('instructor_id', instructorId)
          .order('created_at', ascending: false)
          .limit(10);

      return {
        'instructor_profile': instructorResponse,
        'work_history': workHistoryResponse,
        'certifications': certificationsResponse,
        'subscription_config': subscriptionResponse,
        'recent_reviews': reviewsResponse,
      };
    } catch (e) {
      throw Exception('Failed to fetch instructor detail: $e');
    }
  }

  /// Update instructor submission status (approve/reject)
  Future<void> updateInstructorSubmissionStatus({
    required String instructorId,
    required String status, // 'approved', 'rejected', 'needs_revision'
    String? adminFeedback,
  }) async {
    try {
      // Update subscription config status
      await _supabase.from('instructor_subscription_configs').update({
        'submission_status': status,
        'admin_feedback': adminFeedback,
        'reviewed_at': DateTime.now().toIso8601String(),
      }).eq('instructor_id', instructorId);

      // If approved, make instructor public
      if (status == 'approved') {
        await _supabase.from('instructors').update({
          'is_public': true,
          'updated_at': DateTime.now().toIso8601String(),
        }).eq('id', instructorId);
      }
    } catch (e) {
      throw Exception('Failed to update submission status: $e');
    }
  }

  /// Get instructor submission statistics for admin dashboard
  Future<Map<String, int>> getSubmissionStatistics() async {
    try {
      final response = await _supabase
          .from('instructor_subscription_configs')
          .select('submission_status');

      final stats = <String, int>{
        'total': response.length,
        'pending': 0,
        'approved': 0,
        'rejected': 0,
        'needs_revision': 0,
      };

      for (final item in response) {
        final status = item['submission_status'] as String;
        switch (status) {
          case 'submitted':
          case 'under_review':
            stats['pending'] = (stats['pending'] ?? 0) + 1;
            break;
          case 'approved':
            stats['approved'] = (stats['approved'] ?? 0) + 1;
            break;
          case 'rejected':
            stats['rejected'] = (stats['rejected'] ?? 0) + 1;
            break;
          case 'needs_revision':
            stats['needs_revision'] = (stats['needs_revision'] ?? 0) + 1;
            break;
        }
      }

      return stats;
    } catch (e) {
      throw Exception('Failed to fetch submission statistics: $e');
    }
  }
}

/// Profile completion status model
class ProfileCompletionStatus {
  final bool isComplete;
  final double completionPercentage;
  final List<String> missingItems;

  const ProfileCompletionStatus({
    required this.isComplete,
    required this.completionPercentage,
    required this.missingItems,
  });
}

/// Course visibility management extension for InstructorProfileRepository
extension CourseVisibilityManagement on InstructorProfileRepository {
  /// Hide instructor course (only if no active students)
  Future<Map<String, dynamic>> hideCourse({
    required String instructorId,
    String? reason,
  }) async {
    try {
      debugPrint('🔄 Hiding course for instructor: $instructorId');

      // Use database function to safely hide course
      final result = await _supabase.rpc(
        'hide_instructor_course',
        params: {
          'instructor_profile_id': instructorId,
          'hide_reason': reason,
        },
      );

      final response = result as Map<String, dynamic>;
      debugPrint('✅ Hide course result: $response');

      return response;
    } catch (e) {
      debugPrint('❌ Error hiding course: $e');
      throw Exception('Failed to hide course: $e');
    }
  }

  /// Reactivate instructor course (requires re-approval)
  Future<Map<String, dynamic>> reactivateCourse({
    required String instructorId,
  }) async {
    try {
      debugPrint('🔄 Reactivating course for instructor: $instructorId');

      // Use database function to reactivate course
      final result = await _supabase.rpc(
        'reactivate_instructor_course',
        params: {
          'instructor_profile_id': instructorId,
        },
      );

      final response = result as Map<String, dynamic>;
      debugPrint('✅ Reactivate course result: $response');

      return response;
    } catch (e) {
      debugPrint('❌ Error reactivating course: $e');
      throw Exception('Failed to reactivate course: $e');
    }
  }

  /// Check if instructor can hide course (always true - can hide even with active students)
  Future<bool> canHideCourse(String instructorId) async {
    try {
      debugPrint('🔍 Checking if instructor can hide course: $instructorId');

      // Instructors can always hide their course
      // They will continue serving existing students but won't accept new ones
      return true;
    } catch (e) {
      debugPrint('❌ Error checking if can hide course: $e');
      return false;
    }
  }

  /// Get course status information
  Future<Map<String, dynamic>> getCourseStatus(String instructorId) async {
    try {
      debugPrint('🔍 Getting course status for instructor: $instructorId');

      final response = await _supabase
          .from('instructors')
          .select(
              'course_status, course_hidden_at, course_hidden_reason, can_accept_new_students, is_public')
          .eq('profile_id', instructorId)
          .single();

      // Get active students count
      final activeStudentsResponse = await _supabase
          .from('enrollments')
          .select('id')
          .eq('instructor_id', instructorId)
          .eq('is_active', true);

      final result = {
        'course_status': response['course_status'] as String? ?? 'active',
        'course_hidden_at': response['course_hidden_at'] as String?,
        'course_hidden_reason': response['course_hidden_reason'] as String?,
        'can_accept_new_students':
            response['can_accept_new_students'] as bool? ?? true,
        'is_public': response['is_public'] as bool? ?? false,
        'active_students_count': activeStudentsResponse.length,
        'can_hide_course': activeStudentsResponse.isEmpty,
      };

      debugPrint('✅ Course status: $result');
      return result;
    } catch (e) {
      debugPrint('❌ Error getting course status: $e');
      throw Exception('Failed to get course status: $e');
    }
  }
}
