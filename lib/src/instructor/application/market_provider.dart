import 'package:hooks_riverpod/hooks_riverpod.dart';
import '../domain/market_models.dart';
import 'market_repository.dart';

/// Provider for featured market products
final featuredProductsProvider = FutureProvider<List<MarketProduct>>((ref) async {
  final repository = ref.read(marketRepositoryProvider);
  return repository.getFeaturedProducts();
});

/// Provider for all market products
final allProductsProvider = FutureProvider<List<MarketProduct>>((ref) async {
  final repository = ref.read(marketRepositoryProvider);
  return repository.getProducts();
});

/// Provider for product categories
final productCategoriesProvider = FutureProvider<List<ProductCategory>>((ref) async {
  final repository = ref.read(marketRepositoryProvider);
  return repository.getCategories();
});

/// Provider for products by category
final productsByCategoryProvider = FutureProvider.family<List<MarketProduct>, ProductCategory>((ref, category) async {
  final repository = ref.read(marketRepositoryProvider);
  return repository.getProducts(category: category);
});

/// Provider for popular products
final popularProductsProvider = FutureProvider<List<MarketProduct>>((ref) async {
  final repository = ref.read(marketRepositoryProvider);
  return repository.getProducts(popularOnly: true);
});

/// Provider for new products
final newProductsProvider = FutureProvider<List<MarketProduct>>((ref) async {
  final repository = ref.read(marketRepositoryProvider);
  return repository.getProducts(newOnly: true);
});

/// Provider for product search
final productSearchProvider = FutureProvider.family<List<MarketProduct>, String>((ref, query) async {
  final repository = ref.read(marketRepositoryProvider);
  return repository.searchProducts(query);
});

/// State notifier for explore tab state
class ExploreTabState {
  final bool isLoading;
  final String? error;
  final ProductCategory? selectedCategory;

  const ExploreTabState({
    this.isLoading = false,
    this.error,
    this.selectedCategory,
  });

  ExploreTabState copyWith({
    bool? isLoading,
    String? error,
    ProductCategory? selectedCategory,
    bool clearError = false,
    bool clearCategory = false,
  }) {
    return ExploreTabState(
      isLoading: isLoading ?? this.isLoading,
      error: clearError ? null : (error ?? this.error),
      selectedCategory: clearCategory ? null : (selectedCategory ?? this.selectedCategory),
    );
  }
}

class ExploreTabNotifier extends StateNotifier<ExploreTabState> {
  ExploreTabNotifier() : super(const ExploreTabState());

  void setLoading(bool loading) {
    state = state.copyWith(isLoading: loading);
  }

  void setError(String? error) {
    state = state.copyWith(error: error, clearError: error == null);
  }

  void selectCategory(ProductCategory? category) {
    state = state.copyWith(
      selectedCategory: category,
      clearCategory: category == null,
    );
  }

  void clearCategory() {
    state = state.copyWith(clearCategory: true);
  }
}

/// Provider for explore tab state
final exploreTabProvider = StateNotifierProvider<ExploreTabNotifier, ExploreTabState>((ref) {
  return ExploreTabNotifier();
});
