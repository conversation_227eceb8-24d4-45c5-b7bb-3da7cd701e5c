import 'package:hooks_riverpod/hooks_riverpod.dart';
import 'package:supabase_flutter/supabase_flutter.dart';
import 'package:flutter/foundation.dart';

/// Service to handle instructor registration and profile creation
class InstructorRegistrationService {
  final SupabaseClient _supabase;

  InstructorRegistrationService(this._supabase);

  /// Create instructor record in instructors table after registration
  Future<void> createInstructorRecord(
    String userId, {
    required String name,
    String? surname,
    String? email,
    String? photoUrl,
  }) async {
    try {
      debugPrint('🔄 Creating instructor record for user: $userId');
      debugPrint('📝 Name: $name, Email: $email');

      // Check if instructor record already exists
      final existingInstructor =
          await _supabase
              .from('instructors')
              .select('id')
              .eq('id', userId)
              .maybeSingle();

      if (existingInstructor != null) {
        debugPrint('✅ Instructor record already exists');
        return;
      }

      // Create instructor record (✅ CLEANED: Only existing columns)
      await _supabase.from('instructors').insert({
        'id': userId,
        'profile_id': userId, // Link to profiles table
        'is_public': false, // Not public until approved
        'created_at': DateTime.now().toIso8601String(),
        'updated_at': DateTime.now().toIso8601String(),
      });

      debugPrint('✅ Instructor record created successfully');
    } catch (e) {
      debugPrint('❌ Error creating instructor record: $e');
      rethrow;
    }
  }

  /// Sync existing instructor profiles from profiles table to instructors table
  Future<void> syncExistingInstructors() async {
    try {
      debugPrint('🔄 Syncing existing instructor profiles...');

      // Get all instructor profiles that don't have instructor records
      final instructorProfiles = await _supabase
          .from('profiles')
          .select('id, name, surname, email')
          .eq('role', 'instructor');

      for (final profile in instructorProfiles) {
        final userId = profile['id'] as String;

        // Check if instructor record exists
        final existingInstructor =
            await _supabase
                .from('instructors')
                .select('id')
                .eq('id', userId)
                .maybeSingle();

        if (existingInstructor == null) {
          // Create instructor record
          await createInstructorRecord(
            userId,
            name: profile['name'] as String? ?? 'Unknown',
            surname: profile['surname'] as String?,
            email: profile['email'] as String?,
            photoUrl: null, // avatar_url moved to user_profiles table
          );
        }
      }

      debugPrint('✅ Instructor sync completed');
    } catch (e) {
      debugPrint('❌ Error syncing instructors: $e');
      rethrow;
    }
  }

  /// Check if current user has instructor record and create if missing
  Future<void> ensureInstructorRecord(String userId) async {
    try {
      debugPrint('🔄 Ensuring instructor record exists for: $userId');

      // Check if instructor record exists
      final existingInstructor =
          await _supabase
              .from('instructors')
              .select('id')
              .eq('id', userId)
              .maybeSingle();

      if (existingInstructor != null) {
        debugPrint('✅ Instructor record already exists');
        return;
      }

      // Get profile data
      final profile =
          await _supabase
              .from('profiles')
              .select('name, surname, email')
              .eq('id', userId)
              .eq('role', 'instructor')
              .maybeSingle();

      if (profile == null) {
        debugPrint('❌ User is not an instructor or profile not found');
        return;
      }

      // Create instructor record
      await createInstructorRecord(
        userId,
        name: profile['name'] as String? ?? 'Unknown',
        surname: profile['surname'] as String?,
        email: profile['email'] as String?,
        photoUrl: null, // avatar_url moved to user_profiles table
      );

      debugPrint('✅ Instructor record created from profile data');
    } catch (e) {
      debugPrint('❌ Error ensuring instructor record: $e');
      rethrow;
    }
  }

  /// Update instructor public status after approval
  Future<void> updateInstructorStatus(String userId, {bool? isPublic}) async {
    try {
      debugPrint('🔄 Updating instructor status for: $userId');

      final updateData = <String, dynamic>{
        'updated_at': DateTime.now().toIso8601String(),
      };

      if (isPublic != null) updateData['is_public'] = isPublic;

      await _supabase.from('instructors').update(updateData).eq('id', userId);

      debugPrint('✅ Instructor status updated successfully');
    } catch (e) {
      debugPrint('❌ Error updating instructor status: $e');
      rethrow;
    }
  }

  /// Check if instructor profile is ready for submission
  Future<bool> isProfileReadyForSubmission(String instructorId) async {
    try {
      final response = await _supabase.rpc(
        'is_instructor_profile_ready',
        params: {'instructor_id': instructorId},
      );
      return response as bool? ?? false;
    } catch (e) {
      debugPrint('❌ Error checking profile readiness: $e');
      return false;
    }
  }
}

/// Provider for instructor registration service
final instructorRegistrationServiceProvider =
    Provider<InstructorRegistrationService>((ref) {
      return InstructorRegistrationService(Supabase.instance.client);
    });
