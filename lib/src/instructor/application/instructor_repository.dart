import 'package:hooks_riverpod/hooks_riverpod.dart';
import 'package:supabase_flutter/supabase_flutter.dart';
import 'package:flutter/material.dart';
import '../domain/instructor_models.dart';

/// Provider for instructor repository
final instructorRepositoryProvider = Provider<InstructorRepository>((ref) {
  return InstructorRepository();
});

/// Repository for instructor-related data operations
class InstructorRepository {
  final SupabaseClient _supabase = Supabase.instance.client;

  /// Get instructor dashboard data
  Future<InstructorDashboard> getInstructorDashboard(
    String instructorId,
  ) async {
    try {
      // Get instructor profile
      final profile = await _getInstructorProfile(instructorId);

      // Get instructor statistics
      final stats = await _getInstructorStats(instructorId);

      // Get priority alerts
      final alerts = await _getPriorityAlerts(instructorId);

      // Get task overview
      final taskOverview = await _getTaskOverview(instructorId);

      // Get new students
      final newStudents = await _getNewStudents(instructorId);

      // Get weekly progress
      final weeklyProgress = await _getWeeklyProgress(instructorId);

      return InstructorDashboard(
        profile: profile,
        stats: stats,
        priorityAlerts: alerts,
        taskOverview: taskOverview,
        newStudents: newStudents,
        weeklyProgress: weeklyProgress,
      );
    } catch (e) {
      debugPrint('❌ Error getting instructor dashboard: $e');
      rethrow; // Don't return mock data, let the error bubble up
    }
  }

  /// Get instructor profile information
  Future<InstructorProfile> _getInstructorProfile(String instructorId) async {
    try {
      debugPrint('🔍 Fetching instructor profile for ID: $instructorId');

      // Get instructor data from profiles table (main source of truth)
      final profileResponse = await _supabase
          .from('profiles')
          .select('*')
          .eq('id', instructorId)
          .single();

      debugPrint('✅ Profile data: ${profileResponse.toString()}');

      // Try to get additional instructor data from instructors table (✅ CLEANED: Only existing columns)
      final instructorResponse = await _supabase
          .from('instructors')
          .select(
            'id, title, photo_url, experience_years, rating, is_public',
          )
          .eq('id', instructorId)
          .maybeSingle();

      debugPrint('📋 Instructor data: ${instructorResponse.toString()}');

      final name = profileResponse['name'] as String? ?? '';
      final surname = profileResponse['surname'] as String? ?? '';
      final fullName = '$name $surname'.trim();
      // Note: avatar_url moved to user_profiles table, using null for now
      final avatarUrl = null;

      debugPrint(
        '🔍 Homepage - Raw avatar_url from profiles: $avatarUrl (moved to user_profiles)',
      );
      debugPrint('👤 Homepage - Full name: $fullName');

      return InstructorProfile(
        id: instructorId,
        name: fullName.isNotEmpty ? fullName : 'Instructor',
        title: instructorResponse?['title'] as String? ?? 'Fitness Instructor',
        avatarUrl: avatarUrl,
        email: profileResponse['email'] as String,
        experienceYears: instructorResponse?['experience_years'] as int? ?? 0,
      );
    } catch (e) {
      debugPrint('❌ Error fetching instructor profile: $e');
      rethrow; // Don't return mock data, let the error bubble up
    }
  }

  /// Get instructor statistics
  Future<InstructorStats> _getInstructorStats(String instructorId) async {
    try {
      debugPrint('📊 Getting stats for instructor: $instructorId');

      // Get total students count from enrollments table
      final totalStudentsResponse = await _supabase
          .from('enrollments')
          .select('id')
          .eq('instructor_id', instructorId)
          .eq('is_active', true);

      final totalStudents = totalStudentsResponse.length;
      debugPrint('👥 Total students: $totalStudents');

      // Get new students today
      final today = DateTime.now();
      final startOfDay = DateTime(today.year, today.month, today.day);

      final newStudentsTodayResponse = await _supabase
          .from('enrollments')
          .select('id')
          .eq('instructor_id', instructorId)
          .gte('enrolled_at', startOfDay.toIso8601String());

      final newStudentsToday = newStudentsTodayResponse.length;
      debugPrint('🆕 New students today: $newStudentsToday');

      // Get instructor rating
      final ratingResponse = await _supabase
          .from('instructor_reviews')
          .select('rating')
          .eq('instructor_id', instructorId);

      double averageRating = 0.0;
      if (ratingResponse.isNotEmpty) {
        final ratings = ratingResponse.map((r) => r['rating'] as num).toList();
        averageRating = ratings.reduce((a, b) => a + b) / ratings.length;
      }

      debugPrint(
        '⭐ Average rating: $averageRating (${ratingResponse.length} reviews)',
      );

      // Calculate new students change (yesterday vs today)
      final yesterday = DateTime(today.year, today.month, today.day - 1);
      final startOfYesterday = yesterday;
      final endOfYesterday = DateTime(
        yesterday.year,
        yesterday.month,
        yesterday.day,
        23,
        59,
        59,
      );

      final newStudentsYesterdayResponse = await _supabase
          .from('enrollments')
          .select('id')
          .eq('instructor_id', instructorId)
          .gte('enrolled_at', startOfYesterday.toIso8601String())
          .lte('enrolled_at', endOfYesterday.toIso8601String());

      final newStudentsYesterday = newStudentsYesterdayResponse.length;
      final newStudentsChange = newStudentsToday - newStudentsYesterday;

      debugPrint(
        '📈 New students change: $newStudentsChange (today: $newStudentsToday, yesterday: $newStudentsYesterday)',
      );

      return InstructorStats(
        totalStudents: totalStudents,
        newStudentsToday: newStudentsToday,
        newStudentsChange: newStudentsChange,
        averageRating: averageRating,
        totalReviews: ratingResponse.length,
      );
    } catch (e) {
      debugPrint('❌ Error getting instructor stats: $e');
      // Return default stats instead of throwing
      return const InstructorStats(
        totalStudents: 0,
        newStudentsToday: 0,
        newStudentsChange: 0,
        averageRating: 0.0,
        totalReviews: 0,
      );
    }
  }

  /// Get priority alerts for instructor
  Future<List<PriorityAlert>> _getPriorityAlerts(String instructorId) async {
    try {
      debugPrint('🚨 Getting priority alerts for instructor: $instructorId');

      final alerts = <PriorityAlert>[];

      // Check for inactive students (no activity in last 7 days)
      final sevenDaysAgo = DateTime.now().subtract(const Duration(days: 7));

      // Get students who haven't been active recently
      final inactiveStudentsResponse = await _supabase
          .from('enrollments')
          .select('''
            student_id,
            enrolled_at,
            profiles!enrollments_student_id_fkey(
              name,
              surname
            )
          ''')
          .eq('instructor_id', instructorId)
          .eq('is_active', true)
          .lt('enrolled_at', sevenDaysAgo.toIso8601String());

      // Create alerts for inactive students
      for (final student in inactiveStudentsResponse) {
        final profiles = student['profiles'] as Map<String, dynamic>?;
        final name = profiles?['name'] as String? ?? '';
        final surname = profiles?['surname'] as String? ?? '';
        final fullName = '$name $surname'.trim();

        if (fullName.isNotEmpty) {
          alerts.add(
            PriorityAlert(
              id: 'inactive_${student['student_id']}',
              studentId: student['student_id'] as String,
              studentName: fullName,
              message: "hasn't been active for 7+ days",
              type: AlertType.inactivity,
              actionLabel: 'Contact',
              createdAt: DateTime.parse(student['enrolled_at'] as String),
            ),
          );
        }
      }

      debugPrint('🚨 Found ${alerts.length} priority alerts');
      return alerts;
    } catch (e) {
      debugPrint('❌ Error getting priority alerts: $e');
      return [];
    }
  }

  /// Get task overview for instructor
  Future<TaskOverview> _getTaskOverview(String instructorId) async {
    try {
      debugPrint('🔍 Getting task overview for instructor: $instructorId');

      // Get all students for this instructor
      final enrollments = await _supabase
          .from('enrollments')
          .select('student_id')
          .eq('instructor_id', instructorId)
          .eq('is_active', true);

      if (enrollments.isEmpty) {
        debugPrint('📊 No students found for instructor');
        return const TaskOverview(
          workouts: 0,
          cardio: 0,
          nutrition: 0,
          feedback: 0,
          questions: 0,
          supplements: 0,
        );
      }

      final studentIds =
          enrollments.map((e) => e['user_id'] as String).toList();
      debugPrint('📊 Checking tasks for ${studentIds.length} students');

      // Count pending workout plans (plans with empty plan_data)
      final workoutTasks = await _supabase
          .from('student_workout_plans')
          .select('id')
          .inFilter('student_id', studentIds)
          .eq('instructor_id', instructorId)
          .eq('is_active', true)
          .or('plan_data.is.null,plan_data.eq.{}');

      // Count pending nutrition plans
      final nutritionTasks = await _supabase
          .from('student_nutrition_plans')
          .select('id')
          .inFilter('student_id', studentIds)
          .eq('instructor_id', instructorId)
          .eq('is_active', true)
          .or('plan_data.is.null,plan_data.eq.{}');

      // Count pending cardio plans
      final cardioTasks = await _supabase
          .from('student_cardio_plans')
          .select('id')
          .inFilter('student_id', studentIds)
          .eq('instructor_id', instructorId)
          .eq('is_active', true)
          .or('plan_data.is.null,plan_data.eq.{}');

      // Count pending supplement plans (plans with empty supplements array)
      final supplementTasks = await _supabase
          .from('student_supplement_plans')
          .select('id')
          .inFilter('student_id', studentIds)
          .eq('instructor_id', instructorId)
          .eq('is_active', true)
          .or('supplements.is.null,supplements.eq.[]');

      // Feedback and questions are not automatically created, so they remain 0 for now
      final feedbackTasks = 0;
      final questionTasks = 0;

      debugPrint(
        '📊 Task counts - Workout: ${workoutTasks.length}, Nutrition: ${nutritionTasks.length}, Cardio: ${cardioTasks.length}, Supplement: ${supplementTasks.length}',
      );

      return TaskOverview(
        workouts: workoutTasks.length,
        cardio: cardioTasks.length,
        nutrition: nutritionTasks.length,
        feedback: feedbackTasks,
        questions: questionTasks,
        supplements: supplementTasks.length,
      );
    } catch (e) {
      debugPrint('❌ Error getting task overview: $e');
      // Return mock data on error
      return const TaskOverview(
        workouts: 0,
        cardio: 0,
        nutrition: 0,
        feedback: 0,
        questions: 0,
        supplements: 0,
      );
    }
  }

  /// Get new students for instructor
  Future<List<NewStudent>> _getNewStudents(String instructorId) async {
    try {
      debugPrint('👥 Getting new students for instructor: $instructorId');

      // Get new students from enrollments table (CLEAN ARCHITECTURE)
      var response = await _supabase
          .from('enrollments')
          .select('''
            student_id,
            enrolled_at,
            profiles!student_instructor_relationships_clean_student_id_fkey(
              id,
              name,
              surname,
              email
            )
          ''')
          .eq('instructor_id', instructorId)
          .eq('is_active', true)
          .order('enrolled_at', ascending: false)
          .limit(5);

      return response.map<NewStudent>((enrollment) {
        final profile = enrollment['profiles'];
        final firstName = profile['name'] as String? ?? '';
        final lastName = profile['surname'] as String? ?? '';
        final fullName = '$firstName $lastName'.trim();

        return NewStudent(
          id: profile['id'] as String,
          name: fullName.isNotEmpty
              ? fullName
              : (profile['email'] as String? ?? 'Unknown').split('@')[0],
          avatarUrl: null, // avatar_url moved to user_profiles table
          joinedAt: DateTime.parse(enrollment['enrolled_at'] as String),
          programType: 'Standard', // Would come from enrollment data
          hasCompletedProfile: true, // Would check profile completion
        );
      }).toList();
    } catch (e) {
      debugPrint('❌ Error getting new students: $e');
      // Fallback to student_profiles table
      try {
        final response = await _supabase
            .from('student_profiles')
            .select('''
              user_id,
              created_at
            ''')
            .eq('current_instructor_id', instructorId)
            .order('created_at', ascending: false)
            .limit(5);

        // Get profiles separately to avoid foreign key issues
        final userIds = response.map((sp) => sp['user_id'] as String).toList();
        final profilesResponse = await _supabase
            .from('profiles')
            .select('id, name, surname, email')
            .inFilter('id', userIds);

        return response.map<NewStudent>((studentProfile) {
          final userId = studentProfile['user_id'] as String;
          final profile = profilesResponse.firstWhere(
            (p) => p['id'] == userId,
            orElse: () => {
              'id': userId,
              'name': 'Unknown',
              'surname': '',
              'email': '<EMAIL>',
            },
          );

          final firstName = profile['name'] as String? ?? '';
          final lastName = profile['surname'] as String? ?? '';
          final fullName = '$firstName $lastName'.trim();

          return NewStudent(
            id: profile['id'] as String,
            name: fullName.isNotEmpty
                ? fullName
                : (profile['email'] as String? ?? 'Unknown').split('@')[0],
            avatarUrl: null, // avatar_url moved to user_profiles table
            joinedAt: DateTime.parse(studentProfile['created_at'] as String),
            programType: 'Standard',
            hasCompletedProfile: true,
          );
        }).toList();
      } catch (e2) {
        debugPrint('❌ Error getting students from student_profiles table: $e2');
        return [];
      }
    }
  }

  /// Get weekly progress summary
  Future<WeeklyProgress> _getWeeklyProgress(String instructorId) async {
    try {
      debugPrint('📅 Getting weekly progress for instructor: $instructorId');

      final now = DateTime.now();

      // Get total students for this instructor
      final totalStudentsResponse = await _supabase
          .from('enrollments')
          .select('student_id')
          .eq('instructor_id', instructorId)
          .eq('is_active', true);

      final totalStudents = totalStudentsResponse.length;

      // Calculate inactive students (no activity in last 7 days)
      final sevenDaysAgo = now.subtract(const Duration(days: 7));
      final inactiveStudentsResponse = await _supabase
          .from('enrollments')
          .select('student_id')
          .eq('instructor_id', instructorId)
          .eq('is_active', true)
          .lt('enrolled_at', sevenDaysAgo.toIso8601String());

      final inactiveStudents = inactiveStudentsResponse.length;

      // For now, use calculated values for other metrics
      // These would come from workout_sessions, nutrition_logs, etc. tables in a real implementation
      final totalCompletedTasks =
          totalStudents * 5; // Estimate: 5 tasks per student per week
      final missedWorkouts =
          (totalStudents * 0.1).round(); // Estimate: 10% miss rate
      final completionRate = totalStudents > 0
          ? (totalStudents - inactiveStudents) / totalStudents
          : 0.0;

      debugPrint(
        '📊 Weekly progress: completed=$totalCompletedTasks, missed=$missedWorkouts, inactive=$inactiveStudents, rate=${(completionRate * 100).toInt()}%',
      );

      return WeeklyProgress(
        totalCompletedTasks: totalCompletedTasks,
        missedWorkouts: missedWorkouts,
        inactiveStudents: inactiveStudents,
        completionRate: completionRate,
      );
    } catch (e) {
      debugPrint('❌ Error getting weekly progress: $e');
      return const WeeklyProgress(
        totalCompletedTasks: 0,
        missedWorkouts: 0,
        inactiveStudents: 0,
        completionRate: 0.0,
      );
    }
  }

  /// Handle priority alert action
  Future<void> handleAlertAction(String alertId, String action) async {
    // Implementation for handling alert actions
    // This would update the alert status and trigger appropriate actions
  }

  /// Create tasks for new student enrollment
  Future<void> createTasksForNewStudent({
    required String instructorId,
    required String studentId,
    required String programType,
  }) async {
    final tasks = [
      {
        'instructor_id': instructorId,
        'student_id': studentId,
        'task_type': 'workout',
        'title': 'Create Workout Plan',
        'description': 'Design initial workout plan for new student',
        'status': 'pending',
        'priority': 'high',
        'created_at': DateTime.now().toIso8601String(),
      },
      {
        'instructor_id': instructorId,
        'student_id': studentId,
        'task_type': 'cardio',
        'title': 'Create Cardio Plan',
        'description': 'Design cardio routine for new student',
        'status': 'pending',
        'priority': 'medium',
        'created_at': DateTime.now().toIso8601String(),
      },
      {
        'instructor_id': instructorId,
        'student_id': studentId,
        'task_type': 'nutrition',
        'title': 'Create Nutrition Plan',
        'description': 'Design nutrition plan for new student',
        'status': 'pending',
        'priority': 'high',
        'created_at': DateTime.now().toIso8601String(),
      },
    ];

    // Add supplements task for premium students
    if (programType.toLowerCase().contains('premium')) {
      tasks.add({
        'instructor_id': instructorId,
        'student_id': studentId,
        'task_type': 'supplement',
        'title': 'Create Supplements Plan',
        'description': 'Recommend supplements for new premium student',
        'status': 'pending',
        'priority': 'low',
        'created_at': DateTime.now().toIso8601String(),
      });
    }

    // Insert tasks into database
    await _supabase.from('instructor_tasks').insert(tasks);
  }
}
