import 'package:flutter/foundation.dart';
import 'package:hooks_riverpod/hooks_riverpod.dart';
import '../domain/market_models.dart';

/// Provider for market repository
final marketRepositoryProvider = Provider<MarketRepository>((ref) {
  return MarketRepository();
});

/// Repository for FitGo Market data
class MarketRepository {
  /// Get all market products
  Future<List<MarketProduct>> getProducts({
    ProductCategory? category,
    bool popularOnly = false,
    bool newOnly = false,
  }) async {
    try {
      // Simulate network delay
      await Future.delayed(const Duration(milliseconds: 500));
      
      var products = MockMarketData.getProducts();
      
      // Apply filters
      if (category != null) {
        products = products.where((p) => p.category == category).toList();
      }
      
      if (popularOnly) {
        products = products.where((p) => p.isPopular).toList();
      }
      
      if (newOnly) {
        products = products.where((p) => p.isNew).toList();
      }
      
      debugPrint('📦 Fetched ${products.length} market products');
      return products;
    } catch (e) {
      debugPrint('❌ Error fetching market products: $e');
      return [];
    }
  }

  /// Get featured/popular products for home display
  Future<List<MarketProduct>> getFeaturedProducts() async {
    try {
      final allProducts = await getProducts();
      
      // Return mix of popular and new products
      final featured = <MarketProduct>[];
      
      // Add popular products first
      featured.addAll(allProducts.where((p) => p.isPopular).take(3));
      
      // Add new products
      featured.addAll(allProducts.where((p) => p.isNew && !p.isPopular).take(2));
      
      // Fill remaining with highest rated
      if (featured.length < 5) {
        final remaining = allProducts
            .where((p) => !featured.contains(p))
            .toList()
          ..sort((a, b) => b.rating.compareTo(a.rating));
        
        featured.addAll(remaining.take(5 - featured.length));
      }
      
      return featured.take(5).toList();
    } catch (e) {
      debugPrint('❌ Error fetching featured products: $e');
      return [];
    }
  }

  /// Get product categories
  Future<List<ProductCategory>> getCategories() async {
    try {
      await Future.delayed(const Duration(milliseconds: 200));
      return MockMarketData.getCategories();
    } catch (e) {
      debugPrint('❌ Error fetching categories: $e');
      return [];
    }
  }

  /// Search products by name or description
  Future<List<MarketProduct>> searchProducts(String query) async {
    try {
      if (query.isEmpty) return [];
      
      final allProducts = await getProducts();
      final lowercaseQuery = query.toLowerCase();
      
      return allProducts.where((product) {
        return product.name.toLowerCase().contains(lowercaseQuery) ||
               product.description.toLowerCase().contains(lowercaseQuery) ||
               product.benefits.any((benefit) => 
                   benefit.toLowerCase().contains(lowercaseQuery));
      }).toList();
    } catch (e) {
      debugPrint('❌ Error searching products: $e');
      return [];
    }
  }

  /// Get product by ID
  Future<MarketProduct?> getProductById(String id) async {
    try {
      final allProducts = await getProducts();
      return allProducts.firstWhere((p) => p.id == id);
    } catch (e) {
      debugPrint('❌ Error fetching product by ID: $e');
      return null;
    }
  }
}
