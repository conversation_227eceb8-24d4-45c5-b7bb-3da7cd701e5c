import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:supabase_flutter/supabase_flutter.dart';
import '../data/student_workout_assignment_repository.dart';
import '../domain/student_workout_assignment.dart';

// Repository provider
final studentWorkoutAssignmentRepositoryProvider = Provider<StudentWorkoutAssignmentRepository>((ref) {
  return StudentWorkoutAssignmentRepository(Supabase.instance.client);
});

// Student workout assignment provider
final studentWorkoutAssignmentProvider = FutureProvider.family<StudentWorkoutAssignment?, ({String studentId, String instructorId})>((ref, params) async {
  final repository = ref.read(studentWorkoutAssignmentRepositoryProvider);
  return repository.getStudentWorkoutAssignment(
    studentId: params.studentId,
    instructorId: params.instructorId,
  );
});

// State notifier for managing workout assignments
final studentWorkoutAssignmentNotifierProvider = StateNotifierProvider<StudentWorkoutAssignmentNotifier, AsyncValue<StudentWorkoutAssignment?>>((ref) {
  final repository = ref.read(studentWorkoutAssignmentRepositoryProvider);
  return StudentWorkoutAssignmentNotifier(repository);
});

class StudentWorkoutAssignmentNotifier extends StateNotifier<AsyncValue<StudentWorkoutAssignment?>> {
  final StudentWorkoutAssignmentRepository _repository;

  StudentWorkoutAssignmentNotifier(this._repository) : super(const AsyncValue.loading());

  /// Load student's workout assignment
  Future<void> loadStudentWorkoutAssignment({
    required String studentId,
    required String instructorId,
  }) async {
    state = const AsyncValue.loading();
    try {
      final assignment = await _repository.getStudentWorkoutAssignment(
        studentId: studentId,
        instructorId: instructorId,
      );
      state = AsyncValue.data(assignment);
    } catch (error, stackTrace) {
      state = AsyncValue.error(error, stackTrace);
    }
  }

  /// Assign workout template to student
  Future<StudentWorkoutAssignment?> assignWorkoutTemplate({
    required String templateId,
    required String studentId,
    required String instructorId,
  }) async {
    try {
      print('🔄 Assigning workout template $templateId to student $studentId');
      
      final assignment = await _repository.assignWorkoutTemplateToStudent(
        templateId: templateId,
        studentId: studentId,
        instructorId: instructorId,
      );

      // Update state with new assignment
      state = AsyncValue.data(assignment);
      print('✅ Workout template assigned successfully');
      
      return assignment;
    } catch (e) {
      print('❌ Error assigning workout template: $e');
      state = AsyncValue.error(e, StackTrace.current);
      return null;
    }
  }

  /// Create custom workout plan for student
  Future<StudentWorkoutAssignment?> createCustomWorkoutPlan({
    required String studentId,
    required String instructorId,
    required String planName,
    String? description,
  }) async {
    try {
      print('🔄 Creating custom workout plan for student $studentId');
      
      final assignment = await _repository.createCustomWorkoutPlan(
        studentId: studentId,
        instructorId: instructorId,
        planName: planName,
        description: description,
      );

      // Update state with new assignment
      state = AsyncValue.data(assignment);
      print('✅ Custom workout plan created successfully');
      
      return assignment;
    } catch (e) {
      print('❌ Error creating custom workout plan: $e');
      state = AsyncValue.error(e, StackTrace.current);
      return null;
    }
  }

  /// Update workout assignment
  Future<StudentWorkoutAssignment?> updateWorkoutAssignment({
    required String assignmentId,
    String? planName,
    String? description,
    Map<String, dynamic>? planData,
  }) async {
    try {
      print('🔄 Updating workout assignment $assignmentId');
      
      final assignment = await _repository.updateWorkoutAssignment(
        assignmentId: assignmentId,
        planName: planName,
        description: description,
        planData: planData,
      );

      // Update state with updated assignment
      state = AsyncValue.data(assignment);
      print('✅ Workout assignment updated successfully');
      
      return assignment;
    } catch (e) {
      print('❌ Error updating workout assignment: $e');
      state = AsyncValue.error(e, StackTrace.current);
      return null;
    }
  }

  /// Clear current assignment state
  void clearAssignment() {
    state = const AsyncValue.data(null);
  }
}
