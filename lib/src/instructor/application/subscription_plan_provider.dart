import 'package:hooks_riverpod/hooks_riverpod.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_hooks/flutter_hooks.dart';
import 'package:fitgo_app/src/shared/constants/app_text_style.dart';
import '../domain/subscription_plan_models.dart';
import 'subscription_plan_repository.dart';
import 'instructor_provider.dart';
import 'instructor_profile_repository.dart';

// Custom exception for profile incomplete
class ProfileIncompleteException implements Exception {
  final String message;
  ProfileIncompleteException(this.message);
}

/// Provider for subscription plan configuration
final subscriptionPlanConfigProvider =
    FutureProvider<InstructorSubscriptionConfig>((ref) async {
  final instructorId = ref.watch(currentInstructorIdProvider);
  if (instructorId == null) {
    throw Exception('No instructor ID available');
  }

  final repository = ref.read(subscriptionPlanRepositoryProvider);
  return repository.getInstructorSubscriptionConfig(instructorId);
});

/// Provider for subscription plan state management
final subscriptionPlanNotifierProvider =
    StateNotifierProvider<SubscriptionPlanNotifier, SubscriptionPlanState>((
  ref,
) {
  return SubscriptionPlanNotifier(ref);
});

/// State class for subscription plan management
class SubscriptionPlanState {
  final InstructorSubscriptionConfig? config;
  final bool isLoading;
  final String? error;
  final PlanDuration selectedDuration;
  final bool isSubmitting;
  final ValidationResult? validationResult;
  final bool showValidation;
  final Map<String, dynamic>? courseStatus;
  final bool isCourseStatusLoading;

  const SubscriptionPlanState({
    this.config,
    this.isLoading = false,
    this.error,
    this.selectedDuration = PlanDuration.monthly,
    this.isSubmitting = false,
    this.validationResult,
    this.showValidation = false,
    this.courseStatus,
    this.isCourseStatusLoading = false,
  });

  SubscriptionPlanState copyWith({
    InstructorSubscriptionConfig? config,
    bool? isLoading,
    String? error,
    PlanDuration? selectedDuration,
    bool? isSubmitting,
    ValidationResult? validationResult,
    bool? showValidation,
    Map<String, dynamic>? courseStatus,
    bool? isCourseStatusLoading,
  }) {
    return SubscriptionPlanState(
      config: config ?? this.config,
      isLoading: isLoading ?? this.isLoading,
      error: error,
      selectedDuration: selectedDuration ?? this.selectedDuration,
      isSubmitting: isSubmitting ?? this.isSubmitting,
      validationResult: validationResult ?? this.validationResult,
      showValidation: showValidation ?? this.showValidation,
      courseStatus: courseStatus ?? this.courseStatus,
      isCourseStatusLoading:
          isCourseStatusLoading ?? this.isCourseStatusLoading,
    );
  }
}

/// State notifier for subscription plan management
class SubscriptionPlanNotifier extends StateNotifier<SubscriptionPlanState> {
  final Ref _ref;

  SubscriptionPlanNotifier(this._ref) : super(const SubscriptionPlanState());

  /// Initialize subscription plan configuration
  Future<void> initializeConfig() async {
    if (state.isLoading) return;

    state = state.copyWith(isLoading: true, error: null);

    try {
      final instructorId = _ref.read(currentInstructorIdProvider);
      if (instructorId == null) {
        throw Exception('No instructor ID available');
      }

      final repository = _ref.read(subscriptionPlanRepositoryProvider);
      final config = await repository.getInstructorSubscriptionConfig(
        instructorId,
      );

      state = state.copyWith(config: config, isLoading: false);
    } catch (e) {
      state = state.copyWith(isLoading: false, error: e.toString());
    }
  }

  /// Update base monthly price
  void updateBaseMonthlyPrice(double price) {
    if (state.config == null) return;

    final repository = _ref.read(subscriptionPlanRepositoryProvider);

    // Recalculate all plan pricing based on new base price
    final basicPricing = <PlanDuration, PlanPricing>{};
    final premiumPricing = <PlanDuration, PlanPricing>{};

    for (final duration in PlanDuration.values) {
      basicPricing[duration] = repository.calculatePlanPricing(
        basePrice: price,
        duration: duration,
        planType: PlanType.basic,
      );

      premiumPricing[duration] = repository.calculatePlanPricing(
        basePrice: price,
        duration: duration,
        planType: PlanType.premium,
      );
    }

    final updatedConfig = state.config!.copyWith(
      baseMonthlyPrice: price,
      basicPlanPricing: basicPricing,
      premiumPlanPricing: premiumPricing,
    );

    state = state.copyWith(config: updatedConfig);
    _validateConfiguration();
  }

  /// Update premium plan custom pricing
  void updatePremiumPlanPricing(PlanDuration duration, double monthlyPrice) {
    if (state.config == null) return;

    final totalPrice = monthlyPrice * duration.months;
    final updatedPricing = PlanPricing(
      monthlyPrice: monthlyPrice,
      totalPrice: totalPrice,
      duration: duration,
      features: PlanFeatures.getFeaturesForPlan(PlanType.premium),
      isCustomPrice: true,
    );

    final updatedPremiumPricing = Map<PlanDuration, PlanPricing>.from(
      state.config!.premiumPlanPricing,
    );
    updatedPremiumPricing[duration] = updatedPricing;

    final updatedConfig = state.config!.copyWith(
      premiumPlanPricing: updatedPremiumPricing,
    );

    state = state.copyWith(config: updatedConfig);
    _validateConfiguration();
  }

  /// Change selected duration tab
  void selectDuration(PlanDuration duration) {
    state = state.copyWith(selectedDuration: duration);
  }

  /// Add discount code
  Future<void> addDiscountCode(DiscountCode discountCode) async {
    if (state.config == null) return;

    try {
      final instructorId = _ref.read(currentInstructorIdProvider);
      if (instructorId == null) return;

      final repository = _ref.read(subscriptionPlanRepositoryProvider);
      final createdCode = await repository.createDiscountCode(
        instructorId,
        discountCode,
      );

      final updatedCodes = [...state.config!.discountCodes, createdCode];
      final updatedConfig = state.config!.copyWith(discountCodes: updatedCodes);

      state = state.copyWith(config: updatedConfig);
      _validateConfiguration();
    } catch (e) {
      debugPrint('❌ Error adding discount code: $e');

      // User-friendly error message
      String errorMessage = 'Failed to create discount code. Please try again.';
      if (e.toString().contains('duplicate key')) {
        errorMessage =
            'Configuration already exists. Please refresh and try again.';
      } else if (e.toString().contains('unique constraint')) {
        errorMessage = 'Duplicate entry detected. Please refresh the page.';
      }

      state = state.copyWith(error: errorMessage);
    }
  }

  /// Remove discount code
  Future<void> removeDiscountCode(String discountCodeId) async {
    if (state.config == null) return;

    try {
      final repository = _ref.read(subscriptionPlanRepositoryProvider);
      await repository.deleteDiscountCode(discountCodeId);

      final updatedCodes = state.config!.discountCodes
          .where((code) => code.id != discountCodeId)
          .toList();
      final updatedConfig = state.config!.copyWith(discountCodes: updatedCodes);

      state = state.copyWith(config: updatedConfig);
      _validateConfiguration();
    } catch (e) {
      state = state.copyWith(error: e.toString());
    }
  }

  /// Save configuration
  Future<void> saveConfiguration() async {
    if (state.config == null) return;

    try {
      final instructorId = _ref.read(currentInstructorIdProvider);
      if (instructorId == null) return;

      state = state.copyWith(isLoading: true);

      final repository = _ref.read(subscriptionPlanRepositoryProvider);
      await repository.saveInstructorSubscriptionConfig(
        instructorId,
        state.config!,
      );

      state = state.copyWith(isLoading: false);
    } catch (e) {
      state = state.copyWith(isLoading: false, error: e.toString());
    }
  }

  /// Submit for review
  Future<bool> submitForReview() async {
    if (state.config == null) return false;

    // Validate before submission
    _validateConfiguration();
    if (state.validationResult?.isValid != true) {
      state = state.copyWith(showValidation: true);
      return false;
    }

    try {
      final instructorId = _ref.read(currentInstructorIdProvider);
      if (instructorId == null) return false;

      state = state.copyWith(isSubmitting: true);

      final repository = _ref.read(subscriptionPlanRepositoryProvider);
      await repository.submitForReview(instructorId, state.config!);

      // Refresh config to get updated status
      await initializeConfig();

      state = state.copyWith(isSubmitting: false, error: null);
      return true;
    } catch (e) {
      // Handle profile incomplete exception with user-friendly message
      String errorMessage;
      if (e is ProfileIncompleteException) {
        errorMessage = e.message;
      } else {
        errorMessage =
            'Gönderim sırasında bir hata oluştu. Lütfen tekrar deneyin.';
        debugPrint(
          '❌ Submit for review error: $e',
        ); // Log technical details for developers
      }

      state = state.copyWith(
        isSubmitting: false,
        error: errorMessage,
      );
      return false;
    }
  }

  /// Show discount code creation dialog
  void showCreateDiscountCodeDialog(BuildContext context) {
    showDialog(
      context: context,
      builder: (context) => _CreateDiscountCodeDialog(
        onCreateCode: (code) => addDiscountCode(code),
      ),
    );
  }

  /// Private validation method
  void _validateConfiguration() {
    if (state.config == null) return;

    final repository = _ref.read(subscriptionPlanRepositoryProvider);
    final validationResult = repository.validateConfiguration(state.config!);

    state = state.copyWith(validationResult: validationResult);
  }

  /// Clear error state
  void clearError() {
    state = state.copyWith(error: null);
  }

  /// Hide validation messages
  void hideValidation() {
    state = state.copyWith(showValidation: false);
  }

  /// Load course status information
  Future<void> loadCourseStatus() async {
    if (state.isCourseStatusLoading) return;

    state = state.copyWith(isCourseStatusLoading: true);

    try {
      final instructorId = _ref.read(currentInstructorIdProvider);
      if (instructorId == null) {
        throw Exception('No instructor ID available');
      }

      final repository = _ref.read(instructorProfileRepositoryProvider);
      final courseStatus = await repository.getCourseStatus(instructorId);

      state = state.copyWith(
        courseStatus: courseStatus,
        isCourseStatusLoading: false,
      );
    } catch (e) {
      state = state.copyWith(
        isCourseStatusLoading: false,
        error: 'Failed to load course status: $e',
      );
    }
  }

  /// Hide instructor course
  Future<bool> hideCourse({String? reason}) async {
    try {
      final instructorId = _ref.read(currentInstructorIdProvider);
      if (instructorId == null) {
        throw Exception('No instructor ID available');
      }

      state = state.copyWith(isSubmitting: true);

      final repository = _ref.read(instructorProfileRepositoryProvider);
      final result = await repository.hideCourse(
        instructorId: instructorId,
        reason: reason,
      );

      if (result['success'] == true) {
        // Refresh course status
        await loadCourseStatus();
        state = state.copyWith(isSubmitting: false);
        return true;
      } else {
        state = state.copyWith(
          isSubmitting: false,
          error: result['message'] as String? ?? 'Failed to hide course',
        );
        return false;
      }
    } catch (e) {
      state = state.copyWith(
        isSubmitting: false,
        error: 'Failed to hide course: $e',
      );
      return false;
    }
  }

  /// Reactivate instructor course
  Future<bool> reactivateCourse() async {
    try {
      final instructorId = _ref.read(currentInstructorIdProvider);
      if (instructorId == null) {
        throw Exception('No instructor ID available');
      }

      state = state.copyWith(isSubmitting: true);

      final repository = _ref.read(instructorProfileRepositoryProvider);
      final result = await repository.reactivateCourse(
        instructorId: instructorId,
      );

      if (result['success'] == true) {
        // Refresh course status and config
        await loadCourseStatus();
        await initializeConfig();
        state = state.copyWith(isSubmitting: false);
        return true;
      } else {
        state = state.copyWith(
          isSubmitting: false,
          error: result['message'] as String? ?? 'Failed to reactivate course',
        );
        return false;
      }
    } catch (e) {
      state = state.copyWith(
        isSubmitting: false,
        error: 'Failed to reactivate course: $e',
      );
      return false;
    }
  }
}

/// Provider for current plan pricing based on selected duration
final currentPlanPricingProvider = Provider<Map<PlanType, PlanPricing?>>((ref) {
  final planState = ref.watch(subscriptionPlanNotifierProvider);
  final selectedDuration = planState.selectedDuration;
  final config = planState.config;

  if (config == null) return {PlanType.basic: null, PlanType.premium: null};

  return {
    PlanType.basic: config.basicPlanPricing[selectedDuration],
    PlanType.premium: config.premiumPlanPricing[selectedDuration],
  };
});

/// Provider for submission button state
final submissionButtonStateProvider = Provider<SubmissionButtonState>((ref) {
  final planState = ref.watch(subscriptionPlanNotifierProvider);
  final config = planState.config;

  if (config == null) {
    return const SubmissionButtonState(
      isEnabled: false,
      text: 'Loading...',
      isLoading: true,
    );
  }

  final canSubmit = config.submissionStatus.canSubmit;
  final isValid = planState.validationResult?.isValid ?? false;

  return SubmissionButtonState(
    isEnabled: canSubmit && isValid && !planState.isSubmitting,
    text: _getSubmissionButtonText(
      config.submissionStatus,
      planState.isSubmitting,
    ),
    isLoading: planState.isSubmitting,
    status: config.submissionStatus,
  );
});

String _getSubmissionButtonText(SubmissionStatus status, bool isSubmitting) {
  if (isSubmitting) return 'Submitting...';

  switch (status) {
    case SubmissionStatus.draft:
      return 'Submit for Review';
    case SubmissionStatus.rejected:
    case SubmissionStatus.needsRevision:
      return 'Resubmit for Review';
    case SubmissionStatus.submitted:
      return 'Submitted';
    case SubmissionStatus.underReview:
      return 'Under Review';
    case SubmissionStatus.approved:
      return 'Approved';
  }
}

/// Submission button state model
class SubmissionButtonState {
  final bool isEnabled;
  final String text;
  final bool isLoading;
  final SubmissionStatus? status;

  const SubmissionButtonState({
    required this.isEnabled,
    required this.text,
    this.isLoading = false,
    this.status,
  });
}

/// Create discount code dialog widget
class _CreateDiscountCodeDialog extends HookWidget {
  final Function(DiscountCode) onCreateCode;

  const _CreateDiscountCodeDialog({required this.onCreateCode});

  @override
  Widget build(BuildContext context) {
    final codeController = useTextEditingController();
    final discountController = useTextEditingController();
    final usageLimitController = useTextEditingController(text: '100');
    final expiryDate = useState(DateTime.now().add(const Duration(days: 30)));
    final selectedPlans = useState<Set<PlanType>>({
      PlanType.basic,
      PlanType.premium,
    });
    final formKey = useMemoized(() => GlobalKey<FormState>());
    final isLoading = useState(false);

    return Dialog(
      backgroundColor: const Color(0xFF1F2937),
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
      insetPadding: const EdgeInsets.symmetric(horizontal: 16, vertical: 24),
      child: Container(
        constraints: BoxConstraints(
          maxHeight: MediaQuery.of(context).size.height * 0.85,
          maxWidth: MediaQuery.of(context).size.width - 32,
        ),
        decoration: BoxDecoration(
          color: const Color(0xFF1F2937),
          borderRadius: BorderRadius.circular(12),
        ),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            // Title
            Container(
              padding: const EdgeInsets.fromLTRB(24, 24, 24, 16),
              child: Text(
                'Create Discount Code',
                style: ATextStyle.title.copyWith(
                  color: Colors.white,
                  fontSize: 20,
                ),
              ),
            ),
            // Content
            Flexible(
              child: SingleChildScrollView(
                padding: const EdgeInsets.symmetric(horizontal: 24),
                child: Form(
                  key: formKey,
                  child: Column(
                    mainAxisSize: MainAxisSize.min,
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      // Code Input
                      Text(
                        'Discount Code',
                        style: ATextStyle.medium.copyWith(color: Colors.white),
                      ),
                      const SizedBox(height: 8),
                      TextFormField(
                        controller: codeController,
                        style: const TextStyle(color: Colors.white),
                        textCapitalization: TextCapitalization.characters,
                        inputFormatters: [
                          FilteringTextInputFormatter.allow(
                            RegExp(r'[A-Z0-9]'),
                          ),
                          LengthLimitingTextInputFormatter(20),
                        ],
                        decoration: _buildInputDecoration(
                          hintText: 'e.g., SUMMER2024',
                        ),
                        validator: (value) {
                          if (value == null || value.trim().isEmpty) {
                            return 'Discount code is required';
                          }
                          if (value.trim().length < 3) {
                            return 'Code must be at least 3 characters';
                          }
                          return null;
                        },
                      ),
                      const SizedBox(height: 16),

                      // Discount Percentage
                      Text(
                        'Discount Percentage',
                        style: ATextStyle.medium.copyWith(color: Colors.white),
                      ),
                      const SizedBox(height: 8),
                      TextFormField(
                        controller: discountController,
                        style: const TextStyle(color: Colors.white),
                        keyboardType: TextInputType.number,
                        inputFormatters: [
                          FilteringTextInputFormatter.digitsOnly,
                          LengthLimitingTextInputFormatter(2),
                        ],
                        decoration: _buildInputDecoration(
                          hintText: '20',
                          suffixText: '%',
                        ),
                        validator: (value) {
                          if (value == null || value.isEmpty) {
                            return 'Discount percentage is required';
                          }
                          final discount = int.tryParse(value);
                          if (discount == null ||
                              discount <= 0 ||
                              discount > 50) {
                            return 'Enter a value between 1-50';
                          }
                          return null;
                        },
                      ),
                      const SizedBox(height: 16),

                      // Usage Limit
                      Text(
                        'Usage Limit',
                        style: ATextStyle.medium.copyWith(color: Colors.white),
                      ),
                      const SizedBox(height: 8),
                      TextFormField(
                        controller: usageLimitController,
                        style: const TextStyle(color: Colors.white),
                        keyboardType: TextInputType.number,
                        inputFormatters: [
                          FilteringTextInputFormatter.digitsOnly,
                          LengthLimitingTextInputFormatter(4),
                        ],
                        decoration: _buildInputDecoration(
                          hintText: '100',
                          suffixText: 'uses',
                        ),
                        validator: (value) {
                          if (value == null || value.isEmpty) {
                            return 'Usage limit is required';
                          }
                          final limit = int.tryParse(value);
                          if (limit == null || limit <= 0) {
                            return 'Enter a valid usage limit';
                          }
                          return null;
                        },
                      ),
                      const SizedBox(height: 16),

                      // Expiry Date
                      Text(
                        'Expiry Date',
                        style: ATextStyle.medium.copyWith(color: Colors.white),
                      ),
                      const SizedBox(height: 8),
                      Container(
                        width: double.infinity,
                        padding: const EdgeInsets.all(16),
                        decoration: BoxDecoration(
                          color: const Color(0xFF374151),
                          borderRadius: BorderRadius.circular(8),
                          border: Border.all(color: const Color(0xFF374151)),
                        ),
                        child: Row(
                          children: [
                            const Icon(
                              Icons.calendar_today,
                              color: Color(0xFFFACC15),
                              size: 20,
                            ),
                            const SizedBox(width: 12),
                            Expanded(
                              child: Text(
                                '${expiryDate.value.day}/${expiryDate.value.month}/${expiryDate.value.year}',
                                style: const TextStyle(
                                  color: Colors.white,
                                  fontSize: 16,
                                ),
                              ),
                            ),
                            TextButton(
                              onPressed: () async {
                                final date = await showDatePicker(
                                  context: context,
                                  initialDate: expiryDate.value,
                                  firstDate: DateTime.now(),
                                  lastDate: DateTime.now().add(
                                    const Duration(days: 365),
                                  ),
                                  builder: (context, child) {
                                    return Theme(
                                      data: Theme.of(context).copyWith(
                                        colorScheme: const ColorScheme.dark(
                                          primary: Color(0xFFFACC15),
                                          surface: Color(0xFF1F2937),
                                        ),
                                      ),
                                      child: child!,
                                    );
                                  },
                                );
                                if (date != null) {
                                  expiryDate.value = date;
                                }
                              },
                              child: const Text(
                                'Change',
                                style: TextStyle(color: Color(0xFFFACC15)),
                              ),
                            ),
                          ],
                        ),
                      ),
                      const SizedBox(height: 16),

                      // Applicable Plans
                      Text(
                        'Applicable Plans',
                        style: ATextStyle.medium.copyWith(color: Colors.white),
                      ),
                      const SizedBox(height: 8),
                      ...PlanType.values.map((plan) {
                        return CheckboxListTile(
                          value: selectedPlans.value.contains(plan),
                          onChanged: (bool? value) {
                            final newSet = Set<PlanType>.from(
                              selectedPlans.value,
                            );
                            if (value == true) {
                              newSet.add(plan);
                            } else {
                              newSet.remove(plan);
                            }
                            selectedPlans.value = newSet;
                          },
                          title: Text(
                            plan.displayName,
                            style: const TextStyle(color: Colors.white),
                          ),
                          activeColor: const Color(0xFFFACC15),
                          checkColor: Colors.black,
                          contentPadding: EdgeInsets.zero,
                        );
                      }),
                    ],
                  ),
                ),
              ),
            ),
            // Actions
            Container(
              padding: const EdgeInsets.fromLTRB(24, 0, 24, 24),
              child: Row(
                mainAxisAlignment: MainAxisAlignment.end,
                children: [
                  TextButton(
                    onPressed: isLoading.value
                        ? null
                        : () => Navigator.of(context).pop(),
                    child: Text(
                      'Cancel',
                      style: ATextStyle.medium.copyWith(color: Colors.grey),
                    ),
                  ),
                  const SizedBox(width: 12),
                  ElevatedButton(
                    onPressed: isLoading.value
                        ? null
                        : () async {
                            if (!formKey.currentState!.validate()) {
                              return;
                            }

                            if (selectedPlans.value.isEmpty) {
                              ScaffoldMessenger.of(context).showSnackBar(
                                const SnackBar(
                                  content: Text(
                                    'Please select at least one plan',
                                  ),
                                  backgroundColor: Colors.red,
                                ),
                              );
                              return;
                            }

                            isLoading.value = true;

                            try {
                              final discountCode = DiscountCode(
                                id: DateTime.now()
                                    .millisecondsSinceEpoch
                                    .toString(),
                                code: codeController.text.trim().toUpperCase(),
                                discountPercentage: double.parse(
                                  discountController.text,
                                ),
                                expiryDate: expiryDate.value,
                                usageLimit: int.parse(
                                  usageLimitController.text,
                                ),
                                applicablePlans: selectedPlans.value.toList(),
                              );

                              onCreateCode(discountCode);
                              Navigator.of(context).pop();
                            } catch (e) {
                              ScaffoldMessenger.of(context).showSnackBar(
                                SnackBar(
                                  content: Text(
                                    'Error creating discount code: $e',
                                  ),
                                  backgroundColor: Colors.red,
                                ),
                              );
                            } finally {
                              isLoading.value = false;
                            }
                          },
                    style: ElevatedButton.styleFrom(
                      backgroundColor: const Color(0xFFFACC15),
                      foregroundColor: Colors.black,
                      shape: RoundedRectangleBorder(
                        borderRadius: BorderRadius.circular(8),
                      ),
                    ),
                    child: isLoading.value
                        ? const SizedBox(
                            width: 20,
                            height: 20,
                            child: CircularProgressIndicator(
                              strokeWidth: 2,
                              valueColor: AlwaysStoppedAnimation<Color>(
                                Colors.black,
                              ),
                            ),
                          )
                        : Text(
                            'Create Code',
                            style: ATextStyle.medium.copyWith(
                              color: Colors.black,
                              fontWeight: FontWeight.w600,
                            ),
                          ),
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }

  InputDecoration _buildInputDecoration({
    required String hintText,
    String? suffixText,
  }) {
    return InputDecoration(
      hintText: hintText,
      hintStyle: const TextStyle(color: Colors.grey),
      suffixText: suffixText,
      suffixStyle: TextStyle(
        color: suffixText == '%' ? const Color(0xFFFACC15) : Colors.grey,
      ),
      filled: true,
      fillColor: const Color(0xFF374151),
      border: OutlineInputBorder(
        borderRadius: BorderRadius.circular(8),
        borderSide: const BorderSide(color: Color(0xFF374151)),
      ),
      enabledBorder: OutlineInputBorder(
        borderRadius: BorderRadius.circular(8),
        borderSide: const BorderSide(color: Color(0xFF374151)),
      ),
      focusedBorder: OutlineInputBorder(
        borderRadius: BorderRadius.circular(8),
        borderSide: const BorderSide(color: Color(0xFFFACC15)),
      ),
      errorBorder: OutlineInputBorder(
        borderRadius: BorderRadius.circular(8),
        borderSide: const BorderSide(color: Colors.red),
      ),
      focusedErrorBorder: OutlineInputBorder(
        borderRadius: BorderRadius.circular(8),
        borderSide: const BorderSide(color: Colors.red),
      ),
      contentPadding: const EdgeInsets.all(16),
    );
  }
}
