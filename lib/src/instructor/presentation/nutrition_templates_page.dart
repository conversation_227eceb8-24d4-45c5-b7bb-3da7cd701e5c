import 'package:flutter/material.dart';
import 'package:flutter_hooks/flutter_hooks.dart';
import 'package:hooks_riverpod/hooks_riverpod.dart';
import '../application/nutrition_template_provider.dart';
import '../domain/nutrition_template.dart';
import 'edit_nutrition_template_page.dart';

class NutritionTemplatesPage extends HookConsumerWidget {
  const NutritionTemplatesPage({super.key});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final searchController = useTextEditingController();
    final currentPage = useState<int>(1);
    final isLoadingMore = useState<bool>(false);
    final hasMoreData = useState<bool>(true);
    final nutritionTemplatesAsync = ref.watch(
      nutritionTemplateNotifierProvider,
    );
    final searchQuery = useState<String>('');

    return Scaffold(
      backgroundColor: const Color(0xFF111827),
      appBar: AppBar(
        backgroundColor: const Color(0xFF111827),
        elevation: 0,
        leading: IconButton(
          icon: const Icon(Icons.arrow_back, color: Colors.white),
          onPressed: () => Navigator.pop(context),
        ),
        title: const Text(
          'Nutrition Templates',
          style: TextStyle(
            color: Colors.white,
            fontSize: 20,
            fontWeight: FontWeight.w600,
          ),
        ),
        actions: [
          Container(
            margin: const EdgeInsets.only(right: 16),
            child: ElevatedButton.icon(
              onPressed: () => _navigateToCreateTemplate(context),
              icon: const Icon(Icons.add, color: Color(0xFF111827), size: 18),
              label: const Text(
                'New Template',
                style: TextStyle(
                  color: Color(0xFF111827),
                  fontWeight: FontWeight.w600,
                  fontSize: 14,
                ),
              ),
              style: ElevatedButton.styleFrom(
                backgroundColor: const Color(0xFFFACC15),
                elevation: 0,
                padding: const EdgeInsets.symmetric(
                  horizontal: 16,
                  vertical: 8,
                ),
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(8),
                ),
              ),
            ),
          ),
        ],
      ),
      body: Column(
        children: [
          // Search Bar
          _buildSearchBar(searchController, searchQuery),

          // Templates List
          Expanded(
            child: nutritionTemplatesAsync.when(
              data:
                  (templates) => _buildTemplatesList(
                    context,
                    ref,
                    templates,
                    searchQuery.value,
                    currentPage,
                    isLoadingMore,
                    hasMoreData,
                  ),
              loading: () => _buildLoadingState(),
              error:
                  (error, stack) =>
                      _buildErrorState(context, ref, error.toString()),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildTemplatesList(
    BuildContext context,
    WidgetRef ref,
    List<NutritionTemplate> templates,
    String searchQuery,
    ValueNotifier<int> currentPage,
    ValueNotifier<bool> isLoadingMore,
    ValueNotifier<bool> hasMoreData,
  ) {
    // Filter templates based on search query
    final filteredTemplates =
        templates.where((template) {
          if (searchQuery.isEmpty) return true;
          return template.name.toLowerCase().contains(
                searchQuery.toLowerCase(),
              ) ||
              (template.description?.toLowerCase().contains(
                    searchQuery.toLowerCase(),
                  ) ??
                  false);
        }).toList();

    if (filteredTemplates.isEmpty) {
      return _buildEmptyState(context, searchQuery.isNotEmpty);
    }

    return NotificationListener<ScrollNotification>(
      onNotification: (ScrollNotification scrollInfo) {
        if (!isLoadingMore.value &&
            hasMoreData.value &&
            scrollInfo.metrics.pixels == scrollInfo.metrics.maxScrollExtent) {
          _loadMoreTemplates(ref, currentPage, isLoadingMore, hasMoreData);
        }
        return false;
      },
      child: ListView.builder(
        padding: const EdgeInsets.all(16),
        itemCount: filteredTemplates.length + (isLoadingMore.value ? 1 : 0),
        itemBuilder: (context, index) {
          if (index == filteredTemplates.length) {
            return _buildLoadingIndicator();
          }
          final template = filteredTemplates[index];
          return _buildTemplateCard(context, template);
        },
      ),
    );
  }

  Widget _buildErrorState(BuildContext context, WidgetRef ref, String error) {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(Icons.error_outline, color: Colors.red[400], size: 48),
          const SizedBox(height: 16),
          Text(
            'Error loading templates',
            style: TextStyle(
              color: Colors.red[400],
              fontSize: 18,
              fontWeight: FontWeight.w600,
            ),
          ),
          const SizedBox(height: 8),
          Text(
            error,
            style: TextStyle(color: Colors.grey[400], fontSize: 14),
            textAlign: TextAlign.center,
          ),
          const SizedBox(height: 16),
          ElevatedButton(
            onPressed:
                () =>
                    ref
                        .read(nutritionTemplateNotifierProvider.notifier)
                        .loadTemplates(),
            child: const Text('Retry'),
          ),
        ],
      ),
    );
  }

  Widget _buildEmptyState(BuildContext context, bool isSearching) {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(
            isSearching ? Icons.search_off : Icons.restaurant_menu,
            color: Colors.grey[600],
            size: 64,
          ),
          const SizedBox(height: 16),
          Text(
            isSearching ? 'No templates found' : 'No nutrition templates yet',
            style: TextStyle(
              color: Colors.grey[400],
              fontSize: 18,
              fontWeight: FontWeight.w600,
            ),
          ),
          const SizedBox(height: 8),
          Text(
            isSearching
                ? 'Try adjusting your search terms'
                : 'Create your first nutrition template to get started',
            style: TextStyle(color: Colors.grey[500], fontSize: 14),
            textAlign: TextAlign.center,
          ),
          if (!isSearching) ...[
            const SizedBox(height: 24),
            ElevatedButton.icon(
              onPressed: () => _navigateToCreateTemplate(context),
              icon: const Icon(Icons.add),
              label: const Text('Create Template'),
              style: ElevatedButton.styleFrom(
                backgroundColor: const Color(0xFFFACC15),
                foregroundColor: const Color(0xFF111827),
              ),
            ),
          ],
        ],
      ),
    );
  }

  Widget _buildTemplateCard(BuildContext context, NutritionTemplate template) {
    final daysSinceUpdate =
        DateTime.now().difference(template.updatedAt).inDays;
    final lastModified =
        daysSinceUpdate == 0
            ? 'Updated today'
            : daysSinceUpdate == 1
            ? 'Updated 1 day ago'
            : 'Updated $daysSinceUpdate days ago';

    return GestureDetector(
      onTap: () => _navigateToEditTemplate(context, template),
      child: Container(
        margin: const EdgeInsets.only(bottom: 16),
        padding: const EdgeInsets.all(20),
        decoration: BoxDecoration(
          color: const Color(0xFF1F2937),
          borderRadius: BorderRadius.circular(16),
          border: Border.all(color: const Color(0xFF374151)),
          boxShadow: [
            BoxShadow(
              color: Colors.black.withValues(alpha: 0.1),
              blurRadius: 8,
              offset: const Offset(0, 2),
            ),
          ],
        ),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Header with title and last modified
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        template.name,
                        style: const TextStyle(
                          color: Colors.white,
                          fontSize: 18,
                          fontWeight: FontWeight.w600,
                        ),
                      ),
                      const SizedBox(height: 4),
                      Text(
                        lastModified,
                        style: TextStyle(color: Colors.grey[500], fontSize: 12),
                      ),
                    ],
                  ),
                ),
                Icon(
                  Icons.arrow_forward_ios,
                  color: Colors.grey[600],
                  size: 16,
                ),
              ],
            ),

            // Description or goal
            if (template.description != null &&
                template.description!.isNotEmpty) ...[
              const SizedBox(height: 12),
              Text(
                template.description!,
                style: TextStyle(
                  color: Colors.grey[400],
                  fontSize: 14,
                  height: 1.4,
                ),
                maxLines: 2,
                overflow: TextOverflow.ellipsis,
              ),
            ],

            // Tags
            if (template.tags.isNotEmpty) ...[
              const SizedBox(height: 16),
              Wrap(
                spacing: 8,
                runSpacing: 8,
                children:
                    template.tags.take(3).map((tag) {
                      return Container(
                        padding: const EdgeInsets.symmetric(
                          horizontal: 10,
                          vertical: 4,
                        ),
                        decoration: BoxDecoration(
                          color: const Color(0xFFFACC15).withValues(alpha: 0.1),
                          borderRadius: BorderRadius.circular(12),
                          border: Border.all(
                            color: const Color(
                              0xFFFACC15,
                            ).withValues(alpha: 0.3),
                          ),
                        ),
                        child: Text(
                          tag,
                          style: const TextStyle(
                            color: Color(0xFFFACC15),
                            fontSize: 12,
                            fontWeight: FontWeight.w500,
                          ),
                        ),
                      );
                    }).toList(),
              ),
            ],
          ],
        ),
      ),
    );
  }

  // Navigation methods
  void _navigateToCreateTemplate(BuildContext context) async {
    await Navigator.push(
      context,
      MaterialPageRoute(
        builder: (context) => const EditNutritionTemplatePage(),
      ),
    );
    // Refresh templates when returning from create/edit
    if (context.mounted) {
      final ref = ProviderScope.containerOf(context);
      ref.read(nutritionTemplateNotifierProvider.notifier).loadTemplates();
    }
  }

  void _navigateToEditTemplate(
    BuildContext context,
    NutritionTemplate template,
  ) async {
    await Navigator.push(
      context,
      MaterialPageRoute(
        builder: (context) => EditNutritionTemplatePage(template: template),
      ),
    );
    // Refresh templates when returning from create/edit
    if (context.mounted) {
      final ref = ProviderScope.containerOf(context);
      ref.read(nutritionTemplateNotifierProvider.notifier).loadTemplates();
    }
  }

  // UI Builder methods
  Widget _buildSearchBar(
    TextEditingController controller,
    ValueNotifier<String> searchQuery,
  ) {
    return Container(
      padding: const EdgeInsets.all(16),
      child: TextField(
        controller: controller,
        onChanged: (value) {
          searchQuery.value = value;
        },
        style: const TextStyle(color: Colors.white),
        decoration: InputDecoration(
          hintText: 'Search templates...',
          hintStyle: TextStyle(color: Colors.grey[400]),
          prefixIcon: Icon(Icons.search, color: Colors.grey[400]),
          filled: true,
          fillColor: const Color(0xFF1F2937),
          border: OutlineInputBorder(
            borderRadius: BorderRadius.circular(12),
            borderSide: BorderSide.none,
          ),
          contentPadding: const EdgeInsets.symmetric(
            horizontal: 16,
            vertical: 12,
          ),
        ),
      ),
    );
  }

  Widget _buildLoadingState() {
    return const Center(
      child: CircularProgressIndicator(color: Color(0xFFFACC15)),
    );
  }

  // Pagination methods
  void _loadMoreTemplates(
    WidgetRef ref,
    ValueNotifier<int> currentPage,
    ValueNotifier<bool> isLoadingMore,
    ValueNotifier<bool> hasMoreData,
  ) async {
    if (isLoadingMore.value || !hasMoreData.value) return;

    isLoadingMore.value = true;
    currentPage.value++;

    try {
      // Simulate API call delay
      await Future.delayed(const Duration(milliseconds: 500));

      // For now, we'll simulate that there's no more data after page 3
      if (currentPage.value >= 3) {
        hasMoreData.value = false;
      }

      // In a real implementation, you would call the repository with pagination parameters
      // final newTemplates = await repository.getNutritionTemplates(
      //   instructorId: instructorId,
      //   page: currentPage.value,
      //   limit: 10,
      // );
    } catch (e) {
      // Handle error
      currentPage.value--;
    } finally {
      isLoadingMore.value = false;
    }
  }

  Widget _buildLoadingIndicator() {
    return Container(
      padding: const EdgeInsets.all(16),
      alignment: Alignment.center,
      child: const CircularProgressIndicator(
        valueColor: AlwaysStoppedAnimation<Color>(Color(0xFFFACC15)),
      ),
    );
  }
}
