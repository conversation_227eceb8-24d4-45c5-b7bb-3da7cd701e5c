import 'package:flutter/material.dart';
import 'package:hooks_riverpod/hooks_riverpod.dart';
import 'package:fitgo_app/src/shared/widgets/text_widget/text_widget.dart';
import 'package:fitgo_app/src/shared/constants/app_text_style.dart';
import 'package:fitgo_app/src/instructor/domain/capacity_upgrade_models.dart';
import 'package:fitgo_app/src/instructor/application/capacity_upgrade_service.dart';

class CapacityUpgradeScreen extends HookConsumerWidget {
  final String instructorId;

  const CapacityUpgradeScreen({
    super.key,
    required this.instructorId,
  });

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    return Scaffold(
      backgroundColor: const Color(0xFF111827),
      appBar: AppBar(
        backgroundColor: const Color(0xFF111827),
        elevation: 0,
        leading: IconButton(
          icon: const Icon(Icons.arrow_back_ios, color: Colors.white),
          onPressed: () => Navigator.of(context).pop(),
        ),
        title: Row(
          children: [
            Icon(
              Icons.trending_up,
              color: const Color(0xFFFACC15),
              size: 24,
            ),
            const SizedBox(width: 8),
            TextWidget(
              'Kapasite Yükseltme',
              style: ATextStyle.large.copyWith(
                color: Colors.white,
                fontWeight: FontWeight.bold,
              ),
            ),
          ],
        ),
      ),
      body: FutureBuilder<InstructorCapacityStatus>(
        future: CapacityUpgradeService().getCapacityStatus(instructorId),
        builder: (context, snapshot) {
          if (snapshot.connectionState == ConnectionState.waiting) {
            return const Center(
              child: CircularProgressIndicator(
                color: Color(0xFFFACC15),
              ),
            );
          }

          if (snapshot.hasError) {
            return Center(
              child: Column(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  Icon(
                    Icons.error_outline,
                    color: Colors.red,
                    size: 64,
                  ),
                  const SizedBox(height: 16),
                  TextWidget(
                    'Kapasite bilgileri yüklenemedi',
                    style: ATextStyle.medium.copyWith(color: Colors.white),
                  ),
                ],
              ),
            );
          }

          final status = snapshot.data!;
          return _buildContent(context, ref, status);
        },
      ),
    );
  }

  Widget _buildContent(BuildContext context, WidgetRef ref, InstructorCapacityStatus status) {
    return SingleChildScrollView(
      padding: const EdgeInsets.all(16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Current Status Card
          _buildCurrentStatusCard(status),
          const SizedBox(height: 24),

          // Upgrade Packages
          TextWidget(
            'Kapasite Yükseltme Paketleri',
            style: ATextStyle.large.copyWith(
              color: Colors.white,
              fontWeight: FontWeight.bold,
            ),
          ),
          const SizedBox(height: 8),
          TextWidget(
            'Daha fazla öğrenci kabul etmek için kapasiteni yükselt',
            style: ATextStyle.small.copyWith(
              color: Colors.grey[400],
            ),
          ),
          const SizedBox(height: 16),

          // Package Cards
          FutureBuilder<List<CapacityUpgradePackage>>(
            future: CapacityUpgradeService().getRecommendedPackages(instructorId),
            builder: (context, snapshot) {
              if (snapshot.connectionState == ConnectionState.waiting) {
                return const Center(child: CircularProgressIndicator());
              }

              final packages = snapshot.data ?? CapacityUpgradePackages.packages;
              return Column(
                children: packages.map((package) => 
                  _buildPackageCard(context, ref, package, status)
                ).toList(),
              );
            },
          ),

          const SizedBox(height: 24),

          // Benefits Section
          _buildBenefitsSection(),

          const SizedBox(height: 24),

          // FAQ Section
          _buildFAQSection(),
        ],
      ),
    );
  }

  Widget _buildCurrentStatusCard(InstructorCapacityStatus status) {
    return Container(
      padding: const EdgeInsets.all(20),
      decoration: BoxDecoration(
        color: const Color(0xFF1F2937),
        borderRadius: BorderRadius.circular(16),
        border: Border.all(
          color: const Color(0xFFFACC15).withOpacity(0.2),
          width: 1,
        ),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Icon(
                Icons.people,
                color: const Color(0xFFFACC15),
                size: 24,
              ),
              const SizedBox(width: 8),
              TextWidget(
                'Mevcut Kapasite Durumu',
                style: ATextStyle.medium.copyWith(
                  color: Colors.white,
                  fontWeight: FontWeight.w600,
                ),
              ),
            ],
          ),
          const SizedBox(height: 16),

          // Capacity Progress
          Row(
            children: [
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Row(
                      mainAxisAlignment: MainAxisAlignment.spaceBetween,
                      children: [
                        TextWidget(
                          '${status.usedCapacity}/${status.maxCapacity} Öğrenci',
                          style: ATextStyle.large.copyWith(
                            color: Colors.white,
                            fontWeight: FontWeight.bold,
                          ),
                        ),
                        TextWidget(
                          '${status.utilizationPercentage.toInt()}%',
                          style: ATextStyle.medium.copyWith(
                            color: const Color(0xFFFACC15),
                            fontWeight: FontWeight.w600,
                          ),
                        ),
                      ],
                    ),
                    const SizedBox(height: 8),
                    LinearProgressIndicator(
                      value: status.utilizationPercentage / 100,
                      backgroundColor: Colors.grey[800],
                      valueColor: AlwaysStoppedAnimation<Color>(
                        status.isAtCapacity 
                            ? Colors.red 
                            : status.isNearCapacity 
                                ? Colors.orange 
                                : const Color(0xFFFACC15),
                      ),
                    ),
                  ],
                ),
              ),
            ],
          ),
          const SizedBox(height: 12),

          // Status Message
          Container(
            padding: const EdgeInsets.all(12),
            decoration: BoxDecoration(
              color: status.isAtCapacity 
                  ? Colors.red.withOpacity(0.1)
                  : status.isNearCapacity 
                      ? Colors.orange.withOpacity(0.1)
                      : const Color(0xFFFACC15).withOpacity(0.1),
              borderRadius: BorderRadius.circular(8),
            ),
            child: Row(
              children: [
                Icon(
                  status.isAtCapacity 
                      ? Icons.warning 
                      : status.isNearCapacity 
                          ? Icons.info_outline 
                          : Icons.check_circle,
                  color: status.isAtCapacity 
                      ? Colors.red 
                      : status.isNearCapacity 
                          ? Colors.orange 
                          : const Color(0xFFFACC15),
                  size: 20,
                ),
                const SizedBox(width: 8),
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      TextWidget(
                        status.statusText,
                        style: ATextStyle.small.copyWith(
                          color: Colors.white,
                          fontWeight: FontWeight.w600,
                        ),
                      ),
                      TextWidget(
                        status.statusDescription,
                        style: ATextStyle.small.copyWith(
                          color: Colors.grey[400],
                        ),
                      ),
                    ],
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildPackageCard(BuildContext context, WidgetRef ref, CapacityUpgradePackage package, InstructorCapacityStatus status) {
    return Container(
      margin: const EdgeInsets.only(bottom: 16),
      decoration: BoxDecoration(
        color: const Color(0xFF1F2937),
        borderRadius: BorderRadius.circular(16),
        border: Border.all(
          color: package.isPopular 
              ? const Color(0xFFFACC15) 
              : Colors.grey[700]!,
          width: package.isPopular ? 2 : 1,
        ),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Header with popular badge
          if (package.isPopular)
            Container(
              width: double.infinity,
              padding: const EdgeInsets.symmetric(vertical: 8),
              decoration: BoxDecoration(
                color: const Color(0xFFFACC15),
                borderRadius: const BorderRadius.only(
                  topLeft: Radius.circular(16),
                  topRight: Radius.circular(16),
                ),
              ),
              child: Center(
                child: TextWidget(
                  '🔥 EN POPÜLER',
                  style: ATextStyle.small.copyWith(
                    color: Colors.black,
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ),
            ),

          Padding(
            padding: const EdgeInsets.all(20),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                // Package name and description
                Row(
                  children: [
                    Expanded(
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          TextWidget(
                            package.name,
                            style: ATextStyle.large.copyWith(
                              color: Colors.white,
                              fontWeight: FontWeight.bold,
                            ),
                          ),
                          const SizedBox(height: 4),
                          TextWidget(
                            package.description,
                            style: ATextStyle.small.copyWith(
                              color: Colors.grey[400],
                            ),
                          ),
                        ],
                      ),
                    ),
                    if (package.discountText != null)
                      Container(
                        padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                        decoration: BoxDecoration(
                          color: Colors.red,
                          borderRadius: BorderRadius.circular(12),
                        ),
                        child: TextWidget(
                          package.discountText!,
                          style: ATextStyle.small.copyWith(
                            color: Colors.white,
                            fontWeight: FontWeight.w600,
                            fontSize: 10,
                          ),
                        ),
                      ),
                  ],
                ),
                const SizedBox(height: 16),

                // Price
                Row(
                  children: [
                    if (package.hasDiscount) ...[
                      TextWidget(
                        '₺${package.originalPrice!.toStringAsFixed(0)}',
                        style: ATextStyle.medium.copyWith(
                          color: Colors.grey[500],
                          decoration: TextDecoration.lineThrough,
                        ),
                      ),
                      const SizedBox(width: 8),
                    ],
                    TextWidget(
                      '₺${package.price.toStringAsFixed(0)}',
                      style: ATextStyle.large.copyWith(
                        color: const Color(0xFFFACC15),
                        fontWeight: FontWeight.bold,
                        fontSize: 24,
                      ),
                    ),
                    TextWidget(
                      '/${package.duration == 'monthly' ? 'ay' : package.duration == 'yearly' ? 'yıl' : 'tek seferlik'}',
                      style: ATextStyle.small.copyWith(
                        color: Colors.grey[400],
                      ),
                    ),
                  ],
                ),
                const SizedBox(height: 8),
                TextWidget(
                  'Öğrenci başına ₺${package.pricePerStudent}',
                  style: ATextStyle.small.copyWith(
                    color: Colors.grey[400],
                  ),
                ),
                const SizedBox(height: 16),

                // Features
                ...package.features.map((feature) => Padding(
                  padding: const EdgeInsets.only(bottom: 8),
                  child: Row(
                    children: [
                      Icon(
                        Icons.check_circle,
                        color: const Color(0xFFFACC15),
                        size: 16,
                      ),
                      const SizedBox(width: 8),
                      Expanded(
                        child: TextWidget(
                          feature,
                          style: ATextStyle.small.copyWith(
                            color: Colors.white,
                          ),
                        ),
                      ),
                    ],
                  ),
                )),
                const SizedBox(height: 16),

                // Purchase button
                SizedBox(
                  width: double.infinity,
                  child: ElevatedButton(
                    onPressed: status.canUpgrade 
                        ? () => _showPurchaseDialog(context, ref, package)
                        : null,
                    style: ElevatedButton.styleFrom(
                      backgroundColor: package.isPopular 
                          ? const Color(0xFFFACC15) 
                          : Colors.grey[700],
                      foregroundColor: package.isPopular 
                          ? Colors.black 
                          : Colors.white,
                      padding: const EdgeInsets.symmetric(vertical: 16),
                      shape: RoundedRectangleBorder(
                        borderRadius: BorderRadius.circular(12),
                      ),
                    ),
                    child: TextWidget(
                      status.canUpgrade ? 'Satın Al' : 'Şu Anda Mevcut Değil',
                      style: ATextStyle.medium.copyWith(
                        fontWeight: FontWeight.w600,
                      ),
                    ),
                  ),
                ),

                if (!status.canUpgrade && status.upgradeRestriction != null) ...[
                  const SizedBox(height: 8),
                  TextWidget(
                    status.upgradeRestriction!,
                    style: ATextStyle.small.copyWith(
                      color: Colors.red,
                    ),
                  ),
                ],
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildBenefitsSection() {
    final benefits = [
      {
        'icon': Icons.trending_up,
        'title': 'Daha Fazla Gelir',
        'description': 'Daha fazla öğrenci = daha fazla gelir potansiyeli',
      },
      {
        'icon': Icons.star,
        'title': 'Premium Özellikler',
        'description': 'Gelişmiş istatistikler ve raporlama araçları',
      },
      {
        'icon': Icons.support_agent,
        'title': 'Öncelikli Destek',
        'description': '7/24 premium müşteri desteği',
      },
      {
        'icon': Icons.analytics,
        'title': 'Detaylı Analizler',
        'description': 'Öğrenci performansı ve gelir analizleri',
      },
    ];

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        TextWidget(
          'Kapasite Yükseltmenin Avantajları',
          style: ATextStyle.large.copyWith(
            color: Colors.white,
            fontWeight: FontWeight.bold,
          ),
        ),
        const SizedBox(height: 16),
        ...benefits.map((benefit) => Container(
          margin: const EdgeInsets.only(bottom: 12),
          padding: const EdgeInsets.all(16),
          decoration: BoxDecoration(
            color: const Color(0xFF1F2937),
            borderRadius: BorderRadius.circular(12),
          ),
          child: Row(
            children: [
              Container(
                padding: const EdgeInsets.all(8),
                decoration: BoxDecoration(
                  color: const Color(0xFFFACC15).withOpacity(0.2),
                  borderRadius: BorderRadius.circular(8),
                ),
                child: Icon(
                  benefit['icon'] as IconData,
                  color: const Color(0xFFFACC15),
                  size: 20,
                ),
              ),
              const SizedBox(width: 12),
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    TextWidget(
                      benefit['title'] as String,
                      style: ATextStyle.medium.copyWith(
                        color: Colors.white,
                        fontWeight: FontWeight.w600,
                      ),
                    ),
                    TextWidget(
                      benefit['description'] as String,
                      style: ATextStyle.small.copyWith(
                        color: Colors.grey[400],
                      ),
                    ),
                  ],
                ),
              ),
            ],
          ),
        )),
      ],
    );
  }

  Widget _buildFAQSection() {
    final faqs = [
      {
        'question': 'Kapasite yükseltme nasıl çalışır?',
        'answer': 'Paket satın aldıktan sonra kapasitiniz otomatik olarak artar ve hemen yeni öğrenci kabul edebilirsiniz.',
      },
      {
        'question': 'Ödeme güvenli mi?',
        'answer': 'Evet, tüm ödemeler SSL şifreleme ile korunur ve güvenli ödeme sistemleri kullanılır.',
      },
      {
        'question': 'İptal edebilir miyim?',
        'answer': 'Aylık paketleri istediğiniz zaman iptal edebilirsiniz. Yıllık paketler için iade politikamız geçerlidir.',
      },
    ];

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        TextWidget(
          'Sık Sorulan Sorular',
          style: ATextStyle.large.copyWith(
            color: Colors.white,
            fontWeight: FontWeight.bold,
          ),
        ),
        const SizedBox(height: 16),
        ...faqs.map((faq) => Container(
          margin: const EdgeInsets.only(bottom: 12),
          padding: const EdgeInsets.all(16),
          decoration: BoxDecoration(
            color: const Color(0xFF1F2937),
            borderRadius: BorderRadius.circular(12),
          ),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              TextWidget(
                faq['question'] as String,
                style: ATextStyle.medium.copyWith(
                  color: Colors.white,
                  fontWeight: FontWeight.w600,
                ),
              ),
              const SizedBox(height: 8),
              TextWidget(
                faq['answer'] as String,
                style: ATextStyle.small.copyWith(
                  color: Colors.grey[400],
                ),
              ),
            ],
          ),
        )),
      ],
    );
  }

  void _showPurchaseDialog(BuildContext context, WidgetRef ref, CapacityUpgradePackage package) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        backgroundColor: const Color(0xFF1F2937),
        title: TextWidget(
          'Satın Alma Onayı',
          style: ATextStyle.large.copyWith(
            color: Colors.white,
            fontWeight: FontWeight.bold,
          ),
        ),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            TextWidget(
              '${package.name} paketini satın almak istediğinizden emin misiniz?',
              style: ATextStyle.medium.copyWith(color: Colors.white),
            ),
            const SizedBox(height: 16),
            Container(
              padding: const EdgeInsets.all(12),
              decoration: BoxDecoration(
                color: const Color(0xFFFACC15).withOpacity(0.1),
                borderRadius: BorderRadius.circular(8),
              ),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  TextWidget(
                    'Paket: ${package.name}',
                    style: ATextStyle.small.copyWith(color: Colors.white),
                  ),
                  TextWidget(
                    'Ek Kapasite: +${package.additionalCapacity} öğrenci',
                    style: ATextStyle.small.copyWith(color: Colors.white),
                  ),
                  TextWidget(
                    'Fiyat: ₺${package.price.toStringAsFixed(0)}',
                    style: ATextStyle.small.copyWith(
                      color: const Color(0xFFFACC15),
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                ],
              ),
            ),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: TextWidget(
              'İptal',
              style: ATextStyle.medium.copyWith(color: Colors.grey[400]),
            ),
          ),
          ElevatedButton(
            onPressed: () async {
              Navigator.of(context).pop();
              await _processPurchase(context, ref, package);
            },
            style: ElevatedButton.styleFrom(
              backgroundColor: const Color(0xFFFACC15),
              foregroundColor: Colors.black,
            ),
            child: TextWidget(
              'Satın Al',
              style: ATextStyle.medium.copyWith(
                fontWeight: FontWeight.w600,
              ),
            ),
          ),
        ],
      ),
    );
  }

  Future<void> _processPurchase(BuildContext context, WidgetRef ref, CapacityUpgradePackage package) async {
    // Show loading dialog
    showDialog(
      context: context,
      barrierDismissible: false,
      builder: (context) => AlertDialog(
        backgroundColor: const Color(0xFF1F2937),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            const CircularProgressIndicator(color: Color(0xFFFACC15)),
            const SizedBox(height: 16),
            TextWidget(
              'Satın alma işlemi gerçekleştiriliyor...',
              style: ATextStyle.medium.copyWith(color: Colors.white),
            ),
          ],
        ),
      ),
    );

    try {
      final service = CapacityUpgradeService();
      final result = await service.purchaseCapacityUpgrade(
        instructorId: instructorId,
        packageId: package.id,
        paymentMethod: 'credit_card',
      );

      Navigator.of(context).pop(); // Close loading dialog

      if (result.success) {
        // Show success dialog
        showDialog(
          context: context,
          builder: (context) => AlertDialog(
            backgroundColor: const Color(0xFF1F2937),
            title: Row(
              children: [
                Icon(Icons.check_circle, color: const Color(0xFFFACC15)),
                const SizedBox(width: 8),
                TextWidget(
                  'Başarılı!',
                  style: ATextStyle.large.copyWith(
                    color: Colors.white,
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ],
            ),
            content: TextWidget(
              'Kapasite yükseltmeniz başarıyla tamamlandı. Yeni kapasitiniz: ${result.newCapacity} öğrenci',
              style: ATextStyle.medium.copyWith(color: Colors.white),
            ),
            actions: [
              ElevatedButton(
                onPressed: () {
                  Navigator.of(context).pop();
                  Navigator.of(context).pop(); // Go back to profile
                },
                style: ElevatedButton.styleFrom(
                  backgroundColor: const Color(0xFFFACC15),
                  foregroundColor: Colors.black,
                ),
                child: TextWidget(
                  'Tamam',
                  style: ATextStyle.medium.copyWith(
                    fontWeight: FontWeight.w600,
                  ),
                ),
              ),
            ],
          ),
        );
      } else {
        // Show error dialog
        showDialog(
          context: context,
          builder: (context) => AlertDialog(
            backgroundColor: const Color(0xFF1F2937),
            title: Row(
              children: [
                Icon(Icons.error, color: Colors.red),
                const SizedBox(width: 8),
                TextWidget(
                  'Hata',
                  style: ATextStyle.large.copyWith(
                    color: Colors.white,
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ],
            ),
            content: TextWidget(
              result.error ?? 'Bilinmeyen bir hata oluştu',
              style: ATextStyle.medium.copyWith(color: Colors.white),
            ),
            actions: [
              TextButton(
                onPressed: () => Navigator.of(context).pop(),
                child: TextWidget(
                  'Tamam',
                  style: ATextStyle.medium.copyWith(color: Colors.grey[400]),
                ),
              ),
            ],
          ),
        );
      }
    } catch (e) {
      Navigator.of(context).pop(); // Close loading dialog
      
      // Show error dialog
      showDialog(
        context: context,
        builder: (context) => AlertDialog(
          backgroundColor: const Color(0xFF1F2937),
          title: TextWidget(
            'Hata',
            style: ATextStyle.large.copyWith(
              color: Colors.white,
              fontWeight: FontWeight.bold,
            ),
          ),
          content: TextWidget(
            'Satın alma işlemi sırasında bir hata oluştu: $e',
            style: ATextStyle.medium.copyWith(color: Colors.white),
          ),
          actions: [
            TextButton(
              onPressed: () => Navigator.of(context).pop(),
              child: TextWidget(
                'Tamam',
                style: ATextStyle.medium.copyWith(color: Colors.grey[400]),
              ),
            ),
          ],
        ),
      );
    }
  }
}
