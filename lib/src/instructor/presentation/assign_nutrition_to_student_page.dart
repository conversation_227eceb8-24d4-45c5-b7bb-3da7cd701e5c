import 'package:flutter/material.dart';
import 'package:flutter_hooks/flutter_hooks.dart';
import 'package:hooks_riverpod/hooks_riverpod.dart';
import '../application/nutrition_template_provider.dart';
import '../application/student_nutrition_assignment_provider.dart';
import '../domain/nutrition_template.dart';
import '../domain/student_models.dart';
import '../domain/student_nutrition_assignment.dart';
import '../../shared/utils/app_logger.dart';
// import 'manual_nutrition_plan_builder_page.dart'; // TODO: Create this page

/// Nutrition Plan Assignment page for instructors
class AssignNutritionToStudentPage extends HookConsumerWidget {
  final InstructorStudent student;
  final String instructorId;

  const AssignNutritionToStudentPage({
    super.key,
    required this.student,
    required this.instructorId,
  });

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final selectedMode = useState<NutritionAssignmentMode>(NutritionAssignmentMode.template);
    final searchController = useTextEditingController();
    final searchQuery = useState<String>('');
    final selectedTags = useState<List<String>>([]);

    AppLogger.info('Opening nutrition assignment for student: ${student.name}', tag: 'NUTRITION_ASSIGNMENT');

    return Scaffold(
      backgroundColor: const Color(0xFF111827),
      appBar: AppBar(
        backgroundColor: const Color(0xFF111827),
        elevation: 0,
        leading: IconButton(
          icon: const Icon(Icons.arrow_back, color: Colors.white),
          onPressed: () => Navigator.pop(context),
        ),
        title: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const Text(
              'Assign Nutrition Plan',
              style: TextStyle(
                color: Colors.white,
                fontSize: 18,
                fontWeight: FontWeight.bold,
              ),
            ),
            Text(
              'To: ${student.name}',
              style: const TextStyle(
                color: Color(0xFFFACC15),
                fontSize: 14,
                fontWeight: FontWeight.normal,
              ),
            ),
          ],
        ),
      ),
      body: Column(
        children: [
          // Mode Selection
          _buildModeSelection(selectedMode),
          
          // Content based on selected mode
          Expanded(
            child: selectedMode.value == NutritionAssignmentMode.template
                ? _buildTemplateAssignmentView(context, ref, searchController, searchQuery, selectedTags)
                : _buildManualPlanView(context, ref),
          ),
        ],
      ),
    );
  }

  Widget _buildModeSelection(ValueNotifier<NutritionAssignmentMode> selectedMode) {
    return Container(
      margin: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: const Color(0xFF1F2937),
        borderRadius: BorderRadius.circular(12),
        border: Border.all(color: const Color(0xFF374151)),
      ),
      child: Row(
        children: [
          Expanded(
            child: _buildModeButton(
              'Assign Template',
              Icons.folder_copy,
              selectedMode.value == NutritionAssignmentMode.template,
              () {
                AppLogger.userAction('Selected template assignment mode', tag: 'NUTRITION_ASSIGNMENT');
                selectedMode.value = NutritionAssignmentMode.template;
              },
            ),
          ),
          Container(
            width: 1,
            height: 48,
            color: const Color(0xFF374151),
          ),
          Expanded(
            child: _buildModeButton(
              'Create Manual',
              Icons.edit,
              selectedMode.value == NutritionAssignmentMode.manual,
              () {
                AppLogger.userAction('Selected manual plan creation mode', tag: 'NUTRITION_ASSIGNMENT');
                selectedMode.value = NutritionAssignmentMode.manual;
              },
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildModeButton(String title, IconData icon, bool isSelected, VoidCallback onTap) {
    return InkWell(
      onTap: onTap,
      borderRadius: BorderRadius.circular(12),
      child: Container(
        padding: const EdgeInsets.symmetric(vertical: 12, horizontal: 16),
        child: Row(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              icon,
              color: isSelected ? const Color(0xFFFACC15) : const Color(0xFF9CA3AF),
              size: 20,
            ),
            const SizedBox(width: 8),
            Text(
              title,
              style: TextStyle(
                color: isSelected ? const Color(0xFFFACC15) : const Color(0xFF9CA3AF),
                fontSize: 14,
                fontWeight: isSelected ? FontWeight.w600 : FontWeight.normal,
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildTemplateAssignmentView(
    BuildContext context,
    WidgetRef ref,
    TextEditingController searchController,
    ValueNotifier<String> searchQuery,
    ValueNotifier<List<String>> selectedTags,
  ) {
    final nutritionTemplatesAsync = ref.watch(nutritionTemplateNotifierProvider);

    return Column(
      children: [
        // Search and Filter
        _buildSearchAndFilter(searchController, searchQuery, selectedTags),
        
        // Templates List
        Expanded(
          child: nutritionTemplatesAsync.when(
            data: (templates) => _buildTemplatesList(context, ref, templates, searchQuery.value, selectedTags.value),
            loading: () => _buildLoadingState(),
            error: (error, stack) => _buildErrorState(context, ref, error.toString()),
          ),
        ),
      ],
    );
  }

  Widget _buildManualPlanView(BuildContext context, WidgetRef ref) {
    return Container(
      padding: const EdgeInsets.all(16),
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Container(
            padding: const EdgeInsets.all(24),
            decoration: BoxDecoration(
              color: const Color(0xFF1F2937),
              borderRadius: BorderRadius.circular(16),
              border: Border.all(color: const Color(0xFF374151)),
            ),
            child: Column(
              children: [
                const Icon(
                  Icons.restaurant_menu,
                  color: Color(0xFFFACC15),
                  size: 48,
                ),
                const SizedBox(height: 16),
                const Text(
                  'Create Custom Nutrition Plan',
                  style: TextStyle(
                    color: Colors.white,
                    fontSize: 18,
                    fontWeight: FontWeight.bold,
                  ),
                ),
                const SizedBox(height: 8),
                Text(
                  'Build a personalized meal plan for ${student.name} from scratch',
                  textAlign: TextAlign.center,
                  style: const TextStyle(
                    color: Color(0xFF9CA3AF),
                    fontSize: 14,
                  ),
                ),
                const SizedBox(height: 24),
                SizedBox(
                  width: double.infinity,
                  child: ElevatedButton(
                    onPressed: () => _navigateToManualPlanBuilder(context, ref),
                    style: ElevatedButton.styleFrom(
                      backgroundColor: const Color(0xFFFACC15),
                      foregroundColor: Colors.black,
                      padding: const EdgeInsets.symmetric(vertical: 16),
                      shape: RoundedRectangleBorder(
                        borderRadius: BorderRadius.circular(12),
                      ),
                    ),
                    child: const Text(
                      'Start Building Plan',
                      style: TextStyle(
                        fontSize: 16,
                        fontWeight: FontWeight.w600,
                      ),
                    ),
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildSearchAndFilter(
    TextEditingController searchController,
    ValueNotifier<String> searchQuery,
    ValueNotifier<List<String>> selectedTags,
  ) {
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
      child: Column(
        children: [
          // Search Bar
          Container(
            decoration: BoxDecoration(
              color: const Color(0xFF1F2937),
              borderRadius: BorderRadius.circular(12),
              border: Border.all(color: const Color(0xFF374151)),
            ),
            child: TextField(
              controller: searchController,
              onChanged: (value) => searchQuery.value = value,
              style: const TextStyle(color: Colors.white),
              decoration: const InputDecoration(
                hintText: 'Search nutrition templates...',
                hintStyle: TextStyle(color: Color(0xFF9CA3AF)),
                prefixIcon: Icon(Icons.search, color: Color(0xFF9CA3AF)),
                border: InputBorder.none,
                contentPadding: EdgeInsets.symmetric(horizontal: 16, vertical: 12),
              ),
            ),
          ),
          
          const SizedBox(height: 12),
          
          // Filter Tags
          SizedBox(
            height: 40,
            child: ListView.builder(
              scrollDirection: Axis.horizontal,
              itemCount: NutritionTags.all.length,
              itemBuilder: (context, index) {
                final tag = NutritionTags.all[index];
                final isSelected = selectedTags.value.contains(tag);
                
                return Container(
                  margin: const EdgeInsets.only(right: 8),
                  child: FilterChip(
                    label: Text(tag),
                    selected: isSelected,
                    onSelected: (selected) {
                      if (selected) {
                        selectedTags.value = [...selectedTags.value, tag];
                      } else {
                        selectedTags.value = selectedTags.value.where((t) => t != tag).toList();
                      }
                    },
                    backgroundColor: const Color(0xFF1F2937),
                    selectedColor: const Color(0xFFFACC15).withOpacity(0.2),
                    labelStyle: TextStyle(
                      color: isSelected ? const Color(0xFFFACC15) : const Color(0xFF9CA3AF),
                      fontSize: 12,
                    ),
                    side: BorderSide(
                      color: isSelected ? const Color(0xFFFACC15) : const Color(0xFF374151),
                    ),
                  ),
                );
              },
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildTemplatesList(
    BuildContext context,
    WidgetRef ref,
    List<NutritionTemplate> templates,
    String searchQuery,
    List<String> selectedTags,
  ) {
    // Filter templates
    var filteredTemplates = templates.where((template) {
      final matchesSearch = searchQuery.isEmpty ||
          template.name.toLowerCase().contains(searchQuery.toLowerCase()) ||
          (template.description?.toLowerCase().contains(searchQuery.toLowerCase()) ?? false);
      
      final matchesTags = selectedTags.isEmpty ||
          selectedTags.any((tag) => template.tags.contains(tag));
      
      return matchesSearch && matchesTags;
    }).toList();

    if (filteredTemplates.isEmpty) {
      return _buildEmptyState(searchQuery.isNotEmpty || selectedTags.isNotEmpty);
    }

    return ListView.builder(
      padding: const EdgeInsets.symmetric(horizontal: 16),
      itemCount: filteredTemplates.length,
      itemBuilder: (context, index) {
        final template = filteredTemplates[index];
        return _buildTemplateCard(context, ref, template);
      },
    );
  }

  Widget _buildTemplateCard(BuildContext context, WidgetRef ref, NutritionTemplate template) {
    return Container(
      margin: const EdgeInsets.only(bottom: 12),
      decoration: BoxDecoration(
        color: const Color(0xFF1F2937),
        borderRadius: BorderRadius.circular(16),
        border: Border.all(color: const Color(0xFF374151)),
      ),
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Header
            Row(
              children: [
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        template.name,
                        style: const TextStyle(
                          color: Colors.white,
                          fontSize: 16,
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                      if (template.description != null) ...[
                        const SizedBox(height: 4),
                        Text(
                          template.description!,
                          style: const TextStyle(
                            color: Color(0xFF9CA3AF),
                            fontSize: 14,
                          ),
                        ),
                      ],
                    ],
                  ),
                ),
                ElevatedButton(
                  onPressed: () => _assignTemplate(context, ref, template),
                  style: ElevatedButton.styleFrom(
                    backgroundColor: const Color(0xFFFACC15),
                    foregroundColor: Colors.black,
                    padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
                    shape: RoundedRectangleBorder(
                      borderRadius: BorderRadius.circular(8),
                    ),
                  ),
                  child: const Text(
                    'Apply',
                    style: TextStyle(
                      fontSize: 14,
                      fontWeight: FontWeight.w600,
                    ),
                  ),
                ),
              ],
            ),
            
            const SizedBox(height: 12),
            
            // Tags
            if (template.tags.isNotEmpty) ...[
              Wrap(
                spacing: 6,
                runSpacing: 4,
                children: template.tags.map((tag) => _buildTag(tag)).toList(),
              ),
              const SizedBox(height: 12),
            ],
            
            // Macronutrient breakdown
            Row(
              children: [
                _buildMacroChip('Protein', '${template.macros.proteinPercentage.toInt()}%', const Color(0xFF10B981)),
                const SizedBox(width: 8),
                _buildMacroChip('Carbs', '${template.macros.carbsPercentage.toInt()}%', const Color(0xFF3B82F6)),
                const SizedBox(width: 8),
                _buildMacroChip('Fats', '${template.macros.fatsPercentage.toInt()}%', const Color(0xFFF59E0B)),
              ],
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildTag(String tag) {
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
      decoration: BoxDecoration(
        color: const Color(0xFFFACC15).withOpacity(0.1),
        borderRadius: BorderRadius.circular(12),
        border: Border.all(color: const Color(0xFFFACC15).withOpacity(0.3)),
      ),
      child: Text(
        tag,
        style: const TextStyle(
          color: Color(0xFFFACC15),
          fontSize: 12,
          fontWeight: FontWeight.w500,
        ),
      ),
    );
  }

  Widget _buildMacroChip(String label, String value, Color color) {
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
      decoration: BoxDecoration(
        color: color.withOpacity(0.1),
        borderRadius: BorderRadius.circular(8),
        border: Border.all(color: color.withOpacity(0.3)),
      ),
      child: Text(
        '$label $value',
        style: TextStyle(
          color: color,
          fontSize: 12,
          fontWeight: FontWeight.w500,
        ),
      ),
    );
  }

  Widget _buildLoadingState() {
    return const Center(
      child: CircularProgressIndicator(color: Color(0xFFFACC15)),
    );
  }

  Widget _buildErrorState(BuildContext context, WidgetRef ref, String error) {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          const Icon(
            Icons.error_outline,
            color: Color(0xFFEF4444),
            size: 48,
          ),
          const SizedBox(height: 16),
          const Text(
            'Failed to load templates',
            style: TextStyle(
              color: Colors.white,
              fontSize: 18,
              fontWeight: FontWeight.bold,
            ),
          ),
          const SizedBox(height: 8),
          Text(
            error,
            textAlign: TextAlign.center,
            style: const TextStyle(
              color: Color(0xFF9CA3AF),
              fontSize: 14,
            ),
          ),
          const SizedBox(height: 24),
          ElevatedButton(
            onPressed: () => ref.read(nutritionTemplateNotifierProvider.notifier).loadTemplates(),
            style: ElevatedButton.styleFrom(
              backgroundColor: const Color(0xFFFACC15),
              foregroundColor: Colors.black,
            ),
            child: const Text('Retry'),
          ),
        ],
      ),
    );
  }

  Widget _buildEmptyState(bool isFiltered) {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          const Icon(
            Icons.restaurant_menu,
            color: Color(0xFF9CA3AF),
            size: 48,
          ),
          const SizedBox(height: 16),
          Text(
            isFiltered ? 'No templates match your filters' : 'No nutrition templates found',
            style: const TextStyle(
              color: Colors.white,
              fontSize: 18,
              fontWeight: FontWeight.bold,
            ),
          ),
          const SizedBox(height: 8),
          Text(
            isFiltered 
                ? 'Try adjusting your search or filter criteria'
                : 'Create your first nutrition template to get started',
            textAlign: TextAlign.center,
            style: const TextStyle(
              color: Color(0xFF9CA3AF),
              fontSize: 14,
            ),
          ),
        ],
      ),
    );
  }

  void _assignTemplate(BuildContext context, WidgetRef ref, NutritionTemplate template) async {
    AppLogger.userAction('Assigning template: ${template.name} to student: ${student.name}', tag: 'NUTRITION_ASSIGNMENT');
    
    try {
      final notifier = ref.read(studentNutritionAssignmentNotifierProvider.notifier);

      final assignment = await notifier.assignNutritionTemplate(
        templateId: template.id,
        studentId: student.id,
        instructorId: instructorId,
        notes: 'Assigned from template: ${template.name}',
      );

      if (assignment != null && context.mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Nutrition plan "${template.name}" assigned to ${student.name}!'),
            backgroundColor: Colors.green,
          ),
        );

        // Navigate back to previous screen with success result
        Navigator.pop(context, true);
      }
    } catch (e) {
      if (context.mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Error assigning template: $e'),
            backgroundColor: Colors.red,
          ),
        );
      }
    }
  }

  void _navigateToManualPlanBuilder(BuildContext context, WidgetRef ref) {
    AppLogger.navigation('AssignNutritionToStudentPage', 'ManualNutritionPlanBuilderPage', tag: 'NUTRITION_ASSIGNMENT');

    // TODO: Implement manual nutrition plan builder
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(
        content: Text('Manual nutrition plan builder - Coming Soon!'),
        backgroundColor: Color(0xFFFACC15),
      ),
    );
  }
}

enum NutritionAssignmentMode {
  template,
  manual,
}
