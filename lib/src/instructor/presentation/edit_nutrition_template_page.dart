import 'package:flutter/material.dart';
import 'package:flutter_hooks/flutter_hooks.dart';
import 'package:hooks_riverpod/hooks_riverpod.dart';
import '../domain/nutrition_template.dart';
import '../application/nutrition_template_provider.dart';
import '../application/instructor_provider.dart';
import 'add_food_item_page.dart';

class EditNutritionTemplatePage extends HookConsumerWidget {
  final NutritionTemplate? template; // null for create, non-null for edit

  const EditNutritionTemplatePage({super.key, this.template});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final nameController = useTextEditingController(text: template?.name ?? '');
    final descriptionController = useTextEditingController(
      text: template?.description ?? '',
    );

    final selectedGoal = useState<String>(
      template?.tags.firstWhere(
            (tag) => [
              'Weight Loss',
              'Bulking',
              'Keto',
              'Maintenance',
              'Cutting',
            ].contains(tag),
            orElse: () => 'Maintenance',
          ) ??
          'Maintenance',
    );

    final meals = useState<List<NutritionTemplateMeal>>(template?.meals ?? []);
    final isLoading = useState<bool>(false);

    // Initialize default meals if creating new template
    useEffect(() {
      if (template == null && meals.value.isEmpty) {
        meals.value = _getDefaultMeals();
      }
      return null;
    }, []);

    // Save template function
    Future<void> saveTemplate() async {
      if (nameController.text.trim().isEmpty) {
        if (context.mounted) {
          _showError(context, 'Please enter a template name');
        }
        return;
      }

      isLoading.value = true;

      try {
        final nutritionNotifier = ref.read(
          nutritionTemplateNotifierProvider.notifier,
        );

        final instructorId = ref.read(currentInstructorIdProvider);
        if (instructorId == null) {
          throw Exception('No instructor ID available');
        }

        final newTemplate = NutritionTemplate(
          id: template?.id ?? '',
          instructorId: instructorId,
          name: nameController.text.trim(),
          description:
              descriptionController.text.trim().isEmpty
                  ? null
                  : descriptionController.text.trim(),
          tags: [selectedGoal.value],
          macros: const MacronutrientBreakdown(
            proteinPercentage: 25.0,
            carbsPercentage: 45.0,
            fatsPercentage: 30.0,
          ),
          createdAt: template?.createdAt ?? DateTime.now(),
          updatedAt: DateTime.now(),
          meals: meals.value,
        );

        if (template == null) {
          await nutritionNotifier.createNutritionTemplate(newTemplate);
        } else {
          await nutritionNotifier.updateNutritionTemplate(newTemplate);
        }

        if (context.mounted) {
          Navigator.pop(context);
        }
      } catch (e) {
        if (context.mounted) {
          _showError(context, 'Error saving template: $e');
        }
      } finally {
        isLoading.value = false;
      }
    }

    return Scaffold(
      backgroundColor: const Color(0xFF111827),
      appBar: AppBar(
        backgroundColor: const Color(0xFF111827),
        elevation: 0,
        leading: IconButton(
          icon: const Icon(Icons.arrow_back, color: Colors.white),
          onPressed: () => Navigator.pop(context),
        ),
        title: Text(
          template == null ? 'Create Template' : 'Edit Template',
          style: const TextStyle(
            color: Colors.white,
            fontSize: 20,
            fontWeight: FontWeight.w600,
          ),
        ),
        actions: [
          Container(
            margin: const EdgeInsets.only(right: 16),
            child: ElevatedButton(
              onPressed: isLoading.value ? null : saveTemplate,
              style: ElevatedButton.styleFrom(
                backgroundColor: const Color(0xFFFACC15),
                foregroundColor: const Color(0xFF111827),
                elevation: 0,
                padding: const EdgeInsets.symmetric(
                  horizontal: 20,
                  vertical: 8,
                ),
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(8),
                ),
              ),
              child:
                  isLoading.value
                      ? const SizedBox(
                        width: 16,
                        height: 16,
                        child: CircularProgressIndicator(
                          strokeWidth: 2,
                          color: Color(0xFF111827),
                        ),
                      )
                      : const Text(
                        'Save',
                        style: TextStyle(
                          fontWeight: FontWeight.w600,
                          fontSize: 14,
                        ),
                      ),
            ),
          ),
        ],
      ),
      body: SingleChildScrollView(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Template Info Section
            _buildTemplateInfoSection(
              nameController,
              descriptionController,
              selectedGoal,
            ),

            const SizedBox(height: 32),

            // Meals Section
            _buildMealsSection(context, meals),

            const SizedBox(height: 32),
          ],
        ),
      ),
    );
  }

  // Helper methods
  List<NutritionTemplateMeal> _getDefaultMeals() {
    final now = DateTime.now();
    return [
      NutritionTemplateMeal(
        id: 'breakfast',
        templateId: '',
        name: 'Breakfast',
        mealType: 'breakfast',
        orderIndex: 0,
        createdAt: now,
        updatedAt: now,
      ),
      NutritionTemplateMeal(
        id: 'lunch',
        templateId: '',
        name: 'Lunch',
        mealType: 'lunch',
        orderIndex: 1,
        createdAt: now,
        updatedAt: now,
      ),
      NutritionTemplateMeal(
        id: 'dinner',
        templateId: '',
        name: 'Dinner',
        mealType: 'dinner',
        orderIndex: 2,
        createdAt: now,
        updatedAt: now,
      ),
      NutritionTemplateMeal(
        id: 'snacks',
        templateId: '',
        name: 'Snacks',
        mealType: 'snack',
        orderIndex: 3,
        createdAt: now,
        updatedAt: now,
      ),
    ];
  }

  void _showError(BuildContext context, String message) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(content: Text(message), backgroundColor: Colors.red),
    );
  }

  Widget _buildTemplateInfoSection(
    TextEditingController nameController,
    TextEditingController descriptionController,
    ValueNotifier<String> selectedGoal,
  ) {
    return Container(
      padding: const EdgeInsets.all(20),
      decoration: BoxDecoration(
        color: const Color(0xFF1F2937),
        borderRadius: BorderRadius.circular(16),
        border: Border.all(color: const Color(0xFF374151)),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          const Text(
            'Template Information',
            style: TextStyle(
              color: Colors.white,
              fontSize: 18,
              fontWeight: FontWeight.w600,
            ),
          ),
          const SizedBox(height: 20),

          // Template Name
          _buildSectionTitle('Template Name *'),
          const SizedBox(height: 8),
          _buildTextField(
            controller: nameController,
            hintText: 'e.g., Bulking Phase Plan',
          ),

          const SizedBox(height: 20),

          // Description
          _buildSectionTitle('Description (Optional)'),
          const SizedBox(height: 8),
          _buildTextField(
            controller: descriptionController,
            hintText: 'e.g., High-calorie meal plan for muscle gain',
            maxLines: 3,
          ),

          const SizedBox(height: 20),

          // Goal Selection
          _buildSectionTitle('Goal'),
          const SizedBox(height: 8),
          _buildGoalDropdown(selectedGoal),
        ],
      ),
    );
  }

  Widget _buildMealsSection(
    BuildContext context,
    ValueNotifier<List<NutritionTemplateMeal>> meals,
  ) {
    return Container(
      padding: const EdgeInsets.all(20),
      decoration: BoxDecoration(
        color: const Color(0xFF1F2937),
        borderRadius: BorderRadius.circular(16),
        border: Border.all(color: const Color(0xFF374151)),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              const Text(
                'Meals',
                style: TextStyle(
                  color: Colors.white,
                  fontSize: 18,
                  fontWeight: FontWeight.w600,
                ),
              ),
              ElevatedButton.icon(
                onPressed: () => _addNewMeal(meals),
                icon: const Icon(Icons.add, size: 16),
                label: const Text('Add Meal'),
                style: ElevatedButton.styleFrom(
                  backgroundColor: const Color(0xFFFACC15),
                  foregroundColor: const Color(0xFF111827),
                  padding: const EdgeInsets.symmetric(
                    horizontal: 12,
                    vertical: 8,
                  ),
                  shape: RoundedRectangleBorder(
                    borderRadius: BorderRadius.circular(8),
                  ),
                ),
              ),
            ],
          ),
          const SizedBox(height: 20),

          if (meals.value.isEmpty)
            _buildEmptyMealsState(context, meals)
          else
            ...meals.value.asMap().entries.map((entry) {
              final index = entry.key;
              final meal = entry.value;
              return _buildMealCard(context, meal, index, meals);
            }).toList(),
        ],
      ),
    );
  }

  Widget _buildEmptyMealsState(
    BuildContext context,
    ValueNotifier<List<NutritionTemplateMeal>> meals,
  ) {
    return Container(
      padding: const EdgeInsets.all(32),
      child: Column(
        children: [
          Icon(Icons.restaurant_menu, color: Colors.grey[600], size: 48),
          const SizedBox(height: 16),
          Text(
            'No meals added yet',
            style: TextStyle(
              color: Colors.grey[400],
              fontSize: 16,
              fontWeight: FontWeight.w600,
            ),
          ),
          const SizedBox(height: 8),
          Text(
            'Add meals to organize your nutrition template',
            style: TextStyle(color: Colors.grey[500], fontSize: 14),
            textAlign: TextAlign.center,
          ),
          const SizedBox(height: 20),
          ElevatedButton.icon(
            onPressed: () => _addNewMeal(meals),
            icon: const Icon(Icons.add),
            label: const Text('Add First Meal'),
            style: ElevatedButton.styleFrom(
              backgroundColor: const Color(0xFFFACC15),
              foregroundColor: const Color(0xFF111827),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildMealCard(
    BuildContext context,
    NutritionTemplateMeal meal,
    int index,
    ValueNotifier<List<NutritionTemplateMeal>> meals,
  ) {
    return Container(
      margin: const EdgeInsets.only(bottom: 16),
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: const Color(0xFF111827),
        borderRadius: BorderRadius.circular(12),
        border: Border.all(color: const Color(0xFF374151)),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Meal Header
          Row(
            children: [
              Expanded(
                child: Text(
                  meal.name,
                  style: const TextStyle(
                    color: Colors.white,
                    fontSize: 16,
                    fontWeight: FontWeight.w600,
                  ),
                ),
              ),
              PopupMenuButton<String>(
                icon: Icon(Icons.more_vert, color: Colors.grey[400]),
                color: const Color(0xFF1F2937),
                onSelected: (value) {
                  switch (value) {
                    case 'edit':
                      _editMealName(context, meal, meals);
                      break;
                    case 'delete':
                      _deleteMeal(context, index, meals);
                      break;
                  }
                },
                itemBuilder:
                    (context) => [
                      const PopupMenuItem(
                        value: 'edit',
                        child: Row(
                          children: [
                            Icon(Icons.edit, color: Colors.white, size: 16),
                            SizedBox(width: 8),
                            Text(
                              'Edit Name',
                              style: TextStyle(color: Colors.white),
                            ),
                          ],
                        ),
                      ),
                      const PopupMenuItem(
                        value: 'delete',
                        child: Row(
                          children: [
                            Icon(Icons.delete, color: Colors.red, size: 16),
                            SizedBox(width: 8),
                            Text(
                              'Delete Meal',
                              style: TextStyle(color: Colors.red),
                            ),
                          ],
                        ),
                      ),
                    ],
              ),
            ],
          ),

          const SizedBox(height: 12),

          // Food Items List
          if (meal.items.isEmpty)
            _buildEmptyFoodItemsState(context, meal)
          else
            ...meal.items.map((item) => _buildFoodItemTile(item)).toList(),

          const SizedBox(height: 12),

          // Add Food Item Button
          SizedBox(
            width: double.infinity,
            child: OutlinedButton.icon(
              onPressed: () => _navigateToAddFoodItem(context, meal, meals),
              icon: const Icon(Icons.add, size: 16),
              label: const Text('Add Food Item'),
              style: OutlinedButton.styleFrom(
                foregroundColor: const Color(0xFFFACC15),
                side: const BorderSide(color: Color(0xFFFACC15)),
                padding: const EdgeInsets.symmetric(vertical: 12),
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(8),
                ),
              ),
            ),
          ),
        ],
      ),
    );
  }

  // Additional helper methods
  void _addNewMeal(ValueNotifier<List<NutritionTemplateMeal>> meals) {
    final now = DateTime.now();
    final newMeal = NutritionTemplateMeal(
      id: 'meal_${now.millisecondsSinceEpoch}',
      templateId: '',
      name: 'New Meal',
      mealType: 'custom',
      orderIndex: meals.value.length,
      createdAt: now,
      updatedAt: now,
    );

    meals.value = [...meals.value, newMeal];
  }

  void _editMealName(
    BuildContext context,
    NutritionTemplateMeal meal,
    ValueNotifier<List<NutritionTemplateMeal>> meals,
  ) {
    final controller = TextEditingController(text: meal.name);

    showDialog(
      context: context,
      builder:
          (context) => AlertDialog(
            backgroundColor: const Color(0xFF1F2937),
            title: const Text(
              'Edit Meal Name',
              style: TextStyle(color: Colors.white),
            ),
            content: TextField(
              controller: controller,
              style: const TextStyle(color: Colors.white),
              decoration: InputDecoration(
                hintText: 'Enter meal name',
                hintStyle: TextStyle(color: Colors.grey[400]),
                filled: true,
                fillColor: const Color(0xFF111827),
                border: OutlineInputBorder(
                  borderRadius: BorderRadius.circular(8),
                  borderSide: const BorderSide(color: Color(0xFF374151)),
                ),
              ),
            ),
            actions: [
              TextButton(
                onPressed: () => Navigator.pop(context),
                child: const Text(
                  'Cancel',
                  style: TextStyle(color: Colors.grey),
                ),
              ),
              ElevatedButton(
                onPressed: () {
                  if (controller.text.trim().isNotEmpty) {
                    final updatedMeals =
                        meals.value.map((m) {
                          if (m.id == meal.id) {
                            return NutritionTemplateMeal(
                              id: m.id,
                              templateId: m.templateId,
                              name: controller.text.trim(),
                              mealType: m.mealType,
                              orderIndex: m.orderIndex,
                              createdAt: m.createdAt,
                              updatedAt: DateTime.now(),
                              items: m.items,
                            );
                          }
                          return m;
                        }).toList();

                    meals.value = updatedMeals;
                    Navigator.pop(context);
                  }
                },
                style: ElevatedButton.styleFrom(
                  backgroundColor: const Color(0xFFFACC15),
                  foregroundColor: const Color(0xFF111827),
                ),
                child: const Text('Save'),
              ),
            ],
          ),
    );
  }

  void _deleteMeal(
    BuildContext context,
    int index,
    ValueNotifier<List<NutritionTemplateMeal>> meals,
  ) {
    showDialog(
      context: context,
      builder:
          (context) => AlertDialog(
            backgroundColor: const Color(0xFF1F2937),
            title: const Text(
              'Delete Meal',
              style: TextStyle(color: Colors.white),
            ),
            content: const Text(
              'Are you sure you want to delete this meal? This action cannot be undone.',
              style: TextStyle(color: Colors.grey),
            ),
            actions: [
              TextButton(
                onPressed: () => Navigator.pop(context),
                child: const Text(
                  'Cancel',
                  style: TextStyle(color: Colors.grey),
                ),
              ),
              ElevatedButton(
                onPressed: () {
                  final updatedMeals = List<NutritionTemplateMeal>.from(
                    meals.value,
                  );
                  updatedMeals.removeAt(index);
                  meals.value = updatedMeals;
                  Navigator.pop(context);
                },
                style: ElevatedButton.styleFrom(
                  backgroundColor: Colors.red,
                  foregroundColor: Colors.white,
                ),
                child: const Text('Delete'),
              ),
            ],
          ),
    );
  }

  Widget _buildEmptyFoodItemsState(
    BuildContext context,
    NutritionTemplateMeal meal,
  ) {
    return Container(
      padding: const EdgeInsets.all(16),
      child: Column(
        children: [
          Icon(Icons.fastfood, color: Colors.grey[600], size: 32),
          const SizedBox(height: 8),
          Text(
            'No food items added',
            style: TextStyle(color: Colors.grey[400], fontSize: 14),
          ),
        ],
      ),
    );
  }

  Widget _buildFoodItemTile(NutritionTemplateMealItem item) {
    return Container(
      margin: const EdgeInsets.only(bottom: 8),
      padding: const EdgeInsets.all(12),
      decoration: BoxDecoration(
        color: const Color(0xFF1F2937),
        borderRadius: BorderRadius.circular(8),
        border: Border.all(color: const Color(0xFF374151)),
      ),
      child: Row(
        children: [
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  item.foodName,
                  style: const TextStyle(
                    color: Colors.white,
                    fontSize: 14,
                    fontWeight: FontWeight.w500,
                  ),
                ),
                const SizedBox(height: 4),
                Text(
                  '${item.quantity} ${item.unit}',
                  style: TextStyle(color: Colors.grey[400], fontSize: 12),
                ),
              ],
            ),
          ),
          IconButton(
            onPressed: () {
              // TODO: Remove food item
            },
            icon: Icon(
              Icons.remove_circle_outline,
              color: Colors.red[400],
              size: 20,
            ),
          ),
        ],
      ),
    );
  }

  void _navigateToAddFoodItem(
    BuildContext context,
    NutritionTemplateMeal meal,
    ValueNotifier<List<NutritionTemplateMeal>> meals,
  ) async {
    final result = await Navigator.push<NutritionTemplateMealItem>(
      context,
      MaterialPageRoute(
        builder:
            (context) => AddFoodItemPage(mealId: meal.id, mealName: meal.name),
      ),
    );

    if (result != null) {
      // Add the food item to the meal
      final updatedMeals =
          meals.value.map((m) {
            if (m.id == meal.id) {
              return NutritionTemplateMeal(
                id: m.id,
                templateId: m.templateId,
                name: m.name,
                mealType: m.mealType,
                orderIndex: m.orderIndex,
                createdAt: m.createdAt,
                updatedAt: DateTime.now(),
                items: [...m.items, result],
              );
            }
            return m;
          }).toList();

      meals.value = updatedMeals;
    }
  }

  Widget _buildSectionTitle(String title) {
    return Text(
      title,
      style: const TextStyle(
        color: Colors.white,
        fontSize: 14,
        fontWeight: FontWeight.w600,
      ),
    );
  }

  Widget _buildTextField({
    required TextEditingController controller,
    required String hintText,
    int maxLines = 1,
  }) {
    return TextField(
      controller: controller,
      maxLines: maxLines,
      style: const TextStyle(color: Colors.white),
      decoration: InputDecoration(
        hintText: hintText,
        hintStyle: TextStyle(color: Colors.grey[400]),
        filled: true,
        fillColor: const Color(0xFF111827),
        border: OutlineInputBorder(
          borderRadius: BorderRadius.circular(12),
          borderSide: const BorderSide(color: Color(0xFF374151)),
        ),
        enabledBorder: OutlineInputBorder(
          borderRadius: BorderRadius.circular(12),
          borderSide: const BorderSide(color: Color(0xFF374151)),
        ),
        focusedBorder: OutlineInputBorder(
          borderRadius: BorderRadius.circular(12),
          borderSide: const BorderSide(color: Color(0xFFFACC15)),
        ),
        contentPadding: const EdgeInsets.symmetric(
          horizontal: 16,
          vertical: 12,
        ),
      ),
    );
  }

  Widget _buildGoalDropdown(ValueNotifier<String> selectedGoal) {
    const goals = ['Weight Loss', 'Bulking', 'Keto', 'Maintenance', 'Cutting'];

    return Container(
      width: double.infinity,
      padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 4),
      decoration: BoxDecoration(
        color: const Color(0xFF111827),
        borderRadius: BorderRadius.circular(12),
        border: Border.all(color: const Color(0xFF374151)),
      ),
      child: DropdownButtonHideUnderline(
        child: DropdownButton<String>(
          value: selectedGoal.value,
          dropdownColor: const Color(0xFF1F2937),
          style: const TextStyle(color: Colors.white),
          icon: const Icon(Icons.keyboard_arrow_down, color: Colors.white),
          items:
              goals.map((goal) {
                return DropdownMenuItem<String>(value: goal, child: Text(goal));
              }).toList(),
          onChanged: (value) {
            if (value != null) {
              selectedGoal.value = value;
            }
          },
        ),
      ),
    );
  }
}
