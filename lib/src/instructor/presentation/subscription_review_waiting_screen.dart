import 'dart:async';
import 'package:flutter/material.dart';
import 'package:flutter_hooks/flutter_hooks.dart';
import 'package:hooks_riverpod/hooks_riverpod.dart';
import 'package:go_router/go_router.dart';

import '../../theme/colors.dart';
import '../../shared/constants/app_text_style.dart';
import '../../shared/widgets/text_widget/text_widget.dart';
import '../../shared/extensions/string_hardcoded.dart';
import '../../shared/widgets/app_drawer.dart';
import '../application/subscription_plan_provider.dart';
import '../domain/subscription_plan_models.dart';

/// Screen shown to instructors while their subscription plans are under review
class SubscriptionReviewWaitingScreen extends HookConsumerWidget {
  const SubscriptionReviewWaitingScreen({super.key});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final animationController = useAnimationController(
      duration: const Duration(seconds: 2),
    );

    final scaleAnimation = useAnimation(
      Tween<double>(begin: 0.8, end: 1.2).animate(
        CurvedAnimation(parent: animationController, curve: Curves.easeInOut),
      ),
    );

    // Check submission status every 30 seconds
    useEffect(() {
      animationController.repeat(reverse: true);

      final timer = Timer.periodic(const Duration(seconds: 30), (timer) async {
        try {
          final notifier = ref.read(subscriptionPlanNotifierProvider.notifier);
          await notifier.initializeConfig();

          final planState = ref.read(subscriptionPlanNotifierProvider);

          // If status changed from submitted, navigate accordingly
          if (planState.config?.submissionStatus !=
                  SubmissionStatus.submitted &&
              planState.config?.submissionStatus !=
                  SubmissionStatus.underReview) {
            timer.cancel();
            if (context.mounted) {
              // Navigate back to subscription management
              context.go('/instructor-main');
            }
          }
        } catch (e) {
          // Continue checking even if there's an error
        }
      });

      return () => timer.cancel();
    }, []);

    return Scaffold(
      backgroundColor: const Color(0xFF111827),
      endDrawer: const AppDrawer(),
      body: SafeArea(
        child: Padding(
          padding: const EdgeInsets.all(24.0),
          child: Column(
            children: [
              // Header
              Row(
                children: [
                  // Back button removed - instructor should stay on this screen
                  Expanded(
                    child: TextWidget(
                      'İnceleme Bekleniyor'.hardcoded,
                      style: ATextStyle.large.copyWith(
                        color: AColor.textColor,
                        fontWeight: FontWeight.bold,
                      ),
                      textAlign: TextAlign.center,
                    ),
                  ),
                  Builder(
                    builder:
                        (context) => IconButton(
                          onPressed: () => Scaffold.of(context).openEndDrawer(),
                          icon: const Icon(
                            Icons.menu,
                            color: AColor.textColor,
                            size: 28,
                          ),
                        ),
                  ),
                ],
              ),

              // Content (scrollable)
              Expanded(
                child: SingleChildScrollView(
                  child: Column(
                    children: [
                      const SizedBox(height: 60),
                      Transform.scale(
                        scale: scaleAnimation,
                        child: Container(
                          width: 120,
                          height: 120,
                          decoration: BoxDecoration(
                            color: AColor.fitgoGreen.withValues(alpha: 0.1),
                            shape: BoxShape.circle,
                            border: Border.all(
                              color: AColor.fitgoGreen,
                              width: 3,
                            ),
                          ),
                          child: const Icon(
                            Icons.pending_actions,
                            color: AColor.fitgoGreen,
                            size: 60,
                          ),
                        ),
                      ),
                      const SizedBox(height: 32),
                      TextWidget(
                        'Abonelik Planlarınız İnceleniyor'.hardcoded,
                        style: ATextStyle.semiLarge.copyWith(
                          color: AColor.textColor,
                          fontWeight: FontWeight.bold,
                          fontSize: 24,
                        ),
                        textAlign: TextAlign.center,
                      ),
                      const SizedBox(height: 16),
                      TextWidget(
                        'Planlarınız admin tarafından inceleniyor. Onaylandığında bildirim alacaksınız.'
                            .hardcoded,
                        style: ATextStyle.medium.copyWith(
                          color: AColor.textColor.withValues(alpha: 0.8),
                          height: 1.5,
                        ),
                        textAlign: TextAlign.center,
                      ),
                      const SizedBox(height: 48),

                      // Status Timeline
                      _buildStatusTimeline(),

                      const SizedBox(height: 48),

                      // Info Cards
                      _buildInfoCards(),
                    ],
                  ),
                ),
              ),

              // Bottom Actions
              _buildBottomActions(context, ref),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildStatusTimeline() {
    return Container(
      padding: const EdgeInsets.all(20),
      decoration: BoxDecoration(
        color: const Color(0xFF1F2937),
        borderRadius: BorderRadius.circular(16),
        border: Border.all(color: Colors.grey[700]!),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          TextWidget(
            'İnceleme Süreci'.hardcoded,
            style: ATextStyle.medium.copyWith(
              color: AColor.textColor,
              fontWeight: FontWeight.bold,
            ),
          ),
          const SizedBox(height: 16),
          _buildTimelineItem(
            icon: Icons.check_circle,
            title: 'Planlar Gönderildi',
            subtitle: 'Abonelik planlarınız başarıyla gönderildi',
            isCompleted: true,
          ),
          _buildTimelineItem(
            icon: Icons.pending,
            title: 'Admin İncelemesi',
            subtitle: 'Planlarınız admin tarafından inceleniyor',
            isCompleted: false,
            isActive: true,
          ),
          _buildTimelineItem(
            icon: Icons.verified,
            title: 'Onay & Yayınlama',
            subtitle: 'Onaylandığında planlarınız yayınlanacak',
            isCompleted: false,
          ),
        ],
      ),
    );
  }

  Widget _buildTimelineItem({
    required IconData icon,
    required String title,
    required String subtitle,
    required bool isCompleted,
    bool isActive = false,
  }) {
    final color =
        isCompleted
            ? AColor.fitgoGreen
            : isActive
            ? Colors.orange
            : Colors.grey;

    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 8),
      child: Row(
        children: [
          Container(
            width: 40,
            height: 40,
            decoration: BoxDecoration(
              color: color.withValues(alpha: 0.1),
              shape: BoxShape.circle,
              border: Border.all(color: color, width: 2),
            ),
            child: Icon(icon, color: color, size: 20),
          ),
          const SizedBox(width: 16),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                TextWidget(
                  title,
                  style: ATextStyle.medium.copyWith(
                    color: color,
                    fontWeight: FontWeight.w600,
                  ),
                ),
                const SizedBox(height: 4),
                TextWidget(
                  subtitle,
                  style: ATextStyle.small.copyWith(
                    color: AColor.textColor.withValues(alpha: 0.7),
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildInfoCards() {
    return Column(
      children: [
        _buildInfoCard(
          icon: Icons.access_time,
          title: 'Beklenen Süre',
          description: 'İnceleme süreci genellikle 1-3 iş günü sürer',
          color: Colors.blue,
        ),
        const SizedBox(height: 16),
        _buildInfoCard(
          icon: Icons.notifications_active,
          title: 'Bildirim Alacaksınız',
          description: 'Sonuç hakkında uygulama içi bildirim alacaksınız',
          color: Colors.purple,
        ),
        const SizedBox(height: 16),
        _buildInfoCard(
          icon: Icons.edit_note,
          title: 'Revizyon Gerekirse',
          description: 'Değişiklik gerekirse size geri bildirim verilecek',
          color: Colors.orange,
        ),
      ],
    );
  }

  Widget _buildInfoCard({
    required IconData icon,
    required String title,
    required String description,
    required Color color,
  }) {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: const Color(0xFF1F2937),
        borderRadius: BorderRadius.circular(12),
        border: Border.all(color: Colors.grey[700]!),
      ),
      child: Row(
        children: [
          Container(
            width: 48,
            height: 48,
            decoration: BoxDecoration(
              color: color.withValues(alpha: 0.1),
              borderRadius: BorderRadius.circular(12),
            ),
            child: Icon(icon, color: color, size: 24),
          ),
          const SizedBox(width: 16),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                TextWidget(
                  title,
                  style: ATextStyle.medium.copyWith(
                    color: AColor.textColor,
                    fontWeight: FontWeight.w600,
                  ),
                ),
                const SizedBox(height: 4),
                TextWidget(
                  description,
                  style: ATextStyle.small.copyWith(
                    color: AColor.textColor.withValues(alpha: 0.7),
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildBottomActions(BuildContext context, WidgetRef ref) {
    return Column(
      children: [
        const SizedBox(height: 24),
        SizedBox(
          width: double.infinity,
          child: ElevatedButton.icon(
            onPressed: () {
              context.go('/instructor-main');
            },
            icon: const Icon(Icons.home, size: 20),
            label: const Text('Ana Sayfaya Dön'),
            style: ElevatedButton.styleFrom(
              backgroundColor: const Color(0xFF374151),
              foregroundColor: Colors.white,
              padding: const EdgeInsets.symmetric(vertical: 16),
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(12),
              ),
            ),
          ),
        ),
      ],
    );
  }
}
