import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:hooks_riverpod/hooks_riverpod.dart';
import 'package:flutter_hooks/flutter_hooks.dart';
import 'package:fitgo_app/src/shared/constants/app_text_style.dart';
import 'package:fitgo_app/src/shared/constants/currency_constants.dart';
import '../application/subscription_plan_provider.dart';
import '../application/instructor_profile_repository.dart';
import '../application/instructor_provider.dart';
import '../domain/subscription_plan_models.dart';

class SubscriptionPlanManagementScreen extends HookConsumerWidget {
  const SubscriptionPlanManagementScreen({super.key});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final planState = ref.watch(subscriptionPlanNotifierProvider);

    // Initialize configuration when screen loads
    useEffect(() {
      WidgetsBinding.instance.addPostFrameCallback((_) {
        ref.read(subscriptionPlanNotifierProvider.notifier).initializeConfig();
      });
      return null;
    }, []);

    return Scaffold(
      backgroundColor: const Color(0xFF111827),
      appBar: AppBar(
        backgroundColor: const Color(0xFF111827),
        elevation: 0,
        title: Text(
          'Manage Subscription Plans',
          style: ATextStyle.title.copyWith(color: Colors.white, fontSize: 20),
        ),
        leading: IconButton(
          onPressed: () => Navigator.of(context).pop(),
          icon: const Icon(Icons.arrow_back, color: Colors.white),
        ),
      ),
      body: planState.isLoading
          ? const Center(
              child: CircularProgressIndicator(color: Color(0xFFFACC15)),
            )
          : planState.error != null
              ? _buildErrorState(ref, planState.error!)
              : planState.config != null
                  ? _buildPlanManagementContent(context, ref, planState)
                  : const Center(child: Text('No configuration available')),
    );
  }

  Widget _buildErrorState(WidgetRef ref, String error) {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          const Icon(Icons.error_outline, color: Colors.red, size: 48),
          const SizedBox(height: 16),
          Text(
            'Error loading configuration',
            style: ATextStyle.title.copyWith(color: Colors.white),
          ),
          const SizedBox(height: 8),
          Text(
            error,
            style: ATextStyle.medium.copyWith(color: Colors.grey),
            textAlign: TextAlign.center,
          ),
          const SizedBox(height: 16),
          ElevatedButton(
            onPressed: () => ref
                .read(subscriptionPlanNotifierProvider.notifier)
                .initializeConfig(),
            style: ElevatedButton.styleFrom(
              backgroundColor: const Color(0xFFFACC15),
              foregroundColor: Colors.black,
            ),
            child: const Text('Retry'),
          ),
        ],
      ),
    );
  }

  Widget _buildPlanManagementContent(
    BuildContext context,
    WidgetRef ref,
    SubscriptionPlanState planState,
  ) {
    return Column(
      children: [
        // Status Banner
        if (planState.config!.submissionStatus != SubmissionStatus.draft)
          _buildStatusBanner(
            planState.config!.submissionStatus,
            planState.config!.adminFeedback,
          ),

        // Scrollable Content
        Expanded(
          child: SingleChildScrollView(
            padding: const EdgeInsets.all(16),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                // Profile Completion Check
                _buildProfileCompletionCheck(context, ref),
                const SizedBox(height: 24),

                // Monthly Plan Price Section
                _buildMonthlyPriceSection(context, ref, planState),
                const SizedBox(height: 24),

                // Duration Tabs
                _buildDurationTabs(ref, planState),
                const SizedBox(height: 24),

                // Plan Boxes
                _buildPlanBoxes(context, ref, planState),
                const SizedBox(height: 24),

                // Discount Code Management
                _buildDiscountCodeSection(context, ref, planState),
                const SizedBox(height: 24),

                // Validation Messages
                if (planState.showValidation &&
                    planState.validationResult != null)
                  _buildValidationMessages(planState.validationResult!),

                const SizedBox(height: 100), // Space for floating button
              ],
            ),
          ),
        ),

        // Submit for Review Button
        _buildSubmitButton(context, ref),
      ],
    );
  }

  Widget _buildStatusBanner(SubmissionStatus status, String? feedback) {
    Color backgroundColor;
    Color textColor;
    IconData icon;

    switch (status) {
      case SubmissionStatus.submitted:
      case SubmissionStatus.underReview:
        backgroundColor = Colors.blue.withValues(alpha: 0.1);
        textColor = Colors.blue;
        icon = Icons.hourglass_empty;
        break;
      case SubmissionStatus.approved:
        backgroundColor = Colors.green.withValues(alpha: 0.1);
        textColor = Colors.green;
        icon = Icons.check_circle;
        break;
      case SubmissionStatus.rejected:
      case SubmissionStatus.needsRevision:
        backgroundColor = Colors.red.withValues(alpha: 0.1);
        textColor = Colors.red;
        icon = Icons.error;
        break;
      default:
        backgroundColor = Colors.grey.withValues(alpha: 0.1);
        textColor = Colors.grey;
        icon = Icons.info;
    }

    return Container(
      width: double.infinity,
      padding: const EdgeInsets.all(16),
      color: backgroundColor,
      child: Row(
        children: [
          Icon(icon, color: textColor),
          const SizedBox(width: 12),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  status.displayName,
                  style: ATextStyle.medium.copyWith(
                    color: textColor,
                    fontWeight: FontWeight.w600,
                  ),
                ),
                Text(
                  status.description,
                  style: ATextStyle.small.copyWith(color: textColor),
                ),
                if (feedback != null) ...[
                  const SizedBox(height: 4),
                  Text(
                    'Feedback: $feedback',
                    style: ATextStyle.small.copyWith(color: textColor),
                  ),
                ],
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildMonthlyPriceSection(
    BuildContext context,
    WidgetRef ref,
    SubscriptionPlanState planState,
  ) {
    final earningsController = useTextEditingController(
      text: planState.config!.baseMonthlyPrice.toStringAsFixed(2),
    );

    return Container(
      padding: const EdgeInsets.all(20),
      decoration: BoxDecoration(
        color: const Color(0xFF1F2937),
        borderRadius: BorderRadius.circular(16),
        border: Border.all(color: const Color(0xFF374151)),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Icon(
                Icons.monetization_on,
                color: const Color(0xFFFACC15),
                size: 24,
              ),
              const SizedBox(width: 8),
              Text(
                'Öğrenciden Kazanmak İstediğiniz Miktar',
                style: ATextStyle.title.copyWith(
                  color: Colors.white,
                  fontSize: 18,
                ),
              ),
            ],
          ),
          const SizedBox(height: 12),
          Row(
            children: [
              Text(
                CurrencyConstants.getCurrencySymbol(
                  CurrencyConstants.defaultCurrency,
                ),
                style: const TextStyle(
                  color: Color(0xFFFACC15),
                  fontSize: 24,
                  fontWeight: FontWeight.bold,
                ),
              ),
              const SizedBox(width: 8),
              Expanded(
                child: TextField(
                  controller: earningsController,
                  keyboardType: const TextInputType.numberWithOptions(
                    decimal: true,
                  ),
                  inputFormatters: [
                    FilteringTextInputFormatter.allow(
                      RegExp(r'^\d+\.?\d{0,2}'),
                    ),
                  ],
                  style: const TextStyle(
                    color: Colors.white,
                    fontSize: 24,
                    fontWeight: FontWeight.bold,
                  ),
                  decoration: const InputDecoration(
                    border: InputBorder.none,
                    hintText: '30.00',
                    hintStyle: TextStyle(color: Colors.grey),
                  ),
                  onChanged: (value) {
                    final earnings = double.tryParse(value);
                    if (earnings != null && earnings > 0) {
                      ref
                          .read(subscriptionPlanNotifierProvider.notifier)
                          .updateBaseMonthlyPrice(earnings);
                    }
                  },
                ),
              ),
              const Text(
                '/ay',
                style: TextStyle(color: Colors.grey, fontSize: 16),
              ),
            ],
          ),
          const SizedBox(height: 16),
          Container(
            padding: const EdgeInsets.all(12),
            decoration: BoxDecoration(
              color: const Color(0xFF374151),
              borderRadius: BorderRadius.circular(8),
            ),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  '💡 Nasıl Çalışır?',
                  style: ATextStyle.medium.copyWith(
                    color: const Color(0xFFFACC15),
                    fontWeight: FontWeight.w600,
                  ),
                ),
                const SizedBox(height: 8),
                Text(
                  '• Girdiğiniz miktar sizin net kazancınızdır\n• Basic ve Premium plan fiyatları otomatik hesaplanır\n• Uygulama komisyonu şeffaf şekilde gösterilir',
                  style: ATextStyle.small.copyWith(color: Colors.grey[300]),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildDurationTabs(WidgetRef ref, SubscriptionPlanState planState) {
    return Container(
      padding: const EdgeInsets.all(4),
      decoration: BoxDecoration(
        color: const Color(0xFF1F2937),
        borderRadius: BorderRadius.circular(12),
        border: Border.all(color: const Color(0xFF374151)),
      ),
      child: Row(
        children: PlanDuration.values.map((duration) {
          final isSelected = planState.selectedDuration == duration;
          return Expanded(
            child: GestureDetector(
              onTap: () => ref
                  .read(subscriptionPlanNotifierProvider.notifier)
                  .selectDuration(duration),
              child: Container(
                padding: const EdgeInsets.symmetric(vertical: 12),
                decoration: BoxDecoration(
                  color:
                      isSelected ? const Color(0xFFFACC15) : Colors.transparent,
                  borderRadius: BorderRadius.circular(8),
                ),
                child: Text(
                  duration.displayName,
                  textAlign: TextAlign.center,
                  style: ATextStyle.medium.copyWith(
                    color: isSelected ? Colors.black : Colors.white,
                    fontWeight:
                        isSelected ? FontWeight.w600 : FontWeight.normal,
                  ),
                ),
              ),
            ),
          );
        }).toList(),
      ),
    );
  }

  Widget _buildPlanBoxes(
    BuildContext context,
    WidgetRef ref,
    SubscriptionPlanState planState,
  ) {
    final currentPricing = ref.watch(currentPlanPricingProvider);
    final basicPricing = currentPricing[PlanType.basic];
    final premiumPricing = currentPricing[PlanType.premium];

    return Column(
      children: [
        // Basic Plan
        if (basicPricing != null)
          _buildPlanBox(
            context: context,
            ref: ref,
            planType: PlanType.basic,
            pricing: basicPricing,
            trainerEarnings: planState.config!.baseMonthlyPrice,
          ),
        const SizedBox(height: 16),

        // Premium Plan
        if (premiumPricing != null)
          _buildPlanBox(
            context: context,
            ref: ref,
            planType: PlanType.premium,
            pricing: premiumPricing,
            trainerEarnings: planState.config!.baseMonthlyPrice,
          ),
      ],
    );
  }

  Widget _buildPlanBox({
    required BuildContext context,
    required WidgetRef ref,
    required PlanType planType,
    required PlanPricing pricing,
    required double trainerEarnings,
  }) {
    final isPremium = planType == PlanType.premium;

    return Container(
      padding: const EdgeInsets.all(20),
      decoration: BoxDecoration(
        gradient: isPremium
            ? const LinearGradient(
                colors: [Color(0xFF1F2937), Color(0xFF374151)],
                begin: Alignment.topLeft,
                end: Alignment.bottomRight,
              )
            : null,
        color: isPremium ? null : const Color(0xFF1F2937),
        borderRadius: BorderRadius.circular(16),
        border: Border.all(
          color: isPremium ? const Color(0xFFFACC15) : const Color(0xFF374151),
          width: isPremium ? 2 : 1,
        ),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Header
          Row(
            children: [
              if (isPremium) const Text('👑 ', style: TextStyle(fontSize: 20)),
              Text(
                planType.displayName,
                style: ATextStyle.title.copyWith(
                  color: Colors.white,
                  fontSize: 20,
                  fontWeight: FontWeight.bold,
                ),
              ),
              const Spacer(),
            ],
          ),
          const SizedBox(height: 12),

          // Price
          Row(
            crossAxisAlignment: CrossAxisAlignment.baseline,
            textBaseline: TextBaseline.alphabetic,
            children: [
              Text(
                pricing.monthlyPrice.asCurrency,
                style: ATextStyle.title.copyWith(
                  color: isPremium ? const Color(0xFFFACC15) : Colors.white,
                  fontSize: 32,
                  fontWeight: FontWeight.bold,
                ),
              ),
              Text(
                '/month',
                style: ATextStyle.medium.copyWith(color: Colors.grey),
              ),
            ],
          ),

          // Savings indicator for longer durations
          if (pricing.duration != PlanDuration.monthly) ...[
            const SizedBox(height: 8),
            Container(
              padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
              decoration: BoxDecoration(
                color: const Color(0xFF10B981),
                borderRadius: BorderRadius.circular(12),
              ),
              child: Text(
                'Save ${pricing.calculateSavingsPercentage(pricing.monthlyPrice * (1 / pricing.duration.discountMultiplier)).toStringAsFixed(0)}%',
                style: ATextStyle.small.copyWith(
                  color: Colors.white,
                  fontWeight: FontWeight.w600,
                ),
              ),
            ),
          ],

          const SizedBox(height: 16),

          // Features
          ...pricing.features.map(
            (feature) => Padding(
              padding: const EdgeInsets.only(bottom: 8),
              child: Row(
                children: [
                  Icon(
                    isPremium ? Icons.star : Icons.check,
                    color: isPremium
                        ? const Color(0xFFFACC15)
                        : const Color(0xFF10B981),
                    size: 16,
                  ),
                  const SizedBox(width: 8),
                  Expanded(
                    child: Text(
                      feature,
                      style: ATextStyle.small.copyWith(color: Colors.grey[300]),
                    ),
                  ),
                ],
              ),
            ),
          ),

          const SizedBox(height: 16),

          // Commission Breakdown
          Container(
            padding: const EdgeInsets.all(12),
            decoration: BoxDecoration(
              color: const Color(0xFF374151).withValues(alpha: 0.5),
              borderRadius: BorderRadius.circular(8),
              border: Border.all(color: const Color(0xFF4B5563)),
            ),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  '💰 Gelir Dağılımı',
                  style: ATextStyle.small.copyWith(
                    color: const Color(0xFFFACC15),
                    fontWeight: FontWeight.w600,
                  ),
                ),
                const SizedBox(height: 8),
                _buildCommissionRow(
                  'Sizin Kazancınız',
                  trainerEarnings,
                  const Color(0xFF10B981),
                ),
                _buildCommissionRow(
                  'Uygulama Komisyonu',
                  pricing.monthlyPrice - trainerEarnings,
                  Colors.grey,
                ),
                const Divider(color: Color(0xFF4B5563), height: 16),
                _buildCommissionRow(
                  'Toplam Fiyat',
                  pricing.monthlyPrice,
                  Colors.white,
                  isBold: true,
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildCommissionRow(
    String label,
    double amount,
    Color color, {
    bool isBold = false,
  }) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 2),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          Text(
            label,
            style: ATextStyle.small.copyWith(
              color: color,
              fontWeight: isBold ? FontWeight.w600 : FontWeight.normal,
            ),
          ),
          Text(
            amount.asCurrency,
            style: ATextStyle.small.copyWith(
              color: color,
              fontWeight: isBold ? FontWeight.w600 : FontWeight.normal,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildDiscountCodeSection(
    BuildContext context,
    WidgetRef ref,
    SubscriptionPlanState planState,
  ) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Row(
          children: [
            Text(
              'Discount Codes',
              style: ATextStyle.title.copyWith(
                color: Colors.white,
                fontSize: 18,
              ),
            ),
            const Spacer(),
            IconButton(
              onPressed: () => ref
                  .read(subscriptionPlanNotifierProvider.notifier)
                  .showCreateDiscountCodeDialog(context),
              icon: const Icon(Icons.add, color: Color(0xFFFACC15)),
              style: IconButton.styleFrom(
                backgroundColor: const Color(0xFFFACC15).withValues(alpha: 0.1),
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(8),
                ),
              ),
            ),
          ],
        ),
        const SizedBox(height: 12),
        if (planState.config!.discountCodes.isEmpty)
          Container(
            width: double.infinity,
            padding: const EdgeInsets.all(24),
            decoration: BoxDecoration(
              color: const Color(0xFF1F2937),
              borderRadius: BorderRadius.circular(12),
              border: Border.all(color: const Color(0xFF374151)),
            ),
            child: Column(
              children: [
                const Icon(Icons.local_offer, color: Colors.grey, size: 32),
                const SizedBox(height: 12),
                Text(
                  'No discount codes yet',
                  style: ATextStyle.medium.copyWith(color: Colors.grey),
                ),
                Text(
                  'Create discount codes to attract new clients',
                  style: ATextStyle.small.copyWith(color: Colors.grey),
                ),
              ],
            ),
          )
        else
          ...planState.config!.discountCodes.map(
            (code) => _buildDiscountCodeCard(ref, code),
          ),
      ],
    );
  }

  Widget _buildDiscountCodeCard(WidgetRef ref, DiscountCode code) {
    return Container(
      margin: const EdgeInsets.only(bottom: 8),
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: const Color(0xFF1F2937),
        borderRadius: BorderRadius.circular(12),
        border: Border.all(color: const Color(0xFF374151)),
      ),
      child: Row(
        children: [
          Container(
            padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
            decoration: BoxDecoration(
              color: const Color(0xFFFACC15).withValues(alpha: 0.1),
              borderRadius: BorderRadius.circular(8),
              border: Border.all(color: const Color(0xFFFACC15)),
            ),
            child: Text(
              code.code,
              style: ATextStyle.medium.copyWith(
                color: const Color(0xFFFACC15),
                fontWeight: FontWeight.w600,
              ),
            ),
          ),
          const SizedBox(width: 12),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  '${code.discountPercentage.toStringAsFixed(0)}% off',
                  style: ATextStyle.medium.copyWith(color: Colors.white),
                ),
                Text(
                  'Expires: ${code.expiryDate.toString().split(' ')[0]}',
                  style: ATextStyle.small.copyWith(color: Colors.grey),
                ),
                Text(
                  'Used: ${code.usageCount}/${code.usageLimit}',
                  style: ATextStyle.small.copyWith(color: Colors.grey),
                ),
              ],
            ),
          ),
          IconButton(
            onPressed: () => ref
                .read(subscriptionPlanNotifierProvider.notifier)
                .removeDiscountCode(code.id),
            icon: const Icon(Icons.delete, color: Colors.red, size: 20),
          ),
        ],
      ),
    );
  }

  Widget _buildValidationMessages(ValidationResult validationResult) {
    if (!validationResult.hasErrors && !validationResult.hasWarnings) {
      return const SizedBox.shrink();
    }

    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: validationResult.hasErrors
            ? Colors.red.withValues(alpha: 0.1)
            : Colors.orange.withValues(alpha: 0.1),
        borderRadius: BorderRadius.circular(12),
        border: Border.all(
          color: validationResult.hasErrors ? Colors.red : Colors.orange,
        ),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Icon(
                validationResult.hasErrors ? Icons.error : Icons.warning,
                color: validationResult.hasErrors ? Colors.red : Colors.orange,
              ),
              const SizedBox(width: 8),
              Text(
                validationResult.hasErrors ? 'Errors' : 'Warnings',
                style: ATextStyle.medium.copyWith(
                  color:
                      validationResult.hasErrors ? Colors.red : Colors.orange,
                  fontWeight: FontWeight.w600,
                ),
              ),
            ],
          ),
          const SizedBox(height: 8),
          ...validationResult.errors.map(
            (error) => Padding(
              padding: const EdgeInsets.only(bottom: 4),
              child: Text(
                '• $error',
                style: ATextStyle.small.copyWith(color: Colors.red),
              ),
            ),
          ),
          ...validationResult.warnings.map(
            (warning) => Padding(
              padding: const EdgeInsets.only(bottom: 4),
              child: Text(
                '• $warning',
                style: ATextStyle.small.copyWith(color: Colors.orange),
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildSubmitButton(BuildContext context, WidgetRef ref) {
    final buttonState = ref.watch(submissionButtonStateProvider);

    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: const Color(0xFF111827),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.2),
            blurRadius: 10,
            offset: const Offset(0, -5),
          ),
        ],
      ),
      child: SafeArea(
        child: Container(
          width: double.infinity,
          height: 56,
          decoration: BoxDecoration(
            gradient: buttonState.isEnabled
                ? const LinearGradient(
                    colors: [Color(0xFFFACC15), Color(0xFFF59E0B)],
                    begin: Alignment.topLeft,
                    end: Alignment.bottomRight,
                  )
                : null,
            color: buttonState.isEnabled ? null : Colors.grey,
            borderRadius: BorderRadius.circular(16),
            boxShadow: buttonState.isEnabled
                ? [
                    BoxShadow(
                      color: const Color(0xFFFACC15).withValues(alpha: 0.3),
                      blurRadius: 20,
                      offset: const Offset(0, 8),
                    ),
                  ]
                : null,
          ),
          child: Material(
            color: Colors.transparent,
            child: InkWell(
              onTap: buttonState.isEnabled
                  ? () async {
                      try {
                        final success = await ref
                            .read(subscriptionPlanNotifierProvider.notifier)
                            .submitForReview();

                        if (success && context.mounted) {
                          ScaffoldMessenger.of(context).showSnackBar(
                            const SnackBar(
                              content: Text(
                                'Successfully submitted for review!',
                              ),
                              backgroundColor: Color(0xFF10B981),
                            ),
                          );
                          Navigator.of(context).pop();
                        } else if (context.mounted) {
                          // Check if there's an error in the state
                          final planState =
                              ref.read(subscriptionPlanNotifierProvider);
                          if (planState.error != null) {
                            ScaffoldMessenger.of(context).showSnackBar(
                              SnackBar(
                                content: Text(planState.error!),
                                backgroundColor: Colors.red,
                              ),
                            );
                          }
                        }
                      } catch (e) {
                        if (context.mounted) {
                          ScaffoldMessenger.of(context).showSnackBar(
                            SnackBar(
                              content: Text('Error: ${e.toString()}'),
                              backgroundColor: Colors.red,
                            ),
                          );
                        }
                      }
                    }
                  : null,
              borderRadius: BorderRadius.circular(16),
              child: Center(
                child: buttonState.isLoading
                    ? const SizedBox(
                        width: 24,
                        height: 24,
                        child: CircularProgressIndicator(
                          color: Colors.black,
                          strokeWidth: 2,
                        ),
                      )
                    : Text(
                        buttonState.text,
                        style: ATextStyle.title.copyWith(
                          color: buttonState.isEnabled
                              ? Colors.black
                              : Colors.grey[600],
                          fontSize: 18,
                          fontWeight: FontWeight.bold,
                        ),
                      ),
              ),
            ),
          ),
        ),
      ),
    );
  }

  /// Build profile completion check widget
  Widget _buildProfileCompletionCheck(BuildContext context, WidgetRef ref) {
    final instructorId = ref.watch(currentInstructorIdProvider);

    if (instructorId == null) {
      return const SizedBox.shrink();
    }

    return FutureBuilder<ProfileCompletionStatus>(
      future: InstructorProfileRepository().checkProfileCompletion(
        instructorId,
      ),
      builder: (context, snapshot) {
        if (snapshot.connectionState == ConnectionState.waiting) {
          return Container(
            padding: const EdgeInsets.all(16),
            decoration: BoxDecoration(
              color: const Color(0xFF1E293B),
              borderRadius: BorderRadius.circular(12),
              border: Border.all(color: Colors.grey[700]!),
            ),
            child: const Row(
              children: [
                SizedBox(
                  width: 20,
                  height: 20,
                  child: CircularProgressIndicator(
                    strokeWidth: 2,
                    valueColor: AlwaysStoppedAnimation<Color>(
                      Color(0xFFFACC15),
                    ),
                  ),
                ),
                SizedBox(width: 12),
                Text(
                  'Profil tamamlanma durumu kontrol ediliyor...',
                  style: TextStyle(color: Colors.white),
                ),
              ],
            ),
          );
        }

        if (snapshot.hasError || !snapshot.hasData) {
          return const SizedBox.shrink();
        }

        final completionStatus = snapshot.data!;

        if (completionStatus.isComplete) {
          return Container(
            padding: const EdgeInsets.all(16),
            decoration: BoxDecoration(
              color: Colors.green.withValues(alpha: 0.1),
              borderRadius: BorderRadius.circular(12),
              border: Border.all(color: Colors.green),
            ),
            child: Row(
              children: [
                const Icon(Icons.check_circle, color: Colors.green, size: 24),
                const SizedBox(width: 12),
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      const Text(
                        'Profil Tamamlandı ✅',
                        style: TextStyle(
                          color: Colors.green,
                          fontWeight: FontWeight.bold,
                          fontSize: 16,
                        ),
                      ),
                      Text(
                        'Abonelik planlarınızı gönderebilirsiniz',
                        style: TextStyle(
                          color: Colors.green[700],
                          fontSize: 14,
                        ),
                      ),
                    ],
                  ),
                ),
              ],
            ),
          );
        }

        // Profile incomplete
        return Container(
          padding: const EdgeInsets.all(16),
          decoration: BoxDecoration(
            color: Colors.orange.withValues(alpha: 0.1),
            borderRadius: BorderRadius.circular(12),
            border: Border.all(color: Colors.orange),
          ),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Row(
                children: [
                  const Icon(Icons.warning, color: Colors.orange, size: 24),
                  const SizedBox(width: 12),
                  Expanded(
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        const Text(
                          'Profil Tamamlanması Gerekiyor',
                          style: TextStyle(
                            color: Colors.orange,
                            fontWeight: FontWeight.bold,
                            fontSize: 16,
                          ),
                        ),
                        Text(
                          'Abonelik planı oluşturmadan önce profil bilgilerinizi tamamlayın',
                          style: TextStyle(
                            color: Colors.orange[700],
                            fontSize: 14,
                          ),
                        ),
                      ],
                    ),
                  ),
                ],
              ),
              const SizedBox(height: 12),
              Container(
                padding: const EdgeInsets.all(12),
                decoration: BoxDecoration(
                  color: Colors.orange.withValues(alpha: 0.05),
                  borderRadius: BorderRadius.circular(8),
                ),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      'Eksik Bilgiler:',
                      style: TextStyle(
                        color: Colors.orange[800],
                        fontWeight: FontWeight.w600,
                        fontSize: 14,
                      ),
                    ),
                    const SizedBox(height: 8),
                    ...completionStatus.missingItems.map(
                      (item) => Padding(
                        padding: const EdgeInsets.only(bottom: 4),
                        child: Row(
                          children: [
                            Icon(
                              Icons.circle,
                              size: 6,
                              color: Colors.orange[700],
                            ),
                            const SizedBox(width: 8),
                            Expanded(
                              child: Text(
                                item,
                                style: TextStyle(
                                  color: Colors.orange[700],
                                  fontSize: 13,
                                ),
                              ),
                            ),
                          ],
                        ),
                      ),
                    ),
                  ],
                ),
              ),
              const SizedBox(height: 12),
              SizedBox(
                width: double.infinity,
                child: ElevatedButton.icon(
                  onPressed: () {
                    Navigator.of(context).pop(); // Go back to profile
                  },
                  icon: const Icon(Icons.edit, size: 18),
                  label: const Text('Profili Tamamla'),
                  style: ElevatedButton.styleFrom(
                    backgroundColor: Colors.orange,
                    foregroundColor: Colors.white,
                    padding: const EdgeInsets.symmetric(vertical: 12),
                    shape: RoundedRectangleBorder(
                      borderRadius: BorderRadius.circular(8),
                    ),
                  ),
                ),
              ),
            ],
          ),
        );
      },
    );
  }
}
