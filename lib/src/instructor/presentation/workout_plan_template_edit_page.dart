import 'package:flutter/material.dart';
import 'package:hooks_riverpod/hooks_riverpod.dart';
import 'package:flutter_hooks/flutter_hooks.dart';
import 'package:fitgo_app/src/shared/constants/app_text_style.dart';
import '../domain/workout_plan_template.dart';
import '../application/workout_plan_template_provider.dart';

class WorkoutPlanTemplateEditPage extends HookConsumerWidget {
  final WorkoutPlanTemplate? template;

  const WorkoutPlanTemplateEditPage({
    super.key,
    this.template,
  });

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    // Debug template information
    print('🔍 DEBUG: Template in build method:');
    print('🔍 DEBUG: Template ID: ${template?.id}');
    print('🔍 DEBUG: Template name: ${template?.name}');
    print('🔍 DEBUG: Template is null: ${template == null}');

    final nameController = useTextEditingController(text: template?.name ?? '');
    final notesController = useTextEditingController(text: template?.notes ?? '');
    final selectedTags = useState<List<String>>(template?.tags ?? []);
    final selectedPlanIndex = useState<int>(0);
    final isLoading = useState<bool>(false);
    final currentTemplate = useState<WorkoutPlanTemplate?>(template);
    final plans = useState<List<WorkoutTemplatePlan>>(
      template?.plans ?? [
        WorkoutTemplatePlan(
          id: 'temp-${DateTime.now().millisecondsSinceEpoch}',
          templateId: template?.id ?? '',
          name: 'Plan 1',
          orderIndex: 0,
          createdAt: DateTime.now(),
          updatedAt: DateTime.now(),
          exercises: [],
        ),
      ],
    );



    final availableTags = ref.watch(availableTagsProvider);

    return Scaffold(
      backgroundColor: const Color(0xFF111827),
      appBar: AppBar(
        backgroundColor: const Color(0xFF111827),
        elevation: 0,
        leading: IconButton(
          icon: const Icon(Icons.arrow_back, color: Colors.white),
          onPressed: () => Navigator.of(context).pop(),
        ),
        title: Text(
          template == null ? 'Create Template' : 'Edit Template',
          style: ATextStyle.title.copyWith(
            color: Colors.white,
            fontSize: 20,
            fontWeight: FontWeight.bold,
          ),
        ),
        actions: [
          if (isLoading.value)
            const Padding(
              padding: EdgeInsets.all(16),
              child: SizedBox(
                width: 20,
                height: 20,
                child: CircularProgressIndicator(
                  color: Color(0xFFFACC15),
                  strokeWidth: 2,
                ),
              ),
            ),
        ],
      ),
      body: Column(
        children: [
          Expanded(
            child: SingleChildScrollView(
              padding: const EdgeInsets.all(16),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  // Template Name Section
                  _buildNameSection(nameController),
                  const SizedBox(height: 24),

                  // Tags Section
                  _buildTagsSection(selectedTags, availableTags),
                  const SizedBox(height: 24),

                  // Plan Tabs Section
                  _buildPlanTabsSection(plans, selectedPlanIndex),
                  const SizedBox(height: 16),

                  // Trainer's Notes Section
                  _buildNotesSection(notesController),
                  const SizedBox(height: 24),

                  // Exercises List Section
                  _buildExercisesSection(context, plans, selectedPlanIndex, ref),
                  const SizedBox(height: 100), // Space for bottom button
                ],
              ),
            ),
          ),

          // Complete Template Button
          _buildCompleteButton(
            context,
            ref,
            nameController,
            notesController,
            selectedTags,
            plans,
            isLoading,
          ),
        ],
      ),
    );
  }

  Widget _buildNameSection(TextEditingController nameController) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'Template Name',
          style: ATextStyle.medium.copyWith(
            color: Colors.white,
            fontWeight: FontWeight.w600,
          ),
        ),
        const SizedBox(height: 8),
        Container(
          decoration: BoxDecoration(
            color: const Color(0xFF374151),
            borderRadius: BorderRadius.circular(12),
            border: Border.all(
              color: Colors.white.withOpacity(0.1),
            ),
          ),
          child: TextField(
            controller: nameController,
            style: const TextStyle(color: Colors.white),
            decoration: InputDecoration(
              hintText: 'Enter template name...',
              hintStyle: TextStyle(color: Colors.white.withOpacity(0.6)),
              border: InputBorder.none,
              contentPadding: const EdgeInsets.symmetric(
                horizontal: 16,
                vertical: 12,
              ),
            ),
          ),
        ),
      ],
    );
  }

  Widget _buildTagsSection(ValueNotifier<List<String>> selectedTags, List<String> availableTags) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'Tags',
          style: ATextStyle.medium.copyWith(
            color: Colors.white,
            fontWeight: FontWeight.w600,
          ),
        ),
        const SizedBox(height: 8),
        Wrap(
          spacing: 8,
          runSpacing: 8,
          children: availableTags.map((tag) {
            final isSelected = selectedTags.value.contains(tag);
            return GestureDetector(
              onTap: () {
                if (isSelected) {
                  selectedTags.value = selectedTags.value.where((t) => t != tag).toList();
                } else {
                  selectedTags.value = [...selectedTags.value, tag];
                }
              },
              child: Container(
                padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
                decoration: BoxDecoration(
                  color: isSelected ? const Color(0xFFFACC15) : const Color(0xFF374151),
                  borderRadius: BorderRadius.circular(20),
                  border: Border.all(
                    color: isSelected ? const Color(0xFFFACC15) : Colors.white.withOpacity(0.2),
                  ),
                ),
                child: Text(
                  tag,
                  style: ATextStyle.small.copyWith(
                    color: isSelected ? Colors.black : Colors.white,
                    fontWeight: isSelected ? FontWeight.w600 : FontWeight.normal,
                  ),
                ),
              ),
            );
          }).toList(),
        ),
      ],
    );
  }

  Widget _buildPlanTabsSection(
    ValueNotifier<List<WorkoutTemplatePlan>> plans,
    ValueNotifier<int> selectedPlanIndex,
  ) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Row(
          children: [
            Text(
              'Plans',
              style: ATextStyle.medium.copyWith(
                color: Colors.white,
                fontWeight: FontWeight.w600,
              ),
            ),
            const Spacer(),
            IconButton(
              onPressed: () => _addNewPlan(plans, selectedPlanIndex),
              icon: const Icon(Icons.add, color: Color(0xFFFACC15)),
            ),
          ],
        ),
        const SizedBox(height: 8),
        SizedBox(
          height: 40,
          child: ListView.builder(
            scrollDirection: Axis.horizontal,
            itemCount: plans.value.length,
            itemBuilder: (context, index) {
              final plan = plans.value[index];
              final isSelected = selectedPlanIndex.value == index;
              return GestureDetector(
                onTap: () => selectedPlanIndex.value = index,
                child: Container(
                  margin: const EdgeInsets.only(right: 8),
                  padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
                  decoration: BoxDecoration(
                    color: isSelected ? const Color(0xFFFACC15) : const Color(0xFF374151),
                    borderRadius: BorderRadius.circular(20),
                    border: Border.all(
                      color: isSelected ? const Color(0xFFFACC15) : Colors.white.withOpacity(0.2),
                    ),
                  ),
                  child: Row(
                    mainAxisSize: MainAxisSize.min,
                    children: [
                      Text(
                        plan.name,
                        style: ATextStyle.small.copyWith(
                          color: isSelected ? Colors.black : Colors.white,
                          fontWeight: isSelected ? FontWeight.w600 : FontWeight.normal,
                        ),
                      ),
                      if (plans.value.length > 1) ...[
                        const SizedBox(width: 4),
                        GestureDetector(
                          onTap: () => _deletePlan(plans, selectedPlanIndex, index),
                          child: Icon(
                            Icons.close,
                            size: 16,
                            color: isSelected ? Colors.black : Colors.white.withOpacity(0.7),
                          ),
                        ),
                      ],
                    ],
                  ),
                ),
              );
            },
          ),
        ),
      ],
    );
  }

  Widget _buildNotesSection(TextEditingController notesController) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'Trainer\'s Notes',
          style: ATextStyle.medium.copyWith(
            color: Colors.white,
            fontWeight: FontWeight.w600,
          ),
        ),
        const SizedBox(height: 8),
        Container(
          decoration: BoxDecoration(
            color: const Color(0xFF374151),
            borderRadius: BorderRadius.circular(12),
            border: Border.all(
              color: Colors.white.withOpacity(0.1),
            ),
          ),
          child: TextField(
            controller: notesController,
            style: const TextStyle(color: Colors.white),
            maxLines: 4,
            decoration: InputDecoration(
              hintText: 'Add notes for your students...',
              hintStyle: TextStyle(color: Colors.white.withOpacity(0.6)),
              border: InputBorder.none,
              contentPadding: const EdgeInsets.all(16),
            ),
          ),
        ),
      ],
    );
  }

  Widget _buildExercisesSection(
    BuildContext context,
    ValueNotifier<List<WorkoutTemplatePlan>> plans,
    ValueNotifier<int> selectedPlanIndex,
    WidgetRef ref,
  ) {
    final currentPlan = plans.value[selectedPlanIndex.value];

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Row(
          children: [
            Text(
              'Exercises (${currentPlan.name})',
              style: ATextStyle.medium.copyWith(
                color: Colors.white,
                fontWeight: FontWeight.w600,
              ),
            ),
            const Spacer(),
            ElevatedButton.icon(
              onPressed: () => _navigateToAddExercise(context, ref, plans, selectedPlanIndex),
              style: ElevatedButton.styleFrom(
                backgroundColor: const Color(0xFFFACC15),
                foregroundColor: Colors.black,
                padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
              ),
              icon: const Icon(Icons.add, size: 18),
              label: const Text('Add Exercise'),
            ),
          ],
        ),
        const SizedBox(height: 16),

        if (currentPlan.exercises.isEmpty)
          _buildEmptyExercisesState()
        else
          ...currentPlan.exercises.asMap().entries.map((entry) {
            final index = entry.key;
            final exercise = entry.value;
            return _buildExerciseCard(context, exercise, index, plans, selectedPlanIndex);
          }).toList(),
      ],
    );
  }

  Widget _buildEmptyExercisesState() {
    return Container(
      width: double.infinity,
      padding: const EdgeInsets.all(32),
      decoration: BoxDecoration(
        color: const Color(0xFF1F2937),
        borderRadius: BorderRadius.circular(12),
        border: Border.all(
          color: Colors.white.withOpacity(0.1),
        ),
      ),
      child: Column(
        children: [
          Icon(
            Icons.fitness_center,
            color: Colors.white.withOpacity(0.3),
            size: 48,
          ),
          const SizedBox(height: 16),
          Text(
            'No exercises added yet',
            style: ATextStyle.medium.copyWith(
              color: Colors.white,
              fontWeight: FontWeight.w600,
            ),
          ),
          const SizedBox(height: 8),
          Text(
            'Tap "Add Exercise" to start building your workout plan',
            style: ATextStyle.small.copyWith(
              color: Colors.white.withOpacity(0.7),
            ),
            textAlign: TextAlign.center,
          ),
        ],
      ),
    );
  }

  Widget _buildExerciseCard(
    BuildContext context,
    WorkoutTemplatePlanExercise exercise,
    int index,
    ValueNotifier<List<WorkoutTemplatePlan>> plans,
    ValueNotifier<int> selectedPlanIndex,
  ) {
    return Container(
      margin: const EdgeInsets.only(bottom: 12),
      decoration: BoxDecoration(
        color: const Color(0xFF1F2937),
        borderRadius: BorderRadius.circular(12),
        border: Border.all(
          color: Colors.white.withOpacity(0.1),
        ),
      ),
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Row(
          children: [
            // Exercise Image Placeholder
            Container(
              width: 60,
              height: 60,
              decoration: BoxDecoration(
                color: const Color(0xFF374151),
                borderRadius: BorderRadius.circular(8),
              ),
              child: exercise.imageUrl != null
                  ? ClipRRect(
                      borderRadius: BorderRadius.circular(8),
                      child: Image.network(
                        exercise.imageUrl!,
                        fit: BoxFit.cover,
                        errorBuilder: (context, error, stackTrace) {
                          return const Icon(
                            Icons.fitness_center,
                            color: Colors.white54,
                          );
                        },
                      ),
                    )
                  : const Icon(
                      Icons.fitness_center,
                      color: Colors.white54,
                    ),
            ),
            const SizedBox(width: 16),

            // Exercise Details
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    exercise.exerciseName ?? 'Unknown Exercise',
                    style: ATextStyle.medium.copyWith(
                      color: Colors.white,
                      fontWeight: FontWeight.w600,
                    ),
                  ),
                  const SizedBox(height: 4),
                  Row(
                    children: [
                      if (exercise.sets != null) ...[
                        Text(
                          '${exercise.sets} sets',
                          style: ATextStyle.small.copyWith(
                            color: Colors.white.withOpacity(0.7),
                          ),
                        ),
                        const SizedBox(width: 8),
                      ],
                      if (exercise.reps != null) ...[
                        Text(
                          '${exercise.reps} reps',
                          style: ATextStyle.small.copyWith(
                            color: Colors.white.withOpacity(0.7),
                          ),
                        ),
                        const SizedBox(width: 8),
                      ],
                      if (exercise.restTime != null) ...[
                        Text(
                          '${exercise.restTime}s rest',
                          style: ATextStyle.small.copyWith(
                            color: Colors.white.withOpacity(0.7),
                          ),
                        ),
                      ],
                    ],
                  ),
                  if (exercise.notes != null && exercise.notes!.isNotEmpty) ...[
                    const SizedBox(height: 4),
                    Text(
                      exercise.notes!,
                      style: ATextStyle.small.copyWith(
                        color: Colors.white.withOpacity(0.6),
                        fontStyle: FontStyle.italic,
                      ),
                      maxLines: 2,
                      overflow: TextOverflow.ellipsis,
                    ),
                  ],
                ],
              ),
            ),

            // Action Buttons
            PopupMenuButton<String>(
              icon: Icon(
                Icons.more_vert,
                color: Colors.white.withOpacity(0.7),
              ),
              color: const Color(0xFF374151),
              onSelected: (value) {
                if (value == 'edit') {
                  _editExercise(context, exercise, plans, selectedPlanIndex);
                } else if (value == 'delete') {
                  _deleteExercise(index, plans, selectedPlanIndex);
                }
              },
              itemBuilder: (context) => [
                PopupMenuItem(
                  value: 'edit',
                  child: Row(
                    children: [
                      const Icon(Icons.edit, color: Colors.white, size: 20),
                      const SizedBox(width: 8),
                      Text(
                        'Edit',
                        style: ATextStyle.small.copyWith(color: Colors.white),
                      ),
                    ],
                  ),
                ),
                PopupMenuItem(
                  value: 'delete',
                  child: Row(
                    children: [
                      const Icon(Icons.delete, color: Colors.red, size: 20),
                      const SizedBox(width: 8),
                      Text(
                        'Delete',
                        style: ATextStyle.small.copyWith(color: Colors.red),
                      ),
                    ],
                  ),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildCompleteButton(
    BuildContext context,
    WidgetRef ref,
    TextEditingController nameController,
    TextEditingController notesController,
    ValueNotifier<List<String>> selectedTags,
    ValueNotifier<List<WorkoutTemplatePlan>> plans,
    ValueNotifier<bool> isLoading,
  ) {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: const Color(0xFF111827),
        border: Border(
          top: BorderSide(
            color: Colors.white.withOpacity(0.1),
          ),
        ),
      ),
      child: SizedBox(
        width: double.infinity,
        child: ElevatedButton(
          onPressed: isLoading.value
              ? null
              : () => _saveTemplate(
                    context,
                    ref,
                    nameController,
                    notesController,
                    selectedTags,
                    plans,
                    isLoading,
                  ),
          style: ElevatedButton.styleFrom(
            backgroundColor: const Color(0xFFFACC15),
            foregroundColor: Colors.black,
            padding: const EdgeInsets.symmetric(vertical: 16),
            shape: RoundedRectangleBorder(
              borderRadius: BorderRadius.circular(12),
            ),
          ),
          child: Text(
            template == null ? 'Create Template' : 'Update Template',
            style: ATextStyle.medium.copyWith(
              color: Colors.black,
              fontWeight: FontWeight.w600,
            ),
          ),
        ),
      ),
    );
  }

  // Helper methods
  void _addNewPlan(
    ValueNotifier<List<WorkoutTemplatePlan>> plans,
    ValueNotifier<int> selectedPlanIndex,
  ) {
    final newPlan = WorkoutTemplatePlan(
      id: 'temp-${DateTime.now().millisecondsSinceEpoch}',
      templateId: template?.id ?? '',
      name: 'Plan ${plans.value.length + 1}',
      orderIndex: plans.value.length,
      createdAt: DateTime.now(),
      updatedAt: DateTime.now(),
      exercises: [],
    );

    plans.value = [...plans.value, newPlan];
    selectedPlanIndex.value = plans.value.length - 1;
  }

  void _deletePlan(
    ValueNotifier<List<WorkoutTemplatePlan>> plans,
    ValueNotifier<int> selectedPlanIndex,
    int index,
  ) {
    if (plans.value.length <= 1) return;

    final updatedPlans = List<WorkoutTemplatePlan>.from(plans.value);
    updatedPlans.removeAt(index);
    plans.value = updatedPlans;

    if (selectedPlanIndex.value >= updatedPlans.length) {
      selectedPlanIndex.value = updatedPlans.length - 1;
    }
  }

  void _navigateToAddExercise(
    BuildContext context,
    WidgetRef ref,
    ValueNotifier<List<WorkoutTemplatePlan>> plans,
    ValueNotifier<int> selectedPlanIndex,
  ) async {
    debugPrint('🚀 Navigating to Add Exercise page...');
    final currentPlan = plans.value[selectedPlanIndex.value];

    // Check if plan needs to be saved to Supabase first
    String actualPlanId = currentPlan.id;
    if (currentPlan.id.startsWith('temp-')) {
      try {
        // Debug: Check template ID
        print('🔍 DEBUG: Template ID: ${template?.id}');
        print('🔍 DEBUG: Current plan: ${currentPlan.name}');

        // For temporary plans, store exercises as pending
        if (currentPlan.id.startsWith('temp-')) {
          print('🔍 DEBUG: Storing exercise as pending for temp plan: ${currentPlan.id}');
        }

        // Skip saving plan to Supabase for now - we'll save everything when template is saved
        print('🔍 DEBUG: Using temporary plan ID for now: ${currentPlan.id}');

        // Keep using the temporary plan ID for now
        actualPlanId = currentPlan.id;
      } catch (e) {
        print('❌ DEBUG: Failed to save plan: $e');
        print('❌ DEBUG: Template: ${template?.toJson()}');
        print('❌ DEBUG: Current plan: ${currentPlan.name}');

        if (context.mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Text('Failed to save plan: $e'),
              backgroundColor: Colors.red,
            ),
          );
        }
        return;
      }
    }

    final result = await Navigator.of(context).push<WorkoutTemplatePlanExercise>(
      MaterialPageRoute(
        builder: (context) => _SupabaseAddExercisePage(
          planId: actualPlanId,
          planName: currentPlan.name,
          onExerciseAdded: (exercise) {
            // Add exercise to current plan
            final updatedExercises = <WorkoutTemplatePlanExercise>[...currentPlan.exercises, exercise];

            final updatedPlan = WorkoutTemplatePlan(
              id: actualPlanId,
              templateId: currentPlan.templateId,
              name: currentPlan.name,
              description: currentPlan.description,
              orderIndex: currentPlan.orderIndex,
              createdAt: currentPlan.createdAt,
              updatedAt: DateTime.now(),
              exercises: updatedExercises,
            );

            final updatedPlans = List<WorkoutTemplatePlan>.from(plans.value);
            updatedPlans[selectedPlanIndex.value] = updatedPlan;
            plans.value = updatedPlans;

            print('🔍 DEBUG: Added exercise: ${exercise.exerciseName} to plan: ${currentPlan.id}');
          },
        ),
      ),
    );

    if (result != null && context.mounted) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text('${result.exerciseName} added to ${currentPlan.name}'),
          backgroundColor: Colors.green,
        ),
      );
    }
  }

  void _editExercise(
    BuildContext context,
    WorkoutTemplatePlanExercise exercise,
    ValueNotifier<List<WorkoutTemplatePlan>> plans,
    ValueNotifier<int> selectedPlanIndex,
  ) {
    // TODO: Open edit exercise dialog
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(
        content: Text('Edit Exercise - Coming Soon!'),
        backgroundColor: Color(0xFF3B82F6),
      ),
    );
  }

  void _deleteExercise(
    int index,
    ValueNotifier<List<WorkoutTemplatePlan>> plans,
    ValueNotifier<int> selectedPlanIndex,
  ) {
    final currentPlan = plans.value[selectedPlanIndex.value];
    final updatedExercises = List<WorkoutTemplatePlanExercise>.from(currentPlan.exercises);
    updatedExercises.removeAt(index);

    final updatedPlan = WorkoutTemplatePlan(
      id: currentPlan.id,
      templateId: currentPlan.templateId,
      name: currentPlan.name,
      description: currentPlan.description,
      orderIndex: currentPlan.orderIndex,
      createdAt: currentPlan.createdAt,
      updatedAt: DateTime.now(),
      exercises: updatedExercises,
    );

    final updatedPlans = List<WorkoutTemplatePlan>.from(plans.value);
    updatedPlans[selectedPlanIndex.value] = updatedPlan;
    plans.value = updatedPlans;
  }

  Future<void> _saveTemplate(
    BuildContext context,
    WidgetRef ref,
    TextEditingController nameController,
    TextEditingController notesController,
    ValueNotifier<List<String>> selectedTags,
    ValueNotifier<List<WorkoutTemplatePlan>> plans,
    ValueNotifier<bool> isLoading,
  ) async {
    if (nameController.text.trim().isEmpty) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(
          content: Text('Please enter a template name'),
          backgroundColor: Colors.red,
        ),
      );
      return;
    }

    isLoading.value = true;

    try {
      final notifier = ref.read(workoutPlanTemplateNotifierProvider.notifier);

      if (template == null) {
        // Create new template
        await notifier.createTemplate(
          name: nameController.text.trim(),
          description: null,
          tags: selectedTags.value,
          notes: notesController.text.trim().isEmpty ? null : notesController.text.trim(),
        );

        // Get the created template
        final templates = await ref.read(workoutPlanTemplateRepositoryProvider).getWorkoutPlanTemplates();
        final newTemplate = templates.first;

        // Create plans for the template if any
        final repository = ref.read(workoutPlanTemplateRepositoryProvider);

        for (int i = 0; i < plans.value.length; i++) {
          final plan = plans.value[i];
          if (plan.id.startsWith('temp-')) {
            // Create real plan
            final savedPlan = await repository.addPlanToTemplate(
              templateId: newTemplate.id,
              name: plan.name,
              description: plan.description,
            );

            // Save exercises for this plan
            for (final exercise in plan.exercises) {
              await repository.addExerciseToPlan(
                planId: savedPlan.id,
                exerciseId: exercise.exerciseId,
                sets: exercise.sets,
                reps: exercise.reps,
                restTime: exercise.restTime,
                notes: exercise.notes,
              );
            }

            print('✅ DEBUG: Saved ${plan.exercises.length} exercises for plan: ${plan.name}');
          }
        }

        if (context.mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            const SnackBar(
              content: Text('Template created successfully!'),
              backgroundColor: Colors.green,
            ),
          );
          Navigator.of(context).pop();
        }
      } else {
        // Update existing template
        await notifier.updateTemplate(
          templateId: template!.id,
          name: nameController.text.trim(),
          description: null,
          tags: selectedTags.value,
          notes: notesController.text.trim().isEmpty ? null : notesController.text.trim(),
        );

        if (context.mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            const SnackBar(
              content: Text('Template updated successfully!'),
              backgroundColor: Colors.green,
            ),
          );
          Navigator.of(context).pop();
        }
      }
    } catch (e) {
      if (context.mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Failed to save template: $e'),
            backgroundColor: Colors.red,
          ),
        );
      }
    } finally {
      isLoading.value = false;
    }
  }
}

// Supabase-Integrated Add Exercise Page
class _SupabaseAddExercisePage extends HookConsumerWidget {
  final String planId;
  final String planName;
  final Function(WorkoutTemplatePlanExercise)? onExerciseAdded;

  const _SupabaseAddExercisePage({
    super.key,
    required this.planId,
    required this.planName,
    this.onExerciseAdded,
  });

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final selectedExercise = useState<Exercise?>(null);
    final selectedCategory = useState<String>('All');
    final searchQuery = useState<String>('');
    final searchController = useTextEditingController();
    final setsController = useTextEditingController();
    final repsController = useTextEditingController();
    final restTimeController = useTextEditingController();
    final notesController = useTextEditingController();

    // Update filters when category or search changes
    useEffect(() {
      // Use Future.microtask to avoid modifying provider during build
      Future.microtask(() {
        ref.read(exerciseFiltersProvider.notifier).state = ExerciseFilters(
          categoryFilter: selectedCategory.value,
          searchQuery: searchQuery.value,
        );
      });
      return null;
    }, [selectedCategory.value, searchQuery.value]);

    // Watch filtered exercises from Supabase
    final exercisesAsync = ref.watch(filteredExercisesProvider);
    final muscleGroups = ref.watch(muscleGroupsProvider);

    return Scaffold(
      backgroundColor: const Color(0xFF111827),
      appBar: AppBar(
        backgroundColor: const Color(0xFF111827),
        elevation: 0,
        leading: IconButton(
          icon: const Icon(Icons.arrow_back, color: Colors.white),
          onPressed: () => Navigator.of(context).pop(),
        ),
        title: Text(
          'Add Exercise to $planName',
          style: ATextStyle.title.copyWith(
            color: Colors.white,
            fontSize: 18,
            fontWeight: FontWeight.bold,
          ),
        ),
      ),
      body: Column(
        children: [
          // Search & Filter Section
          Container(
            padding: const EdgeInsets.all(16),
            decoration: BoxDecoration(
              color: const Color(0xFF1F2937),
              border: Border(
                bottom: BorderSide(
                  color: Colors.white.withOpacity(0.1),
                ),
              ),
            ),
            child: Column(
              children: [
                // Search Bar
                Container(
                  decoration: BoxDecoration(
                    color: const Color(0xFF374151),
                    borderRadius: BorderRadius.circular(12),
                  ),
                  child: TextField(
                    controller: searchController,
                    style: const TextStyle(color: Colors.white),
                    onChanged: (value) {
                      Future.microtask(() {
                        searchQuery.value = value;
                      });
                    },
                    decoration: InputDecoration(
                      hintText: 'Search exercises...',
                      hintStyle: TextStyle(color: Colors.white.withOpacity(0.6)),
                      prefixIcon: Icon(
                        Icons.search,
                        color: Colors.white.withOpacity(0.6),
                      ),
                      border: InputBorder.none,
                      contentPadding: const EdgeInsets.symmetric(
                        horizontal: 16,
                        vertical: 12,
                      ),
                    ),
                  ),
                ),
                const SizedBox(height: 16),

                // Category Filter Buttons
                SizedBox(
                  height: 40,
                  child: ListView.builder(
                    scrollDirection: Axis.horizontal,
                    itemCount: muscleGroups.length,
                    itemBuilder: (context, index) {
                      final category = muscleGroups[index];
                      final isSelected = selectedCategory.value == category;
                      return Container(
                        margin: const EdgeInsets.only(right: 8),
                        child: FilterChip(
                          label: Text(
                            category,
                            style: TextStyle(
                              color: isSelected ? Colors.black : Colors.white,
                              fontWeight: isSelected ? FontWeight.w600 : FontWeight.normal,
                              fontSize: 12,
                            ),
                          ),
                          selected: isSelected,
                          onSelected: (selected) {
                            Future.microtask(() {
                              selectedCategory.value = category;
                            });
                          },
                          backgroundColor: const Color(0xFF374151),
                          selectedColor: const Color(0xFFFACC15),
                          checkmarkColor: Colors.black,
                          side: BorderSide.none,
                          padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 8),
                        ),
                      );
                    },
                  ),
                ),
              ],
            ),
          ),

          // Exercise Grid Section
          Expanded(
            child: exercisesAsync.when(
              loading: () => const Center(
                child: CircularProgressIndicator(
                  color: Color(0xFFFACC15),
                ),
              ),
              error: (error, stack) => _buildErrorState(error.toString()),
              data: (exercises) {
                if (exercises.isEmpty) {
                  return _buildEmptyState(searchQuery.value, selectedCategory.value, () {
                    Future.microtask(() {
                      searchQuery.value = '';
                      selectedCategory.value = 'All';
                      searchController.clear();
                    });
                  });
                }

                return GridView.builder(
                  padding: const EdgeInsets.all(16),
                  gridDelegate: const SliverGridDelegateWithFixedCrossAxisCount(
                    crossAxisCount: 2,
                    childAspectRatio: 0.85,
                    crossAxisSpacing: 12,
                    mainAxisSpacing: 12,
                  ),
                  itemCount: exercises.length,
                  itemBuilder: (context, index) {
                    final exercise = exercises[index];
                    final isSelected = selectedExercise.value?.id == exercise.id;

                    return _buildExerciseCard(exercise, isSelected, () {
                      selectedExercise.value = exercise;
                    });
                  },
                );
              },
            ),
          ),

          // Selected Exercise Details Section
          if (selectedExercise.value != null)
            _buildSelectedExerciseDetails(
              selectedExercise.value!,
              setsController,
              repsController,
              restTimeController,
              notesController,
              () => _addExerciseToSupabase(
                context,
                ref,
                selectedExercise.value!,
                setsController,
                repsController,
                restTimeController,
                notesController,
              ),
            ),
        ],
      ),
    );
  }


  // Helper methods for the Supabase Add Exercise Page
  Widget _buildErrorState(String error) {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(
            Icons.error_outline,
            color: Colors.red.shade400,
            size: 64,
          ),
          const SizedBox(height: 16),
          Text(
            'Failed to load exercises',
            style: ATextStyle.medium.copyWith(color: Colors.white),
          ),
          const SizedBox(height: 8),
          Text(
            error,
            style: ATextStyle.small.copyWith(color: Colors.white70),
            textAlign: TextAlign.center,
          ),
        ],
      ),
    );
  }

  // Helper methods for the Supabase Add Exercise Page
  Widget _buildEmptyState(String searchQuery, String selectedCategory, VoidCallback onClearFilters) {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(
            Icons.search_off,
            color: Colors.white.withOpacity(0.3),
            size: 64,
          ),
          const SizedBox(height: 16),
          Text(
            'No exercises found',
            style: ATextStyle.medium.copyWith(
              color: Colors.white,
              fontWeight: FontWeight.w600,
            ),
          ),
          const SizedBox(height: 8),
          Text(
            searchQuery.isNotEmpty
                ? 'Try adjusting your search term'
                : 'Try selecting a different category',
            style: ATextStyle.small.copyWith(
              color: Colors.white.withOpacity(0.7),
            ),
            textAlign: TextAlign.center,
          ),
          const SizedBox(height: 16),
          ElevatedButton(
            onPressed: onClearFilters,
            style: ElevatedButton.styleFrom(
              backgroundColor: const Color(0xFFFACC15),
              foregroundColor: Colors.black,
            ),
            child: const Text('Clear Filters'),
          ),
        ],
      ),
    );
  }

  Widget _buildExerciseCard(Exercise exercise, bool isSelected, VoidCallback onTap) {
    return GestureDetector(
      onTap: onTap,
      child: Container(
        decoration: BoxDecoration(
          color: const Color(0xFF1F2937),
          borderRadius: BorderRadius.circular(16),
          border: Border.all(
            color: isSelected ? const Color(0xFFFACC15) : Colors.white.withOpacity(0.1),
            width: isSelected ? 3 : 1,
          ),
          boxShadow: isSelected ? [
            BoxShadow(
              color: const Color(0xFFFACC15).withOpacity(0.3),
              blurRadius: 8,
              spreadRadius: 2,
            ),
          ] : null,
        ),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Exercise Image
            Expanded(
              flex: 3,
              child: Container(
                width: double.infinity,
                decoration: BoxDecoration(
                  color: const Color(0xFF374151),
                  borderRadius: const BorderRadius.only(
                    topLeft: Radius.circular(16),
                    topRight: Radius.circular(16),
                  ),
                ),
                child: ClipRRect(
                  borderRadius: const BorderRadius.only(
                    topLeft: Radius.circular(16),
                    topRight: Radius.circular(16),
                  ),
                  child: exercise.imageUrl != null
                      ? Image.network(
                          exercise.imageUrl!,
                          fit: BoxFit.cover,
                          errorBuilder: (context, error, stackTrace) {
                            return const Icon(
                              Icons.fitness_center,
                              color: Colors.white54,
                              size: 40,
                            );
                          },
                        )
                      : const Icon(
                          Icons.fitness_center,
                          color: Colors.white54,
                          size: 40,
                        ),
                ),
              ),
            ),

            // Exercise Info
            Expanded(
              flex: 2,
              child: Padding(
                padding: const EdgeInsets.all(12),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      exercise.name,
                      style: ATextStyle.small.copyWith(
                        color: Colors.white,
                        fontWeight: FontWeight.w600,
                      ),
                      maxLines: 2,
                      overflow: TextOverflow.ellipsis,
                    ),
                    const SizedBox(height: 4),
                    if (exercise.muscleGroups.isNotEmpty)
                      Container(
                        padding: const EdgeInsets.symmetric(horizontal: 6, vertical: 2),
                        decoration: BoxDecoration(
                          color: const Color(0xFF3B82F6).withOpacity(0.2),
                          borderRadius: BorderRadius.circular(4),
                        ),
                        child: Text(
                          exercise.muscleGroups.first.toUpperCase(),
                          style: ATextStyle.small.copyWith(
                            color: const Color(0xFF3B82F6),
                            fontSize: 10,
                          ),
                        ),
                      ),
                    const Spacer(),

                    // Select Button
                    Container(
                      width: double.infinity,
                      height: 28,
                      decoration: BoxDecoration(
                        color: isSelected ? const Color(0xFFFACC15) : const Color(0xFF374151),
                        borderRadius: BorderRadius.circular(6),
                      ),
                      child: Center(
                        child: Text(
                          isSelected ? 'Selected' : 'Select',
                          style: ATextStyle.small.copyWith(
                            color: isSelected ? Colors.black : Colors.white,
                            fontWeight: FontWeight.w600,
                            fontSize: 11,
                          ),
                        ),
                      ),
                    ),
                  ],
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildSelectedExerciseDetails(
    Exercise exercise,
    TextEditingController setsController,
    TextEditingController repsController,
    TextEditingController restTimeController,
    TextEditingController notesController,
    VoidCallback onAddExercise,
  ) {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: const Color(0xFF1F2937),
        border: Border(
          top: BorderSide(
            color: Colors.white.withOpacity(0.1),
          ),
        ),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Selected Exercise Preview
          Row(
            children: [
              // Large Exercise Image
              Container(
                width: 80,
                height: 80,
                decoration: BoxDecoration(
                  color: const Color(0xFF374151),
                  borderRadius: BorderRadius.circular(12),
                ),
                child: ClipRRect(
                  borderRadius: BorderRadius.circular(12),
                  child: exercise.imageUrl != null
                      ? Image.network(
                          exercise.imageUrl!,
                          fit: BoxFit.cover,
                          errorBuilder: (context, error, stackTrace) {
                            return const Icon(
                              Icons.fitness_center,
                              color: Colors.white54,
                              size: 32,
                            );
                          },
                        )
                      : const Icon(
                          Icons.fitness_center,
                          color: Colors.white54,
                          size: 32,
                        ),
                ),
              ),
              const SizedBox(width: 16),

              // Exercise Info
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      exercise.name,
                      style: ATextStyle.medium.copyWith(
                        color: Colors.white,
                        fontWeight: FontWeight.w600,
                      ),
                    ),
                    const SizedBox(height: 4),
                    if (exercise.muscleGroups.isNotEmpty)
                      Container(
                        padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                        decoration: BoxDecoration(
                          color: const Color(0xFF3B82F6).withOpacity(0.2),
                          borderRadius: BorderRadius.circular(6),
                        ),
                        child: Text(
                          exercise.muscleGroups.first.toUpperCase(),
                          style: ATextStyle.small.copyWith(
                            color: const Color(0xFF3B82F6),
                            fontSize: 12,
                          ),
                        ),
                      ),
                  ],
                ),
              ),
            ],
          ),
          const SizedBox(height: 24),

          Text(
            'Exercise Configuration',
            style: ATextStyle.medium.copyWith(
              color: Colors.white,
              fontWeight: FontWeight.w600,
            ),
          ),
          const SizedBox(height: 16),

          // Sets, Reps, Rest Time Row
          Row(
            children: [
              Expanded(
                child: _buildNumberField('Sets', setsController),
              ),
              const SizedBox(width: 12),
              Expanded(
                child: _buildNumberField('Reps', repsController),
              ),
              const SizedBox(width: 12),
              Expanded(
                child: _buildNumberField('Rest (sec)', restTimeController),
              ),
            ],
          ),
          const SizedBox(height: 16),

          // Notes Field
          Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                'Notes (Optional)',
                style: ATextStyle.small.copyWith(
                  color: Colors.white.withOpacity(0.7),
                ),
              ),
              const SizedBox(height: 8),
              Container(
                decoration: BoxDecoration(
                  color: const Color(0xFF374151),
                  borderRadius: BorderRadius.circular(8),
                ),
                child: TextField(
                  controller: notesController,
                  style: const TextStyle(color: Colors.white),
                  maxLines: 3,
                  decoration: InputDecoration(
                    hintText: 'Add any special instructions or notes...',
                    hintStyle: TextStyle(color: Colors.white.withOpacity(0.6)),
                    border: InputBorder.none,
                    contentPadding: const EdgeInsets.all(12),
                  ),
                ),
              ),
            ],
          ),
          const SizedBox(height: 24),

          // Add Exercise Button
          SizedBox(
            width: double.infinity,
            child: ElevatedButton(
              onPressed: onAddExercise,
              style: ElevatedButton.styleFrom(
                backgroundColor: const Color(0xFFFACC15),
                foregroundColor: Colors.black,
                padding: const EdgeInsets.symmetric(vertical: 16),
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(12),
                ),
                elevation: 4,
              ),
              child: Row(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  const Icon(Icons.add, color: Colors.black),
                  const SizedBox(width: 8),
                  Text(
                    'Add Exercise to $planName',
                    style: ATextStyle.medium.copyWith(
                      color: Colors.black,
                      fontWeight: FontWeight.w600,
                    ),
                  ),
                ],
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildNumberField(String label, TextEditingController controller) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          label,
          style: ATextStyle.small.copyWith(
            color: Colors.white.withOpacity(0.7),
          ),
        ),
        const SizedBox(height: 4),
        Container(
          decoration: BoxDecoration(
            color: const Color(0xFF374151),
            borderRadius: BorderRadius.circular(8),
          ),
          child: TextField(
            controller: controller,
            style: const TextStyle(color: Colors.white),
            keyboardType: TextInputType.number,
            decoration: InputDecoration(
              hintText: '0',
              hintStyle: TextStyle(color: Colors.white.withOpacity(0.6)),
              border: InputBorder.none,
              contentPadding: const EdgeInsets.all(12),
            ),
          ),
        ),
      ],
    );
  }

  Future<void> _addExerciseToSupabase(
    BuildContext context,
    WidgetRef ref,
    Exercise exercise,
    TextEditingController setsController,
    TextEditingController repsController,
    TextEditingController restTimeController,
    TextEditingController notesController,
  ) async {
    try {
      print('🔍 DEBUG: Adding exercise to plan');
      print('🔍 DEBUG: Plan ID: $planId');
      print('🔍 DEBUG: Exercise ID: ${exercise.id}');
      print('🔍 DEBUG: Exercise name: ${exercise.name}');
      print('🔍 DEBUG: Sets: ${setsController.text}');
      print('🔍 DEBUG: Reps: ${repsController.text}');
      print('🔍 DEBUG: Rest time: ${restTimeController.text}');

      // Check if this is a temporary plan ID
      if (planId.startsWith('temp-')) {
        // Create a temporary exercise object for UI purposes
        final tempExercise = WorkoutTemplatePlanExercise(
          id: 'temp-exercise-${DateTime.now().millisecondsSinceEpoch}',
          planId: planId,
          exerciseId: exercise.id,
          orderIndex: 0,
          sets: int.tryParse(setsController.text),
          reps: int.tryParse(repsController.text),
          restTime: int.tryParse(restTimeController.text),
          notes: notesController.text.trim().isEmpty ? null : notesController.text.trim(),
          createdAt: DateTime.now(),
          exerciseName: exercise.name,
          exerciseDescription: exercise.description,
          muscleGroups: exercise.muscleGroups,
          equipment: exercise.equipment,
          difficultyLevel: exercise.difficultyLevel,
          imageUrl: exercise.imageUrl,
        );

        print('✅ DEBUG: Created temporary exercise: ${tempExercise.id}');

        if (onExerciseAdded != null) {
          onExerciseAdded!(tempExercise);
        }

        // Show success message
        if (context.mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Text('${exercise.name} added to $planName (will be saved when template is saved)'),
              backgroundColor: const Color(0xFF10B981),
            ),
          );
          Navigator.of(context).pop(tempExercise);
        }
        return;
      }

      // For real plan IDs, save to Supabase
      final repository = ref.read(workoutPlanTemplateRepositoryProvider);

      final addedExercise = await repository.addExerciseToPlan(
        planId: planId,
        exerciseId: exercise.id,
        sets: int.tryParse(setsController.text),
        reps: int.tryParse(repsController.text),
        restTime: int.tryParse(restTimeController.text),
        notes: notesController.text.trim().isEmpty ? null : notesController.text.trim(),
      );

      print('✅ DEBUG: Exercise added successfully: ${addedExercise.id}');

      if (onExerciseAdded != null) {
        onExerciseAdded!(addedExercise);
      }

      // Show success message
      if (context.mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('${exercise.name} added to $planName'),
            backgroundColor: const Color(0xFF10B981),
          ),
        );
        Navigator.of(context).pop(addedExercise);
      }
    } catch (e) {
      print('❌ DEBUG: Failed to add exercise: $e');
      print('❌ DEBUG: Plan ID: $planId');
      print('❌ DEBUG: Exercise: ${exercise.name} (${exercise.id})');

      if (context.mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Failed to add exercise: $e'),
            backgroundColor: Colors.red,
          ),
        );
      }
    }
  }
}
