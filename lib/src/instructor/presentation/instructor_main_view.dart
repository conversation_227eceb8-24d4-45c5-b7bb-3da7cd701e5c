import 'package:fitgo_app/src/shared/constants/assets.gen.dart';
import 'package:fitgo_app/src/shared/widgets/image/asset_image.dart';
import 'package:flutter/material.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:hooks_riverpod/hooks_riverpod.dart';
import 'package:flutter_hooks/flutter_hooks.dart';
import 'package:fitgo_app/src/theme/colors.dart';
import 'package:fitgo_app/src/shared/widgets/text_widget/text_widget.dart';
import 'package:fitgo_app/src/shared/constants/app_text_style.dart';
import 'package:fitgo_app/src/shared/extensions/string_hardcoded.dart';
import '../application/instructor_provider.dart';
import 'components/instructor_homepage.dart';
import 'components/instructor_members_tab.dart';
import 'components/instructor_profile_tab.dart';
import 'components/instructor_explore_tab.dart';

class InstructorMainView extends HookConsumerWidget {
  const InstructorMainView({super.key});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final currentTab = ref.watch(instructorBottomNavTabProvider);

    // Initialize dashboard when screen loads
    useEffect(() {
      WidgetsBinding.instance.addPostFrameCallback((_) {
        ref
            .read(instructorDashboardNotifierProvider.notifier)
            .initializeDashboard();
      });
      return null;
    }, []);

    return Scaffold(
      backgroundColor: const Color(0xFF111827),
      body: _buildCurrentTabContent(currentTab),
      bottomNavigationBar: _buildBottomNavigationBar(ref, currentTab),
    );
  }

  Widget _buildCurrentTabContent(int currentTab) {
    switch (currentTab) {
      case 0:
        return const InstructorHomepage();
      case 1:
        return const InstructorMembersTab();
      case 2:
        return const InstructorProfileTab();
      case 3:
        return const InstructorExploreTab();
      default:
        return const InstructorHomepage();
    }
  }

  Widget _buildBottomNavigationBar(WidgetRef ref, int currentTab) {
    return Container(
      decoration: BoxDecoration(
        color: const Color(0xFF1F2937),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withOpacity(0.1),
            blurRadius: 10,
            offset: const Offset(0, -5),
          ),
        ],
      ),
      child: SafeArea(
        child: Padding(
          padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
          child: Row(
            mainAxisAlignment: MainAxisAlignment.spaceAround,
            children: [
              _buildNavItem(
                ref,
                0,
                Assets.icons.bottomHomeIcon.path,
                'Home',
                currentTab,
              ), // Homepage icon for instructor home
              _buildNavItem(
                ref,
                1,
                Assets.icons.bottomMembersIcon.path,
                'Members'.hardcoded,
                currentTab,
              ), // Members icon
              _buildNavItem(
                ref,
                2,
                Assets.icons.bottomProfileIcon.path,
                'Profile'.hardcoded,
                currentTab,
              ), // Profile icon
              _buildNavItem(
                ref,
                3,
                Assets.icons.bottomExploreIcon.path,
                'Explore'.hardcoded,
                currentTab,
              ), // Explore icon
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildNavItem(
    WidgetRef ref,
    int index,
    String iconPath,
    String label,
    int currentTab,
  ) {
    final isSelected = currentTab == index;

    return Expanded(
      child: GestureDetector(
        onTap:
            () =>
                ref.read(instructorBottomNavTabProvider.notifier).state = index,
        behavior: HitTestBehavior.opaque,
        child: AnimatedContainer(
          duration: const Duration(milliseconds: 250),
          curve: Curves.easeInOut,
          padding: const EdgeInsets.symmetric(vertical: 8, horizontal: 6),
          child: Column(
            mainAxisSize: MainAxisSize.min,
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              // Icon with color change when selected
              AnimatedContainer(
                duration: const Duration(milliseconds: 250),
                curve: Curves.easeInOut,
                width: 24,
                height: 24,
                child: AImage(
                  imgPath: iconPath,
                  width: 24,
                  height: 24,
                  color:
                      isSelected
                          ? AColor
                              .fitgoGreen // Green when selected
                          : AColor.grey, // Gray when not selected
                ),
              ),

              const SizedBox(height: 4),

              // Label with green color when selected
              AnimatedDefaultTextStyle(
                duration: const Duration(milliseconds: 250),
                curve: Curves.easeInOut,
                style: TextStyle(
                  color:
                      isSelected
                          ? AColor
                              .fitgoGreen // Green when selected
                          : AColor.grey, // Gray when not selected
                  fontWeight: isSelected ? FontWeight.w600 : FontWeight.w500,
                  fontSize: 11,
                ),
                child: Text(label),
              ),
            ],
          ),
        ),
      ),
    );
  }
}

// Home Tab for Instructors
class InstructorHomeTab extends StatelessWidget {
  const InstructorHomeTab({super.key});

  @override
  Widget build(BuildContext context) {
    return Container(
      padding: const EdgeInsets.all(16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          TextWidget(
            'Dashboard'.hardcoded,
            style: ATextStyle.title.copyWith(color: AColor.white),
          ),
          const SizedBox(height: 20),
          Expanded(
            child: GridView.count(
              crossAxisCount: 2,
              crossAxisSpacing: 16,
              mainAxisSpacing: 16,
              children: [
                _buildDashboardCard(
                  'My Courses'.hardcoded,
                  '12',
                  Icons.fitness_center,
                  AColor.buttonColor,
                ),
                _buildDashboardCard(
                  'Students'.hardcoded,
                  '45',
                  Icons.people,
                  Colors.green,
                ),
                _buildDashboardCard(
                  'Revenue'.hardcoded,
                  '\$2,450',
                  Icons.attach_money,
                  Colors.orange,
                ),
                _buildDashboardCard(
                  'Reviews'.hardcoded,
                  '4.8',
                  Icons.star,
                  Colors.yellow,
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildDashboardCard(
    String title,
    String value,
    IconData icon,
    Color color,
  ) {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: AColor.bottomSheetBackgroundColor,
        borderRadius: BorderRadius.circular(12),
        border: Border.all(color: AColor.textSecondaryColor),
      ),
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(icon, color: color, size: 32),
          const SizedBox(height: 8),
          TextWidget(
            value,
            style: ATextStyle.title.copyWith(color: AColor.white),
          ),
          const SizedBox(height: 4),
          TextWidget(
            title,
            style: ATextStyle.medium.copyWith(color: AColor.grey),
          ),
        ],
      ),
    );
  }
}
