import 'package:flutter/material.dart';
import 'package:flutter_hooks/flutter_hooks.dart';
import 'package:hooks_riverpod/hooks_riverpod.dart';
import 'package:supabase_flutter/supabase_flutter.dart';
import '../application/workout_plan_template_provider.dart';
import '../application/student_workout_assignment_provider.dart';
import '../domain/workout_plan_template.dart';
import '../domain/student_models.dart';
import 'workout_plan_template_edit_page.dart';
import 'manual_exercise_plan_builder_page.dart';

// Provider to check assigned templates for a student-instructor pair from database
final databaseAssignedTemplatesProvider =
    FutureProvider.family<Set<String>, Map<String, String>>((
      ref,
      params,
    ) async {
      final studentId = params['studentId']!;
      final instructorId = params['instructorId']!;

      try {
        final supabase = Supabase.instance.client;
        final response = await supabase
            .from('student_workout_plans')
            .select('template_id')
            .eq('student_id', studentId)
            .eq('instructor_id', instructorId)
            .not('template_id', 'is', null);

        final templateIds =
            response.map((row) => row['template_id'] as String).toSet();

        return templateIds;
      } catch (e) {
        print('Error fetching assigned templates: $e');
        return <String>{};
      }
    });

// Local state for tracking newly assigned templates in this session
final sessionAssignedTemplatesProvider = StateProvider<Set<String>>(
  (ref) => {},
);

class AssignExerciseToStudentPage extends HookConsumerWidget {
  final InstructorStudent student;
  final String instructorId;

  const AssignExerciseToStudentPage({
    super.key,
    required this.student,
    required this.instructorId,
  });

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final searchController = useTextEditingController();
    final searchQuery = useState<String>('');
    final selectedFilter = useState<String>('All');
    final isLoading = useState<bool>(false);

    final workoutTemplatesAsync = ref.watch(
      workoutPlanTemplatesWithInstructorProvider(instructorId),
    );

    return Scaffold(
      backgroundColor: const Color(0xFF111827),
      appBar: AppBar(
        backgroundColor: const Color(0xFF111827),
        elevation: 0,
        leading: IconButton(
          icon: const Icon(Icons.arrow_back, color: Colors.white),
          onPressed: () => Navigator.pop(context),
        ),
        title: const Text(
          'Assign Exercise',
          style: TextStyle(
            color: Colors.white,
            fontSize: 20,
            fontWeight: FontWeight.w600,
          ),
        ),
        actions: [
          IconButton(
            icon: const Icon(Icons.add, color: Color(0xFFFACC15)),
            onPressed: () => _navigateToCreateCustomPlan(context, ref),
          ),
        ],
      ),
      body: Column(
        children: [
          // Student Info Header
          _buildStudentHeader(),

          // Search Bar
          _buildSearchBar(searchController, searchQuery),

          // Filter Chips
          _buildFilterChips(selectedFilter),

          // Templates List
          Expanded(
            child: workoutTemplatesAsync.when(
              data:
                  (templates) => _buildTemplatesList(
                    context,
                    ref,
                    templates,
                    searchQuery.value,
                    selectedFilter.value,
                    isLoading,
                  ),
              loading: () => _buildLoadingState(),
              error:
                  (error, stack) =>
                      _buildErrorState(context, ref, error.toString()),
            ),
          ),

          // Custom Plan Button
          _buildCustomPlanButton(context, ref, isLoading),
        ],
      ),
    );
  }

  Widget _buildStudentHeader() {
    return Container(
      margin: const EdgeInsets.all(16),
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: const Color(0xFF1F2937),
        borderRadius: BorderRadius.circular(12),
        border: Border.all(color: const Color(0xFF374151)),
      ),
      child: Row(
        children: [
          CircleAvatar(
            radius: 24,
            backgroundColor: const Color(0xFFFACC15),
            backgroundImage:
                student.profileImageUrl != null
                    ? NetworkImage(student.profileImageUrl!)
                    : null,
            child:
                student.profileImageUrl == null
                    ? Text(
                      student.name.isNotEmpty
                          ? student.name[0].toUpperCase()
                          : 'S',
                      style: const TextStyle(
                        color: Color(0xFF111827),
                        fontWeight: FontWeight.bold,
                        fontSize: 18,
                      ),
                    )
                    : null,
          ),
          const SizedBox(width: 12),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  student.name,
                  style: const TextStyle(
                    color: Colors.white,
                    fontSize: 16,
                    fontWeight: FontWeight.w600,
                  ),
                ),
                const SizedBox(height: 4),
                Text(
                  'Assigning workout plan',
                  style: TextStyle(color: Colors.grey[400], fontSize: 14),
                ),
              ],
            ),
          ),
          Container(
            padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
            decoration: BoxDecoration(
              color:
                  student.planType == StudentPlanType.premium
                      ? const Color(0xFFFACC15).withValues(alpha: 0.2)
                      : const Color(0xFF374151),
              borderRadius: BorderRadius.circular(6),
            ),
            child: Text(
              student.planType.displayName,
              style: TextStyle(
                color:
                    student.planType == StudentPlanType.premium
                        ? const Color(0xFFFACC15)
                        : Colors.grey[300],
                fontSize: 12,
                fontWeight: FontWeight.w500,
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildSearchBar(
    TextEditingController searchController,
    ValueNotifier<String> searchQuery,
  ) {
    return Container(
      margin: const EdgeInsets.symmetric(horizontal: 16),
      child: TextField(
        controller: searchController,
        style: const TextStyle(color: Colors.white),
        decoration: InputDecoration(
          hintText: 'Search workout templates...',
          hintStyle: TextStyle(color: Colors.grey[400]),
          prefixIcon: Icon(Icons.search, color: Colors.grey[400]),
          filled: true,
          fillColor: const Color(0xFF1F2937),
          border: OutlineInputBorder(
            borderRadius: BorderRadius.circular(12),
            borderSide: const BorderSide(color: Color(0xFF374151)),
          ),
          enabledBorder: OutlineInputBorder(
            borderRadius: BorderRadius.circular(12),
            borderSide: const BorderSide(color: Color(0xFF374151)),
          ),
          focusedBorder: OutlineInputBorder(
            borderRadius: BorderRadius.circular(12),
            borderSide: const BorderSide(color: Color(0xFFFACC15)),
          ),
        ),
        onChanged: (value) {
          searchQuery.value = value;
        },
      ),
    );
  }

  Widget _buildFilterChips(ValueNotifier<String> selectedFilter) {
    final filters = ['All', 'Strength', 'Cardio', 'Flexibility'];

    return Container(
      height: 50,
      margin: const EdgeInsets.symmetric(vertical: 16),
      child: ListView.builder(
        scrollDirection: Axis.horizontal,
        padding: const EdgeInsets.symmetric(horizontal: 16),
        itemCount: filters.length,
        itemBuilder: (context, index) {
          final filter = filters[index];
          final isSelected = selectedFilter.value == filter;

          return Container(
            margin: const EdgeInsets.only(right: 8),
            child: FilterChip(
              label: Text(filter),
              selected: isSelected,
              onSelected: (selected) {
                selectedFilter.value = filter;
              },
              backgroundColor: const Color(0xFF1F2937),
              selectedColor: const Color(0xFFFACC15).withOpacity(0.2),
              labelStyle: TextStyle(
                color: isSelected ? const Color(0xFFFACC15) : Colors.grey[300],
                fontWeight: isSelected ? FontWeight.w600 : FontWeight.normal,
              ),
              side: BorderSide(
                color:
                    isSelected
                        ? const Color(0xFFFACC15)
                        : const Color(0xFF374151),
              ),
            ),
          );
        },
      ),
    );
  }

  Widget _buildLoadingState() {
    return const Center(
      child: CircularProgressIndicator(
        valueColor: AlwaysStoppedAnimation<Color>(Color(0xFFFACC15)),
      ),
    );
  }

  Widget _buildErrorState(BuildContext context, WidgetRef ref, String error) {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(Icons.error_outline, color: Colors.red[400], size: 48),
          const SizedBox(height: 16),
          Text(
            'Error loading templates',
            style: TextStyle(
              color: Colors.red[400],
              fontSize: 18,
              fontWeight: FontWeight.w600,
            ),
          ),
          const SizedBox(height: 8),
          Text(
            error,
            style: TextStyle(color: Colors.grey[400], fontSize: 14),
            textAlign: TextAlign.center,
          ),
          const SizedBox(height: 16),
          ElevatedButton(
            onPressed:
                () => ref.refresh(
                  workoutPlanTemplatesWithInstructorProvider(instructorId),
                ),
            child: const Text('Retry'),
          ),
        ],
      ),
    );
  }

  Widget _buildCustomPlanButton(
    BuildContext context,
    WidgetRef ref,
    ValueNotifier<bool> isLoading,
  ) {
    return Container(
      padding: const EdgeInsets.all(16),
      child: SizedBox(
        width: double.infinity,
        child: ElevatedButton.icon(
          onPressed:
              isLoading.value
                  ? null
                  : () => _createCustomPlan(context, ref, isLoading),
          icon: const Icon(Icons.add),
          label: const Text('Assign Custom Plan'),
          style: ElevatedButton.styleFrom(
            backgroundColor: const Color(0xFF374151),
            foregroundColor: Colors.white,
            padding: const EdgeInsets.symmetric(vertical: 16),
            shape: RoundedRectangleBorder(
              borderRadius: BorderRadius.circular(12),
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildTemplatesList(
    BuildContext context,
    WidgetRef ref,
    List<WorkoutPlanTemplate> templates,
    String searchQuery,
    String selectedFilter,
    ValueNotifier<bool> isLoading,
  ) {
    // Filter templates
    var filteredTemplates =
        templates.where((template) {
          final matchesSearch =
              searchQuery.isEmpty ||
              template.name.toLowerCase().contains(searchQuery.toLowerCase());

          final matchesFilter =
              selectedFilter == 'All' ||
              template.tags.any(
                (tag) =>
                    tag.toLowerCase().contains(selectedFilter.toLowerCase()),
              );

          return matchesSearch && matchesFilter;
        }).toList();

    if (filteredTemplates.isEmpty) {
      return _buildEmptyState(
        searchQuery.isNotEmpty || selectedFilter != 'All',
      );
    }

    return ListView.builder(
      padding: const EdgeInsets.symmetric(horizontal: 16),
      itemCount: filteredTemplates.length,
      itemBuilder: (context, index) {
        final template = filteredTemplates[index];
        return _buildTemplateCard(context, ref, template, isLoading);
      },
    );
  }

  Widget _buildEmptyState(bool isFiltered) {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(
            Icons.fitness_center_outlined,
            color: Colors.grey[400],
            size: 64,
          ),
          const SizedBox(height: 16),
          Text(
            isFiltered ? 'No templates found' : 'No workout templates yet',
            style: TextStyle(
              color: Colors.grey[400],
              fontSize: 18,
              fontWeight: FontWeight.w600,
            ),
          ),
          const SizedBox(height: 8),
          Text(
            isFiltered
                ? 'Try adjusting your search or filters'
                : 'Create your first workout template',
            style: TextStyle(color: Colors.grey[500], fontSize: 14),
            textAlign: TextAlign.center,
          ),
        ],
      ),
    );
  }

  Widget _buildTemplateCard(
    BuildContext context,
    WidgetRef ref,
    WorkoutPlanTemplate template,
    ValueNotifier<bool> isLoading,
  ) {
    final exerciseCount = template.plans.fold<int>(
      0,
      (total, plan) => total + plan.exercises.length,
    );

    final daysSinceUpdate =
        DateTime.now().difference(template.updatedAt).inDays;
    final lastModified =
        daysSinceUpdate == 0
            ? 'Updated today'
            : daysSinceUpdate == 1
            ? 'Updated 1 day ago'
            : 'Updated $daysSinceUpdate days ago';

    return Container(
      margin: const EdgeInsets.only(bottom: 12),
      decoration: BoxDecoration(
        color: const Color(0xFF1F2937),
        borderRadius: BorderRadius.circular(12),
        border: Border.all(color: const Color(0xFF374151)),
      ),
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Header
            Row(
              children: [
                Expanded(
                  child: Text(
                    template.name,
                    style: const TextStyle(
                      color: Colors.white,
                      fontSize: 16,
                      fontWeight: FontWeight.w600,
                    ),
                  ),
                ),
                Container(
                  padding: const EdgeInsets.symmetric(
                    horizontal: 8,
                    vertical: 4,
                  ),
                  decoration: BoxDecoration(
                    color: const Color(0xFF374151),
                    borderRadius: BorderRadius.circular(6),
                  ),
                  child: Text(
                    '$exerciseCount exercises',
                    style: TextStyle(
                      color: Colors.grey[300],
                      fontSize: 12,
                      fontWeight: FontWeight.w500,
                    ),
                  ),
                ),
              ],
            ),

            if (template.description != null) ...[
              const SizedBox(height: 8),
              Text(
                template.description!,
                style: TextStyle(color: Colors.grey[400], fontSize: 14),
                maxLines: 2,
                overflow: TextOverflow.ellipsis,
              ),
            ],

            // Tags
            if (template.tags.isNotEmpty) ...[
              const SizedBox(height: 12),
              Wrap(
                spacing: 6,
                runSpacing: 6,
                children:
                    template.tags.map((tag) {
                      return Container(
                        padding: const EdgeInsets.symmetric(
                          horizontal: 8,
                          vertical: 4,
                        ),
                        decoration: BoxDecoration(
                          color: const Color(0xFFFACC15).withValues(alpha: 0.2),
                          borderRadius: BorderRadius.circular(6),
                        ),
                        child: Text(
                          tag,
                          style: const TextStyle(
                            color: Color(0xFFFACC15),
                            fontSize: 12,
                            fontWeight: FontWeight.w500,
                          ),
                        ),
                      );
                    }).toList(),
              ),
            ],

            const SizedBox(height: 12),

            // Footer
            Row(
              children: [
                Expanded(
                  child: Text(
                    lastModified,
                    style: TextStyle(color: Colors.grey[500], fontSize: 12),
                  ),
                ),
                Consumer(
                  builder: (context, ref, child) {
                    final assignedTemplatesAsync = ref.watch(
                      databaseAssignedTemplatesProvider({
                        'studentId': student.id,
                        'instructorId': instructorId,
                      }),
                    );
                    final sessionAssigned = ref.watch(
                      sessionAssignedTemplatesProvider,
                    );

                    final isAssigned = assignedTemplatesAsync.when(
                      data:
                          (assignedTemplates) =>
                              assignedTemplates.contains(template.id) ||
                              sessionAssigned.contains(template.id),
                      loading: () => false,
                      error: (_, __) => sessionAssigned.contains(template.id),
                    );

                    return ElevatedButton(
                      onPressed:
                          isLoading.value
                              ? null
                              : isAssigned
                              ? () => _showAssignedDialog(context, template)
                              : () => _assignTemplate(
                                context,
                                ref,
                                template,
                                isLoading,
                              ),
                      style: ElevatedButton.styleFrom(
                        backgroundColor:
                            isLoading.value
                                ? Colors.grey
                                : isAssigned
                                ? Colors.green
                                : const Color(0xFFFACC15),
                        foregroundColor: const Color(0xFF111827),
                        padding: const EdgeInsets.symmetric(
                          horizontal: 16,
                          vertical: 8,
                        ),
                        shape: RoundedRectangleBorder(
                          borderRadius: BorderRadius.circular(8),
                        ),
                      ),
                      child:
                          isLoading.value
                              ? const SizedBox(
                                width: 16,
                                height: 16,
                                child: CircularProgressIndicator(
                                  strokeWidth: 2,
                                  valueColor: AlwaysStoppedAnimation<Color>(
                                    Color(0xFF111827),
                                  ),
                                ),
                              )
                              : Row(
                                mainAxisSize: MainAxisSize.min,
                                children: [
                                  if (isAssigned) ...[
                                    const Icon(Icons.check, size: 16),
                                    const SizedBox(width: 4),
                                  ],
                                  Text(
                                    isAssigned ? 'Applied' : 'Apply',
                                    style: const TextStyle(
                                      fontSize: 14,
                                      fontWeight: FontWeight.w600,
                                    ),
                                  ),
                                ],
                              ),
                    );
                  },
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  // Navigation and action methods
  void _navigateToCreateCustomPlan(BuildContext context, WidgetRef ref) {
    Navigator.push(
      context,
      MaterialPageRoute(
        builder:
            (context) => WorkoutPlanTemplateEditPage(
              // Create a new template specifically for this student
              template: null,
            ),
      ),
    );
  }

  Future<void> _createCustomPlan(
    BuildContext context,
    WidgetRef ref,
    ValueNotifier<bool> isLoading,
  ) async {
    isLoading.value = true;

    try {
      final notifier = ref.read(
        studentWorkoutAssignmentNotifierProvider.notifier,
      );

      final assignment = await notifier.createCustomWorkoutPlan(
        studentId: student.id,
        instructorId: instructorId,
        planName: 'Custom Plan for ${student.name}',
        description: 'Personalized workout plan',
      );

      if (assignment != null && context.mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('Custom workout plan created successfully!'),
            backgroundColor: Colors.green,
          ),
        );

        // Navigate to manual plan builder
        Navigator.push(
          context,
          MaterialPageRoute(
            builder:
                (context) => ManualExercisePlanBuilderPage(
                  student: student,
                  instructorId: instructorId,
                  existingAssignment: assignment,
                ),
          ),
        );
      }
    } catch (e) {
      if (context.mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Error creating custom plan: $e'),
            backgroundColor: Colors.red,
          ),
        );
      }
    } finally {
      isLoading.value = false;
    }
  }

  void _showAssignedDialog(BuildContext context, WorkoutPlanTemplate template) {
    showDialog(
      context: context,
      builder:
          (context) => AlertDialog(
            backgroundColor: const Color(0xFF1F2937),
            title: const Text(
              'Already Applied',
              style: TextStyle(color: Colors.white),
            ),
            content: Text(
              'The template "${template.name}" is already assigned to ${student.name}.\n\nWould you like to view the assigned plan or assign a different template?',
              style: const TextStyle(color: Colors.white70),
            ),
            actions: [
              TextButton(
                onPressed: () => Navigator.pop(context),
                child: const Text(
                  'Cancel',
                  style: TextStyle(color: Colors.grey),
                ),
              ),
              ElevatedButton(
                onPressed: () {
                  Navigator.pop(context);
                  // TODO: Navigate to view assigned plan
                },
                style: ElevatedButton.styleFrom(
                  backgroundColor: const Color(0xFFFACC15),
                  foregroundColor: const Color(0xFF111827),
                ),
                child: const Text('View Plan'),
              ),
            ],
          ),
    );
  }

  Future<void> _assignTemplate(
    BuildContext context,
    WidgetRef ref,
    WorkoutPlanTemplate template,
    ValueNotifier<bool> isLoading,
  ) async {
    print('🎯 Apply button pressed for template: ${template.name}');
    print('👤 Student: ${student.name} (${student.id})');
    print('👨‍🏫 Instructor: $instructorId');

    isLoading.value = true;

    // Show immediate feedback
    if (context.mounted) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text('Assigning "${template.name}" to ${student.name}...'),
          backgroundColor: Colors.orange,
          duration: const Duration(seconds: 2),
        ),
      );
    }

    try {
      final notifier = ref.read(
        studentWorkoutAssignmentNotifierProvider.notifier,
      );

      print('🔄 Starting assignment process...');
      final assignment = await notifier.assignWorkoutTemplate(
        templateId: template.id,
        studentId: student.id,
        instructorId: instructorId,
      );

      print(
        '📊 Assignment result: ${assignment != null ? 'SUCCESS' : 'FAILED'}',
      );

      if (assignment != null && context.mounted) {
        print('✅ Assignment successful, showing success message');

        // Mark template as assigned in session
        ref
            .read(sessionAssignedTemplatesProvider.notifier)
            .update((state) => {...state, template.id});

        // Refresh the assigned templates provider to get updated data
        ref.invalidate(databaseAssignedTemplatesProvider);

        ScaffoldMessenger.of(context).clearSnackBars();
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Row(
              children: [
                const Icon(Icons.check_circle, color: Colors.white),
                const SizedBox(width: 8),
                Expanded(
                  child: Text(
                    '✅ "${template.name}" successfully assigned to ${student.name}!',
                    style: const TextStyle(fontWeight: FontWeight.w600),
                  ),
                ),
              ],
            ),
            backgroundColor: Colors.green,
            duration: const Duration(seconds: 4),
          ),
        );

        // Wait a bit before navigating back
        await Future.delayed(const Duration(milliseconds: 1500));
        if (context.mounted) {
          print('🔙 Navigating back to previous screen');
          Navigator.pop(context);
        }
      } else {
        print('❌ Assignment failed - assignment is null');
        if (context.mounted) {
          ScaffoldMessenger.of(context).clearSnackBars();
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: const Row(
                children: [
                  Icon(Icons.error, color: Colors.white),
                  SizedBox(width: 8),
                  Text('Assignment failed - please try again'),
                ],
              ),
              backgroundColor: Colors.red,
              duration: const Duration(seconds: 3),
            ),
          );
        }
      }
    } catch (e) {
      print('💥 Exception during assignment: $e');
      if (context.mounted) {
        ScaffoldMessenger.of(context).clearSnackBars();
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Row(
              children: [
                const Icon(Icons.error, color: Colors.white),
                const SizedBox(width: 8),
                Expanded(
                  child: Text(
                    'Error: ${e.toString().contains('Exception:') ? e.toString().split('Exception: ').last : e.toString()}',
                    style: const TextStyle(fontWeight: FontWeight.w600),
                  ),
                ),
              ],
            ),
            backgroundColor: Colors.red,
            duration: const Duration(seconds: 5),
          ),
        );
      }
    } finally {
      print('🏁 Assignment process completed');
      isLoading.value = false;
    }
  }
}
