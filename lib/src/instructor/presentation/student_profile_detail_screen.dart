import 'package:flutter/material.dart';
import 'package:hooks_riverpod/hooks_riverpod.dart';
import 'package:fitgo_app/src/shared/constants/app_text_style.dart';
import 'package:fitgo_app/src/shared/extensions/build_context/screen_util_ext.dart';
import '../application/students_provider.dart';
import '../application/instructor_provider.dart';
import '../domain/student_models.dart';
import 'assign_exercise_to_student_page.dart';
import 'assign_nutrition_to_student_page.dart';
import 'assign_supplement_to_student_page.dart';
import '../../shopping_list/presentation/assign_shopping_list_page.dart';
import '../../conversations/presentation/instructor_questions_inbox_screen.dart';

class StudentProfileDetailScreen extends HookConsumerWidget {
  final String studentId;

  const StudentProfileDetailScreen({super.key, required this.studentId});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final profileDetailAsync = ref.watch(
      studentProfileDetailProvider(studentId),
    );

    return Scaffold(
      backgroundColor: const Color(0xFF111827),
      appBar: AppBar(
        backgroundColor: const Color(0xFF111827),
        elevation: 0,
        leading: IconButton(
          icon: const Icon(Icons.arrow_back, color: Colors.white),
          onPressed: () => Navigator.of(context).pop(),
        ),
        title: Text(
          'Student Profile',
          style: ATextStyle.large.copyWith(color: Colors.white),
        ),
      ),
      body: profileDetailAsync.when(
        loading:
            () => const Center(
              child: CircularProgressIndicator(color: Color(0xFFFACC15)),
            ),
        error:
            (error, stack) => Center(
              child: Column(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  const Icon(Icons.error_outline, color: Colors.red, size: 48),
                  const SizedBox(height: 16),
                  Text(
                    'Error loading profile',
                    style: ATextStyle.medium.copyWith(color: Colors.white),
                  ),
                  const SizedBox(height: 8),
                  Text(
                    error.toString(),
                    style: ATextStyle.small.copyWith(color: Colors.grey),
                    textAlign: TextAlign.center,
                  ),
                  const SizedBox(height: 16),
                  ElevatedButton(
                    onPressed:
                        () => ref.refresh(
                          studentProfileDetailProvider(studentId),
                        ),
                    style: ElevatedButton.styleFrom(
                      backgroundColor: const Color(0xFFFACC15),
                      foregroundColor: Colors.black,
                    ),
                    child: const Text('Retry'),
                  ),
                ],
              ),
            ),
        data: (profile) {
          if (profile == null) {
            return Center(
              child: Column(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  const Icon(Icons.person_off, color: Colors.grey, size: 48),
                  const SizedBox(height: 16),
                  Text(
                    'Student profile not found',
                    style: ATextStyle.medium.copyWith(color: Colors.white),
                  ),
                ],
              ),
            );
          }

          return _buildProfileContent(context, ref, profile);
        },
      ),
    );
  }

  Widget _buildProfileContent(
    BuildContext context,
    WidgetRef ref,
    StudentProfileDetail profile,
  ) {
    return SingleChildScrollView(
      padding: const EdgeInsets.all(16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Header Section
          _buildHeaderSection(profile),
          const SizedBox(height: 24),

          // Student Info Section
          _buildStudentInfoSection(profile),
          const SizedBox(height: 24),

          // Photos Section
          _buildPhotosSection(context, profile),
          const SizedBox(height: 24),

          // Action Grid Section
          _buildActionGridSection(context, profile, ref),
          const SizedBox(height: 24),
        ],
      ),
    );
  }

  Widget _buildHeaderSection(StudentProfileDetail profile) {
    return Container(
      padding: const EdgeInsets.all(20),
      decoration: BoxDecoration(
        color: const Color(0xFF1F2937),
        borderRadius: BorderRadius.circular(16),
        border: Border.all(color: const Color(0xFF374151)),
      ),
      child: Column(
        children: [
          // Profile Image and Premium Badge
          Stack(
            children: [
              CircleAvatar(
                radius: 40,
                backgroundColor: const Color(0xFFFACC15),
                backgroundImage:
                    profile.profileImageUrl != null
                        ? NetworkImage(profile.profileImageUrl!)
                        : null,
                child:
                    profile.profileImageUrl == null
                        ? Text(
                          profile.initials,
                          style: const TextStyle(
                            color: Colors.black,
                            fontSize: 24,
                            fontWeight: FontWeight.bold,
                          ),
                        )
                        : null,
              ),
              if (profile.planType == StudentPlanType.premium)
                Positioned(
                  top: 0,
                  right: 0,
                  child: Container(
                    padding: const EdgeInsets.symmetric(
                      horizontal: 8,
                      vertical: 4,
                    ),
                    decoration: BoxDecoration(
                      color: const Color(0xFFFACC15),
                      borderRadius: BorderRadius.circular(12),
                    ),
                    child: const Text('👑', style: TextStyle(fontSize: 12)),
                  ),
                ),
            ],
          ),
          const SizedBox(height: 16),

          // Name
          Text(
            profile.name,
            style: ATextStyle.large.copyWith(
              color: Colors.white,
              fontWeight: FontWeight.bold,
            ),
            textAlign: TextAlign.center,
          ),
          const SizedBox(height: 8),

          // Membership Duration
          Text(
            profile.membershipDuration,
            style: ATextStyle.medium.copyWith(color: Colors.grey),
            textAlign: TextAlign.center,
          ),
        ],
      ),
    );
  }

  Widget _buildStudentInfoSection(StudentProfileDetail profile) {
    return Container(
      padding: const EdgeInsets.all(20),
      decoration: BoxDecoration(
        color: const Color(0xFF1F2937),
        borderRadius: BorderRadius.circular(16),
        border: Border.all(color: const Color(0xFF374151)),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            'Student Information',
            style: ATextStyle.large.copyWith(
              color: Colors.white,
              fontWeight: FontWeight.bold,
            ),
          ),
          const SizedBox(height: 16),

          // Weight and Height Row
          Row(
            children: [
              Expanded(
                child: _buildInfoItem(
                  'Weight',
                  '${profile.weight?.toStringAsFixed(1) ?? '--'} kg',
                  Icons.monitor_weight,
                ),
              ),
              const SizedBox(width: 16),
              Expanded(
                child: _buildInfoItem(
                  'Height',
                  '${profile.height?.toStringAsFixed(0) ?? '--'} cm',
                  Icons.height,
                ),
              ),
            ],
          ),
          const SizedBox(height: 16),

          // Bio & Goals
          if (profile.fitnessGoals != null &&
              profile.fitnessGoals!.isNotEmpty) ...[
            _buildInfoItem('Bio & Goals', profile.fitnessGoals!, Icons.flag),
            const SizedBox(height: 16),
          ],

          // Medical Conditions
          if (profile.medicalConditions != null &&
              profile.medicalConditions!.isNotEmpty) ...[
            _buildInfoItem(
              'Medical Conditions',
              profile.medicalConditions!,
              Icons.medical_services,
            ),
            const SizedBox(height: 16),
          ],

          // Preferences
          if (profile.preferences.isNotEmpty) ...[
            Text(
              'Preferences',
              style: ATextStyle.medium.copyWith(
                color: Colors.white,
                fontWeight: FontWeight.w600,
              ),
            ),
            const SizedBox(height: 8),
            Wrap(
              spacing: 8,
              runSpacing: 8,
              children:
                  profile.preferences.map((preference) {
                    return Container(
                      padding: const EdgeInsets.symmetric(
                        horizontal: 12,
                        vertical: 6,
                      ),
                      decoration: BoxDecoration(
                        color: const Color(0xFFFACC15).withOpacity(0.2),
                        borderRadius: BorderRadius.circular(16),
                        border: Border.all(
                          color: const Color(0xFFFACC15).withOpacity(0.5),
                        ),
                      ),
                      child: Text(
                        preference,
                        style: ATextStyle.small.copyWith(
                          color: const Color(0xFFFACC15),
                        ),
                      ),
                    );
                  }).toList(),
            ),
          ],
        ],
      ),
    );
  }

  Widget _buildInfoItem(String label, String value, IconData icon) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Row(
          children: [
            Icon(icon, color: const Color(0xFFFACC15), size: 16),
            const SizedBox(width: 8),
            Text(
              label,
              style: ATextStyle.small.copyWith(
                color: Colors.grey,
                fontWeight: FontWeight.w500,
              ),
            ),
          ],
        ),
        const SizedBox(height: 4),
        Text(value, style: ATextStyle.medium.copyWith(color: Colors.white)),
      ],
    );
  }

  Widget _buildPhotosSection(
    BuildContext context,
    StudentProfileDetail profile,
  ) {
    return Container(
      padding: const EdgeInsets.all(20),
      decoration: BoxDecoration(
        color: const Color(0xFF1F2937),
        borderRadius: BorderRadius.circular(16),
        border: Border.all(color: const Color(0xFF374151)),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            'Photos',
            style: ATextStyle.large.copyWith(
              color: Colors.white,
              fontWeight: FontWeight.bold,
            ),
          ),
          const SizedBox(height: 16),
          SizedBox(
            width: double.infinity,
            child: ElevatedButton.icon(
              onPressed: () => _showPhotosBottomSheet(context, profile),
              icon: const Icon(Icons.photo_library),
              label: Text(
                profile.hasPhotos ? 'Show Photos' : 'No photos uploaded yet',
                style: ATextStyle.medium,
              ),
              style: ElevatedButton.styleFrom(
                backgroundColor:
                    profile.hasPhotos
                        ? const Color(0xFFFACC15)
                        : const Color(0xFF374151),
                foregroundColor: profile.hasPhotos ? Colors.black : Colors.grey,
                padding: const EdgeInsets.symmetric(vertical: 16),
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(12),
                ),
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildActionGridSection(
    BuildContext context,
    StudentProfileDetail profile,
    WidgetRef ref,
  ) {
    final actions = [
      _ActionItem('Workout Plan', Icons.fitness_center, Colors.blue),
      _ActionItem('Cardio Plan', Icons.directions_run, Colors.green),
      _ActionItem('Nutrition Plan', Icons.restaurant, Colors.orange),
      _ActionItem('Progress Photos', Icons.photo_camera, Colors.purple),
      _ActionItem('Chat', Icons.chat, Colors.cyan),
      _ActionItem('Feedback', Icons.feedback, Colors.pink),
      _ActionItem('Supplements', Icons.medication, Colors.red),
      _ActionItem('Shopping List', Icons.shopping_cart, Colors.indigo),
      _ActionItem('Reports', Icons.analytics, Colors.teal),
    ];

    return Container(
      padding: const EdgeInsets.all(20),
      decoration: BoxDecoration(
        color: const Color(0xFF1F2937),
        borderRadius: BorderRadius.circular(16),
        border: Border.all(color: const Color(0xFF374151)),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            'Actions',
            style: ATextStyle.large.copyWith(
              color: Colors.white,
              fontWeight: FontWeight.bold,
            ),
          ),
          const SizedBox(height: 16),
          GridView.builder(
            shrinkWrap: true,
            physics: const NeverScrollableScrollPhysics(),
            gridDelegate: const SliverGridDelegateWithFixedCrossAxisCount(
              crossAxisCount: 3,
              crossAxisSpacing: 12,
              mainAxisSpacing: 12,
              childAspectRatio: 1,
            ),
            itemCount: actions.length,
            itemBuilder: (context, index) {
              final action = actions[index];
              return _buildActionButton(action, context, ref);
            },
          ),
        ],
      ),
    );
  }

  Widget _buildActionButton(
    _ActionItem action,
    BuildContext context,
    WidgetRef ref,
  ) {
    return Container(
      decoration: BoxDecoration(
        color: const Color(0xFF374151),
        borderRadius: BorderRadius.circular(12),
        border: Border.all(color: const Color(0xFF4B5563)),
      ),
      child: InkWell(
        onTap: () => _handleActionTap(action, context, ref),
        borderRadius: BorderRadius.circular(12),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(action.icon, color: action.color, size: 24),
            const SizedBox(height: 8),
            Text(
              action.title,
              style: ATextStyle.small.copyWith(
                color: Colors.white,
                fontWeight: FontWeight.w500,
              ),
              textAlign: TextAlign.center,
              maxLines: 2,
              overflow: TextOverflow.ellipsis,
            ),
          ],
        ),
      ),
    );
  }

  void _handleActionTap(
    _ActionItem action,
    BuildContext context,
    WidgetRef ref,
  ) {
    final instructorId = ref.read(currentInstructorIdProvider);
    if (instructorId == null) return;

    switch (action.title) {
      case 'Workout Plan':
        // Navigate to assign exercise page
        Navigator.push(
          context,
          MaterialPageRoute(
            builder:
                (context) => AssignExerciseToStudentPage(
                  student: InstructorStudent(
                    id: studentId,
                    name: 'Student', // Will be loaded from actual data
                    email:
                        '<EMAIL>', // Will be loaded from actual data
                    profileImageUrl: null,
                    joinedAt: DateTime.now(), // Will be loaded from actual data
                    planType:
                        StudentPlanType
                            .premium, // Will be loaded from actual data
                    isOnline: false,
                    statusCounts: const StudentStatusCounts(
                      workoutPending: 1,
                      nutritionPending: 1,
                      cardioPending: 1,
                      supplementPending: 1,
                    ),
                    lastActiveAt: DateTime.now(),
                  ),
                  instructorId: instructorId,
                ),
          ),
        );
        break;
      case 'Nutrition Plan':
        // Navigate to assign nutrition page
        Navigator.push(
          context,
          MaterialPageRoute(
            builder:
                (context) => AssignNutritionToStudentPage(
                  student: InstructorStudent(
                    id: studentId,
                    name: 'Student', // Will be loaded from actual data
                    email:
                        '<EMAIL>', // Will be loaded from actual data
                    profileImageUrl: null,
                    joinedAt: DateTime.now(), // Will be loaded from actual data
                    planType:
                        StudentPlanType
                            .premium, // Will be loaded from actual data
                    isOnline: false,
                    statusCounts: const StudentStatusCounts(
                      workoutPending: 1,
                      nutritionPending: 1,
                      cardioPending: 1,
                      supplementPending: 1,
                    ),
                    lastActiveAt: DateTime.now(),
                  ),
                  instructorId: instructorId,
                ),
          ),
        );
        break;
      case 'Supplements':
        // Navigate to assign supplements page
        Navigator.push(
          context,
          MaterialPageRoute(
            builder: (context) => AssignSupplementToStudentPage(
              student: InstructorStudent(
                id: studentId,
                name: 'Student', // Will be loaded from actual data
                email: '<EMAIL>', // Will be loaded from actual data
                profileImageUrl: null,
                joinedAt: DateTime.now(), // Will be loaded from actual data
                planType: StudentPlanType.premium, // Will be loaded from actual data
                isOnline: false,
                statusCounts: const StudentStatusCounts(
                  workoutPending: 1,
                  nutritionPending: 1,
                  cardioPending: 1,
                  supplementPending: 1,
                ),
                lastActiveAt: DateTime.now(),
              ),
              instructorId: instructorId,
            ),
          ),
        );
        break;
      case 'Shopping List':
        // Navigate to assign shopping list page
        Navigator.push(
          context,
          MaterialPageRoute(
            builder: (context) => AssignShoppingListPage(
              student: InstructorStudent(
                id: studentId,
                name: 'Student', // Will be loaded from actual data
                email: '<EMAIL>', // Will be loaded from actual data
                profileImageUrl: null,
                joinedAt: DateTime.now(), // Will be loaded from actual data
                planType: StudentPlanType.premium, // Will be loaded from actual data
                isOnline: false,
                statusCounts: const StudentStatusCounts(
                  workoutPending: 1,
                  nutritionPending: 1,
                  cardioPending: 1,
                  supplementPending: 1,
                ),
                lastActiveAt: DateTime.now(),
              ),
              instructorId: instructorId,
            ),
          ),
        );
        break;
      case 'Chat':
        // Navigate to instructor questions inbox
        Navigator.push(
          context,
          MaterialPageRoute(
            builder: (context) => const InstructorQuestionsInboxScreen(),
          ),
        );
        break;
      default:
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('${action.title} - Coming Soon!'),
            backgroundColor: const Color(0xFFFACC15),
          ),
        );
    }
  }

  void _showPhotosBottomSheet(
    BuildContext context,
    StudentProfileDetail profile,
  ) {
    if (!profile.hasPhotos) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(
          content: Text('No photos uploaded yet'),
          backgroundColor: Color(0xFF374151),
        ),
      );
      return;
    }

    showModalBottomSheet(
      context: context,
      backgroundColor: Colors.transparent,
      isScrollControlled: true,
      builder:
          (context) => Container(
            height: context.height * 0.7,
            decoration: const BoxDecoration(
              color: Color(0xFF1F2937),
              borderRadius: BorderRadius.vertical(top: Radius.circular(20)),
            ),
            child: Column(
              children: [
                // Handle bar
                Container(
                  margin: const EdgeInsets.only(top: 12),
                  width: 40,
                  height: 4,
                  decoration: BoxDecoration(
                    color: Colors.grey,
                    borderRadius: BorderRadius.circular(2),
                  ),
                ),

                // Header
                Padding(
                  padding: const EdgeInsets.all(20),
                  child: Row(
                    children: [
                      Text(
                        'Progress Photos',
                        style: ATextStyle.large.copyWith(
                          color: Colors.white,
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                      const Spacer(),
                      IconButton(
                        onPressed: () => Navigator.of(context).pop(),
                        icon: const Icon(Icons.close, color: Colors.white),
                      ),
                    ],
                  ),
                ),

                // Photos Grid
                Expanded(
                  child: Padding(
                    padding: const EdgeInsets.symmetric(horizontal: 20),
                    child: GridView.builder(
                      gridDelegate:
                          const SliverGridDelegateWithFixedCrossAxisCount(
                            crossAxisCount: 2,
                            crossAxisSpacing: 12,
                            mainAxisSpacing: 12,
                            childAspectRatio: 0.8,
                          ),
                      itemCount: profile.photoUrls.length,
                      itemBuilder: (context, index) {
                        final photoUrl = profile.photoUrls[index];
                        final photoType = _getPhotoType(photoUrl, profile);

                        return Container(
                          decoration: BoxDecoration(
                            color: const Color(0xFF374151),
                            borderRadius: BorderRadius.circular(12),
                            border: Border.all(color: const Color(0xFF4B5563)),
                          ),
                          child: Column(
                            children: [
                              Expanded(
                                child: ClipRRect(
                                  borderRadius: const BorderRadius.vertical(
                                    top: Radius.circular(12),
                                  ),
                                  child: Image.network(
                                    photoUrl,
                                    fit: BoxFit.cover,
                                    width: double.infinity,
                                    loadingBuilder: (
                                      context,
                                      child,
                                      loadingProgress,
                                    ) {
                                      if (loadingProgress == null) return child;
                                      return const Center(
                                        child: CircularProgressIndicator(
                                          color: Color(0xFFFACC15),
                                        ),
                                      );
                                    },
                                    errorBuilder: (context, error, stackTrace) {
                                      return const Center(
                                        child: Icon(
                                          Icons.error_outline,
                                          color: Colors.grey,
                                          size: 32,
                                        ),
                                      );
                                    },
                                  ),
                                ),
                              ),
                              Padding(
                                padding: const EdgeInsets.all(12),
                                child: Text(
                                  photoType,
                                  style: ATextStyle.small.copyWith(
                                    color: Colors.white,
                                    fontWeight: FontWeight.w500,
                                  ),
                                ),
                              ),
                            ],
                          ),
                        );
                      },
                    ),
                  ),
                ),
                const SizedBox(height: 20),
              ],
            ),
          ),
    );
  }

  String _getPhotoType(String photoUrl, StudentProfileDetail profile) {
    if (photoUrl == profile.frontPhotoUrl) return 'Front';
    if (photoUrl == profile.sidePhotoUrl) return 'Side';
    if (photoUrl == profile.backPhotoUrl) return 'Back';
    return 'Photo';
  }
}

class _ActionItem {
  final String title;
  final IconData icon;
  final Color color;

  const _ActionItem(this.title, this.icon, this.color);
}
