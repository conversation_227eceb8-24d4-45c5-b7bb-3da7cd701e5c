import 'package:flutter/material.dart';
import 'package:flutter_hooks/flutter_hooks.dart';
import 'package:hooks_riverpod/hooks_riverpod.dart';
import '../domain/student_models.dart';
import '../domain/student_workout_assignment.dart';
import '../application/student_workout_assignment_provider.dart';
import 'exercise_picker_page.dart';

/// Manual Exercise Plan Builder for creating custom workout plans for specific students
class ManualExercisePlanBuilderPage extends HookConsumerWidget {
  final InstructorStudent student;
  final String instructorId;
  final StudentWorkoutAssignment? existingAssignment;

  const ManualExercisePlanBuilderPage({
    super.key,
    required this.student,
    required this.instructorId,
    this.existingAssignment,
  });

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final titleController = useTextEditingController(
      text:
          existingAssignment?.planName ?? 'Full Body Plan for ${student.name}',
    );
    final notesController = useTextEditingController(
      text: existingAssignment?.description ?? '',
    );
    final selectedTabIndex = useState(0);
    final tags = useState<List<String>>(
      existingAssignment?.planData?['tags']?.cast<String>() ?? ['Custom'],
    );
    final plans = useState<List<Map<String, dynamic>>>(
      existingAssignment?.planData?['plans']?.cast<Map<String, dynamic>>() ??
          [
            {
              'id': 'plan_1',
              'name': 'Plan 1',
              'exercises': <Map<String, dynamic>>[],
            },
          ],
    );

    return Scaffold(
      backgroundColor: const Color(0xFF111827),
      appBar: AppBar(
        backgroundColor: const Color(0xFF111827),
        elevation: 0,
        leading: IconButton(
          icon: const Icon(Icons.arrow_back, color: Colors.white),
          onPressed: () => Navigator.pop(context),
        ),
        title: const Text(
          'Manual Plan Builder',
          style: TextStyle(
            color: Colors.white,
            fontSize: 20,
            fontWeight: FontWeight.w600,
          ),
        ),
        actions: [
          TextButton(
            onPressed:
                () => _savePlan(
                  context,
                  ref,
                  titleController.text,
                  notesController.text,
                  tags.value,
                  plans.value,
                ),
            child: const Text(
              'Save',
              style: TextStyle(
                color: Color(0xFFFACC15),
                fontSize: 16,
                fontWeight: FontWeight.w600,
              ),
            ),
          ),
        ],
      ),
      body: Column(
        children: [
          // Student Header
          Container(
            padding: const EdgeInsets.all(16),
            decoration: const BoxDecoration(
              color: Color(0xFF1F2937),
              border: Border(
                bottom: BorderSide(color: Color(0xFF374151), width: 1),
              ),
            ),
            child: Row(
              children: [
                CircleAvatar(
                  radius: 24,
                  backgroundColor: const Color(0xFF374151),
                  backgroundImage:
                      student.profileImageUrl != null
                          ? NetworkImage(student.profileImageUrl!)
                          : null,
                  child:
                      student.profileImageUrl == null
                          ? Text(
                            student.name.isNotEmpty
                                ? student.name[0].toUpperCase()
                                : 'S',
                            style: const TextStyle(
                              color: Colors.white,
                              fontSize: 18,
                              fontWeight: FontWeight.w600,
                            ),
                          )
                          : null,
                ),
                const SizedBox(width: 12),
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        student.name,
                        style: const TextStyle(
                          color: Colors.white,
                          fontSize: 18,
                          fontWeight: FontWeight.w600,
                        ),
                      ),
                      const SizedBox(height: 4),
                      Container(
                        padding: const EdgeInsets.symmetric(
                          horizontal: 8,
                          vertical: 2,
                        ),
                        decoration: BoxDecoration(
                          color:
                              student.planType == StudentPlanType.premium
                                  ? const Color(0xFFFACC15).withOpacity(0.2)
                                  : const Color(0xFF6B7280).withOpacity(0.2),
                          borderRadius: BorderRadius.circular(12),
                        ),
                        child: Text(
                          student.planType == StudentPlanType.premium
                              ? 'Premium'
                              : 'Standard',
                          style: TextStyle(
                            color:
                                student.planType == StudentPlanType.premium
                                    ? const Color(0xFFFACC15)
                                    : const Color(0xFF9CA3AF),
                            fontSize: 12,
                            fontWeight: FontWeight.w500,
                          ),
                        ),
                      ),
                    ],
                  ),
                ),
              ],
            ),
          ),

          // Plan Content
          Expanded(
            child: SingleChildScrollView(
              padding: const EdgeInsets.all(16),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  // Title Field
                  _buildTitleField(titleController),
                  const SizedBox(height: 24),

                  // Tags Section
                  _buildTagsSection(tags),
                  const SizedBox(height: 24),

                  // Plan Tabs
                  _buildPlanTabs(selectedTabIndex, plans),
                  const SizedBox(height: 16),

                  // Current Plan Content
                  _buildCurrentPlanContent(
                    context,
                    selectedTabIndex.value,
                    plans.value,
                    plans,
                  ),
                  const SizedBox(height: 24),

                  // Trainer Notes
                  _buildTrainerNotes(notesController),
                  const SizedBox(height: 100), // Space for bottom button
                ],
              ),
            ),
          ),
        ],
      ),
      bottomNavigationBar: Container(
        padding: const EdgeInsets.all(16),
        decoration: const BoxDecoration(
          color: Color(0xFF1F2937),
          border: Border(top: BorderSide(color: Color(0xFF374151), width: 1)),
        ),
        child: SafeArea(
          child: SizedBox(
            width: double.infinity,
            height: 48,
            child: ElevatedButton(
              onPressed:
                  () => _completePlan(
                    context,
                    ref,
                    titleController.text,
                    notesController.text,
                    tags.value,
                    plans.value,
                  ),
              style: ElevatedButton.styleFrom(
                backgroundColor: const Color(0xFFFACC15),
                foregroundColor: const Color(0xFF111827),
                elevation: 0,
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(12),
                ),
              ),
              child: const Text(
                '✓ Complete Plan',
                style: TextStyle(fontSize: 16, fontWeight: FontWeight.w600),
              ),
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildTitleField(TextEditingController controller) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        const Text(
          'Plan Title',
          style: TextStyle(
            color: Colors.white,
            fontSize: 16,
            fontWeight: FontWeight.w600,
          ),
        ),
        const SizedBox(height: 8),
        TextField(
          controller: controller,
          style: const TextStyle(color: Colors.white),
          decoration: InputDecoration(
            hintText: 'Enter plan title...',
            hintStyle: const TextStyle(color: Color(0xFF6B7280)),
            filled: true,
            fillColor: const Color(0xFF1F2937),
            border: OutlineInputBorder(
              borderRadius: BorderRadius.circular(12),
              borderSide: const BorderSide(color: Color(0xFF374151)),
            ),
            enabledBorder: OutlineInputBorder(
              borderRadius: BorderRadius.circular(12),
              borderSide: const BorderSide(color: Color(0xFF374151)),
            ),
            focusedBorder: OutlineInputBorder(
              borderRadius: BorderRadius.circular(12),
              borderSide: const BorderSide(color: Color(0xFFFACC15)),
            ),
          ),
        ),
      ],
    );
  }

  Widget _buildTagsSection(ValueNotifier<List<String>> tags) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Row(
          children: [
            const Text(
              'Tags',
              style: TextStyle(
                color: Colors.white,
                fontSize: 16,
                fontWeight: FontWeight.w600,
              ),
            ),
            const Spacer(),
            TextButton.icon(
              onPressed: () => _showAddTagDialog(tags),
              icon: const Icon(Icons.add, color: Color(0xFFFACC15), size: 16),
              label: const Text(
                'Add Tag',
                style: TextStyle(color: Color(0xFFFACC15), fontSize: 14),
              ),
            ),
          ],
        ),
        const SizedBox(height: 8),
        Wrap(
          spacing: 8,
          runSpacing: 8,
          children: tags.value.map((tag) => _buildTagChip(tag, tags)).toList(),
        ),
      ],
    );
  }

  Widget _buildTagChip(String tag, ValueNotifier<List<String>> tags) {
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
      decoration: BoxDecoration(
        color: const Color(0xFFFACC15).withOpacity(0.2),
        borderRadius: BorderRadius.circular(16),
        border: Border.all(color: const Color(0xFFFACC15).withOpacity(0.3)),
      ),
      child: Row(
        mainAxisSize: MainAxisSize.min,
        children: [
          Text(
            tag,
            style: const TextStyle(
              color: Color(0xFFFACC15),
              fontSize: 12,
              fontWeight: FontWeight.w500,
            ),
          ),
          const SizedBox(width: 4),
          GestureDetector(
            onTap: () {
              final newTags = List<String>.from(tags.value);
              newTags.remove(tag);
              tags.value = newTags;
            },
            child: const Icon(Icons.close, color: Color(0xFFFACC15), size: 14),
          ),
        ],
      ),
    );
  }

  Widget _buildPlanTabs(
    ValueNotifier<int> selectedTabIndex,
    ValueNotifier<List<Map<String, dynamic>>> plans,
  ) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Row(
          children: [
            const Text(
              'Workout Plans',
              style: TextStyle(
                color: Colors.white,
                fontSize: 16,
                fontWeight: FontWeight.w600,
              ),
            ),
            const Spacer(),
            IconButton(
              onPressed: () => _addNewPlan(plans, selectedTabIndex),
              icon: const Icon(Icons.add, color: Color(0xFFFACC15)),
            ),
          ],
        ),
        const SizedBox(height: 8),
        SizedBox(
          height: 40,
          child: ListView.builder(
            scrollDirection: Axis.horizontal,
            itemCount: plans.value.length,
            itemBuilder: (context, index) {
              final plan = plans.value[index];
              final isSelected = selectedTabIndex.value == index;

              return Padding(
                padding: const EdgeInsets.only(right: 8),
                child: GestureDetector(
                  onTap: () => selectedTabIndex.value = index,
                  child: Container(
                    padding: const EdgeInsets.symmetric(
                      horizontal: 16,
                      vertical: 8,
                    ),
                    decoration: BoxDecoration(
                      color:
                          isSelected
                              ? const Color(0xFFFACC15)
                              : const Color(0xFF1F2937),
                      borderRadius: BorderRadius.circular(20),
                      border: Border.all(
                        color:
                            isSelected
                                ? const Color(0xFFFACC15)
                                : const Color(0xFF374151),
                      ),
                    ),
                    child: Row(
                      mainAxisSize: MainAxisSize.min,
                      children: [
                        Text(
                          plan['name'] ?? 'Plan ${index + 1}',
                          style: TextStyle(
                            color:
                                isSelected
                                    ? const Color(0xFF111827)
                                    : Colors.white,
                            fontSize: 14,
                            fontWeight: FontWeight.w500,
                          ),
                        ),
                        if (plans.value.length > 1) ...[
                          const SizedBox(width: 8),
                          GestureDetector(
                            onTap:
                                () =>
                                    _deletePlan(plans, selectedTabIndex, index),
                            child: Icon(
                              Icons.close,
                              size: 16,
                              color:
                                  isSelected
                                      ? const Color(0xFF111827)
                                      : const Color(0xFF6B7280),
                            ),
                          ),
                        ],
                      ],
                    ),
                  ),
                ),
              );
            },
          ),
        ),
      ],
    );
  }

  Widget _buildCurrentPlanContent(
    BuildContext context,
    int selectedIndex,
    List<Map<String, dynamic>> plans,
    ValueNotifier<List<Map<String, dynamic>>> plansNotifier,
  ) {
    if (selectedIndex >= plans.length) return const SizedBox.shrink();

    final currentPlan = plans[selectedIndex];
    final exercises =
        currentPlan['exercises'] as List<Map<String, dynamic>>? ?? [];

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        const Text(
          'Exercises',
          style: TextStyle(
            color: Colors.white,
            fontSize: 16,
            fontWeight: FontWeight.w600,
          ),
        ),
        const SizedBox(height: 12),

        if (exercises.isEmpty)
          Container(
            width: double.infinity,
            padding: const EdgeInsets.all(24),
            decoration: BoxDecoration(
              color: const Color(0xFF1F2937),
              borderRadius: BorderRadius.circular(12),
              border: Border.all(color: const Color(0xFF374151)),
            ),
            child: Column(
              children: [
                Icon(
                  Icons.fitness_center,
                  color: const Color(0xFF6B7280),
                  size: 48,
                ),
                const SizedBox(height: 12),
                const Text(
                  'No exercises added yet',
                  style: TextStyle(
                    color: Color(0xFF9CA3AF),
                    fontSize: 16,
                    fontWeight: FontWeight.w500,
                  ),
                ),
                const SizedBox(height: 4),
                const Text(
                  'Tap "Add Exercise" to get started',
                  style: TextStyle(color: Color(0xFF6B7280), fontSize: 14),
                ),
              ],
            ),
          )
        else
          ...exercises.asMap().entries.map((entry) {
            final index = entry.key;
            final exercise = entry.value;
            return _buildExerciseCard(exercise, index);
          }).toList(),

        const SizedBox(height: 16),

        // Add Exercise Button
        SizedBox(
          width: double.infinity,
          child: OutlinedButton.icon(
            onPressed:
                () => _navigateToExercisePicker(
                  context,
                  selectedIndex,
                  plans,
                  plansNotifier,
                ),
            icon: const Icon(Icons.add, color: Color(0xFFFACC15)),
            label: const Text(
              'Add Exercise',
              style: TextStyle(
                color: Color(0xFFFACC15),
                fontSize: 16,
                fontWeight: FontWeight.w600,
              ),
            ),
            style: OutlinedButton.styleFrom(
              padding: const EdgeInsets.symmetric(vertical: 16),
              side: const BorderSide(color: Color(0xFFFACC15)),
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(12),
              ),
            ),
          ),
        ),
      ],
    );
  }

  Widget _buildExerciseCard(Map<String, dynamic> exercise, int index) {
    return Container(
      margin: const EdgeInsets.only(bottom: 12),
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: const Color(0xFF1F2937),
        borderRadius: BorderRadius.circular(12),
        border: Border.all(color: const Color(0xFF374151)),
      ),
      child: Row(
        children: [
          // Drag Handle
          Icon(Icons.drag_handle, color: const Color(0xFF6B7280), size: 20),
          const SizedBox(width: 12),

          // Exercise Info
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  exercise['name'] ?? 'Exercise ${index + 1}',
                  style: const TextStyle(
                    color: Colors.white,
                    fontSize: 16,
                    fontWeight: FontWeight.w600,
                  ),
                ),
                const SizedBox(height: 4),
                Row(
                  children: [
                    if (exercise['sets'] != null) ...[
                      Text(
                        '${exercise['sets']} sets',
                        style: const TextStyle(
                          color: Color(0xFF9CA3AF),
                          fontSize: 14,
                        ),
                      ),
                      const SizedBox(width: 8),
                    ],
                    if (exercise['reps'] != null) ...[
                      Text(
                        '${exercise['reps']} reps',
                        style: const TextStyle(
                          color: Color(0xFF9CA3AF),
                          fontSize: 14,
                        ),
                      ),
                      const SizedBox(width: 8),
                    ],
                    if (exercise['rest_time'] != null) ...[
                      Text(
                        '${exercise['rest_time']}s rest',
                        style: const TextStyle(
                          color: Color(0xFF9CA3AF),
                          fontSize: 14,
                        ),
                      ),
                    ],
                  ],
                ),
              ],
            ),
          ),

          // Edit/Delete Actions
          Row(
            mainAxisSize: MainAxisSize.min,
            children: [
              IconButton(
                onPressed: () => _editExercise(exercise, index),
                icon: const Icon(
                  Icons.edit,
                  color: Color(0xFFFACC15),
                  size: 20,
                ),
              ),
              IconButton(
                onPressed: () => _deleteExercise(index),
                icon: const Icon(
                  Icons.delete,
                  color: Color(0xFFEF4444),
                  size: 20,
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildTrainerNotes(TextEditingController controller) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        const Text(
          'Trainer\'s Notes',
          style: TextStyle(
            color: Colors.white,
            fontSize: 16,
            fontWeight: FontWeight.w600,
          ),
        ),
        const SizedBox(height: 8),
        TextField(
          controller: controller,
          maxLines: 4,
          style: const TextStyle(color: Colors.white),
          decoration: InputDecoration(
            hintText: 'Add any special instructions or notes for this plan...',
            hintStyle: const TextStyle(color: Color(0xFF6B7280)),
            filled: true,
            fillColor: const Color(0xFF1F2937),
            border: OutlineInputBorder(
              borderRadius: BorderRadius.circular(12),
              borderSide: const BorderSide(color: Color(0xFF374151)),
            ),
            enabledBorder: OutlineInputBorder(
              borderRadius: BorderRadius.circular(12),
              borderSide: const BorderSide(color: Color(0xFF374151)),
            ),
            focusedBorder: OutlineInputBorder(
              borderRadius: BorderRadius.circular(12),
              borderSide: const BorderSide(color: Color(0xFFFACC15)),
            ),
          ),
        ),
      ],
    );
  }

  // Helper Methods
  void _showAddTagDialog(ValueNotifier<List<String>> tags) {
    // TODO: Implement add tag dialog
  }

  void _addNewPlan(
    ValueNotifier<List<Map<String, dynamic>>> plans,
    ValueNotifier<int> selectedTabIndex,
  ) {
    final newPlan = {
      'id': 'plan_${plans.value.length + 1}',
      'name': 'Plan ${plans.value.length + 1}',
      'exercises': <Map<String, dynamic>>[],
    };

    final newPlans = List<Map<String, dynamic>>.from(plans.value);
    newPlans.add(newPlan);
    plans.value = newPlans;
    selectedTabIndex.value = newPlans.length - 1;
  }

  void _deletePlan(
    ValueNotifier<List<Map<String, dynamic>>> plans,
    ValueNotifier<int> selectedTabIndex,
    int index,
  ) {
    if (plans.value.length <= 1) return; // Don't delete the last plan

    final newPlans = List<Map<String, dynamic>>.from(plans.value);
    newPlans.removeAt(index);
    plans.value = newPlans;

    // Adjust selected tab index if necessary
    if (selectedTabIndex.value >= newPlans.length) {
      selectedTabIndex.value = newPlans.length - 1;
    } else if (selectedTabIndex.value > index) {
      selectedTabIndex.value = selectedTabIndex.value - 1;
    }
  }

  void _navigateToExercisePicker(
    BuildContext context,
    int planIndex,
    List<Map<String, dynamic>> currentPlans,
    ValueNotifier<List<Map<String, dynamic>>> plansNotifier,
  ) async {
    final planName = currentPlans[planIndex]['name'] ?? 'Plan ${planIndex + 1}';

    final result = await Navigator.push(
      context,
      MaterialPageRoute(
        builder:
            (context) =>
                ExercisePickerPage(planIndex: planIndex, planName: planName),
      ),
    );

    if (result != null && result is Map<String, dynamic>) {
      print('🏋️ Exercise selected: ${result['name']}');

      // Add the exercise to the current plan
      final updatedPlans = List<Map<String, dynamic>>.from(currentPlans);
      final currentPlan = Map<String, dynamic>.from(updatedPlans[planIndex]);
      final exercises = List<Map<String, dynamic>>.from(
        currentPlan['exercises'] ?? [],
      );

      exercises.add(result);
      currentPlan['exercises'] = exercises;
      updatedPlans[planIndex] = currentPlan;

      // Update the plans state
      plansNotifier.value = updatedPlans;

      print('✅ Exercise added to plan. Total exercises: ${exercises.length}');
    } else {
      print('❌ No exercise selected or invalid result');
    }
  }

  void _editExercise(Map<String, dynamic> exercise, int index) {
    // TODO: Implement exercise editing
    print('Edit exercise at index $index: ${exercise['name']}');
  }

  void _deleteExercise(int index) {
    // TODO: Implement exercise deletion
    print('Delete exercise at index $index');
  }

  Future<void> _savePlan(
    BuildContext context,
    WidgetRef ref,
    String title,
    String notes,
    List<String> tags,
    List<Map<String, dynamic>> plans,
  ) async {
    try {
      final planData = {
        'tags': tags,
        'plans': plans,
        'created_at': DateTime.now().toIso8601String(),
        'updated_at': DateTime.now().toIso8601String(),
      };

      final notifier = ref.read(
        studentWorkoutAssignmentNotifierProvider.notifier,
      );

      if (existingAssignment != null) {
        // Update existing assignment
        await notifier.updateWorkoutAssignment(
          assignmentId: existingAssignment!.id,
          planName: title,
          description: notes,
          planData: planData,
        );
      } else {
        // Create new assignment
        await notifier.createCustomWorkoutPlan(
          studentId: student.id,
          instructorId: instructorId,
          planName: title,
          description: notes,
        );

        // Update with plan data
        final assignment =
            ref.read(studentWorkoutAssignmentNotifierProvider).value;
        if (assignment != null) {
          await notifier.updateWorkoutAssignment(
            assignmentId: assignment.id,
            planData: planData,
          );
        }
      }

      if (context.mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('Plan saved successfully!'),
            backgroundColor: Colors.green,
          ),
        );
      }
    } catch (e) {
      if (context.mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Error saving plan: $e'),
            backgroundColor: Colors.red,
          ),
        );
      }
    }
  }

  void _completePlan(
    BuildContext context,
    WidgetRef ref,
    String title,
    String notes,
    List<String> tags,
    List<Map<String, dynamic>> plans,
  ) async {
    await _savePlan(context, ref, title, notes, tags, plans);

    if (context.mounted) {
      Navigator.pop(
        context,
        true,
      ); // Return true to indicate plan was completed
    }
  }
}
