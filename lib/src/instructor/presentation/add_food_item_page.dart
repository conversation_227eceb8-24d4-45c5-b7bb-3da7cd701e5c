import 'package:flutter/material.dart';
import 'package:flutter_hooks/flutter_hooks.dart';
import 'package:hooks_riverpod/hooks_riverpod.dart';
import '../domain/nutrition_template.dart';

// Food Item Model
class FoodItem {
  final String id;
  final String name;
  final String category;
  final List<String> tags;
  final String? imageUrl;
  final String defaultUnit;
  final double? caloriesPer100g;
  final double? proteinPer100g;
  final double? carbsPer100g;
  final double? fatsPer100g;

  FoodItem({
    required this.id,
    required this.name,
    required this.category,
    required this.tags,
    this.imageUrl,
    required this.defaultUnit,
    this.caloriesPer100g,
    this.proteinPer100g,
    this.carbsPer100g,
    this.fatsPer100g,
  });

  factory FoodItem.fromJson(Map<String, dynamic> json) {
    return FoodItem(
      id: json['id'],
      name: json['name'],
      category: json['category'],
      tags: List<String>.from(json['tags'] ?? []),
      imageUrl: json['image_url'],
      defaultUnit: json['default_unit'] ?? 'g',
      caloriesPer100g: json['calories_per_100g']?.toDouble(),
      proteinPer100g: json['protein_per_100g']?.toDouble(),
      carbsPer100g: json['carbs_per_100g']?.toDouble(),
      fatsPer100g: json['fats_per_100g']?.toDouble(),
    );
  }
}

// Food Items Provider
final foodItemsProvider = FutureProvider<List<FoodItem>>((ref) async {
  // Mock data for now - will be replaced with Supabase call
  await Future.delayed(const Duration(milliseconds: 500));
  return [
    // Turkish Breakfast Items
    FoodItem(
      id: '1',
      name: 'Menemen',
      category: 'Protein',
      tags: ['turkish', 'eggs', 'breakfast'],
      imageUrl: 'https://images.unsplash.com/photo-1565299624946-b28f40a0ca4b?w=400',
      defaultUnit: 'portion',
      caloriesPer100g: 180,
      proteinPer100g: 12,
      carbsPer100g: 8,
      fatsPer100g: 12,
    ),
    FoodItem(
      id: '2',
      name: 'Sucuklu Yumurta',
      category: 'Protein',
      tags: ['turkish', 'eggs', 'sausage'],
      imageUrl: 'https://images.unsplash.com/photo-1525351484163-7529414344d8?w=400',
      defaultUnit: 'portion',
      caloriesPer100g: 220,
      proteinPer100g: 15,
      carbsPer100g: 3,
      fatsPer100g: 18,
    ),
    FoodItem(
      id: '3',
      name: 'Beyaz Peynir',
      category: 'Dairy',
      tags: ['turkish', 'cheese', 'breakfast'],
      imageUrl: 'https://images.unsplash.com/photo-1486297678162-eb2a19b0a32d?w=400',
      defaultUnit: 'g',
      caloriesPer100g: 250,
      proteinPer100g: 18,
      carbsPer100g: 2,
      fatsPer100g: 20,
    ),

    // International Proteins
    FoodItem(
      id: '4',
      name: 'Grilled Chicken',
      category: 'Protein',
      tags: ['lean', 'grilled', 'high-protein'],
      imageUrl: 'https://images.unsplash.com/photo-1604503468506-a8da13d82791?w=400',
      defaultUnit: 'g',
      caloriesPer100g: 165,
      proteinPer100g: 31,
      carbsPer100g: 0,
      fatsPer100g: 3.6,
    ),
    FoodItem(
      id: '5',
      name: 'Salmon Fillet',
      category: 'Protein',
      tags: ['fish', 'omega-3', 'premium'],
      imageUrl: 'https://images.unsplash.com/photo-1467003909585-2f8a72700288?w=400',
      defaultUnit: 'g',
      caloriesPer100g: 208,
      proteinPer100g: 20,
      carbsPer100g: 0,
      fatsPer100g: 13,
    ),

    // Turkish Carbs
    FoodItem(
      id: '6',
      name: 'Bulgur Pilavı',
      category: 'Carbohydrate',
      tags: ['turkish', 'whole-grain', 'traditional'],
      imageUrl: 'https://images.unsplash.com/photo-1586444248902-2f64eddc13df?w=400',
      defaultUnit: 'g',
      caloriesPer100g: 342,
      proteinPer100g: 12,
      carbsPer100g: 76,
      fatsPer100g: 1.3,
    ),
    FoodItem(
      id: '7',
      name: 'Ekmek (Tam Buğday)',
      category: 'Carbohydrate',
      tags: ['turkish', 'bread', 'whole-wheat'],
      imageUrl: 'https://images.unsplash.com/photo-1509440159596-0249088772ff?w=400',
      defaultUnit: 'slice',
      caloriesPer100g: 247,
      proteinPer100g: 13,
      carbsPer100g: 41,
      fatsPer100g: 4.2,
    ),

    // Turkish Vegetables
    FoodItem(
      id: '8',
      name: 'Patlıcan Salatası',
      category: 'Vegetable',
      tags: ['turkish', 'salad', 'roasted'],
      imageUrl: 'https://images.unsplash.com/photo-1346347852772-e8b5b2b2c6b5?w=400',
      defaultUnit: 'portion',
      caloriesPer100g: 85,
      proteinPer100g: 2,
      carbsPer100g: 12,
      fatsPer100g: 4,
    ),
    FoodItem(
      id: '9',
      name: 'Çoban Salatası',
      category: 'Vegetable',
      tags: ['turkish', 'fresh', 'salad'],
      imageUrl: 'https://images.unsplash.com/photo-1512621776951-a57141f2eefd?w=400',
      defaultUnit: 'portion',
      caloriesPer100g: 45,
      proteinPer100g: 2,
      carbsPer100g: 8,
      fatsPer100g: 1,
    ),

    // Healthy Fats & Nuts
    FoodItem(
      id: '10',
      name: 'Ceviz',
      category: 'Healthy Fat',
      tags: ['nuts', 'omega-3', 'brain-food'],
      imageUrl: 'https://images.unsplash.com/photo-**********-cd47e0ef937f?w=400',
      defaultUnit: 'g',
      caloriesPer100g: 654,
      proteinPer100g: 15,
      carbsPer100g: 14,
      fatsPer100g: 65,
    ),
    FoodItem(
      id: '11',
      name: 'Zeytinyağı',
      category: 'Healthy Fat',
      tags: ['turkish', 'olive-oil', 'cooking'],
      imageUrl: 'https://images.unsplash.com/photo-1474979266404-7eaacbcd87c5?w=400',
      defaultUnit: 'tbsp',
      caloriesPer100g: 884,
      proteinPer100g: 0,
      carbsPer100g: 0,
      fatsPer100g: 100,
    ),

    // Turkish Fruits
    FoodItem(
      id: '12',
      name: 'İncir',
      category: 'Fruit',
      tags: ['turkish', 'fresh', 'fiber'],
      imageUrl: 'https://images.unsplash.com/photo-1568702846914-96b305d2aaeb?w=400',
      defaultUnit: 'pieces',
      caloriesPer100g: 74,
      proteinPer100g: 0.8,
      carbsPer100g: 19,
      fatsPer100g: 0.3,
    ),
    FoodItem(
      id: '13',
      name: 'Üzüm',
      category: 'Fruit',
      tags: ['turkish', 'antioxidants', 'sweet'],
      imageUrl: 'https://images.unsplash.com/photo-1537640538966-79f369143f8f?w=400',
      defaultUnit: 'g',
      caloriesPer100g: 62,
      proteinPer100g: 0.6,
      carbsPer100g: 16,
      fatsPer100g: 0.2,
    ),

    // International Favorites
    FoodItem(
      id: '14',
      name: 'Avocado Toast',
      category: 'Healthy Fat',
      tags: ['trendy', 'healthy', 'breakfast'],
      imageUrl: 'https://images.unsplash.com/photo-1541519227354-08fa5d50c44d?w=400',
      defaultUnit: 'slice',
      caloriesPer100g: 160,
      proteinPer100g: 2,
      carbsPer100g: 9,
      fatsPer100g: 15,
    ),
    FoodItem(
      id: '15',
      name: 'Greek Yogurt',
      category: 'Dairy',
      tags: ['high-protein', 'probiotics', 'creamy'],
      imageUrl: 'https://images.unsplash.com/photo-1571212515416-fef01fc43637?w=400',
      defaultUnit: 'g',
      caloriesPer100g: 59,
      proteinPer100g: 10,
      carbsPer100g: 3.6,
      fatsPer100g: 0.4,
    ),
  ];
});

class AddFoodItemPage extends HookConsumerWidget {
  final String mealId;
  final String mealName;
  final NutritionTemplateMealItem? existingItem; // null for add, non-null for edit

  const AddFoodItemPage({
    super.key,
    required this.mealId,
    required this.mealName,
    this.existingItem,
  });

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final foodNameController = useTextEditingController(
      text: existingItem?.foodName ?? ''
    );
    final quantityController = useTextEditingController(
      text: existingItem?.quantity.toString() ?? ''
    );
    final notesController = useTextEditingController(
      text: existingItem?.notes ?? ''
    );

    final selectedUnit = useState<String>(existingItem?.unit ?? 'g');
    final selectedFoodItem = useState<FoodItem?>(null);
    final isLoading = useState<bool>(false);
    final showFoodSelection = useState<bool>(existingItem == null);

    // Auto-focus on food name when screen opens (only if not selecting from list)
    final foodNameFocusNode = useFocusNode();
    useEffect(() {
      if (!showFoodSelection.value) {
        WidgetsBinding.instance.addPostFrameCallback((_) {
          foodNameFocusNode.requestFocus();
        });
      }
      return null;
    }, [showFoodSelection.value]);

    // Save food item function
    Future<void> saveFoodItem() async {
      // Validation
      if (foodNameController.text.trim().isEmpty) {
        _showError(context, 'Please enter a food name');
        return;
      }

      final quantity = double.tryParse(quantityController.text);
      if (quantity == null || quantity <= 0) {
        _showError(context, 'Please enter a valid quantity');
        return;
      }

      isLoading.value = true;

      try {
        // Create food item object to return to Page 2
        final foodItem = NutritionTemplateMealItem(
          id: existingItem?.id ?? 'item_${DateTime.now().millisecondsSinceEpoch}',
          mealId: mealId,
          foodName: foodNameController.text.trim(),
          quantity: quantity,
          unit: selectedUnit.value,
          notes: notesController.text.trim().isEmpty 
              ? null 
              : notesController.text.trim(),
          createdAt: existingItem?.createdAt ?? DateTime.now(),
          updatedAt: DateTime.now(),
        );

        // Simulate processing delay
        await Future.delayed(const Duration(milliseconds: 500));

        if (context.mounted) {
          // Return the food item to the previous screen
          Navigator.pop(context, foodItem);
        }
      } catch (e) {
        if (context.mounted) {
          _showError(context, 'Error saving food item: $e');
        }
      } finally {
        isLoading.value = false;
      }
    }

    return Scaffold(
      backgroundColor: const Color(0xFF111827),
      appBar: AppBar(
        backgroundColor: const Color(0xFF111827),
        elevation: 0,
        leading: IconButton(
          icon: const Icon(Icons.arrow_back, color: Colors.white),
          onPressed: () => Navigator.pop(context),
        ),
        title: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              existingItem == null ? 'Add Food Item' : 'Edit Food Item',
              style: const TextStyle(
                color: Colors.white,
                fontSize: 20,
                fontWeight: FontWeight.w600,
              ),
            ),
            Text(
              'to $mealName',
              style: TextStyle(
                color: Colors.grey[400],
                fontSize: 14,
                fontWeight: FontWeight.w400,
              ),
            ),
          ],
        ),
        actions: [
          Container(
            margin: const EdgeInsets.only(right: 16),
            child: ElevatedButton(
              onPressed: isLoading.value ? null : saveFoodItem,
              style: ElevatedButton.styleFrom(
                backgroundColor: const Color(0xFFFACC15),
                foregroundColor: const Color(0xFF111827),
                elevation: 0,
                padding: const EdgeInsets.symmetric(horizontal: 20, vertical: 8),
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(8),
                ),
              ),
              child: isLoading.value
                  ? const SizedBox(
                      width: 16,
                      height: 16,
                      child: CircularProgressIndicator(
                        strokeWidth: 2,
                        color: Color(0xFF111827),
                      ),
                    )
                  : Text(
                      existingItem == null ? 'Add' : 'Update',
                      style: const TextStyle(
                        fontWeight: FontWeight.w600,
                        fontSize: 14,
                      ),
                    ),
            ),
          ),
        ],
      ),
      body: SingleChildScrollView(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Food Selection Toggle
            if (existingItem == null) ...[
              Row(
                children: [
                  Expanded(
                    child: ElevatedButton.icon(
                      onPressed: () => showFoodSelection.value = true,
                      icon: const Icon(Icons.restaurant_menu),
                      label: const Text('Select from Food Database'),
                      style: ElevatedButton.styleFrom(
                        backgroundColor: showFoodSelection.value
                            ? const Color(0xFFFACC15)
                            : const Color(0xFF374151),
                        foregroundColor: showFoodSelection.value
                            ? const Color(0xFF111827)
                            : Colors.white,
                        padding: const EdgeInsets.symmetric(vertical: 12),
                      ),
                    ),
                  ),
                  const SizedBox(width: 12),
                  Expanded(
                    child: ElevatedButton.icon(
                      onPressed: () => showFoodSelection.value = false,
                      icon: const Icon(Icons.edit),
                      label: const Text('Enter Manually'),
                      style: ElevatedButton.styleFrom(
                        backgroundColor: !showFoodSelection.value
                            ? const Color(0xFFFACC15)
                            : const Color(0xFF374151),
                        foregroundColor: !showFoodSelection.value
                            ? const Color(0xFF111827)
                            : Colors.white,
                        padding: const EdgeInsets.symmetric(vertical: 12),
                      ),
                    ),
                  ),
                ],
              ),
              const SizedBox(height: 24),
            ],

            // Food Selection or Manual Entry
            if (showFoodSelection.value)
              _buildFoodSelectionSection(selectedFoodItem, foodNameController, selectedUnit)
            else ...[
              // Manual Food Name Entry
              _buildSectionTitle('Food Name *'),
              const SizedBox(height: 8),
              _buildTextField(
                controller: foodNameController,
                focusNode: foodNameFocusNode,
                hintText: 'e.g., Chicken Breast',
                keyboardType: TextInputType.text,
              ),
            ],
            
            const SizedBox(height: 24),
            
            // Quantity and Unit
            _buildSectionTitle('Quantity & Unit *'),
            const SizedBox(height: 8),
            Row(
              children: [
                Expanded(
                  flex: 2,
                  child: _buildTextField(
                    controller: quantityController,
                    hintText: '150',
                    keyboardType: TextInputType.number,
                  ),
                ),
                const SizedBox(width: 12),
                Expanded(
                  child: _buildUnitDropdown(selectedUnit),
                ),
              ],
            ),
            
            const SizedBox(height: 24),
            
            // Notes
            _buildSectionTitle('Notes (Optional)'),
            const SizedBox(height: 8),
            _buildTextField(
              controller: notesController,
              hintText: 'Add cooking notes, brand preferences, etc.',
              keyboardType: TextInputType.multiline,
              maxLines: 3,
            ),
            
            const SizedBox(height: 32),
            
            // Action Buttons
            Row(
              children: [
                Expanded(
                  child: OutlinedButton(
                    onPressed: () => Navigator.pop(context),
                    style: OutlinedButton.styleFrom(
                      foregroundColor: Colors.grey[400],
                      side: BorderSide(color: Colors.grey[600]!),
                      padding: const EdgeInsets.symmetric(vertical: 16),
                      shape: RoundedRectangleBorder(
                        borderRadius: BorderRadius.circular(12),
                      ),
                    ),
                    child: const Text(
                      'Cancel',
                      style: TextStyle(
                        fontSize: 16,
                        fontWeight: FontWeight.w600,
                      ),
                    ),
                  ),
                ),
                const SizedBox(width: 16),
                Expanded(
                  child: ElevatedButton(
                    onPressed: isLoading.value ? null : saveFoodItem,
                    style: ElevatedButton.styleFrom(
                      backgroundColor: const Color(0xFFFACC15),
                      foregroundColor: const Color(0xFF111827),
                      padding: const EdgeInsets.symmetric(vertical: 16),
                      shape: RoundedRectangleBorder(
                        borderRadius: BorderRadius.circular(12),
                      ),
                    ),
                    child: isLoading.value
                        ? const SizedBox(
                            width: 20,
                            height: 20,
                            child: CircularProgressIndicator(
                              strokeWidth: 2,
                              color: Color(0xFF111827),
                            ),
                          )
                        : Text(
                            existingItem == null ? 'Add Food Item' : 'Update Food Item',
                            style: const TextStyle(
                              fontSize: 16,
                              fontWeight: FontWeight.w600,
                            ),
                          ),
                  ),
                ),
              ],
            ),
            
            const SizedBox(height: 32),
          ],
        ),
      ),
    );
  }

  // Helper methods
  void _showError(BuildContext context, String message) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(message),
        backgroundColor: Colors.red,
      ),
    );
  }

  Widget _buildSectionTitle(String title) {
    return Text(
      title,
      style: const TextStyle(
        color: Colors.white,
        fontSize: 16,
        fontWeight: FontWeight.w600,
      ),
    );
  }

  Widget _buildTextField({
    required TextEditingController controller,
    FocusNode? focusNode,
    required String hintText,
    required TextInputType keyboardType,
    int maxLines = 1,
  }) {
    return TextField(
      controller: controller,
      focusNode: focusNode,
      keyboardType: keyboardType,
      maxLines: maxLines,
      style: const TextStyle(color: Colors.white),
      decoration: InputDecoration(
        hintText: hintText,
        hintStyle: TextStyle(color: Colors.grey[400]),
        filled: true,
        fillColor: const Color(0xFF1F2937),
        border: OutlineInputBorder(
          borderRadius: BorderRadius.circular(12),
          borderSide: const BorderSide(color: Color(0xFF374151)),
        ),
        enabledBorder: OutlineInputBorder(
          borderRadius: BorderRadius.circular(12),
          borderSide: const BorderSide(color: Color(0xFF374151)),
        ),
        focusedBorder: OutlineInputBorder(
          borderRadius: BorderRadius.circular(12),
          borderSide: const BorderSide(color: Color(0xFFFACC15)),
        ),
        contentPadding: const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
      ),
    );
  }

  Widget _buildUnitDropdown(ValueNotifier<String> selectedUnit) {
    const units = ['g', 'ml', 'cups', 'pieces', 'slices', 'portion', 'tbsp', 'tsp', 'oz'];
    
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 4),
      decoration: BoxDecoration(
        color: const Color(0xFF1F2937),
        borderRadius: BorderRadius.circular(12),
        border: Border.all(color: const Color(0xFF374151)),
      ),
      child: DropdownButtonHideUnderline(
        child: DropdownButton<String>(
          value: selectedUnit.value,
          dropdownColor: const Color(0xFF1F2937),
          style: const TextStyle(color: Colors.white),
          icon: const Icon(Icons.keyboard_arrow_down, color: Colors.white),
          items: units.map((unit) {
            return DropdownMenuItem<String>(
              value: unit,
              child: Text(unit),
            );
          }).toList(),
          onChanged: (value) {
            if (value != null) {
              selectedUnit.value = value;
            }
          },
        ),
      ),
    );
  }

  Widget _buildFoodSelectionSection(
    ValueNotifier<FoodItem?> selectedFoodItem,
    TextEditingController foodNameController,
    ValueNotifier<String> selectedUnit,
  ) {
    return HookConsumer(
      builder: (context, ref, child) {
        final foodItemsAsync = ref.watch(foodItemsProvider);
        final selectedCategory = useState<String>('All');

        return Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            _buildSectionTitle('Select Food Item *'),
            const SizedBox(height: 8),

            // Food Categories
            Container(
              height: 50,
              child: ListView(
                scrollDirection: Axis.horizontal,
                children: [
                  _buildCategoryChip('All', selectedCategory.value == 'All', () {
                    selectedCategory.value = 'All';
                  }),
                  _buildCategoryChip('Protein', selectedCategory.value == 'Protein', () {
                    selectedCategory.value = 'Protein';
                  }),
                  _buildCategoryChip('Carbohydrate', selectedCategory.value == 'Carbohydrate', () {
                    selectedCategory.value = 'Carbohydrate';
                  }),
                  _buildCategoryChip('Vegetable', selectedCategory.value == 'Vegetable', () {
                    selectedCategory.value = 'Vegetable';
                  }),
                  _buildCategoryChip('Healthy Fat', selectedCategory.value == 'Healthy Fat', () {
                    selectedCategory.value = 'Healthy Fat';
                  }),
                  _buildCategoryChip('Dairy', selectedCategory.value == 'Dairy', () {
                    selectedCategory.value = 'Dairy';
                  }),
                  _buildCategoryChip('Fruit', selectedCategory.value == 'Fruit', () {
                    selectedCategory.value = 'Fruit';
                  }),
                ],
              ),
            ),

            const SizedBox(height: 16),

            // Food Items Grid
            Container(
              height: 300,
              child: foodItemsAsync.when(
                data: (foodItems) {
                  // Filter by category
                  final filteredItems = selectedCategory.value == 'All'
                      ? foodItems
                      : foodItems.where((item) => item.category == selectedCategory.value).toList();

                  if (filteredItems.isEmpty) {
                    return Center(
                      child: Text(
                        'No food items found',
                        style: TextStyle(color: Colors.grey),
                      ),
                    );
                  }

                  return GridView.builder(
                    gridDelegate: const SliverGridDelegateWithFixedCrossAxisCount(
                      crossAxisCount: 2,
                      childAspectRatio: 0.8,
                      crossAxisSpacing: 12,
                      mainAxisSpacing: 12,
                    ),
                    itemCount: filteredItems.length,
                    itemBuilder: (context, index) {
                      final item = filteredItems[index];
                      return _buildFoodItemCard(
                        item,
                        selectedFoodItem,
                        foodNameController,
                        selectedUnit,
                      );
                    },
                  );
                },
                loading: () => Center(child: CircularProgressIndicator()),
                error: (error, stack) => Center(
                  child: Text(
                    'Error loading food items',
                    style: TextStyle(color: Colors.red),
                  ),
                ),
              ),
            ),
          ],
        );
      },
    );
  }

  Widget _buildCategoryChip(String category, bool isSelected, VoidCallback onTap) {
    return Container(
      margin: const EdgeInsets.only(right: 8),
      child: FilterChip(
        label: Text(category),
        selected: isSelected,
        onSelected: (selected) {
          if (selected) onTap();
        },
        backgroundColor: const Color(0xFF374151),
        selectedColor: const Color(0xFFFACC15),
        labelStyle: TextStyle(
          color: isSelected ? const Color(0xFF111827) : Colors.white,
          fontWeight: FontWeight.w500,
        ),
      ),
    );
  }

  Widget _buildFoodItemCard(
    FoodItem foodItem,
    ValueNotifier<FoodItem?> selectedFoodItem,
    TextEditingController foodNameController,
    ValueNotifier<String> selectedUnit,
  ) {
    return ValueListenableBuilder<FoodItem?>(
      valueListenable: selectedFoodItem,
      builder: (context, selected, child) {
        final isSelected = selected?.id == foodItem.id;

        return GestureDetector(
          onTap: () {
            print('🍽️ Selected food: ${foodItem.name}');
            foodNameController.text = foodItem.name;
            selectedUnit.value = foodItem.defaultUnit;
            selectedFoodItem.value = foodItem;
          },
          child: Container(
            decoration: BoxDecoration(
              color: isSelected ? const Color(0xFF374151) : const Color(0xFF1F2937),
              borderRadius: BorderRadius.circular(12),
              border: Border.all(
                color: isSelected ? const Color(0xFFFACC15) : const Color(0xFF374151),
                width: isSelected ? 2 : 1,
              ),
            ),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Food Image
            Expanded(
              flex: 3,
              child: Container(
                width: double.infinity,
                decoration: BoxDecoration(
                  borderRadius: const BorderRadius.vertical(top: Radius.circular(12)),
                  image: DecorationImage(
                    image: NetworkImage(foodItem.imageUrl ?? 'https://images.unsplash.com/photo-1546069901-ba9599a7e63c?w=400'),
                    fit: BoxFit.cover,
                  ),
                ),
              ),
            ),

            // Food Info
            Expanded(
              flex: 2,
              child: Padding(
                padding: const EdgeInsets.all(8),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      foodItem.name,
                      style: const TextStyle(
                        color: Colors.white,
                        fontSize: 14,
                        fontWeight: FontWeight.w600,
                      ),
                      maxLines: 1,
                      overflow: TextOverflow.ellipsis,
                    ),
                    const SizedBox(height: 4),
                    Container(
                      padding: const EdgeInsets.symmetric(horizontal: 6, vertical: 2),
                      decoration: BoxDecoration(
                        color: const Color(0xFFFACC15).withValues(alpha: 0.2),
                        borderRadius: BorderRadius.circular(8),
                      ),
                      child: Text(
                        foodItem.category,
                        style: const TextStyle(
                          color: Color(0xFFFACC15),
                          fontSize: 10,
                          fontWeight: FontWeight.w500,
                        ),
                      ),
                    ),
                  ],
                ),
              ),
            ),
          ],
        ),
      ),
    );
      },
    );
  }
}
