import 'package:flutter/material.dart';
import 'package:hooks_riverpod/hooks_riverpod.dart';
import 'package:flutter_hooks/flutter_hooks.dart';
import 'package:go_router/go_router.dart';
import 'package:fitgo_app/src/shared/constants/app_text_style.dart';
import 'package:fitgo_app/src/shared/extensions/string_hardcoded.dart';
import '../application/workout_plan_template_provider.dart';
import '../domain/workout_plan_template.dart';
import 'workout_plan_template_edit_page.dart';

class WorkoutPlanDraftListPage extends HookConsumerWidget {
  const WorkoutPlanDraftListPage({super.key});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final searchController = useTextEditingController();
    final selectedCategory = useState<String>('All');
    final currentPage = useState<int>(1);
    final isLoadingMore = useState<bool>(false);
    final hasMoreData = useState<bool>(true);
    final templatesAsync = ref.watch(workoutPlanTemplateNotifierProvider);

    return Scaffold(
      backgroundColor: const Color(0xFF111827),
      appBar: AppBar(
        backgroundColor: const Color(0xFF111827),
        elevation: 0,
        leading: IconButton(
          icon: const Icon(Icons.arrow_back, color: Colors.white),
          onPressed: () => Navigator.of(context).pop(),
        ),
        title: Text(
          'Exercise Templates',
          style: ATextStyle.title.copyWith(
            color: Colors.white,
            fontSize: 20,
            fontWeight: FontWeight.bold,
          ),
        ),
        actions: [
          IconButton(
            icon: const Icon(Icons.add, color: Color(0xFFFACC15), size: 28),
            onPressed: () => _navigateToCreateTemplate(context),
          ),
        ],
      ),
      body: Column(
        children: [
          // Search and Filter Section
          Padding(
            padding: const EdgeInsets.all(16),
            child: Column(
              children: [
                // Search Bar
                Container(
                  decoration: BoxDecoration(
                    color: const Color(0xFF374151),
                    borderRadius: BorderRadius.circular(12),
                  ),
                  child: TextField(
                    controller: searchController,
                    style: const TextStyle(color: Colors.white),
                    decoration: InputDecoration(
                      hintText: 'Search templates...',
                      hintStyle: TextStyle(
                        color: Colors.white.withOpacity(0.6),
                      ),
                      prefixIcon: Icon(
                        Icons.search,
                        color: Colors.white.withOpacity(0.6),
                      ),
                      border: InputBorder.none,
                      contentPadding: const EdgeInsets.symmetric(
                        horizontal: 16,
                        vertical: 12,
                      ),
                    ),
                    onChanged: (value) {
                      // TODO: Implement search functionality
                    },
                  ),
                ),
                const SizedBox(height: 16),

                // Category Filter
                SizedBox(
                  height: 40,
                  child: ListView(
                    scrollDirection: Axis.horizontal,
                    children: [
                      _buildCategoryChip('All', selectedCategory.value, (
                        category,
                      ) {
                        selectedCategory.value = category;
                      }),
                      _buildCategoryChip('Strength', selectedCategory.value, (
                        category,
                      ) {
                        selectedCategory.value = category;
                      }),
                      _buildCategoryChip('Cardio', selectedCategory.value, (
                        category,
                      ) {
                        selectedCategory.value = category;
                      }),
                      _buildCategoryChip(
                        'Flexibility',
                        selectedCategory.value,
                        (category) {
                          selectedCategory.value = category;
                        },
                      ),
                      _buildCategoryChip('HIIT', selectedCategory.value, (
                        category,
                      ) {
                        selectedCategory.value = category;
                      }),
                    ],
                  ),
                ),
              ],
            ),
          ),

          // Templates List
          Expanded(
            child: templatesAsync.when(
              loading:
                  () => const Center(
                    child: CircularProgressIndicator(color: Color(0xFFFACC15)),
                  ),
              error:
                  (error, stack) => Center(
                    child: Column(
                      mainAxisAlignment: MainAxisAlignment.center,
                      children: [
                        Icon(
                          Icons.error_outline,
                          color: Colors.red.shade400,
                          size: 48,
                        ),
                        const SizedBox(height: 16),
                        Text(
                          'Failed to load templates',
                          style: ATextStyle.medium.copyWith(
                            color: Colors.white,
                          ),
                        ),
                        const SizedBox(height: 8),
                        Text(
                          error.toString(),
                          style: ATextStyle.small.copyWith(
                            color: Colors.white70,
                          ),
                          textAlign: TextAlign.center,
                        ),
                        const SizedBox(height: 16),
                        ElevatedButton(
                          onPressed:
                              () => ref.refresh(
                                workoutPlanTemplateNotifierProvider,
                              ),
                          style: ElevatedButton.styleFrom(
                            backgroundColor: const Color(0xFFFACC15),
                            foregroundColor: Colors.black,
                          ),
                          child: const Text('Retry'),
                        ),
                      ],
                    ),
                  ),
              data: (templates) {
                final filteredTemplates = _filterTemplates(
                  templates,
                  searchController.text,
                  selectedCategory.value,
                );

                if (filteredTemplates.isEmpty) {
                  return _buildEmptyState(context);
                }

                return NotificationListener<ScrollNotification>(
                  onNotification: (ScrollNotification scrollInfo) {
                    if (!isLoadingMore.value &&
                        hasMoreData.value &&
                        scrollInfo.metrics.pixels ==
                            scrollInfo.metrics.maxScrollExtent) {
                      _loadMoreTemplates(
                        ref,
                        currentPage,
                        isLoadingMore,
                        hasMoreData,
                      );
                    }
                    return false;
                  },
                  child: ListView.builder(
                    padding: const EdgeInsets.symmetric(horizontal: 16),
                    itemCount:
                        filteredTemplates.length +
                        (isLoadingMore.value ? 1 : 0),
                    itemBuilder: (context, index) {
                      if (index == filteredTemplates.length) {
                        return _buildLoadingIndicator();
                      }
                      final template = filteredTemplates[index];
                      return _buildTemplateCard(context, template, ref);
                    },
                  ),
                );
              },
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildCategoryChip(
    String category,
    String selectedCategory,
    Function(String) onSelected,
  ) {
    final isSelected = category == selectedCategory;
    return Container(
      margin: const EdgeInsets.only(right: 8),
      child: FilterChip(
        label: Text(
          category,
          style: TextStyle(
            color: isSelected ? Colors.black : Colors.white,
            fontWeight: isSelected ? FontWeight.w600 : FontWeight.normal,
          ),
        ),
        selected: isSelected,
        onSelected: (selected) => onSelected(category),
        backgroundColor: const Color(0xFF374151),
        selectedColor: const Color(0xFFFACC15),
        checkmarkColor: Colors.black,
        side: BorderSide.none,
      ),
    );
  }

  Widget _buildTemplateCard(
    BuildContext context,
    WorkoutPlanTemplate template,
    WidgetRef ref,
  ) {
    final exerciseCount = template.plans.fold<int>(
      0,
      (total, plan) => total + plan.exercises.length,
    );

    return Container(
      margin: const EdgeInsets.only(bottom: 12),
      decoration: BoxDecoration(
        color: const Color(0xFF1F2937),
        borderRadius: BorderRadius.circular(12),
        border: Border.all(color: Colors.white.withOpacity(0.1), width: 1),
      ),
      child: Material(
        color: Colors.transparent,
        child: InkWell(
          borderRadius: BorderRadius.circular(12),
          onTap: () => _navigateToEditTemplate(context, template),
          child: Padding(
            padding: const EdgeInsets.all(16),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Row(
                  children: [
                    Expanded(
                      child: Text(
                        template.name,
                        style: ATextStyle.medium.copyWith(
                          color: Colors.white,
                          fontWeight: FontWeight.w600,
                        ),
                      ),
                    ),
                    PopupMenuButton<String>(
                      icon: Icon(
                        Icons.more_vert,
                        color: Colors.white.withOpacity(0.7),
                      ),
                      color: const Color(0xFF374151),
                      onSelected: (value) {
                        if (value == 'edit') {
                          _navigateToEditTemplate(context, template);
                        } else if (value == 'delete') {
                          _showDeleteConfirmation(context, template, ref);
                        }
                      },
                      itemBuilder:
                          (context) => [
                            PopupMenuItem(
                              value: 'edit',
                              child: Row(
                                children: [
                                  const Icon(
                                    Icons.edit,
                                    color: Colors.white,
                                    size: 20,
                                  ),
                                  const SizedBox(width: 8),
                                  Text(
                                    'Edit',
                                    style: ATextStyle.small.copyWith(
                                      color: Colors.white,
                                    ),
                                  ),
                                ],
                              ),
                            ),
                            PopupMenuItem(
                              value: 'delete',
                              child: Row(
                                children: [
                                  const Icon(
                                    Icons.delete,
                                    color: Colors.red,
                                    size: 20,
                                  ),
                                  const SizedBox(width: 8),
                                  Text(
                                    'Delete',
                                    style: ATextStyle.small.copyWith(
                                      color: Colors.red,
                                    ),
                                  ),
                                ],
                              ),
                            ),
                          ],
                    ),
                  ],
                ),
                if (template.description != null) ...[
                  const SizedBox(height: 8),
                  Text(
                    template.description!,
                    style: ATextStyle.small.copyWith(
                      color: Colors.white.withOpacity(0.7),
                    ),
                    maxLines: 2,
                    overflow: TextOverflow.ellipsis,
                  ),
                ],
                const SizedBox(height: 12),

                // Tags
                if (template.tags.isNotEmpty) ...[
                  Wrap(
                    spacing: 6,
                    runSpacing: 6,
                    children:
                        template.tags.map((tag) {
                          return Container(
                            padding: const EdgeInsets.symmetric(
                              horizontal: 8,
                              vertical: 4,
                            ),
                            decoration: BoxDecoration(
                              color: const Color(0xFF3B82F6).withOpacity(0.2),
                              borderRadius: BorderRadius.circular(6),
                              border: Border.all(
                                color: const Color(0xFF3B82F6).withOpacity(0.3),
                              ),
                            ),
                            child: Text(
                              tag,
                              style: ATextStyle.small.copyWith(
                                color: const Color(0xFF3B82F6),
                                fontSize: 11,
                              ),
                            ),
                          );
                        }).toList(),
                  ),
                  const SizedBox(height: 12),
                ],

                // Stats Row
                Row(
                  children: [
                    Icon(
                      Icons.fitness_center,
                      color: Colors.white.withOpacity(0.6),
                      size: 16,
                    ),
                    const SizedBox(width: 4),
                    Text(
                      '$exerciseCount exercises',
                      style: ATextStyle.small.copyWith(
                        color: Colors.white.withOpacity(0.6),
                      ),
                    ),
                    const SizedBox(width: 16),
                    Icon(
                      Icons.schedule,
                      color: Colors.white.withOpacity(0.6),
                      size: 16,
                    ),
                    const SizedBox(width: 4),
                    Text(
                      _formatLastModified(template.updatedAt),
                      style: ATextStyle.small.copyWith(
                        color: Colors.white.withOpacity(0.6),
                      ),
                    ),
                  ],
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildEmptyState(BuildContext context) {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(
            Icons.fitness_center,
            color: Colors.white.withOpacity(0.3),
            size: 64,
          ),
          const SizedBox(height: 16),
          Text(
            'No workout templates yet',
            style: ATextStyle.medium.copyWith(
              color: Colors.white,
              fontWeight: FontWeight.w600,
            ),
          ),
          const SizedBox(height: 8),
          Text(
            'Create your first workout template to get started',
            style: ATextStyle.small.copyWith(
              color: Colors.white.withOpacity(0.7),
            ),
            textAlign: TextAlign.center,
          ),
          const SizedBox(height: 24),
          ElevatedButton.icon(
            onPressed: () => _navigateToCreateTemplate(context),
            style: ElevatedButton.styleFrom(
              backgroundColor: const Color(0xFFFACC15),
              foregroundColor: Colors.black,
              padding: const EdgeInsets.symmetric(horizontal: 24, vertical: 12),
            ),
            icon: const Icon(Icons.add),
            label: const Text('Create Template'),
          ),
        ],
      ),
    );
  }

  List<WorkoutPlanTemplate> _filterTemplates(
    List<WorkoutPlanTemplate> templates,
    String searchQuery,
    String selectedCategory,
  ) {
    return templates.where((template) {
      final matchesSearch =
          searchQuery.isEmpty ||
          template.name.toLowerCase().contains(searchQuery.toLowerCase()) ||
          (template.description?.toLowerCase().contains(
                searchQuery.toLowerCase(),
              ) ??
              false);

      final matchesCategory =
          selectedCategory == 'All' ||
          template.tags.any(
            (tag) => tag.toLowerCase() == selectedCategory.toLowerCase(),
          );

      return matchesSearch && matchesCategory;
    }).toList();
  }

  String _formatLastModified(DateTime dateTime) {
    final now = DateTime.now();
    final difference = now.difference(dateTime);

    if (difference.inDays > 0) {
      return '${difference.inDays}d ago';
    } else if (difference.inHours > 0) {
      return '${difference.inHours}h ago';
    } else if (difference.inMinutes > 0) {
      return '${difference.inMinutes}m ago';
    } else {
      return 'Just now';
    }
  }

  void _navigateToCreateTemplate(BuildContext context) {
    Navigator.of(context).push(
      MaterialPageRoute(
        builder: (context) => const WorkoutPlanTemplateEditPage(),
      ),
    );
  }

  void _navigateToEditTemplate(
    BuildContext context,
    WorkoutPlanTemplate template,
  ) {
    Navigator.of(context).push(
      MaterialPageRoute(
        builder: (context) => WorkoutPlanTemplateEditPage(template: template),
      ),
    );
  }

  // Pagination methods
  void _loadMoreTemplates(
    WidgetRef ref,
    ValueNotifier<int> currentPage,
    ValueNotifier<bool> isLoadingMore,
    ValueNotifier<bool> hasMoreData,
  ) async {
    if (isLoadingMore.value || !hasMoreData.value) return;

    isLoadingMore.value = true;
    currentPage.value++;

    try {
      // Simulate API call delay
      await Future.delayed(const Duration(milliseconds: 500));

      // For now, we'll simulate that there's no more data after page 3
      if (currentPage.value >= 3) {
        hasMoreData.value = false;
      }

      // In a real implementation, you would call the repository with pagination parameters
      // final newTemplates = await repository.getWorkoutPlanTemplates(
      //   instructorId: instructorId,
      //   page: currentPage.value,
      //   limit: 10,
      // );
    } catch (e) {
      // Handle error
      currentPage.value--;
    } finally {
      isLoadingMore.value = false;
    }
  }

  Widget _buildLoadingIndicator() {
    return Container(
      padding: const EdgeInsets.all(16),
      alignment: Alignment.center,
      child: const CircularProgressIndicator(
        valueColor: AlwaysStoppedAnimation<Color>(Color(0xFFFACC15)),
      ),
    );
  }

  void _showDeleteConfirmation(
    BuildContext context,
    WorkoutPlanTemplate template,
    WidgetRef ref,
  ) {
    showDialog(
      context: context,
      builder:
          (context) => AlertDialog(
            backgroundColor: const Color(0xFF374151),
            title: Text(
              'Delete Template',
              style: ATextStyle.medium.copyWith(color: Colors.white),
            ),
            content: Text(
              'Are you sure you want to delete "${template.name}"? This action cannot be undone.',
              style: ATextStyle.small.copyWith(color: Colors.white70),
            ),
            actions: [
              TextButton(
                onPressed: () => Navigator.of(context).pop(),
                child: Text(
                  'Cancel',
                  style: ATextStyle.small.copyWith(color: Colors.white70),
                ),
              ),
              TextButton(
                onPressed: () async {
                  Navigator.of(context).pop();
                  try {
                    await ref
                        .read(workoutPlanTemplateNotifierProvider.notifier)
                        .deleteTemplate(template.id);
                    if (context.mounted) {
                      ScaffoldMessenger.of(context).showSnackBar(
                        SnackBar(
                          content: Text('Template "${template.name}" deleted'),
                          backgroundColor: Colors.green,
                        ),
                      );
                    }
                  } catch (e) {
                    if (context.mounted) {
                      ScaffoldMessenger.of(context).showSnackBar(
                        SnackBar(
                          content: Text('Failed to delete template: $e'),
                          backgroundColor: Colors.red,
                        ),
                      );
                    }
                  }
                },
                child: Text(
                  'Delete',
                  style: ATextStyle.small.copyWith(color: Colors.red),
                ),
              ),
            ],
          ),
    );
  }
}
