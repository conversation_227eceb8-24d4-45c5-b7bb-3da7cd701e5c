import 'package:flutter/material.dart';
import 'package:flutter_hooks/flutter_hooks.dart';
import 'package:hooks_riverpod/hooks_riverpod.dart';
import '../domain/student_models.dart';
import '../domain/supplement_models.dart';
import '../application/supplement_provider.dart';
import '../../shared/constants/app_text_style.dart';

import 'supplement_assignment_form_page.dart';

class AssignSupplementToStudentPage extends HookConsumerWidget {
  final InstructorStudent student;
  final String instructorId;

  const AssignSupplementToStudentPage({
    super.key,
    required this.student,
    required this.instructorId,
  });

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final searchController = useTextEditingController();
    final selectedType = useState<SupplementType?>(null);
    final selectedCategory = useState<SupplementCategory?>(null);
    final searchQuery = useState<String>('');

    // Update search query when text changes
    useEffect(() {
      void listener() {
        searchQuery.value = searchController.text;
      }
      searchController.addListener(listener);
      return () => searchController.removeListener(listener);
    }, [searchController]);

    final filters = SupplementFilters(
      type: selectedType.value,
      category: selectedCategory.value,
      searchQuery: searchQuery.value.isEmpty ? null : searchQuery.value,
    );

    final supplementsAsync = ref.watch(supplementsProvider(filters));
    final studentSupplementsAsync = ref.watch(studentSupplementsProvider(
      StudentSupplementParams(studentId: student.id, instructorId: instructorId),
    ));

    return Scaffold(
      backgroundColor: const Color(0xFF111827),
      appBar: AppBar(
        backgroundColor: const Color(0xFF111827),
        title: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'Assign Supplements',
              style: ATextStyle.large.copyWith(
                color: Colors.white,
                fontWeight: FontWeight.bold,
              ),
            ),
            Text(
              'to ${student.name}',
              style: ATextStyle.small.copyWith(color: Colors.grey),
            ),
          ],
        ),
        leading: IconButton(
          icon: const Icon(Icons.arrow_back, color: Colors.white),
          onPressed: () => Navigator.pop(context),
        ),
        actions: [
          IconButton(
            icon: const Icon(Icons.add, color: Color(0xFFFACC15)),
            onPressed: () => _showCreateCustomSupplementDialog(context, ref),
            tooltip: 'Create Custom Supplement',
          ),
        ],
      ),
      body: Column(
        children: [
          // Current Assignments Section
          _buildCurrentAssignmentsSection(context, ref, studentSupplementsAsync),
          
          const Divider(color: Color(0xFF374151), height: 1),
          
          // Available Supplements Section
          Expanded(
            child: Column(
              children: [
                // Search and Filters
                _buildSearchAndFilters(
                  context,
                  searchController,
                  selectedType,
                  selectedCategory,
                  ref,
                ),
                
                // Supplements List
                Expanded(
                  child: _buildSupplementsList(context, ref, supplementsAsync),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildCurrentAssignmentsSection(
    BuildContext context,
    WidgetRef ref,
    AsyncValue<List<StudentSupplementAssignment>> studentSupplementsAsync,
  ) {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: const BoxDecoration(
        color: Color(0xFF1F2937),
        border: Border(bottom: BorderSide(color: Color(0xFF374151))),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              const Icon(Icons.medication, color: Color(0xFFFACC15), size: 20),
              const SizedBox(width: 8),
              Text(
                'Current Supplements',
                style: ATextStyle.medium.copyWith(
                  color: Colors.white,
                  fontWeight: FontWeight.bold,
                ),
              ),
            ],
          ),
          const SizedBox(height: 12),
          studentSupplementsAsync.when(
            data: (assignments) {
              if (assignments.isEmpty) {
                return Container(
                  padding: const EdgeInsets.all(16),
                  decoration: BoxDecoration(
                    color: const Color(0xFF374151),
                    borderRadius: BorderRadius.circular(8),
                  ),
                  child: Row(
                    children: [
                      const Icon(Icons.info_outline, color: Colors.grey, size: 16),
                      const SizedBox(width: 8),
                      Text(
                        'No supplements assigned yet',
                        style: ATextStyle.small.copyWith(color: Colors.grey),
                      ),
                    ],
                  ),
                );
              }

              return SizedBox(
                height: 100,
                child: ListView.builder(
                  scrollDirection: Axis.horizontal,
                  itemCount: assignments.length,
                  itemBuilder: (context, index) {
                    final assignment = assignments[index];
                    return _buildCurrentSupplementCard(context, ref, assignment);
                  },
                ),
              );
            },
            loading: () => const Center(
              child: CircularProgressIndicator(color: Color(0xFFFACC15)),
            ),
            error: (error, stack) => Container(
              padding: const EdgeInsets.all(16),
              decoration: BoxDecoration(
                color: Colors.red.withOpacity(0.1),
                borderRadius: BorderRadius.circular(8),
              ),
              child: Text(
                'Error loading current supplements: $error',
                style: ATextStyle.small.copyWith(color: Colors.red),
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildCurrentSupplementCard(
    BuildContext context,
    WidgetRef ref,
    StudentSupplementAssignment assignment,
  ) {
    return Container(
      width: 200,
      margin: const EdgeInsets.only(right: 12),
      padding: const EdgeInsets.all(12),
      decoration: BoxDecoration(
        color: const Color(0xFF374151),
        borderRadius: BorderRadius.circular(8),
        border: Border.all(color: const Color(0xFF4B5563)),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Expanded(
                child: Text(
                  assignment.supplement?.name ?? 'Unknown Supplement',
                  style: ATextStyle.small.copyWith(
                    color: Colors.white,
                    fontWeight: FontWeight.bold,
                  ),
                  maxLines: 1,
                  overflow: TextOverflow.ellipsis,
                ),
              ),
              Container(
                padding: const EdgeInsets.symmetric(horizontal: 6, vertical: 2),
                decoration: BoxDecoration(
                  color: _getStatusColor(assignment.status),
                  borderRadius: BorderRadius.circular(4),
                ),
                child: Text(
                  assignment.status.displayName,
                  style: ATextStyle.small.copyWith(color: Colors.white),
                ),
              ),
            ],
          ),
          const SizedBox(height: 4),
          Text(
            assignment.supplement?.brand ?? '',
            style: ATextStyle.small.copyWith(color: Colors.grey),
            maxLines: 1,
            overflow: TextOverflow.ellipsis,
          ),
          const SizedBox(height: 4),
          Text(
            'Quantity: ${assignment.quantity}',
            style: ATextStyle.small.copyWith(color: const Color(0xFFFACC15)),
            maxLines: 1,
            overflow: TextOverflow.ellipsis,
          ),
          const Spacer(),
          Row(
            children: [
              Expanded(
                child: TextButton(
                  onPressed: () => _editAssignment(context, ref, assignment),
                  style: TextButton.styleFrom(
                    padding: const EdgeInsets.symmetric(vertical: 4),
                    minimumSize: Size.zero,
                  ),
                  child: Text(
                    'Edit',
                    style: ATextStyle.small.copyWith(color: const Color(0xFFFACC15)),
                  ),
                ),
              ),
              TextButton(
                onPressed: () => _removeAssignment(context, ref, assignment),
                style: TextButton.styleFrom(
                  padding: const EdgeInsets.symmetric(vertical: 4),
                  minimumSize: Size.zero,
                ),
                child: Text(
                  'Remove',
                  style: ATextStyle.small.copyWith(color: Colors.red),
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildSearchAndFilters(
    BuildContext context,
    TextEditingController searchController,
    ValueNotifier<SupplementType?> selectedType,
    ValueNotifier<SupplementCategory?> selectedCategory,
    WidgetRef ref,
  ) {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: const BoxDecoration(
        color: Color(0xFF1F2937),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            'Available Supplements',
            style: ATextStyle.medium.copyWith(
              color: Colors.white,
              fontWeight: FontWeight.bold,
            ),
          ),
          const SizedBox(height: 12),
          
          // Search Bar
          TextField(
            controller: searchController,
            style: ATextStyle.medium.copyWith(color: Colors.white),
            decoration: InputDecoration(
              hintText: 'Search supplements...',
              hintStyle: ATextStyle.medium.copyWith(color: Colors.grey),
              prefixIcon: const Icon(Icons.search, color: Colors.grey),
              filled: true,
              fillColor: const Color(0xFF374151),
              border: OutlineInputBorder(
                borderRadius: BorderRadius.circular(8),
                borderSide: BorderSide.none,
              ),
              contentPadding: const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
            ),
            onChanged: (value) {
              // Search query will be updated automatically via useEffect
            },
          ),
          
          const SizedBox(height: 12),
          
          // Filter Chips
          SingleChildScrollView(
            scrollDirection: Axis.horizontal,
            child: Row(
              children: [
                _buildFilterChip(
                  'All Types',
                  selectedType.value == null,
                  () {
                    selectedType.value = null;
                  },
                ),
                ...SupplementType.values.map((type) => _buildFilterChip(
                  type.displayName,
                  selectedType.value == type,
                  () {
                    selectedType.value = selectedType.value == type ? null : type;
                  },
                )),
              ],
            ),
          ),
          
          const SizedBox(height: 8),
          
          SingleChildScrollView(
            scrollDirection: Axis.horizontal,
            child: Row(
              children: [
                _buildFilterChip(
                  'All Categories',
                  selectedCategory.value == null,
                  () {
                    selectedCategory.value = null;
                  },
                ),
                ...SupplementCategory.values.map((category) => _buildFilterChip(
                  category.displayName,
                  selectedCategory.value == category,
                  () {
                    selectedCategory.value = selectedCategory.value == category ? null : category;
                  },
                )),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildFilterChip(String label, bool isSelected, VoidCallback onTap) {
    return Padding(
      padding: const EdgeInsets.only(right: 8),
      child: FilterChip(
        label: Text(
          label,
          style: ATextStyle.small.copyWith(
            color: isSelected ? Colors.black : Colors.white,
          ),
        ),
        selected: isSelected,
        onSelected: (_) => onTap(),
        backgroundColor: const Color(0xFF374151),
        selectedColor: const Color(0xFFFACC15),
        checkmarkColor: Colors.black,
        side: BorderSide(
          color: isSelected ? const Color(0xFFFACC15) : const Color(0xFF4B5563),
        ),
      ),
    );
  }

  Widget _buildSupplementsList(
    BuildContext context,
    WidgetRef ref,
    AsyncValue<List<Supplement>> supplementsAsync,
  ) {
    return supplementsAsync.when(
      data: (supplements) {
        if (supplements.isEmpty) {
          return Center(
            child: Column(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                const Icon(Icons.medication_outlined, color: Colors.grey, size: 48),
                const SizedBox(height: 16),
                Text(
                  'No supplements found',
                  style: ATextStyle.medium.copyWith(color: Colors.grey),
                ),
                const SizedBox(height: 8),
                Text(
                  'Try adjusting your search or filters',
                  style: ATextStyle.small.copyWith(color: Colors.grey),
                ),
              ],
            ),
          );
        }

        return ListView.builder(
          padding: const EdgeInsets.all(16),
          itemCount: supplements.length,
          itemBuilder: (context, index) {
            final supplement = supplements[index];
            return _buildSupplementCard(context, ref, supplement);
          },
        );
      },
      loading: () => const Center(
        child: CircularProgressIndicator(color: Color(0xFFFACC15)),
      ),
      error: (error, stack) => Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            const Icon(Icons.error_outline, color: Colors.red, size: 48),
            const SizedBox(height: 16),
            Text(
              'Error loading supplements',
              style: ATextStyle.medium.copyWith(color: Colors.red),
            ),
            const SizedBox(height: 8),
            Text(
              error.toString(),
              style: ATextStyle.small.copyWith(color: Colors.grey),
              textAlign: TextAlign.center,
            ),
            const SizedBox(height: 16),
            ElevatedButton(
              onPressed: () => ref.invalidate(supplementsProvider),
              style: ElevatedButton.styleFrom(
                backgroundColor: const Color(0xFFFACC15),
                foregroundColor: Colors.black,
              ),
              child: const Text('Retry'),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildSupplementCard(
    BuildContext context,
    WidgetRef ref,
    Supplement supplement,
  ) {
    return Container(
      margin: const EdgeInsets.only(bottom: 12),
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: const Color(0xFF1F2937),
        borderRadius: BorderRadius.circular(12),
        border: Border.all(color: const Color(0xFF374151)),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              // Supplement Image
              Container(
                width: 60,
                height: 60,
                decoration: BoxDecoration(
                  color: const Color(0xFF374151),
                  borderRadius: BorderRadius.circular(8),
                  image: supplement.imageUrl != null
                      ? DecorationImage(
                          image: NetworkImage(supplement.imageUrl!),
                          fit: BoxFit.cover,
                        )
                      : null,
                ),
                child: supplement.imageUrl == null
                    ? const Icon(Icons.medication, color: Colors.grey, size: 30)
                    : null,
              ),

              const SizedBox(width: 12),

              // Supplement Info
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      supplement.name,
                      style: ATextStyle.medium.copyWith(
                        color: Colors.white,
                        fontWeight: FontWeight.bold,
                      ),
                      maxLines: 2,
                      overflow: TextOverflow.ellipsis,
                    ),
                    const SizedBox(height: 4),
                    Text(
                      supplement.brand,
                      style: ATextStyle.small.copyWith(color: const Color(0xFFFACC15)),
                    ),
                    const SizedBox(height: 4),
                    Row(
                      children: [
                        Container(
                          padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 2),
                          decoration: BoxDecoration(
                            color: _getTypeColor(supplement.type),
                            borderRadius: BorderRadius.circular(4),
                          ),
                          child: Text(
                            supplement.type.displayName,
                            style: ATextStyle.small.copyWith(color: Colors.white),
                          ),
                        ),
                        const SizedBox(width: 8),
                        Container(
                          padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 2),
                          decoration: BoxDecoration(
                            color: const Color(0xFF374151),
                            borderRadius: BorderRadius.circular(4),
                          ),
                          child: Text(
                            supplement.category.displayName,
                            style: ATextStyle.small.copyWith(color: Colors.grey),
                          ),
                        ),
                      ],
                    ),
                  ],
                ),
              ),

              // Assign Button
              ElevatedButton(
                onPressed: () => _assignSupplement(context, ref, supplement),
                style: ElevatedButton.styleFrom(
                  backgroundColor: const Color(0xFFFACC15),
                  foregroundColor: Colors.black,
                  padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
                  shape: RoundedRectangleBorder(
                    borderRadius: BorderRadius.circular(8),
                  ),
                ),
                child: const Text('Assign'),
              ),
            ],
          ),

          if (supplement.description.isNotEmpty) ...[
            const SizedBox(height: 12),
            Text(
              supplement.description,
              style: ATextStyle.small.copyWith(color: Colors.grey),
              maxLines: 2,
              overflow: TextOverflow.ellipsis,
            ),
          ],

          if (supplement.benefits.isNotEmpty) ...[
            const SizedBox(height: 8),
            Wrap(
              spacing: 6,
              runSpacing: 4,
              children: supplement.benefits.take(3).map((benefit) {
                return Container(
                  padding: const EdgeInsets.symmetric(horizontal: 6, vertical: 2),
                  decoration: BoxDecoration(
                    color: Colors.green.withOpacity(0.2),
                    borderRadius: BorderRadius.circular(4),
                  ),
                  child: Text(
                    benefit,
                    style: ATextStyle.small.copyWith(color: Colors.green),
                  ),
                );
              }).toList(),
            ),
          ],
        ],
      ),
    );
  }

  // Helper methods
  Color _getStatusColor(SupplementAssignmentStatus status) {
    switch (status) {
      case SupplementAssignmentStatus.active:
        return Colors.green;
      case SupplementAssignmentStatus.paused:
        return Colors.orange;
      case SupplementAssignmentStatus.completed:
        return Colors.blue;
      case SupplementAssignmentStatus.discontinued:
        return Colors.red;
    }
  }

  Color _getTypeColor(SupplementType type) {
    switch (type) {
      case SupplementType.protein:
        return Colors.blue;
      case SupplementType.creatine:
        return Colors.red;
      case SupplementType.vitamins:
        return Colors.green;
      case SupplementType.minerals:
        return Colors.purple;
      case SupplementType.preworkout:
        return Colors.orange;
      case SupplementType.postworkout:
        return Colors.teal;
      case SupplementType.fatburner:
        return Colors.pink;
      case SupplementType.bcaa:
        return Colors.indigo;
      case SupplementType.glutamine:
        return Colors.cyan;
      case SupplementType.omega3:
        return Colors.amber;
      case SupplementType.multivitamin:
        return Colors.lime;
      case SupplementType.other:
        return Colors.grey;
    }
  }

  // Action methods
  void _assignSupplement(BuildContext context, WidgetRef ref, Supplement supplement) {
    Navigator.push(
      context,
      MaterialPageRoute(
        builder: (context) => SupplementAssignmentFormPage(
          student: student,
          instructorId: instructorId,
          supplement: supplement,
        ),
      ),
    ).then((result) {
      if (result == true) {
        // Refresh the current assignments
        ref.invalidate(studentSupplementsProvider);
      }
    });
  }

  void _editAssignment(BuildContext context, WidgetRef ref, StudentSupplementAssignment assignment) {
    if (assignment.supplement == null) return;

    Navigator.push(
      context,
      MaterialPageRoute(
        builder: (context) => SupplementAssignmentFormPage(
          student: student,
          instructorId: instructorId,
          supplement: assignment.supplement!,
          existingAssignment: assignment,
        ),
      ),
    ).then((result) {
      if (result == true) {
        // Refresh the current assignments
        ref.invalidate(studentSupplementsProvider);
      }
    });
  }

  void _removeAssignment(BuildContext context, WidgetRef ref, StudentSupplementAssignment assignment) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        backgroundColor: const Color(0xFF1F2937),
        title: Text(
          'Remove Supplement',
          style: ATextStyle.medium.copyWith(color: Colors.white),
        ),
        content: Text(
          'Are you sure you want to remove "${assignment.supplement?.name}" from ${student.name}\'s supplements?',
          style: ATextStyle.small.copyWith(color: Colors.grey),
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: Text(
              'Cancel',
              style: ATextStyle.small.copyWith(color: Colors.grey),
            ),
          ),
          TextButton(
            onPressed: () async {
              Navigator.pop(context);

              final notifier = ref.read(supplementAssignmentNotifierProvider.notifier);
              final success = await notifier.removeAssignment(assignment.id);

              if (success && context.mounted) {
                ScaffoldMessenger.of(context).showSnackBar(
                  SnackBar(
                    content: Text('Supplement removed from ${student.name}'),
                    backgroundColor: Colors.green,
                  ),
                );
                ref.invalidate(studentSupplementsProvider);
              }
            },
            child: Text(
              'Remove',
              style: ATextStyle.small.copyWith(color: Colors.red),
            ),
          ),
        ],
      ),
    );
  }

  void _showCreateCustomSupplementDialog(BuildContext context, WidgetRef ref) {
    // TODO: Implement custom supplement creation dialog
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(
        content: Text('Custom supplement creation - Coming Soon!'),
        backgroundColor: Color(0xFFFACC15),
      ),
    );
  }
}
