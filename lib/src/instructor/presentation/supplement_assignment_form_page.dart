import 'package:flutter/material.dart';
import 'package:flutter_hooks/flutter_hooks.dart';
import 'package:hooks_riverpod/hooks_riverpod.dart';
import '../domain/student_models.dart';
import '../domain/supplement_models.dart';
import '../application/supplement_provider.dart';
import '../../shared/constants/app_text_style.dart';

class SupplementAssignmentFormPage extends HookConsumerWidget {
  final InstructorStudent student;
  final String instructorId;
  final Supplement supplement;
  final StudentSupplementAssignment? existingAssignment;

  const SupplementAssignmentFormPage({
    super.key,
    required this.student,
    required this.instructorId,
    required this.supplement,
    this.existingAssignment,
  });

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final isEditing = existingAssignment != null;
    
    // Form controllers
    final quantityController = useTextEditingController(
      text: existingAssignment?.quantity.toString() ?? '1',
    );
    final notesController = useTextEditingController(
      text: existingAssignment?.notes ?? '',
    );
    
    final isLoading = useState<bool>(false);
    final formKey = useMemoized(() => GlobalKey<FormState>());

    return Scaffold(
      backgroundColor: const Color(0xFF111827),
      appBar: AppBar(
        backgroundColor: const Color(0xFF111827),
        title: Text(
          isEditing ? 'Edit Supplement' : 'Assign Supplement',
          style: ATextStyle.large.copyWith(
            color: Colors.white,
            fontWeight: FontWeight.bold,
          ),
        ),
        leading: IconButton(
          icon: const Icon(Icons.arrow_back, color: Colors.white),
          onPressed: () => Navigator.pop(context),
        ),
      ),
      body: Form(
        key: formKey,
        child: SingleChildScrollView(
          padding: const EdgeInsets.all(16),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // Supplement Info Card
              Container(
                padding: const EdgeInsets.all(16),
                decoration: BoxDecoration(
                  color: const Color(0xFF1F2937),
                  borderRadius: BorderRadius.circular(12),
                  border: Border.all(color: const Color(0xFFFACC15), width: 1),
                ),
                child: Row(
                  children: [
                    Container(
                      width: 60,
                      height: 60,
                      decoration: BoxDecoration(
                        color: const Color(0xFFFACC15).withOpacity(0.1),
                        borderRadius: BorderRadius.circular(8),
                      ),
                      child: const Icon(
                        Icons.medication,
                        color: Color(0xFFFACC15),
                        size: 30,
                      ),
                    ),
                    const SizedBox(width: 16),
                    Expanded(
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Text(
                            supplement.name,
                            style: ATextStyle.medium.copyWith(
                              color: Colors.white,
                              fontWeight: FontWeight.bold,
                            ),
                          ),
                          const SizedBox(height: 4),
                          Text(
                            supplement.brand,
                            style: ATextStyle.small.copyWith(
                              color: const Color(0xFFFACC15),
                            ),
                          ),
                          const SizedBox(height: 4),
                          Text(
                            supplement.description,
                            style: ATextStyle.small.copyWith(
                              color: Colors.grey[400],
                            ),
                            maxLines: 2,
                            overflow: TextOverflow.ellipsis,
                          ),
                        ],
                      ),
                    ),
                  ],
                ),
              ),
              
              const SizedBox(height: 24),
              
              // Quantity Section
              Text(
                'Quantity',
                style: ATextStyle.medium.copyWith(
                  color: Colors.white,
                  fontWeight: FontWeight.bold,
                ),
              ),
              const SizedBox(height: 8),
              TextFormField(
                controller: quantityController,
                keyboardType: TextInputType.number,
                style: ATextStyle.medium.copyWith(color: Colors.white),
                decoration: InputDecoration(
                  hintText: 'Enter quantity (e.g., 1, 2, 3...)',
                  hintStyle: ATextStyle.medium.copyWith(color: Colors.grey[500]),
                  filled: true,
                  fillColor: const Color(0xFF1F2937),
                  border: OutlineInputBorder(
                    borderRadius: BorderRadius.circular(8),
                    borderSide: const BorderSide(color: Color(0xFF374151)),
                  ),
                  enabledBorder: OutlineInputBorder(
                    borderRadius: BorderRadius.circular(8),
                    borderSide: const BorderSide(color: Color(0xFF374151)),
                  ),
                  focusedBorder: OutlineInputBorder(
                    borderRadius: BorderRadius.circular(8),
                    borderSide: const BorderSide(color: Color(0xFFFACC15)),
                  ),
                ),
                validator: (value) {
                  if (value == null || value.isEmpty) {
                    return 'Please enter quantity';
                  }
                  final quantity = int.tryParse(value);
                  if (quantity == null || quantity <= 0) {
                    return 'Please enter a valid quantity';
                  }
                  return null;
                },
              ),
              
              const SizedBox(height: 24),
              
              // Notes Section
              Text(
                'Notes (Optional)',
                style: ATextStyle.medium.copyWith(
                  color: Colors.white,
                  fontWeight: FontWeight.bold,
                ),
              ),
              const SizedBox(height: 8),
              TextFormField(
                controller: notesController,
                maxLines: 4,
                style: ATextStyle.medium.copyWith(color: Colors.white),
                decoration: InputDecoration(
                  hintText: 'Add any special instructions or notes...',
                  hintStyle: ATextStyle.medium.copyWith(color: Colors.grey[500]),
                  filled: true,
                  fillColor: const Color(0xFF1F2937),
                  border: OutlineInputBorder(
                    borderRadius: BorderRadius.circular(8),
                    borderSide: const BorderSide(color: Color(0xFF374151)),
                  ),
                  enabledBorder: OutlineInputBorder(
                    borderRadius: BorderRadius.circular(8),
                    borderSide: const BorderSide(color: Color(0xFF374151)),
                  ),
                  focusedBorder: OutlineInputBorder(
                    borderRadius: BorderRadius.circular(8),
                    borderSide: const BorderSide(color: Color(0xFFFACC15)),
                  ),
                ),
              ),
              
              const SizedBox(height: 32),
              
              // Submit Button
              SizedBox(
                width: double.infinity,
                height: 50,
                child: ElevatedButton(
                  onPressed: isLoading.value ? null : () => _handleSubmit(
                    context,
                    ref,
                    isLoading,
                    quantityController,
                    notesController,
                    formKey,
                    isEditing,
                  ),
                  style: ElevatedButton.styleFrom(
                    backgroundColor: const Color(0xFFFACC15),
                    foregroundColor: const Color(0xFF111827),
                    shape: RoundedRectangleBorder(
                      borderRadius: BorderRadius.circular(8),
                    ),
                  ),
                  child: isLoading.value
                      ? const SizedBox(
                          width: 20,
                          height: 20,
                          child: CircularProgressIndicator(
                            strokeWidth: 2,
                            valueColor: AlwaysStoppedAnimation<Color>(Color(0xFF111827)),
                          ),
                        )
                      : Text(
                          isEditing ? 'Update Assignment' : 'Assign Supplement',
                          style: ATextStyle.medium.copyWith(
                            fontWeight: FontWeight.bold,
                          ),
                        ),
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  Future<void> _handleSubmit(
    BuildContext context,
    WidgetRef ref,
    ValueNotifier<bool> isLoading,
    TextEditingController quantityController,
    TextEditingController notesController,
    GlobalKey<FormState> formKey,
    bool isEditing,
  ) async {
    if (!formKey.currentState!.validate()) return;

    isLoading.value = true;

    try {
      final quantity = int.parse(quantityController.text);
      final notes = notesController.text.trim().isEmpty ? null : notesController.text.trim();

      await ref.read(supplementRepositoryProvider).assignSupplementToStudent(
        studentId: student.id,
        instructorId: instructorId,
        supplementId: supplement.id,
        quantity: quantity,
        notes: notes,
      );

      if (context.mounted) {
        Navigator.pop(context, true); // Return true to indicate success
      }
    } catch (e) {
      if (context.mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Failed to assign supplement: $e'),
            backgroundColor: Colors.red,
          ),
        );
      }
    } finally {
      isLoading.value = false;
    }
  }
}
