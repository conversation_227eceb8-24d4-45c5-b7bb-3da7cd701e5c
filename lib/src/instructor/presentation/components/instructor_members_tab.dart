import 'package:flutter/material.dart';
import 'package:hooks_riverpod/hooks_riverpod.dart';
import 'package:flutter_hooks/flutter_hooks.dart';
import 'package:fitgo_app/src/shared/constants/app_text_style.dart';
import '../../application/students_provider.dart';
import '../../domain/student_models.dart';

class InstructorMembersTab extends HookConsumerWidget {
  const InstructorMembersTab({super.key});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final studentsState = ref.watch(studentsListNotifierProvider);
    final studentsStats = ref.watch(studentsStatsProvider);

    // Initialize students when screen loads
    useEffect(() {
      WidgetsBinding.instance.addPostFrameCallback((_) {
        ref.read(studentsListNotifierProvider.notifier).initializeStudents();
      });
      return null;
    }, []);

    return SafeArea(
      child: Column(
        children: [
          // Search and Filter Header
          _buildSearchAndFilterHeader(context, ref, studentsState),

          // Stats Summary
          if (studentsStats.totalStudents > 0) _buildStatsHeader(studentsStats),
          SizedBox(height: 8),
          // Students List
          Expanded(
            child:
                studentsState.isLoading
                    ? const Center(
                      child: CircularProgressIndicator(
                        color: Color(0xFFFACC15),
                      ),
                    )
                    : studentsState.error != null
                    ? _buildErrorState(ref, studentsState.error!)
                    : studentsState.filteredStudents.isEmpty
                    ? _buildEmptyState()
                    : _buildStudentsList(context, ref, studentsState),
          ),

          // Debug info
          if (studentsState.error != null)
            Container(
              margin: const EdgeInsets.all(16),
              padding: const EdgeInsets.all(12),
              decoration: BoxDecoration(
                color: Colors.red.withOpacity(0.1),
                borderRadius: BorderRadius.circular(8),
                border: Border.all(color: Colors.red.withOpacity(0.3)),
              ),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  const Text(
                    'Debug Info:',
                    style: TextStyle(
                      fontWeight: FontWeight.bold,
                      color: Colors.red,
                    ),
                  ),
                  const SizedBox(height: 4),
                  Text(
                    'Error: ${studentsState.error}',
                    style: const TextStyle(fontSize: 12, color: Colors.red),
                  ),
                ],
              ),
            ),
        ],
      ),
    );
  }

  Widget _buildSearchAndFilterHeader(
    BuildContext context,
    WidgetRef ref,
    StudentsListState state,
  ) {
    return Container(
      padding: const EdgeInsets.all(16),
      child: Row(
        children: [
          Expanded(
            child: Container(
              height: 48,
              decoration: BoxDecoration(
                color: const Color(0xFF1F2937),
                borderRadius: BorderRadius.circular(12),
                border: Border.all(color: const Color(0xFF374151)),
              ),
              child: TextField(
                style: const TextStyle(color: Colors.white),
                decoration: const InputDecoration(
                  hintText: 'Search students...',
                  hintStyle: TextStyle(color: Colors.grey),
                  prefixIcon: Icon(Icons.search, color: Colors.grey),
                  border: InputBorder.none,
                  contentPadding: EdgeInsets.symmetric(
                    horizontal: 16,
                    vertical: 12,
                  ),
                ),
                onChanged: (query) {
                  ref
                      .read(studentsListNotifierProvider.notifier)
                      .updateSearchQuery(query);
                },
              ),
            ),
          ),
          const SizedBox(width: 12),
          Container(
            height: 48,
            width: 48,
            decoration: BoxDecoration(
              color: const Color(0xFF1F2937),
              borderRadius: BorderRadius.circular(12),
              border: Border.all(color: const Color(0xFF374151)),
            ),
            child: IconButton(
              onPressed:
                  () => ref
                      .read(studentsListNotifierProvider.notifier)
                      .showFilterBottomSheet(context),
              icon: const Icon(Icons.filter_list, color: Color(0xFFFACC15)),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildStatsHeader(StudentsStats stats) {
    return Container(
      margin: const EdgeInsets.symmetric(horizontal: 16),
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: const Color(0xFF1F2937),
        borderRadius: BorderRadius.circular(12),
        border: Border.all(color: const Color(0xFF374151)),
      ),
      child: Row(
        children: [
          _buildStatItem('Total', stats.totalStudents.toString(), Colors.white),
          _buildStatItem(
            'Active',
            stats.activeStudents.toString(),
            const Color(0xFF10B981),
          ),
          _buildStatItem(
            'New',
            stats.newStudents.toString(),
            const Color(0xFFFACC15),
          ),
          _buildStatItem(
            'Tasks',
            stats.totalPendingTasks.toString(),
            const Color(0xFFEF4444),
          ),
        ],
      ),
    );
  }

  Widget _buildStatItem(String label, String value, Color color) {
    return Expanded(
      child: Column(
        children: [
          Text(
            value,
            style: ATextStyle.title.copyWith(
              color: color,
              fontSize: 20,
              fontWeight: FontWeight.bold,
            ),
          ),
          const SizedBox(height: 4),
          Text(label, style: ATextStyle.small.copyWith(color: Colors.grey)),
        ],
      ),
    );
  }

  Widget _buildErrorState(WidgetRef ref, String error) {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          const Icon(Icons.error_outline, color: Colors.red, size: 48),
          const SizedBox(height: 16),
          Text(
            'Error loading students',
            style: ATextStyle.title.copyWith(color: Colors.white),
          ),
          const SizedBox(height: 8),
          Text(
            error,
            style: ATextStyle.medium.copyWith(color: Colors.grey),
            textAlign: TextAlign.center,
          ),
          const SizedBox(height: 16),
          ElevatedButton(
            onPressed:
                () =>
                    ref
                        .read(studentsListNotifierProvider.notifier)
                        .refreshStudents(),
            style: ElevatedButton.styleFrom(
              backgroundColor: const Color(0xFFFACC15),
              foregroundColor: Colors.black,
            ),
            child: const Text('Retry'),
          ),
        ],
      ),
    );
  }

  Widget _buildEmptyState() {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          const Icon(Icons.people_outline, color: Colors.grey, size: 64),
          const SizedBox(height: 16),
          Text(
            'No Students Yet',
            style: ATextStyle.title.copyWith(color: Colors.white),
          ),
          const SizedBox(height: 8),
          Padding(
            padding: const EdgeInsets.symmetric(horizontal: 32),
            child: Text(
              'You don\'t have any students yet. Once your coaching profile is approved and students join, they will appear here.',
              style: ATextStyle.medium.copyWith(color: Colors.grey),
              textAlign: TextAlign.center,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildStudentsList(
    BuildContext context,
    WidgetRef ref,
    StudentsListState state,
  ) {
    return RefreshIndicator(
      onRefresh:
          () =>
              ref.read(studentsListNotifierProvider.notifier).refreshStudents(),
      color: const Color(0xFFFACC15),
      child: ListView.builder(
        padding: const EdgeInsets.symmetric(horizontal: 16),
        itemCount:
            state.filteredStudents.length + (state.isLoadingMore ? 1 : 0),
        itemBuilder: (context, index) {
          if (index == state.filteredStudents.length) {
            // Loading more indicator
            return const Center(
              child: Padding(
                padding: EdgeInsets.all(16),
                child: CircularProgressIndicator(color: Color(0xFFFACC15)),
              ),
            );
          }

          final student = state.filteredStudents[index];
          return _buildStudentCard(context, ref, student);
        },
      ),
    );
  }

  Widget _buildStudentCard(
    BuildContext context,
    WidgetRef ref,
    InstructorStudent student,
  ) {
    return Container(
      margin: const EdgeInsets.only(bottom: 12),
      decoration: BoxDecoration(
        color: const Color(0xFF1F2937),
        borderRadius: BorderRadius.circular(16),
        border: Border.all(color: const Color(0xFF374151)),
      ),
      child: InkWell(
        onTap:
            () => ref
                .read(studentsListNotifierProvider.notifier)
                .navigateToStudentDetail(context, student.id),
        borderRadius: BorderRadius.circular(16),
        child: Padding(
          padding: const EdgeInsets.all(16),
          child: Column(
            children: [
              // Header Row
              Row(
                children: [
                  // Profile Image with Online Status
                  Stack(
                    children: [
                      CircleAvatar(
                        radius: 24,
                        backgroundColor: const Color(0xFFFACC15),
                        backgroundImage:
                            student.profileImageUrl != null
                                ? NetworkImage(student.profileImageUrl!)
                                : null,
                        child:
                            student.profileImageUrl == null
                                ? Text(
                                  student.initials,
                                  style: const TextStyle(
                                    color: Colors.black,
                                    fontSize: 16,
                                    fontWeight: FontWeight.bold,
                                  ),
                                )
                                : null,
                      ),
                      if (student.isOnline)
                        Positioned(
                          bottom: 0,
                          right: 0,
                          child: Container(
                            width: 12,
                            height: 12,
                            decoration: BoxDecoration(
                              color: const Color(0xFF10B981),
                              shape: BoxShape.circle,
                              border: Border.all(
                                color: const Color(0xFF1F2937),
                                width: 2,
                              ),
                            ),
                          ),
                        ),
                    ],
                  ),
                  const SizedBox(width: 12),

                  // Student Info
                  Expanded(
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text(
                          student.name,
                          style: ATextStyle.medium.copyWith(
                            color: Colors.white,
                            fontWeight: FontWeight.w600,
                          ),
                        ),
                        const SizedBox(height: 4),

                        // Tags Row
                        if (student.allTags.isNotEmpty)
                          Wrap(
                            spacing: 6,
                            runSpacing: 4,
                            children:
                                student.allTags
                                    .take(3)
                                    .map((tag) => _buildTag(tag))
                                    .toList(),
                          ),
                      ],
                    ),
                  ),

                  // Arrow Icon
                  const Icon(
                    Icons.arrow_forward_ios,
                    color: Colors.grey,
                    size: 16,
                  ),
                ],
              ),

              const SizedBox(height: 16),

              // Status Icons Row
              Row(
                mainAxisAlignment: MainAxisAlignment.spaceAround,
                children:
                    StudentProgramType.values.map((programType) {
                      final statusInfo = student.statusCounts.getStatusInfo(
                        programType,
                      );
                      return _buildStatusIcon(
                        context,
                        ref,
                        student,
                        programType,
                        statusInfo,
                      );
                    }).toList(),
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildTag(StudentTag tag) {
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 6, vertical: 2),
      decoration: BoxDecoration(
        color: Color(
          int.parse(tag.color.substring(1), radix: 16) + 0xFF000000,
        ).withValues(alpha: 0.2),
        borderRadius: BorderRadius.circular(8),
        border: Border.all(
          color: Color(
            int.parse(tag.color.substring(1), radix: 16) + 0xFF000000,
          ).withValues(alpha: 0.5),
        ),
      ),
      child: Row(
        mainAxisSize: MainAxisSize.min,
        children: [
          Text(tag.emoji, style: const TextStyle(fontSize: 10)),
          const SizedBox(width: 2),
          Text(
            tag.label,
            style: ATextStyle.small.copyWith(
              color: Color(
                int.parse(tag.color.substring(1), radix: 16) + 0xFF000000,
              ),
              fontSize: 10,
              fontWeight: FontWeight.w600,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildStatusIcon(
    BuildContext context,
    WidgetRef ref,
    InstructorStudent student,
    StudentProgramType programType,
    StatusInfo statusInfo,
  ) {
    return GestureDetector(
      onTap:
          () => ref
              .read(studentsListNotifierProvider.notifier)
              .navigateToProgramManagement(context, student.id, programType),
      child: Container(
        padding: const EdgeInsets.all(8),
        decoration: BoxDecoration(
          color: const Color(0xFF374151),
          borderRadius: BorderRadius.circular(8),
        ),
        child: Stack(
          children: [
            Text(statusInfo.icon, style: const TextStyle(fontSize: 20)),
            if (statusInfo.hasPendingTasks)
              Positioned(
                top: -2,
                right: -2,
                child: Container(
                  padding: const EdgeInsets.all(2),
                  decoration: const BoxDecoration(
                    color: Color(0xFFEF4444),
                    shape: BoxShape.circle,
                  ),
                  constraints: const BoxConstraints(
                    minWidth: 16,
                    minHeight: 16,
                  ),
                  child: Text(
                    statusInfo.pending.toString(),
                    style: const TextStyle(
                      color: Colors.white,
                      fontSize: 10,
                      fontWeight: FontWeight.bold,
                    ),
                    textAlign: TextAlign.center,
                  ),
                ),
              ),
          ],
        ),
      ),
    );
  }
}
