import 'package:flutter/material.dart';
import 'package:hooks_riverpod/hooks_riverpod.dart';
import 'package:flutter_hooks/flutter_hooks.dart';
import 'package:image_picker/image_picker.dart';
import 'dart:io';

import 'package:fitgo_app/src/shared/constants/app_text_style.dart';

import '../../application/instructor_profile_provider.dart';
import '../../domain/instructor_profile_models.dart';
import '../subscription_plan_management_screen.dart';
import '../capacity_upgrade_screen.dart';

class InstructorProfileTab extends HookConsumerWidget {
  const InstructorProfileTab({super.key});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final profileState = ref.watch(instructorProfileNotifierProvider);

    // Initialize profile when screen loads
    useEffect(() {
      WidgetsBinding.instance.addPostFrameCallback((_) {
        ref
            .read(instructorProfileNotifierProvider.notifier)
            .initializeProfile();
      });
      return null;
    }, []);

    return SafeArea(
      child:
          profileState.isLoading
              ? const Center(
                child: CircularProgressIndicator(color: Color(0xFFFACC15)),
              )
              : profileState.error != null
              ? _buildErrorState(ref, profileState.error!)
              : profileState.profileData != null
              ? _buildProfileContent(context, ref, profileState.profileData!)
              : const Center(
                child: Text(
                  'No profile data available',
                  style: TextStyle(color: Colors.white),
                ),
              ),
    );
  }

  Widget _buildErrorState(WidgetRef ref, String error) {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          const Icon(Icons.error_outline, color: Colors.red, size: 48),
          const SizedBox(height: 16),
          Text(
            'Error loading profile',
            style: ATextStyle.title.copyWith(color: Colors.white),
          ),
          const SizedBox(height: 8),
          Text(
            error,
            style: ATextStyle.medium.copyWith(color: Colors.grey),
            textAlign: TextAlign.center,
          ),
          const SizedBox(height: 16),
          ElevatedButton(
            onPressed:
                () =>
                    ref
                        .read(instructorProfileNotifierProvider.notifier)
                        .refreshProfile(),
            style: ElevatedButton.styleFrom(
              backgroundColor: const Color(0xFFFACC15),
              foregroundColor: Colors.black,
            ),
            child: const Text('Retry'),
          ),
        ],
      ),
    );
  }

  Widget _buildProfileContent(
    BuildContext context,
    WidgetRef ref,
    InstructorProfileData profileData,
  ) {
    return RefreshIndicator(
      onRefresh:
          () =>
              ref
                  .read(instructorProfileNotifierProvider.notifier)
                  .refreshProfile(),
      color: const Color(0xFFFACC15),
      child: SingleChildScrollView(
        physics: const AlwaysScrollableScrollPhysics(),
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Header Section
            _buildHeaderSection(context, ref, profileData.basicInfo),
            const SizedBox(height: 24),

            // Capacity Status Section with Upgrade Button
            _buildCapacitySection(context, ref, profileData.basicInfo),
            const SizedBox(height: 24),

            // Bio Section
            _buildBioSection(context, ref, profileData.basicInfo),
            const SizedBox(height: 24),

            // Work History Section
            _buildWorkHistorySection(context, ref, profileData.workHistory),
            const SizedBox(height: 24),

            // Certifications Section
            _buildCertificationsSection(
              context,
              ref,
              profileData.certifications,
            ),
            const SizedBox(height: 24),

            // Client Reviews Section
            _buildReviewsSection(context, ref, profileData.reviews),
            const SizedBox(height: 24),

            // FAQs Section
            _buildFAQsSection(context, ref, profileData.faqs),
            const SizedBox(height: 24),

            // Manage Subscription Button
            _buildSubscriptionButton(
              context,
              ref,
              profileData.subscriptionInfo,
            ),
            const SizedBox(height: 32),
          ],
        ),
      ),
    );
  }

  /// Build header section with profile info and badges
  Widget _buildHeaderSection(
    BuildContext context,
    WidgetRef ref,
    InstructorBasicInfo basicInfo,
  ) {
    final badges = ref.watch(instructorBadgesProvider);

    return Container(
      padding: const EdgeInsets.all(20),
      decoration: BoxDecoration(
        color: const Color(0xFF1F2937),
        borderRadius: BorderRadius.circular(16),
        border: Border.all(color: const Color(0xFF374151)),
      ),
      child: Stack(
        children: [
          // Edit button in top right corner
          if (basicInfo.canEdit)
            Positioned(
              top: 0,
              right: 0,
              child: GestureDetector(
                onTap:
                    () => ref
                        .read(instructorProfileNotifierProvider.notifier)
                        .showEditDialog(
                          context,
                          ProfileEditAction.editBasicInfo,
                        ),
                child: Container(
                  padding: const EdgeInsets.all(8),
                  decoration: BoxDecoration(
                    color: const Color(0xFFFACC15).withOpacity(0.1),
                    borderRadius: BorderRadius.circular(8),
                  ),
                  child: const Icon(
                    Icons.edit,
                    size: 20,
                    color: Color(0xFFFACC15),
                  ),
                ),
              ),
            ),
          Column(
            children: [
              // Profile Picture and Basic Info
              Row(
                children: [
                  Stack(
                    children: [
                      GestureDetector(
                        onTap: () => _showPhotoPickerModal(context, ref),
                        child: CircleAvatar(
                          radius: 40,
                          backgroundColor: const Color(0xFFFACC15),
                          backgroundImage:
                              basicInfo.profilePictureUrl != null
                                  ? NetworkImage(basicInfo.profilePictureUrl!)
                                  : null,
                          child:
                              basicInfo.profilePictureUrl == null
                                  ? const Icon(
                                    Icons.add,
                                    size: 32,
                                    color: Colors.black,
                                  )
                                  : null,
                        ),
                      ),
                      if (basicInfo.canEdit &&
                          basicInfo.profilePictureUrl != null)
                        Positioned(
                          bottom: 0,
                          right: 0,
                          child: GestureDetector(
                            onTap: () => _showPhotoPickerModal(context, ref),
                            child: Container(
                              padding: const EdgeInsets.all(4),
                              decoration: const BoxDecoration(
                                color: Color(0xFFFACC15),
                                shape: BoxShape.circle,
                              ),
                              child: const Icon(
                                Icons.add,
                                size: 16,
                                color: Colors.black,
                              ),
                            ),
                          ),
                        ),
                    ],
                  ),
                  const SizedBox(width: 16),
                  Expanded(
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text(
                          basicInfo.name,
                          style: ATextStyle.title.copyWith(
                            color: Colors.white,
                            fontSize: 24,
                            fontWeight: FontWeight.bold,
                          ),
                        ),
                        const SizedBox(height: 4),
                        Text(
                          basicInfo.title,
                          style: ATextStyle.medium.copyWith(
                            color: const Color(0xFFFACC15),
                            fontSize: 16,
                          ),
                        ),
                        const SizedBox(height: 8),
                        // Rating Row
                        Row(
                          children: [
                            const Icon(
                              Icons.star,
                              color: Color(0xFFFACC15),
                              size: 20,
                            ),
                            const SizedBox(width: 4),
                            Text(
                              basicInfo.averageRating.toStringAsFixed(1),
                              style: ATextStyle.medium.copyWith(
                                color: Colors.white,
                                fontWeight: FontWeight.w600,
                              ),
                            ),
                            const SizedBox(width: 8),
                            Text(
                              '(${basicInfo.totalReviews} reviews)',
                              style: ATextStyle.small.copyWith(
                                color: Colors.grey,
                              ),
                            ),
                          ],
                        ),
                      ],
                    ),
                  ),
                ],
              ),
              const SizedBox(height: 20),

              // Badges
              Wrap(
                spacing: 12,
                runSpacing: 8,
                children: badges.map((badge) => _buildBadge(badge)).toList(),
              ),
            ],
          ),
        ],
      ),
    );
  }

  /// Build badge widget
  Widget _buildBadge(InstructorBadge badge) {
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
      decoration: BoxDecoration(
        color: Color(
          int.parse(badge.type.color.substring(1), radix: 16) + 0xFF000000,
        ).withOpacity(0.1),
        borderRadius: BorderRadius.circular(20),
        border: Border.all(
          color: Color(
            int.parse(badge.type.color.substring(1), radix: 16) + 0xFF000000,
          ).withOpacity(0.3),
        ),
      ),
      child: Row(
        mainAxisSize: MainAxisSize.min,
        children: [
          Text(badge.icon, style: const TextStyle(fontSize: 14)),
          const SizedBox(width: 6),
          Text(
            badge.value,
            style: ATextStyle.small.copyWith(
              color: Color(
                int.parse(badge.type.color.substring(1), radix: 16) +
                    0xFF000000,
              ),
              fontWeight: FontWeight.w600,
            ),
          ),
        ],
      ),
    );
  }

  /// Build work history section
  Widget _buildWorkHistorySection(
    BuildContext context,
    WidgetRef ref,
    List<WorkExperience> workHistory,
  ) {
    return _buildSection(
      title: 'Work History',
      isEmpty: workHistory.isEmpty,
      emptyMessage:
          'Add your work experience to showcase your professional background',
      onAdd:
          () => ref
              .read(instructorProfileNotifierProvider.notifier)
              .showEditDialog(context, ProfileEditAction.addWorkExperience),
      child: Column(
        children:
            workHistory
                .map(
                  (experience) =>
                      _buildWorkExperienceCard(context, ref, experience),
                )
                .toList(),
      ),
    );
  }

  /// Build work experience card
  Widget _buildWorkExperienceCard(
    BuildContext context,
    WidgetRef ref,
    WorkExperience experience,
  ) {
    return Container(
      width: double.infinity, // Full width
      margin: const EdgeInsets.only(bottom: 12),
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: const Color(0xFF374151),
        borderRadius: BorderRadius.circular(12),
        border: Border.all(color: const Color(0xFF4B5563)),
      ),
      child: Stack(
        children: [
          // Three dots menu in top right corner
          Positioned(
            top: -8,
            right: -8,
            child: PopupMenuButton<String>(
              icon: const Icon(Icons.more_vert, color: Colors.grey, size: 20),
              onSelected: (value) {
                if (value == 'edit') {
                  ref
                      .read(instructorProfileNotifierProvider.notifier)
                      .showEditDialog(
                        context,
                        ProfileEditAction.editWorkExperience,
                        data: experience,
                      );
                } else if (value == 'delete') {
                  _showDeleteConfirmation(
                    context,
                    ref,
                    'Delete Work Experience',
                    'Are you sure you want to delete this work experience?',
                    () => ref
                        .read(instructorProfileNotifierProvider.notifier)
                        .handleEditAction(
                          ProfileEditAction.deleteWorkExperience,
                          data: {'workExperienceId': experience.id},
                        ),
                  );
                }
              },
              itemBuilder:
                  (context) => [
                    const PopupMenuItem(
                      value: 'edit',
                      child: Row(
                        children: [
                          Icon(Icons.edit, color: Colors.grey, size: 16),
                          SizedBox(width: 8),
                          Text('Edit'),
                        ],
                      ),
                    ),
                    const PopupMenuItem(
                      value: 'delete',
                      child: Row(
                        children: [
                          Icon(Icons.delete, color: Colors.red, size: 16),
                          SizedBox(width: 8),
                          Text('Delete'),
                        ],
                      ),
                    ),
                  ],
            ),
          ),
          // Content
          Padding(
            padding: const EdgeInsets.only(
              top: 0,
              right: 30, // Add padding for menu
              bottom: 0,
              left: 0,
            ),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Row(
                  children: [
                    Expanded(
                      child: Text(
                        experience.companyName,
                        style: ATextStyle.medium.copyWith(
                          color: Colors.white,
                          fontWeight: FontWeight.w600,
                        ),
                      ),
                    ),
                    // Current badge next to title
                    if (experience.isCurrent)
                      Container(
                        margin: const EdgeInsets.only(left: 8),
                        padding: const EdgeInsets.symmetric(
                          horizontal: 8,
                          vertical: 2,
                        ),
                        decoration: BoxDecoration(
                          color: const Color(0xFF10B981),
                          borderRadius: BorderRadius.circular(12),
                        ),
                        child: Text(
                          'Current',
                          style: ATextStyle.small.copyWith(
                            color: Colors.white,
                            fontSize: 10,
                          ),
                        ),
                      ),
                  ],
                ),
                const SizedBox(height: 4),
                Text(
                  experience.role,
                  style: ATextStyle.medium.copyWith(
                    color: const Color(0xFFFACC15),
                  ),
                ),
                const SizedBox(height: 4),
                Text(
                  experience.duration,
                  style: ATextStyle.small.copyWith(color: Colors.grey),
                ),
                const SizedBox(height: 8),
                Text(
                  experience.description,
                  style: ATextStyle.small.copyWith(color: Colors.grey[300]),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  /// Build certifications section
  Widget _buildCertificationsSection(
    BuildContext context,
    WidgetRef ref,
    List<Certification> certifications,
  ) {
    return _buildSection(
      title: 'Certifications/Licenses',
      isEmpty: certifications.isEmpty,
      emptyMessage: 'Add certifications to build trust with potential clients',
      onAdd:
          () => ref
              .read(instructorProfileNotifierProvider.notifier)
              .showEditDialog(context, ProfileEditAction.addCertification),
      child: Column(
        children:
            certifications
                .map(
                  (certification) =>
                      _buildCertificationCard(context, ref, certification),
                )
                .toList(),
      ),
    );
  }

  /// Build certification card
  Widget _buildCertificationCard(
    BuildContext context,
    WidgetRef ref,
    Certification certification,
  ) {
    return Container(
      width: double.infinity, // Full width
      margin: const EdgeInsets.only(bottom: 12),
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: const Color(0xFF374151),
        borderRadius: BorderRadius.circular(12),
        border: Border.all(color: const Color(0xFF4B5563)),
      ),
      child: Stack(
        children: [
          // Three dots menu in top right corner
          Positioned(
            top: -8,
            right: -8,
            child: PopupMenuButton<String>(
              icon: const Icon(Icons.more_vert, color: Colors.grey, size: 20),
              onSelected: (value) {
                if (value == 'edit') {
                  ref
                      .read(instructorProfileNotifierProvider.notifier)
                      .showEditDialog(
                        context,
                        ProfileEditAction.editCertification,
                        data: certification,
                      );
                } else if (value == 'delete') {
                  _showDeleteConfirmation(
                    context,
                    ref,
                    'Delete Certification',
                    'Are you sure you want to delete this certification?',
                    () => ref
                        .read(instructorProfileNotifierProvider.notifier)
                        .handleEditAction(
                          ProfileEditAction.deleteCertification,
                          data: {'certificationId': certification.id},
                        ),
                  );
                }
              },
              itemBuilder:
                  (context) => [
                    const PopupMenuItem(
                      value: 'edit',
                      child: Row(
                        children: [
                          Icon(Icons.edit, color: Colors.grey, size: 16),
                          SizedBox(width: 8),
                          Text('Edit'),
                        ],
                      ),
                    ),
                    const PopupMenuItem(
                      value: 'delete',
                      child: Row(
                        children: [
                          Icon(Icons.delete, color: Colors.red, size: 16),
                          SizedBox(width: 8),
                          Text('Delete'),
                        ],
                      ),
                    ),
                  ],
            ),
          ),
          // Content
          Padding(
            padding: const EdgeInsets.only(
              top: 0,
              right: 30, // Add padding for menu
              bottom: 0,
              left: 0,
            ),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Row(
                  children: [
                    Expanded(
                      child: Text(
                        certification.name,
                        style: ATextStyle.medium.copyWith(
                          color: Colors.white,
                          fontWeight: FontWeight.w600,
                        ),
                      ),
                    ),
                    // Verified badge next to title
                    if (certification.isVerified)
                      Container(
                        margin: const EdgeInsets.only(left: 8),
                        padding: const EdgeInsets.symmetric(
                          horizontal: 8,
                          vertical: 2,
                        ),
                        decoration: BoxDecoration(
                          color: const Color(0xFF10B981),
                          borderRadius: BorderRadius.circular(12),
                        ),
                        child: Text(
                          'Verified',
                          style: ATextStyle.small.copyWith(
                            color: Colors.white,
                            fontSize: 10,
                          ),
                        ),
                      ),
                  ],
                ),
                const SizedBox(height: 4),
                Text(
                  certification.issuer,
                  style: ATextStyle.medium.copyWith(
                    color: const Color(0xFFFACC15),
                  ),
                ),
                const SizedBox(height: 4),
                Text(
                  certification.year,
                  style: ATextStyle.small.copyWith(color: Colors.grey),
                ),
                if (certification.externalLink != null &&
                    certification.externalLink!.isNotEmpty) ...[
                  const SizedBox(height: 8),
                  Row(
                    children: [
                      const Icon(Icons.link, color: Colors.grey, size: 16),
                      const SizedBox(width: 4),
                      Text(
                        'External Link',
                        style: ATextStyle.small.copyWith(
                          color: Colors.grey[300],
                        ),
                      ),
                    ],
                  ),
                ],
              ],
            ),
          ),
        ],
      ),
    );
  }

  /// Build reviews section
  Widget _buildReviewsSection(
    BuildContext context,
    WidgetRef ref,
    List<ClientReview> reviews,
  ) {
    return _buildSection(
      title: 'Client Reviews',
      isEmpty: reviews.isEmpty,
      emptyMessage:
          'Client reviews will appear here once you start training students',
      onAdd: null, // Reviews can't be added by instructor
      child: Column(
        children:
            reviews
                .take(3)
                .map((review) => _buildReviewCard(context, ref, review))
                .toList(),
      ),
    );
  }

  /// Build review card
  Widget _buildReviewCard(
    BuildContext context,
    WidgetRef ref,
    ClientReview review,
  ) {
    return Container(
      margin: const EdgeInsets.only(bottom: 12),
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: const Color(0xFF374151),
        borderRadius: BorderRadius.circular(12),
        border: Border.all(color: const Color(0xFF4B5563)),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              CircleAvatar(
                radius: 16,
                backgroundColor: const Color(0xFFFACC15),
                backgroundImage:
                    review.clientAvatarUrl != null
                        ? NetworkImage(review.clientAvatarUrl!)
                        : null,
                child:
                    review.clientAvatarUrl == null
                        ? Text(
                          review.clientName.substring(0, 1).toUpperCase(),
                          style: const TextStyle(
                            color: Colors.black,
                            fontSize: 14,
                            fontWeight: FontWeight.bold,
                          ),
                        )
                        : null,
              ),
              const SizedBox(width: 12),
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      review.clientName,
                      style: ATextStyle.medium.copyWith(
                        color: Colors.white,
                        fontWeight: FontWeight.w600,
                      ),
                    ),
                    Row(
                      children: [
                        ...List.generate(5, (index) {
                          return Icon(
                            index < review.rating
                                ? Icons.star
                                : Icons.star_border,
                            color: const Color(0xFFFACC15),
                            size: 16,
                          );
                        }),
                        const SizedBox(width: 8),
                        Text(
                          _formatTimeAgo(review.createdAt),
                          style: ATextStyle.small.copyWith(color: Colors.grey),
                        ),
                      ],
                    ),
                  ],
                ),
              ),
              if (review.canModerate)
                PopupMenuButton<String>(
                  icon: const Icon(
                    Icons.more_vert,
                    color: Colors.grey,
                    size: 20,
                  ),
                  onSelected: (value) {
                    if (value == 'delete') {
                      ref
                          .read(instructorProfileNotifierProvider.notifier)
                          .handleEditAction(
                            ProfileEditAction.deleteReview,
                            data: {'reviewId': review.id},
                          );
                    }
                  },
                  itemBuilder:
                      (context) => [
                        const PopupMenuItem(
                          value: 'delete',
                          child: Text('Delete Review'),
                        ),
                      ],
                ),
            ],
          ),
          const SizedBox(height: 12),
          Text(
            review.comment,
            style: ATextStyle.small.copyWith(color: Colors.grey[300]),
          ),
        ],
      ),
    );
  }

  /// Build FAQs section
  Widget _buildFAQsSection(
    BuildContext context,
    WidgetRef ref,
    List<FAQ> faqs,
  ) {
    final profileState = ref.watch(instructorProfileNotifierProvider);
    final expandedFAQs = profileState.expandedFAQs;

    return _buildSection(
      title: 'Frequently Asked Questions',
      isEmpty: faqs.isEmpty,
      emptyMessage:
          'Add frequently asked questions to help potential clients learn more about you',
      onAdd:
          () => ref
              .read(instructorProfileNotifierProvider.notifier)
              .showEditDialog(context, ProfileEditAction.addFAQ),
      child: Column(
        children: [
          ...expandedFAQs.asMap().entries.map((entry) {
            final index = entry.key;
            final faq = entry.value;
            return Column(
              children: [
                _buildFAQCard(context, ref, faq),
                if (index < expandedFAQs.length - 1) const SizedBox(height: 12),
              ],
            );
          }).toList(),
        ],
      ),
    );
  }

  /// Build FAQ card
  Widget _buildFAQCard(BuildContext context, WidgetRef ref, FAQ faq) {
    return Container(
      decoration: BoxDecoration(
        color: const Color(0xFF374151),
        borderRadius: BorderRadius.circular(12),
        border: Border.all(color: const Color(0xFF4B5563)),
      ),
      child: Column(
        children: [
          InkWell(
            onTap:
                () => ref
                    .read(instructorProfileNotifierProvider.notifier)
                    .toggleFAQ(faq.id),
            borderRadius: BorderRadius.circular(12),
            child: Padding(
              padding: const EdgeInsets.all(8),
              child: Row(
                children: [
                  Expanded(
                    child: Row(
                      spacing: 8,
                      children: [
                        Icon(
                          faq.isExpanded
                              ? Icons.expand_less
                              : Icons.expand_more,
                          color: const Color(0xFFFACC15),
                          size: 20,
                        ),
                        Text(
                          faq.question,
                          style: ATextStyle.medium.copyWith(
                            color: Colors.white,
                            fontWeight: FontWeight.w600,
                          ),
                        ),
                      ],
                    ),
                  ),
                  // Dropdown icon
                  PopupMenuButton<String>(
                    icon: const Icon(
                      Icons.more_vert,
                      color: Colors.grey,
                      size: 20,
                    ),
                    onSelected: (value) {
                      if (value == 'edit') {
                        ref
                            .read(instructorProfileNotifierProvider.notifier)
                            .showEditDialog(
                              context,
                              ProfileEditAction.editFAQ,
                              data: faq,
                            );
                      } else if (value == 'delete') {
                        _showDeleteConfirmation(
                          context,
                          ref,
                          'Delete FAQ',
                          'Are you sure you want to delete this FAQ?',
                          () => ref
                              .read(instructorProfileNotifierProvider.notifier)
                              .handleEditAction(
                                ProfileEditAction.deleteFAQ,
                                data: {'faqId': faq.id},
                              ),
                        );
                      }
                    },
                    itemBuilder:
                        (context) => [
                          const PopupMenuItem(
                            value: 'edit',
                            child: Row(
                              children: [
                                Icon(Icons.edit, color: Colors.grey, size: 16),
                                SizedBox(width: 8),
                                Text('Edit'),
                              ],
                            ),
                          ),
                          const PopupMenuItem(
                            value: 'delete',
                            child: Row(
                              children: [
                                Icon(Icons.delete, color: Colors.red, size: 16),
                                SizedBox(width: 8),
                                Text('Delete'),
                              ],
                            ),
                          ),
                        ],
                  ),
                ],
              ),
            ),
          ),
          if (faq.isExpanded)
            Padding(
              padding: const EdgeInsets.fromLTRB(16, 0, 16, 16),
              child: Text(
                faq.answer,
                style: ATextStyle.small.copyWith(color: Colors.grey[300]),
              ),
            ),
        ],
      ),
    );
  }

  /// Build subscription button
  Widget _buildSubscriptionButton(
    BuildContext context,
    WidgetRef ref,
    SubscriptionInfo? subscriptionInfo,
  ) {
    return Container(
      width: double.infinity,
      padding: const EdgeInsets.all(20),
      decoration: BoxDecoration(
        gradient: const LinearGradient(
          colors: [Color(0xFFFACC15), Color(0xFFF59E0B)],
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
        ),
        borderRadius: BorderRadius.circular(16),
        boxShadow: [
          BoxShadow(
            color: const Color(0xFFFACC15).withValues(alpha: 0.3),
            blurRadius: 20,
            offset: const Offset(0, 8),
          ),
        ],
      ),
      child: Column(
        children: [
          Text(
            'Submit for Review',
            style: ATextStyle.title.copyWith(
              color: Colors.black,
              fontSize: 18,
              fontWeight: FontWeight.bold,
            ),
          ),
          const SizedBox(height: 8),
          Text(
            'Configure your subscription plans and submit for admin approval to start coaching students',
            style: ATextStyle.medium.copyWith(
              color: Colors.black87,
              fontSize: 14,
            ),
            textAlign: TextAlign.center,
          ),
          if (subscriptionInfo != null) ...[
            const SizedBox(height: 12),
            Container(
              padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
              decoration: BoxDecoration(
                color: Colors.black.withValues(alpha: 0.1),
                borderRadius: BorderRadius.circular(20),
              ),
              child: Text(
                '${subscriptionInfo.planName} - \$${subscriptionInfo.monthlyPrice}/month',
                style: ATextStyle.small.copyWith(
                  color: Colors.black87,
                  fontWeight: FontWeight.w600,
                ),
              ),
            ),
          ],
          const SizedBox(height: 16),
          ElevatedButton(
            onPressed: () => _navigateToSubscriptionManagement(context),
            style: ElevatedButton.styleFrom(
              backgroundColor: Colors.black,
              foregroundColor: const Color(0xFFFACC15),
              padding: const EdgeInsets.symmetric(horizontal: 32, vertical: 12),
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(12),
              ),
              elevation: 0,
            ),
            child: Row(
              mainAxisSize: MainAxisSize.min,
              children: [
                const Icon(Icons.settings, size: 18),
                const SizedBox(width: 8),
                const Text('Manage Subscription Plans'),
              ],
            ),
          ),
        ],
      ),
    );
  }

  /// Navigate to subscription management screen
  void _navigateToSubscriptionManagement(BuildContext context) {
    Navigator.of(context).push(
      MaterialPageRoute(
        builder: (context) => const SubscriptionPlanManagementScreen(),
      ),
    );
  }

  /// Build section with title and optional add button
  Widget _buildSection({
    required String title,
    required bool isEmpty,
    required String emptyMessage,
    required Widget child,
    VoidCallback? onAdd,
  }) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Row(
          children: [
            Expanded(
              child: Text(
                title,
                style: ATextStyle.title.copyWith(
                  color: Colors.white,
                  fontSize: 18,
                  fontWeight: FontWeight.bold,
                ),
              ),
            ),
            if (onAdd != null)
              IconButton(
                onPressed: onAdd,
                icon: const Icon(Icons.add, color: Color(0xFFFACC15)),
                style: IconButton.styleFrom(
                  backgroundColor: const Color(0xFFFACC15).withOpacity(0.1),
                  shape: RoundedRectangleBorder(
                    borderRadius: BorderRadius.circular(8),
                  ),
                ),
              ),
          ],
        ),
        const SizedBox(height: 12),
        if (isEmpty)
          _buildEmptyState(emptyMessage)
        else
          Container(
            padding: const EdgeInsets.all(16),
            decoration: BoxDecoration(
              color: const Color(0xFF1F2937),
              borderRadius: BorderRadius.circular(12),
              border: Border.all(color: const Color(0xFF374151)),
            ),
            child: child,
          ),
      ],
    );
  }

  /// Build empty state widget
  Widget _buildEmptyState(String message) {
    return Container(
      width: double.infinity,
      padding: const EdgeInsets.all(24),
      decoration: BoxDecoration(
        color: const Color(0xFF1F2937),
        borderRadius: BorderRadius.circular(12),
        border: Border.all(
          color: const Color(0xFF374151),
          style: BorderStyle.solid,
        ),
      ),
      child: Column(
        children: [
          Icon(Icons.info_outline, color: Colors.grey[400], size: 32),
          const SizedBox(height: 12),
          Text(
            message,
            style: ATextStyle.medium.copyWith(
              color: Colors.grey[400],
              fontSize: 14,
            ),
            textAlign: TextAlign.center,
          ),
        ],
      ),
    );
  }

  /// Build bio section like work history/certifications
  Widget _buildBioSection(
    BuildContext context,
    WidgetRef ref,
    InstructorBasicInfo basicInfo,
  ) {
    final hasBio = basicInfo.bio?.isNotEmpty == true;

    return _buildSection(
      title: 'About Me',
      isEmpty: !hasBio,
      emptyMessage:
          'Add a bio to tell students about yourself and your experience',
      onAdd: () => _showBioEditDialog(context, ref, basicInfo),
      child:
          hasBio
              ? _buildBioCard(context, ref, basicInfo)
              : const SizedBox.shrink(),
    );
  }

  /// Build bio card
  Widget _buildBioCard(
    BuildContext context,
    WidgetRef ref,
    InstructorBasicInfo basicInfo,
  ) {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: const Color(0xFF374151),
        borderRadius: BorderRadius.circular(8),
        border: Border.all(color: const Color(0xFF4B5563)),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Icon(Icons.person, color: const Color(0xFFFACC15), size: 18),
              const SizedBox(width: 8),
              Expanded(
                child: Text(
                  'Personal Bio',
                  style: ATextStyle.medium.copyWith(
                    color: Colors.white,
                    fontWeight: FontWeight.w600,
                  ),
                ),
              ),
              PopupMenuButton<String>(
                icon: const Icon(Icons.more_vert, color: Colors.grey, size: 20),
                color: const Color(0xFF374151),
                onSelected: (value) {
                  if (value == 'edit') {
                    _showBioEditDialog(context, ref, basicInfo);
                  } else if (value == 'delete') {
                    _deleteBio(context, ref, basicInfo);
                  }
                },
                itemBuilder:
                    (context) => [
                      const PopupMenuItem(
                        value: 'edit',
                        child: Row(
                          children: [
                            Icon(Icons.edit, color: Colors.white, size: 16),
                            SizedBox(width: 8),
                            Text('Edit', style: TextStyle(color: Colors.white)),
                          ],
                        ),
                      ),
                      const PopupMenuItem(
                        value: 'delete',
                        child: Row(
                          children: [
                            Icon(Icons.delete, color: Colors.red, size: 16),
                            SizedBox(width: 8),
                            Text('Delete', style: TextStyle(color: Colors.red)),
                          ],
                        ),
                      ),
                    ],
              ),
            ],
          ),
          const SizedBox(height: 12),
          Text(
            basicInfo.bio!,
            style: ATextStyle.medium.copyWith(
              color: Colors.grey[300],
              height: 1.5,
            ),
          ),
        ],
      ),
    );
  }

  /// Build capacity section with upgrade button
  Widget _buildCapacitySection(
    BuildContext context,
    WidgetRef ref,
    InstructorBasicInfo basicInfo,
  ) {
    return Container(
      padding: const EdgeInsets.all(20),
      decoration: BoxDecoration(
        color: const Color(0xFF1F2937),
        borderRadius: BorderRadius.circular(12),
        border: Border.all(
          color: const Color(0xFFFACC15).withOpacity(0.2),
          width: 1,
        ),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Icon(Icons.people, color: const Color(0xFFFACC15), size: 20),
              const SizedBox(width: 8),
              Text(
                'Student Capacity',
                style: ATextStyle.medium.copyWith(
                  color: Colors.white,
                  fontWeight: FontWeight.w600,
                ),
              ),
            ],
          ),

          const SizedBox(height: 12),

          // Compact capacity display
          Row(
            children: [
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      '${basicInfo.currentStudents}/${basicInfo.maxStudents} students',
                      style: ATextStyle.large.copyWith(
                        color: Colors.white,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                    const SizedBox(height: 4),
                    Container(
                      height: 4,
                      decoration: BoxDecoration(
                        color: Colors.grey[800],
                        borderRadius: BorderRadius.circular(2),
                      ),
                      child: FractionallySizedBox(
                        alignment: Alignment.centerLeft,
                        widthFactor:
                            basicInfo.maxStudents > 0
                                ? basicInfo.currentStudents /
                                    basicInfo.maxStudents
                                : 0.0,
                        child: Container(
                          decoration: BoxDecoration(
                            color:
                                basicInfo.currentStudents <
                                        basicInfo.maxStudents
                                    ? const Color(0xFF10B981)
                                    : Colors.red,
                            borderRadius: BorderRadius.circular(2),
                          ),
                        ),
                      ),
                    ),
                  ],
                ),
              ),
              const SizedBox(width: 12),
              ElevatedButton.icon(
                onPressed: () {
                  Navigator.of(context).push(
                    MaterialPageRoute(
                      builder:
                          (context) =>
                              CapacityUpgradeScreen(instructorId: basicInfo.id),
                    ),
                  );
                },
                icon: Icon(Icons.trending_up, color: Colors.black, size: 16),
                label: Text(
                  'Arttır',
                  style: ATextStyle.small.copyWith(
                    color: Colors.black,
                    fontWeight: FontWeight.w600,
                  ),
                ),
                style: ElevatedButton.styleFrom(
                  backgroundColor: const Color(0xFFFACC15),
                  foregroundColor: Colors.black,
                  padding: const EdgeInsets.symmetric(
                    horizontal: 12,
                    vertical: 8,
                  ),
                  minimumSize: Size.zero,
                ),
              ),
            ],
          ),

          const SizedBox(height: 8),

          // Info text
          Text(
            'Daha fazla öğrenci kabul etmek için kapasiteni yükselt',
            style: ATextStyle.small.copyWith(
              color: Colors.grey[400],
              fontSize: 12,
            ),
            textAlign: TextAlign.center,
          ),
        ],
      ),
    );
  }

  /// Format time ago
  String _formatTimeAgo(DateTime dateTime) {
    final now = DateTime.now();
    final difference = now.difference(dateTime);

    if (difference.inDays > 30) {
      return '${(difference.inDays / 30).floor()} month${(difference.inDays / 30).floor() > 1 ? 's' : ''} ago';
    } else if (difference.inDays > 0) {
      return '${difference.inDays} day${difference.inDays > 1 ? 's' : ''} ago';
    } else if (difference.inHours > 0) {
      return '${difference.inHours} hour${difference.inHours > 1 ? 's' : ''} ago';
    } else {
      return '${difference.inMinutes} minute${difference.inMinutes > 1 ? 's' : ''} ago';
    }
  }

  /// Show delete confirmation dialog
  void _showDeleteConfirmation(
    BuildContext context,
    WidgetRef ref,
    String title,
    String message,
    VoidCallback onConfirm,
  ) {
    showDialog(
      context: context,
      builder:
          (context) => AlertDialog(
            backgroundColor: const Color(0xFF1F2937),
            title: Text(
              title,
              style: ATextStyle.title.copyWith(color: Colors.white),
            ),
            content: Text(
              message,
              style: ATextStyle.medium.copyWith(color: Colors.grey[300]),
            ),
            actions: [
              TextButton(
                onPressed: () => Navigator.of(context).pop(),
                child: Text(
                  'Cancel',
                  style: ATextStyle.medium.copyWith(color: Colors.grey),
                ),
              ),
              ElevatedButton(
                onPressed: () {
                  Navigator.of(context).pop();
                  onConfirm();
                },
                style: ElevatedButton.styleFrom(
                  backgroundColor: Colors.red,
                  foregroundColor: Colors.white,
                ),
                child: const Text('Delete'),
              ),
            ],
          ),
    );
  }

  /// Show photo picker modal
  void _showPhotoPickerModal(BuildContext context, WidgetRef ref) {
    showModalBottomSheet(
      context: context,
      backgroundColor: Colors.transparent,
      builder:
          (context) => Container(
            padding: const EdgeInsets.all(24),
            decoration: const BoxDecoration(
              color: Color(0xFF1F2937),
              borderRadius: BorderRadius.vertical(top: Radius.circular(20)),
            ),
            child: Column(
              mainAxisSize: MainAxisSize.min,
              children: [
                Container(
                  width: 40,
                  height: 4,
                  decoration: BoxDecoration(
                    color: Colors.grey[600],
                    borderRadius: BorderRadius.circular(2),
                  ),
                ),
                const SizedBox(height: 24),
                Text(
                  'Profil Fotoğrafı Seç',
                  style: ATextStyle.title.copyWith(
                    color: Colors.white,
                    fontSize: 18,
                    fontWeight: FontWeight.bold,
                  ),
                ),
                const SizedBox(height: 24),
                Row(
                  children: [
                    Expanded(
                      child: _buildPhotoOption(
                        context: context,
                        icon: Icons.camera_alt,
                        label: 'Kamera',
                        onTap: () {
                          Navigator.pop(context);
                          _pickImageFromCamera(ref);
                        },
                      ),
                    ),
                    const SizedBox(width: 16),
                    Expanded(
                      child: _buildPhotoOption(
                        context: context,
                        icon: Icons.photo_library,
                        label: 'Galeri',
                        onTap: () {
                          Navigator.pop(context);
                          _pickImageFromGallery(ref);
                        },
                      ),
                    ),
                  ],
                ),
                const SizedBox(height: 16),
                TextButton(
                  onPressed: () => Navigator.pop(context),
                  child: Text(
                    'İptal',
                    style: ATextStyle.medium.copyWith(color: Colors.white70),
                  ),
                ),
              ],
            ),
          ),
    );
  }

  /// Build photo option widget
  Widget _buildPhotoOption({
    required BuildContext context,
    required IconData icon,
    required String label,
    required VoidCallback onTap,
  }) {
    return GestureDetector(
      onTap: onTap,
      child: Container(
        padding: const EdgeInsets.symmetric(vertical: 20),
        decoration: BoxDecoration(
          color: const Color(0xFF374151),
          borderRadius: BorderRadius.circular(12),
          border: Border.all(color: const Color(0xFF4B5563)),
        ),
        child: Column(
          children: [
            Icon(icon, color: const Color(0xFFFACC15), size: 32),
            const SizedBox(height: 8),
            Text(label, style: ATextStyle.medium.copyWith(color: Colors.white)),
          ],
        ),
      ),
    );
  }

  /// Pick image from camera
  Future<void> _pickImageFromCamera(WidgetRef ref) async {
    try {
      final ImagePicker picker = ImagePicker();
      final XFile? image = await picker.pickImage(
        source: ImageSource.camera,
        imageQuality: 80,
        maxWidth: 1024,
        maxHeight: 1024,
      );

      if (image != null) {
        await _uploadProfilePhoto(ref, File(image.path));
      }
    } catch (e) {
      debugPrint('Error picking image from camera: $e');
    }
  }

  /// Pick image from gallery
  Future<void> _pickImageFromGallery(WidgetRef ref) async {
    try {
      final ImagePicker picker = ImagePicker();
      final XFile? image = await picker.pickImage(
        source: ImageSource.gallery,
        imageQuality: 80,
        maxWidth: 1024,
        maxHeight: 1024,
      );

      if (image != null) {
        await _uploadProfilePhoto(ref, File(image.path));
      }
    } catch (e) {
      debugPrint('Error picking image from gallery: $e');
    }
  }

  /// Upload profile photo
  Future<void> _uploadProfilePhoto(WidgetRef ref, File imageFile) async {
    try {
      debugPrint('Uploading profile photo: ${imageFile.path}');

      // Call the instructor profile provider to upload the photo
      await ref
          .read(instructorProfileNotifierProvider.notifier)
          .uploadProfilePhoto(imageFile);
    } catch (e) {
      debugPrint('Error uploading profile photo: $e');
    }
  }

  /// Delete bio
  void _deleteBio(
    BuildContext context,
    WidgetRef ref,
    InstructorBasicInfo basicInfo,
  ) {
    showDialog(
      context: context,
      builder:
          (context) => AlertDialog(
            backgroundColor: const Color(0xFF1F2937),
            title: Text(
              'Delete Bio',
              style: ATextStyle.medium.copyWith(color: Colors.white),
            ),
            content: Text(
              'Are you sure you want to delete your bio?',
              style: ATextStyle.medium.copyWith(color: Colors.grey[300]),
            ),
            actions: [
              TextButton(
                onPressed: () => Navigator.of(context).pop(),
                child: Text(
                  'Cancel',
                  style: ATextStyle.medium.copyWith(color: Colors.grey[400]),
                ),
              ),
              ElevatedButton(
                onPressed: () async {
                  final updatedBasicInfo = InstructorBasicInfo(
                    id: basicInfo.id,
                    name: basicInfo.name,
                    title: basicInfo.title,
                    bio: null, // Delete bio
                    profilePictureUrl: basicInfo.profilePictureUrl,
                    averageRating: basicInfo.averageRating,
                    totalReviews: basicInfo.totalReviews,
                    experienceYears: basicInfo.experienceYears,
                    totalStudents: basicInfo.totalStudents,
                    primaryCertification: basicInfo.primaryCertification,
                    canEdit: basicInfo.canEdit,
                    maxStudents: basicInfo.maxStudents,
                    currentStudents: basicInfo.currentStudents,
                  );

                  try {
                    await ref
                        .read(instructorProfileNotifierProvider.notifier)
                        .handleEditAction(
                          ProfileEditAction.editBasicInfo,
                          data: {'basicInfo': updatedBasicInfo},
                        );

                    if (context.mounted) {
                      Navigator.of(context).pop();
                      ScaffoldMessenger.of(context).showSnackBar(
                        const SnackBar(
                          content: Text('Bio deleted successfully'),
                          backgroundColor: Color(0xFF10B981),
                        ),
                      );
                    }
                  } catch (e) {
                    if (context.mounted) {
                      ScaffoldMessenger.of(context).showSnackBar(
                        SnackBar(
                          content: Text('Failed to delete bio: $e'),
                          backgroundColor: Colors.red,
                        ),
                      );
                    }
                  }
                },
                style: ElevatedButton.styleFrom(
                  backgroundColor: Colors.red,
                  foregroundColor: Colors.white,
                ),
                child: const Text('Delete'),
              ),
            ],
          ),
    );
  }

  /// Show bio edit dialog
  void _showBioEditDialog(
    BuildContext context,
    WidgetRef ref,
    InstructorBasicInfo basicInfo,
  ) {
    final TextEditingController bioController = TextEditingController(
      text: basicInfo.bio ?? '',
    );

    showDialog(
      context: context,
      builder:
          (context) => AlertDialog(
            backgroundColor: const Color(0xFF1F2937),
            title: Text(
              'Edit Bio',
              style: ATextStyle.medium.copyWith(
                color: Colors.white,
                fontWeight: FontWeight.bold,
              ),
            ),
            content: SizedBox(
              width: double.maxFinite,
              child: TextField(
                controller: bioController,
                maxLines: 5,
                maxLength: 500,
                style: ATextStyle.medium.copyWith(color: Colors.white),
                decoration: InputDecoration(
                  hintText:
                      'Tell students about yourself, your experience, and what makes you unique as a trainer.',
                  hintStyle: ATextStyle.medium.copyWith(
                    color: Colors.grey[500],
                    fontSize: 14,
                  ),
                  filled: true,
                  fillColor: const Color(0xFF374151),
                  border: OutlineInputBorder(
                    borderRadius: BorderRadius.circular(8),
                    borderSide: BorderSide.none,
                  ),
                  focusedBorder: OutlineInputBorder(
                    borderRadius: BorderRadius.circular(8),
                    borderSide: const BorderSide(
                      color: Color(0xFFFACC15),
                      width: 2,
                    ),
                  ),
                  counterStyle: ATextStyle.small.copyWith(
                    color: Colors.grey[400],
                  ),
                ),
              ),
            ),
            actions: [
              TextButton(
                onPressed: () => Navigator.of(context).pop(),
                child: Text(
                  'Cancel',
                  style: ATextStyle.medium.copyWith(color: Colors.grey[400]),
                ),
              ),
              ElevatedButton(
                onPressed: () async {
                  final newBio = bioController.text.trim();

                  // Update bio
                  final updatedBasicInfo = InstructorBasicInfo(
                    id: basicInfo.id,
                    name: basicInfo.name,
                    title: basicInfo.title,
                    bio: newBio.isEmpty ? null : newBio,
                    profilePictureUrl: basicInfo.profilePictureUrl,
                    averageRating: basicInfo.averageRating,
                    totalReviews: basicInfo.totalReviews,
                    experienceYears: basicInfo.experienceYears,
                    totalStudents: basicInfo.totalStudents,
                    primaryCertification: basicInfo.primaryCertification,
                    canEdit: basicInfo.canEdit,
                    maxStudents: basicInfo.maxStudents,
                    currentStudents: basicInfo.currentStudents,
                  );

                  try {
                    await ref
                        .read(instructorProfileNotifierProvider.notifier)
                        .handleEditAction(
                          ProfileEditAction.editBasicInfo,
                          data: {'basicInfo': updatedBasicInfo},
                        );

                    // No need to invalidate - state is updated automatically

                    if (context.mounted) {
                      Navigator.of(context).pop();
                      ScaffoldMessenger.of(context).showSnackBar(
                        const SnackBar(
                          content: Text('Bio updated successfully'),
                          backgroundColor: Color(0xFF10B981),
                        ),
                      );
                    }
                  } catch (e) {
                    if (context.mounted) {
                      ScaffoldMessenger.of(context).showSnackBar(
                        SnackBar(
                          content: Text('Failed to update bio: $e'),
                          backgroundColor: Colors.red,
                        ),
                      );
                    }
                  }
                },
                style: ElevatedButton.styleFrom(
                  backgroundColor: const Color(0xFFFACC15),
                  foregroundColor: Colors.black,
                ),
                child: Text(
                  'Save',
                  style: ATextStyle.medium.copyWith(
                    color: Colors.black,
                    fontWeight: FontWeight.w600,
                  ),
                ),
              ),
            ],
          ),
    );
  }
}
