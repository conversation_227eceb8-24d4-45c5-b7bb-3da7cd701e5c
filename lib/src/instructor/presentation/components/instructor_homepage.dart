import 'package:flutter/material.dart';
import 'package:hooks_riverpod/hooks_riverpod.dart';
import 'package:flutter_hooks/flutter_hooks.dart';
import 'package:fitgo_app/src/shared/widgets/text_widget/text_widget.dart';
import 'package:fitgo_app/src/shared/constants/app_text_style.dart';
import 'package:fitgo_app/src/shared/extensions/string_hardcoded.dart';
import '../../application/instructor_provider.dart';
import '../../domain/instructor_models.dart';

class InstructorHomepage extends HookConsumerWidget {
  const InstructorHomepage({super.key});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final dashboardState = ref.watch(instructorDashboardNotifierProvider);

    return Scaffold(
      backgroundColor: const Color(0xFF111827),
      body: SafeArea(
        child:
            dashboardState.isLoading
                ? const Center(
                  child: CircularProgressIndicator(color: Color(0xFFFACC15)),
                )
                : dashboardState.error != null
                ? _buildErrorState(ref, dashboardState.error!)
                : dashboardState.dashboard != null
                ? _buildDashboardContent(
                  context,
                  ref,
                  dashboardState.dashboard!,
                )
                : const Center(child: Text('No data available')),
      ),
    );
  }

  Widget _buildErrorState(WidgetRef ref, String error) {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          const Icon(Icons.error_outline, color: Colors.red, size: 48),
          const SizedBox(height: 16),
          Text(
            'Error loading dashboard',
            style: ATextStyle.title.copyWith(color: Colors.white),
          ),
          const SizedBox(height: 8),
          Text(
            error,
            style: ATextStyle.medium.copyWith(color: Colors.grey),
            textAlign: TextAlign.center,
          ),
          const SizedBox(height: 16),
          ElevatedButton(
            onPressed:
                () =>
                    ref
                        .read(instructorDashboardNotifierProvider.notifier)
                        .refreshDashboard(),
            style: ElevatedButton.styleFrom(
              backgroundColor: const Color(0xFFFACC15),
              foregroundColor: Colors.black,
            ),
            child: const Text('Retry'),
          ),
        ],
      ),
    );
  }

  Widget _buildDashboardContent(
    BuildContext context,
    WidgetRef ref,
    InstructorDashboard dashboard,
  ) {
    return RefreshIndicator(
      onRefresh:
          () =>
              ref
                  .read(instructorDashboardNotifierProvider.notifier)
                  .refreshDashboard(),
      color: const Color(0xFFFACC15),
      child: SingleChildScrollView(
        physics: const AlwaysScrollableScrollPhysics(),
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Profile Section
            _buildProfileSection(dashboard.profile),
            const SizedBox(height: 24),

            // Stats Cards
            _buildStatsSection(dashboard.stats),
            const SizedBox(height: 24),

            // Priority Alerts
            if (dashboard.priorityAlerts.isNotEmpty) ...[
              _buildPriorityAlertsSection(ref, dashboard.priorityAlerts),
              const SizedBox(height: 24),
            ],

            // Task Overview
            _buildTaskOverviewSection(ref, dashboard.taskOverview),
            const SizedBox(height: 24),

            // New Students
            if (dashboard.newStudents.isNotEmpty) ...[
              _buildNewStudentsSection(ref, dashboard.newStudents),
              const SizedBox(height: 24),
            ],

            // Weekly Progress
            _buildWeeklyProgressSection(dashboard.weeklyProgress),
          ],
        ),
      ),
    );
  }

  Widget _buildProfileSection(InstructorProfile profile) {
    debugPrint('🏠 Homepage - Building profile section');
    debugPrint('👤 Profile name: ${profile.name}');
    debugPrint('📸 Avatar URL: ${profile.avatarUrl}');

    return Container(
      padding: const EdgeInsets.all(20),
      decoration: BoxDecoration(
        color: const Color(0xFF1F2937),
        borderRadius: BorderRadius.circular(16),
        border: Border.all(color: const Color(0xFF374151)),
      ),
      child: Row(
        children: [
          _buildProfileAvatar(profile),
          const SizedBox(width: 16),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  '${profile.name} – ${profile.title}',
                  style: ATextStyle.title.copyWith(
                    color: Colors.white,
                    fontSize: 18,
                  ),
                ),
                const SizedBox(height: 4),
                Text(
                  '${profile.experienceYears} years experience',
                  style: ATextStyle.medium.copyWith(color: Colors.grey),
                ),
              ],
            ),
          ),
          IconButton(
            onPressed: () {
              // TODO: Open menu
            },
            icon: const Icon(Icons.more_vert, color: Colors.grey),
          ),
        ],
      ),
    );
  }

  Widget _buildStatsSection(InstructorStats stats) {
    return Row(
      children: [
        Expanded(
          child: _buildStatCard(
            'Total Students',
            '${stats.totalStudents}',
            Icons.people,
            const Color(0xFF10B981),
          ),
        ),
        const SizedBox(width: 12),
        Expanded(
          child: _buildStatCard(
            'New Today',
            '${stats.newStudentsToday}',
            Icons.person_add,
            const Color(0xFFFACC15),
            changeIndicator:
                stats.newStudentsChange > 0
                    ? '+${stats.newStudentsChange}'
                    : stats.newStudentsChange < 0
                    ? '${stats.newStudentsChange}'
                    : null,
          ),
        ),
      ],
    );
  }

  Widget _buildStatCard(
    String title,
    String value,
    IconData icon,
    Color color, {
    String? changeIndicator,
  }) {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: const Color(0xFF1F2937),
        borderRadius: BorderRadius.circular(12),
        border: Border.all(color: const Color(0xFF374151)),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Icon(icon, color: color, size: 20),
              const Spacer(),
              if (changeIndicator != null)
                Container(
                  padding: const EdgeInsets.symmetric(
                    horizontal: 6,
                    vertical: 2,
                  ),
                  decoration: BoxDecoration(
                    color:
                        changeIndicator.startsWith('+')
                            ? const Color(0xFF10B981) // Green for positive
                            : changeIndicator.startsWith('-')
                            ? Colors
                                .red // Red for negative
                            : const Color(0xFF6B7280), // Gray for zero
                    borderRadius: BorderRadius.circular(8),
                  ),
                  child: Text(
                    changeIndicator,
                    style: const TextStyle(
                      color: Colors.white,
                      fontSize: 10,
                      fontWeight: FontWeight.w600,
                    ),
                  ),
                ),
            ],
          ),
          const SizedBox(height: 8),
          Text(
            value,
            style: ATextStyle.title.copyWith(
              color: Colors.white,
              fontSize: 24,
              fontWeight: FontWeight.bold,
            ),
          ),
          const SizedBox(height: 4),
          Text(title, style: ATextStyle.small.copyWith(color: Colors.grey)),
        ],
      ),
    );
  }

  Widget _buildPriorityAlertsSection(
    WidgetRef ref,
    List<PriorityAlert> alerts,
  ) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Row(
          children: [
            const Icon(Icons.warning, color: Colors.red, size: 20),
            const SizedBox(width: 8),
            Text(
              '🚨 Priority Alerts',
              style: ATextStyle.title.copyWith(
                color: Colors.white,
                fontSize: 18,
              ),
            ),
          ],
        ),
        const SizedBox(height: 12),
        ...alerts.map((alert) => _buildAlertCard(ref, alert)),
      ],
    );
  }

  Widget _buildAlertCard(WidgetRef ref, PriorityAlert alert) {
    return Container(
      margin: const EdgeInsets.only(bottom: 8),
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: const Color(0xFF1F2937),
        borderRadius: BorderRadius.circular(12),
        border: Border.all(color: Colors.red.withValues(alpha: 0.3)),
      ),
      child: Row(
        children: [
          Container(
            width: 4,
            height: 40,
            decoration: BoxDecoration(
              color: Colors.red,
              borderRadius: BorderRadius.circular(2),
            ),
          ),
          const SizedBox(width: 12),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  '${alert.studentName} ${alert.message}',
                  style: ATextStyle.medium.copyWith(color: Colors.white),
                ),
                const SizedBox(height: 4),
                Text(
                  _formatTimeAgo(alert.createdAt),
                  style: ATextStyle.small.copyWith(color: Colors.grey),
                ),
              ],
            ),
          ),
          ElevatedButton(
            onPressed:
                () => ref
                    .read(instructorDashboardNotifierProvider.notifier)
                    .handleAlertAction(alert.id, alert.actionLabel),
            style: ElevatedButton.styleFrom(
              backgroundColor: const Color(0xFFFACC15),
              foregroundColor: Colors.black,
              padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
            ),
            child: Text(alert.actionLabel),
          ),
        ],
      ),
    );
  }

  Widget _buildTaskOverviewSection(WidgetRef ref, TaskOverview taskOverview) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          '📊 Task Overview',
          style: ATextStyle.title.copyWith(color: Colors.white, fontSize: 18),
        ),
        const SizedBox(height: 12),
        GridView.count(
          shrinkWrap: true,
          physics: const NeverScrollableScrollPhysics(),
          crossAxisCount: 3,
          crossAxisSpacing: 8,
          mainAxisSpacing: 8,
          childAspectRatio: 1,
          children: [
            _buildTaskCard(ref, TaskType.workout, taskOverview.workouts),
            _buildTaskCard(ref, TaskType.cardio, taskOverview.cardio),
            _buildTaskCard(ref, TaskType.nutrition, taskOverview.nutrition),
            _buildTaskCard(ref, TaskType.feedback, taskOverview.feedback),
            _buildTaskCard(ref, TaskType.question, taskOverview.questions),
            _buildTaskCard(ref, TaskType.supplement, taskOverview.supplements),
          ],
        ),
      ],
    );
  }

  Widget _buildTaskCard(WidgetRef ref, TaskType taskType, int count) {
    return Builder(
      builder:
          (context) => GestureDetector(
            onTap:
                () => ref
                    .read(instructorDashboardNotifierProvider.notifier)
                    .handleTaskTap(taskType, context),
            child: Container(
              padding: const EdgeInsets.all(12),
              decoration: BoxDecoration(
                color: const Color(0xFF1F2937),
                borderRadius: BorderRadius.circular(12),
                border: Border.all(color: const Color(0xFF374151)),
              ),
              child: Column(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  Text(taskType.icon, style: const TextStyle(fontSize: 24)),
                  const SizedBox(height: 8),
                  Text(
                    '$count',
                    style: ATextStyle.title.copyWith(
                      color: Colors.white,
                      fontSize: 20,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                  const SizedBox(height: 4),
                  Text(
                    taskType.displayName,
                    style: ATextStyle.small.copyWith(
                      color: Colors.grey,
                      fontSize: 10,
                    ),
                    textAlign: TextAlign.center,
                  ),
                ],
              ),
            ),
          ),
    );
  }

  Widget _buildNewStudentsSection(WidgetRef ref, List<NewStudent> newStudents) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          '🧍‍♂️ New Students',
          style: ATextStyle.title.copyWith(color: Colors.white, fontSize: 18),
        ),
        const SizedBox(height: 12),
        ...newStudents.map((student) => _buildNewStudentCard(ref, student)),
      ],
    );
  }

  Widget _buildNewStudentCard(WidgetRef ref, NewStudent student) {
    return Container(
      margin: const EdgeInsets.only(bottom: 8),
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: const Color(0xFF1F2937),
        borderRadius: BorderRadius.circular(12),
        border: Border.all(color: const Color(0xFF374151)),
      ),
      child: Row(
        children: [
          CircleAvatar(
            radius: 20,
            backgroundColor: const Color(0xFFFACC15),
            backgroundImage:
                student.avatarUrl != null
                    ? NetworkImage(student.avatarUrl!)
                    : null,
            child:
                student.avatarUrl == null
                    ? Text(
                      student.name.substring(0, 1).toUpperCase(),
                      style: const TextStyle(
                        color: Colors.black,
                        fontSize: 16,
                        fontWeight: FontWeight.bold,
                      ),
                    )
                    : null,
          ),
          const SizedBox(width: 12),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  student.name,
                  style: ATextStyle.medium.copyWith(color: Colors.white),
                ),
                const SizedBox(height: 2),
                Row(
                  children: [
                    Text(
                      student.programType,
                      style: ATextStyle.small.copyWith(
                        color: const Color(0xFFFACC15),
                      ),
                    ),
                    const SizedBox(width: 8),
                    Text(
                      '• ${_formatTimeAgo(student.joinedAt)}',
                      style: ATextStyle.small.copyWith(color: Colors.grey),
                    ),
                  ],
                ),
              ],
            ),
          ),
          Builder(
            builder:
                (context) => ElevatedButton(
                  onPressed:
                      () => ref
                          .read(instructorDashboardNotifierProvider.notifier)
                          .handleNewStudentView(student.id, context),
                  style: ElevatedButton.styleFrom(
                    backgroundColor: const Color(0xFFFACC15),
                    foregroundColor: Colors.black,
                    padding: const EdgeInsets.symmetric(
                      horizontal: 16,
                      vertical: 8,
                    ),
                  ),
                  child: const Text('View'),
                ),
          ),
        ],
      ),
    );
  }

  Widget _buildWeeklyProgressSection(WeeklyProgress weeklyProgress) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          '📅 Weekly Progress Summary',
          style: ATextStyle.title.copyWith(color: Colors.white, fontSize: 18),
        ),
        const SizedBox(height: 12),
        Container(
          padding: const EdgeInsets.all(16),
          decoration: BoxDecoration(
            color: const Color(0xFF1F2937),
            borderRadius: BorderRadius.circular(12),
            border: Border.all(color: const Color(0xFF374151)),
          ),
          child: Column(
            children: [
              Row(
                children: [
                  Expanded(
                    child: _buildProgressItem(
                      'Completed Tasks',
                      '${weeklyProgress.totalCompletedTasks}',
                      const Color(0xFF10B981),
                    ),
                  ),
                  const SizedBox(width: 16),
                  Expanded(
                    child: _buildProgressItem(
                      'Missed Workouts',
                      '${weeklyProgress.missedWorkouts}',
                      Colors.orange,
                    ),
                  ),
                ],
              ),
              const SizedBox(height: 16),
              Row(
                children: [
                  Expanded(
                    child: _buildProgressItem(
                      'Inactive Students',
                      '${weeklyProgress.inactiveStudents}',
                      Colors.red,
                    ),
                  ),
                  const SizedBox(width: 16),
                  Expanded(
                    child: _buildProgressItem(
                      'Completion Rate',
                      '${(weeklyProgress.completionRate * 100).toInt()}%',
                      const Color(0xFFFACC15),
                    ),
                  ),
                ],
              ),
            ],
          ),
        ),
      ],
    );
  }

  Widget _buildProgressItem(String title, String value, Color color) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          value,
          style: ATextStyle.title.copyWith(
            color: color,
            fontSize: 20,
            fontWeight: FontWeight.bold,
          ),
        ),
        const SizedBox(height: 4),
        Text(title, style: ATextStyle.small.copyWith(color: Colors.grey)),
      ],
    );
  }

  String _formatTimeAgo(DateTime dateTime) {
    final now = DateTime.now();
    final difference = now.difference(dateTime);

    if (difference.inHours < 1) {
      return '${difference.inMinutes}m ago';
    } else if (difference.inDays < 1) {
      return '${difference.inHours}h ago';
    } else {
      return '${difference.inDays}d ago';
    }
  }

  /// Build profile avatar with better error handling and cache busting
  Widget _buildProfileAvatar(InstructorProfile profile) {
    if (profile.avatarUrl != null && profile.avatarUrl!.isNotEmpty) {
      final imageUrl =
          '${profile.avatarUrl!}?t=${DateTime.now().millisecondsSinceEpoch}';
      debugPrint('🔗 Homepage - Final avatar URL: $imageUrl');

      return Container(
        width: 60,
        height: 60,
        decoration: BoxDecoration(
          shape: BoxShape.circle,
          color: const Color(0xFFFACC15),
          image: DecorationImage(
            image: NetworkImage(imageUrl),
            fit: BoxFit.cover,
            onError: (exception, stackTrace) {
              debugPrint('❌ Homepage - Error loading avatar: $exception');
            },
          ),
        ),
      );
    } else {
      debugPrint('📷 Homepage - No avatar URL, showing initial');
      return CircleAvatar(
        radius: 30,
        backgroundColor: const Color(0xFFFACC15),
        child: Text(
          profile.name.substring(0, 1).toUpperCase(),
          style: const TextStyle(
            color: Colors.black,
            fontSize: 24,
            fontWeight: FontWeight.bold,
          ),
        ),
      );
    }
  }
}
