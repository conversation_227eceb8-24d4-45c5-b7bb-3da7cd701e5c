import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:hooks_riverpod/hooks_riverpod.dart';
import 'package:flutter_hooks/flutter_hooks.dart';
import 'package:uuid/uuid.dart';
import 'package:fitgo_app/src/shared/constants/app_text_style.dart';
import '../../domain/instructor_profile_models.dart';
import '../../application/instructor_profile_provider.dart';

/// Basic info edit dialog
class BasicInfoEditDialog extends HookConsumerWidget {
  final InstructorBasicInfo basicInfo;

  const BasicInfoEditDialog({super.key, required this.basicInfo});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final nameController = useTextEditingController(text: basicInfo.name);
    final titleController = useTextEditingController(text: basicInfo.title);
    final experienceController = useTextEditingController(
      text: basicInfo.experienceYears.toString(),
    );
    final isLoading = useState(false);

    return AlertDialog(
      backgroundColor: const Color(0xFF1F2937),
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
      insetPadding: const EdgeInsets.symmetric(horizontal: 16, vertical: 24),
      contentPadding: const EdgeInsets.fromLTRB(24, 20, 24, 0),
      actionsPadding: const EdgeInsets.fromLTRB(24, 0, 24, 24),
      title: Text(
        'Edit Basic Information',
        style: ATextStyle.title.copyWith(color: Colors.white),
      ),
      content: SingleChildScrollView(
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            _buildTextField(
              controller: nameController,
              label: 'Full Name',
              hint: 'Enter your full name',
            ),
            const SizedBox(height: 16),
            _buildTextField(
              controller: titleController,
              label: 'Professional Title',
              hint: 'e.g., Elite Fitness Trainer',
            ),
            const SizedBox(height: 16),
            _buildTextField(
              controller: experienceController,
              label: 'Years of Experience',
              hint: 'Enter years',
              keyboardType: TextInputType.number,
            ),
          ],
        ),
      ),
      actions: [
        TextButton(
          onPressed: () => Navigator.of(context).pop(),
          child: Text(
            'Cancel',
            style: ATextStyle.medium.copyWith(color: Colors.grey),
          ),
        ),
        ElevatedButton(
          onPressed: isLoading.value
              ? null
              : () async {
                  if (isLoading.value) return;

                  isLoading.value = true;

                  try {
                    final updatedInfo = InstructorBasicInfo(
                      id: basicInfo.id,
                      name: nameController.text.trim(),
                      title: titleController.text.trim(),
                      bio: basicInfo.bio, // Keep existing bio
                      profilePictureUrl: basicInfo
                          .profilePictureUrl, // Keep existing photo URL
                      averageRating: basicInfo.averageRating,
                      totalReviews: basicInfo.totalReviews,
                      experienceYears:
                          int.tryParse(experienceController.text) ??
                              basicInfo.experienceYears,
                      totalStudents: basicInfo.totalStudents,
                      primaryCertification: null, // Remove certification
                      canEdit: basicInfo.canEdit,
                      maxStudents: basicInfo.maxStudents,
                      currentStudents: basicInfo.currentStudents,
                    );

                    await ref
                        .read(instructorProfileNotifierProvider.notifier)
                        .handleEditAction(
                      ProfileEditAction.editBasicInfo,
                      data: {'basicInfo': updatedInfo},
                    );

                    if (context.mounted) {
                      Navigator.of(context).pop();
                      ScaffoldMessenger.of(context).showSnackBar(
                        const SnackBar(
                          content:
                              Text('Basic information updated successfully'),
                          backgroundColor: Color(0xFF10B981),
                        ),
                      );
                    }
                  } catch (e) {
                    debugPrint('Error saving basic info: $e');
                    if (context.mounted) {
                      ScaffoldMessenger.of(context).showSnackBar(
                        SnackBar(
                          content: Text('Failed to update: ${e.toString()}'),
                          backgroundColor: Colors.red,
                        ),
                      );
                    }
                  } finally {
                    if (context.mounted) {
                      isLoading.value = false;
                    }
                  }
                },
          style: ElevatedButton.styleFrom(
            backgroundColor: const Color(0xFFFACC15),
            foregroundColor: Colors.black,
          ),
          child: isLoading.value
              ? const SizedBox(
                  width: 20,
                  height: 20,
                  child: CircularProgressIndicator(
                    strokeWidth: 2,
                    valueColor: AlwaysStoppedAnimation<Color>(Colors.black),
                  ),
                )
              : const Text('Save'),
        ),
      ],
    );
  }

  Widget _buildTextField({
    required TextEditingController controller,
    required String label,
    required String hint,
    TextInputType? keyboardType,
  }) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(label, style: ATextStyle.medium.copyWith(color: Colors.white)),
        const SizedBox(height: 8),
        TextField(
          controller: controller,
          keyboardType: keyboardType,
          style: const TextStyle(color: Colors.white),
          decoration: InputDecoration(
            hintText: hint,
            hintStyle: const TextStyle(color: Colors.grey),
            border: OutlineInputBorder(
              borderSide: const BorderSide(color: Color(0xFF374151)),
              borderRadius: BorderRadius.circular(8),
            ),
            enabledBorder: OutlineInputBorder(
              borderSide: const BorderSide(color: Color(0xFF374151)),
              borderRadius: BorderRadius.circular(8),
            ),
            focusedBorder: OutlineInputBorder(
              borderSide: const BorderSide(color: Color(0xFFFACC15)),
              borderRadius: BorderRadius.circular(8),
            ),
            filled: true,
            fillColor: const Color(0xFF374151),
          ),
        ),
      ],
    );
  }
}

/// Work experience edit dialog
class WorkExperienceEditDialog extends HookConsumerWidget {
  final WorkExperience? experience;

  const WorkExperienceEditDialog({super.key, this.experience});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final formKey = useMemoized(() => GlobalKey<FormState>());
    final companyController = useTextEditingController(
      text: experience?.companyName ?? '',
    );
    final roleController = useTextEditingController(
      text: experience?.role ?? '',
    );
    final startYearController = useTextEditingController(
      text: experience?.startYear ?? '',
    );
    final endYearController = useTextEditingController(
      text: experience?.endYear ?? '',
    );
    final descriptionController = useTextEditingController(
      text: experience?.description ?? '',
    );
    final isCurrent = useState(experience?.isCurrent ?? false);
    final isLoading = useState(false);

    return AlertDialog(
      backgroundColor: const Color(0xFF1F2937),
      insetPadding: const EdgeInsets.symmetric(horizontal: 16, vertical: 24),
      contentPadding: const EdgeInsets.fromLTRB(24, 20, 24, 0),
      actionsPadding: const EdgeInsets.fromLTRB(24, 0, 24, 24),
      title: Text(
        experience == null ? 'Add Work Experience' : 'Edit Work Experience',
        style: ATextStyle.title.copyWith(color: Colors.white),
      ),
      content: SingleChildScrollView(
        child: Form(
          key: formKey,
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              _buildTextField(
                controller: companyController,
                label: 'Company/Gym Name',
                hint: 'e.g., Elite Fitness Club',
                validator: (value) {
                  if (value == null || value.trim().isEmpty) {
                    return 'Company/Gym name is required';
                  }
                  if (value.trim().length < 2) {
                    return 'Company name must be at least 2 characters';
                  }
                  return null;
                },
              ),
              const SizedBox(height: 16),
              _buildTextField(
                controller: roleController,
                label: 'Role/Position',
                hint: 'e.g., Senior Trainer',
                validator: (value) {
                  if (value == null || value.trim().isEmpty) {
                    return 'Role/Position is required';
                  }
                  if (value.trim().length < 2) {
                    return 'Role must be at least 2 characters';
                  }
                  return null;
                },
              ),
              const SizedBox(height: 16),
              Row(
                children: [
                  Expanded(
                    child: _buildTextField(
                      controller: startYearController,
                      label: 'Start Year',
                      hint: '2020',
                      keyboardType: TextInputType.number,
                      inputFormatters: [
                        FilteringTextInputFormatter.digitsOnly,
                        LengthLimitingTextInputFormatter(4),
                      ],
                      validator: (value) {
                        if (value == null || value.isEmpty) {
                          return 'Start year is required';
                        }
                        final year = int.tryParse(value);
                        if (year == null) {
                          return 'Please enter a valid year';
                        }
                        final currentYear = DateTime.now().year;
                        if (year < 1950 || year > currentYear) {
                          return 'Year must be between 1950 and $currentYear';
                        }
                        return null;
                      },
                    ),
                  ),
                  const SizedBox(width: 16),
                  Expanded(
                    child: _buildTextField(
                      controller: endYearController,
                      label: 'End Year',
                      hint: 'Present',
                      enabled: !isCurrent.value,
                      keyboardType:
                          !isCurrent.value ? TextInputType.number : null,
                      inputFormatters: !isCurrent.value
                          ? [
                              FilteringTextInputFormatter.digitsOnly,
                              LengthLimitingTextInputFormatter(4),
                            ]
                          : null,
                      validator: !isCurrent.value
                          ? (value) {
                              if (value == null || value.isEmpty) {
                                return 'End year is required';
                              }
                              final year = int.tryParse(value);
                              if (year == null) {
                                return 'Please enter a valid year';
                              }
                              final currentYear = DateTime.now().year;
                              if (year < 1950 || year > currentYear) {
                                return 'Year must be between 1950 and $currentYear';
                              }
                              final startYear = int.tryParse(
                                startYearController.text,
                              );
                              if (startYear != null && year < startYear) {
                                return 'End year must be after start year';
                              }
                              return null;
                            }
                          : null,
                    ),
                  ),
                ],
              ),
              const SizedBox(height: 16),
              Row(
                children: [
                  Checkbox(
                    value: isCurrent.value,
                    onChanged: (value) => isCurrent.value = value ?? false,
                    activeColor: const Color(0xFFFACC15),
                  ),
                  Text(
                    'Currently working here',
                    style: ATextStyle.medium.copyWith(color: Colors.white),
                  ),
                ],
              ),
              const SizedBox(height: 16),
              _buildTextField(
                controller: descriptionController,
                label: 'Description',
                hint: 'Describe your role and responsibilities...',
                maxLines: 3,
              ),
            ],
          ),
        ),
      ),
      actions: [
        TextButton(
          onPressed: () => Navigator.of(context).pop(),
          child: Text(
            'Cancel',
            style: ATextStyle.medium.copyWith(color: Colors.grey),
          ),
        ),
        ElevatedButton(
          onPressed: isLoading.value
              ? null
              : () async {
                  if (isLoading.value) return;

                  // Validate form
                  if (!formKey.currentState!.validate()) {
                    return;
                  }

                  isLoading.value = true;

                  try {
                    final newExperience = WorkExperience(
                      id: experience?.id ?? const Uuid().v4(),
                      companyName: companyController.text.trim(),
                      role: roleController.text.trim(),
                      startYear: startYearController.text.trim(),
                      endYear: isCurrent.value
                          ? null
                          : endYearController.text.trim(),
                      description: descriptionController.text.trim(),
                      isCurrent: isCurrent.value,
                    );

                    await ref
                        .read(instructorProfileNotifierProvider.notifier)
                        .handleEditAction(
                      experience == null
                          ? ProfileEditAction.addWorkExperience
                          : ProfileEditAction.editWorkExperience,
                      data: {'workExperience': newExperience},
                    );

                    if (context.mounted) {
                      Navigator.of(context).pop();
                    }
                  } catch (e) {
                    // Handle error
                    debugPrint('Error saving work experience: $e');
                    if (context.mounted) {
                      ScaffoldMessenger.of(context).showSnackBar(
                        SnackBar(
                          content: Text('Error: ${e.toString()}'),
                          backgroundColor: Colors.red,
                        ),
                      );
                    }
                  } finally {
                    if (context.mounted) {
                      isLoading.value = false;
                    }
                  }
                },
          style: ElevatedButton.styleFrom(
            backgroundColor: const Color(0xFFFACC15),
            foregroundColor: Colors.black,
          ),
          child: isLoading.value
              ? const SizedBox(
                  width: 20,
                  height: 20,
                  child: CircularProgressIndicator(
                    strokeWidth: 2,
                    valueColor: AlwaysStoppedAnimation<Color>(Colors.black),
                  ),
                )
              : Text(experience == null ? 'Add' : 'Save'),
        ),
      ],
    );
  }

  Widget _buildTextField({
    required TextEditingController controller,
    required String label,
    required String hint,
    TextInputType? keyboardType,
    int maxLines = 1,
    bool enabled = true,
    String? Function(String?)? validator,
    List<TextInputFormatter>? inputFormatters,
  }) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(label, style: ATextStyle.medium.copyWith(color: Colors.white)),
        const SizedBox(height: 8),
        TextFormField(
          controller: controller,
          keyboardType: keyboardType,
          maxLines: maxLines,
          enabled: enabled,
          validator: validator,
          inputFormatters: inputFormatters,
          style: TextStyle(color: enabled ? Colors.white : Colors.grey),
          decoration: InputDecoration(
            hintText: hint,
            hintStyle: const TextStyle(color: Colors.grey),
            border: OutlineInputBorder(
              borderSide: const BorderSide(color: Color(0xFF374151)),
              borderRadius: BorderRadius.circular(8),
            ),
            enabledBorder: OutlineInputBorder(
              borderSide: const BorderSide(color: Color(0xFF374151)),
              borderRadius: BorderRadius.circular(8),
            ),
            focusedBorder: OutlineInputBorder(
              borderSide: const BorderSide(color: Color(0xFFFACC15)),
              borderRadius: BorderRadius.circular(8),
            ),
            errorBorder: OutlineInputBorder(
              borderSide: const BorderSide(color: Colors.red),
              borderRadius: BorderRadius.circular(8),
            ),
            focusedErrorBorder: OutlineInputBorder(
              borderSide: const BorderSide(color: Colors.red),
              borderRadius: BorderRadius.circular(8),
            ),
            disabledBorder: OutlineInputBorder(
              borderSide: const BorderSide(color: Color(0xFF4B5563)),
              borderRadius: BorderRadius.circular(8),
            ),
            filled: true,
            fillColor:
                enabled ? const Color(0xFF374151) : const Color(0xFF4B5563),
            contentPadding: const EdgeInsets.all(16),
          ),
        ),
      ],
    );
  }
}

/// Certification edit dialog
class CertificationEditDialog extends HookConsumerWidget {
  final Certification? certification;

  const CertificationEditDialog({super.key, this.certification});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final formKey = useMemoized(() => GlobalKey<FormState>());
    final nameController = useTextEditingController(
      text: certification?.name ?? '',
    );
    final issuerController = useTextEditingController(
      text: certification?.issuer ?? '',
    );
    final yearController = useTextEditingController(
      text: certification?.year ?? '',
    );
    final linkController = useTextEditingController(
      text: certification?.externalLink ?? '',
    );
    final isLoading = useState(false);

    return AlertDialog(
      backgroundColor: const Color(0xFF1F2937),
      insetPadding: const EdgeInsets.symmetric(horizontal: 16, vertical: 24),
      contentPadding: const EdgeInsets.fromLTRB(24, 20, 24, 0),
      actionsPadding: const EdgeInsets.fromLTRB(24, 0, 24, 24),
      title: Text(
        certification == null ? 'Add Certification' : 'Edit Certification',
        style: ATextStyle.title.copyWith(color: Colors.white),
      ),
      content: SingleChildScrollView(
        child: Form(
          key: formKey,
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              _buildTextField(
                controller: nameController,
                label: 'Certification Name',
                hint: 'e.g., NASM CPT',
                validator: (value) {
                  if (value == null || value.trim().isEmpty) {
                    return 'Certification name is required';
                  }
                  if (value.trim().length < 2) {
                    return 'Certification name must be at least 2 characters';
                  }
                  return null;
                },
              ),
              const SizedBox(height: 16),
              _buildTextField(
                controller: issuerController,
                label: 'Issuing Organization',
                hint: 'e.g., National Academy of Sports Medicine',
                validator: (value) {
                  if (value == null || value.trim().isEmpty) {
                    return 'Issuing organization is required';
                  }
                  if (value.trim().length < 2) {
                    return 'Organization name must be at least 2 characters';
                  }
                  return null;
                },
              ),
              const SizedBox(height: 16),
              _buildTextField(
                controller: yearController,
                label: 'Year Obtained',
                hint: '2020',
                keyboardType: TextInputType.number,
                inputFormatters: [
                  FilteringTextInputFormatter.digitsOnly,
                  LengthLimitingTextInputFormatter(4),
                ],
                validator: (value) {
                  if (value == null || value.isEmpty) {
                    return 'Year is required';
                  }
                  final year = int.tryParse(value);
                  if (year == null) {
                    return 'Please enter a valid year';
                  }
                  final currentYear = DateTime.now().year;
                  if (year < 1950 || year > currentYear) {
                    return 'Year must be between 1950 and $currentYear';
                  }
                  return null;
                },
              ),
              const SizedBox(height: 16),
              _buildTextField(
                controller: linkController,
                label: 'Verification Link (Optional)',
                hint: 'https://verify.example.com',
                validator: (value) {
                  if (value != null && value.trim().isNotEmpty) {
                    final urlPattern = RegExp(
                      r'^https?:\/\/(www\.)?[-a-zA-Z0-9@:%._\+~#=]{1,256}\.[a-zA-Z0-9()]{1,6}\b([-a-zA-Z0-9()@:%_\+.~#?&//=]*)$',
                    );
                    if (!urlPattern.hasMatch(value.trim())) {
                      return 'Please enter a valid URL (starting with http:// or https://)';
                    }
                  }
                  return null;
                },
              ),
            ],
          ),
        ),
      ),
      actions: [
        TextButton(
          onPressed: () => Navigator.of(context).pop(),
          child: Text(
            'Cancel',
            style: ATextStyle.medium.copyWith(color: Colors.grey),
          ),
        ),
        ElevatedButton(
          onPressed: isLoading.value
              ? null
              : () async {
                  if (isLoading.value) return;

                  // Validate form
                  if (!formKey.currentState!.validate()) {
                    return;
                  }

                  isLoading.value = true;

                  try {
                    final newCertification = Certification(
                      id: certification?.id ?? const Uuid().v4(),
                      name: nameController.text.trim(),
                      issuer: issuerController.text.trim(),
                      year: yearController.text.trim(),
                      externalLink: linkController.text.trim().isEmpty
                          ? null
                          : linkController.text.trim(),
                      isVerified: certification?.isVerified ?? false,
                    );

                    await ref
                        .read(instructorProfileNotifierProvider.notifier)
                        .handleEditAction(
                      certification == null
                          ? ProfileEditAction.addCertification
                          : ProfileEditAction.editCertification,
                      data: {'certification': newCertification},
                    );

                    if (context.mounted) {
                      Navigator.of(context).pop();
                    }
                  } catch (e) {
                    debugPrint('Error saving certification: $e');
                  } finally {
                    isLoading.value = false;
                  }
                },
          style: ElevatedButton.styleFrom(
            backgroundColor: const Color(0xFFFACC15),
            foregroundColor: Colors.black,
          ),
          child: isLoading.value
              ? const SizedBox(
                  width: 20,
                  height: 20,
                  child: CircularProgressIndicator(
                    strokeWidth: 2,
                    valueColor: AlwaysStoppedAnimation<Color>(Colors.black),
                  ),
                )
              : Text(certification == null ? 'Add' : 'Save'),
        ),
      ],
    );
  }

  Widget _buildTextField({
    required TextEditingController controller,
    required String label,
    required String hint,
    TextInputType? keyboardType,
    String? Function(String?)? validator,
    List<TextInputFormatter>? inputFormatters,
  }) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(label, style: ATextStyle.medium.copyWith(color: Colors.white)),
        const SizedBox(height: 8),
        TextFormField(
          controller: controller,
          keyboardType: keyboardType,
          validator: validator,
          inputFormatters: inputFormatters,
          style: const TextStyle(color: Colors.white),
          decoration: InputDecoration(
            hintText: hint,
            hintStyle: const TextStyle(color: Colors.grey),
            border: OutlineInputBorder(
              borderSide: const BorderSide(color: Color(0xFF374151)),
              borderRadius: BorderRadius.circular(8),
            ),
            enabledBorder: OutlineInputBorder(
              borderSide: const BorderSide(color: Color(0xFF374151)),
              borderRadius: BorderRadius.circular(8),
            ),
            focusedBorder: OutlineInputBorder(
              borderSide: const BorderSide(color: Color(0xFFFACC15)),
              borderRadius: BorderRadius.circular(8),
            ),
            errorBorder: OutlineInputBorder(
              borderSide: const BorderSide(color: Colors.red),
              borderRadius: BorderRadius.circular(8),
            ),
            focusedErrorBorder: OutlineInputBorder(
              borderSide: const BorderSide(color: Colors.red),
              borderRadius: BorderRadius.circular(8),
            ),
            filled: true,
            fillColor: const Color(0xFF374151),
            contentPadding: const EdgeInsets.all(16),
          ),
        ),
      ],
    );
  }
}

/// FAQ edit dialog
class FAQEditDialog extends HookConsumerWidget {
  final FAQ? faq;

  const FAQEditDialog({super.key, this.faq});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final formKey = useMemoized(() => GlobalKey<FormState>());
    final questionController = useTextEditingController(
      text: faq?.question ?? '',
    );
    final answerController = useTextEditingController(text: faq?.answer ?? '');
    final isLoading = useState(false);

    return AlertDialog(
      backgroundColor: const Color(0xFF1F2937),
      insetPadding: const EdgeInsets.symmetric(horizontal: 16, vertical: 24),
      contentPadding: const EdgeInsets.fromLTRB(24, 20, 24, 0),
      actionsPadding: const EdgeInsets.fromLTRB(24, 0, 24, 24),
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
      title: Text(
        faq == null ? 'Add FAQ' : 'Edit FAQ',
        style: ATextStyle.title.copyWith(color: Colors.white),
      ),
      content: SizedBox(
        width: double.maxFinite,
        child: SingleChildScrollView(
          child: Form(
            key: formKey,
            child: Column(
              mainAxisSize: MainAxisSize.min,
              children: [
                _buildTextField(
                  controller: questionController,
                  label: 'Question',
                  hint: 'What is your training style?',
                  validator: (value) {
                    if (value == null || value.trim().isEmpty) {
                      return 'Question is required';
                    }
                    return null;
                  },
                ),
                const SizedBox(height: 16),
                _buildTextField(
                  controller: answerController,
                  label: 'Answer',
                  hint: 'Provide a detailed answer...',
                  maxLines: 4,
                  validator: (value) {
                    if (value == null || value.trim().isEmpty) {
                      return 'Answer is required';
                    }
                    return null;
                  },
                ),
              ],
            ),
          ),
        ),
      ),
      actions: [
        TextButton(
          onPressed: () => Navigator.of(context).pop(),
          child: Text(
            'Cancel',
            style: ATextStyle.medium.copyWith(color: Colors.grey),
          ),
        ),
        ElevatedButton(
          onPressed: isLoading.value
              ? null
              : () async {
                  // Validate form
                  if (!formKey.currentState!.validate()) {
                    return;
                  }

                  isLoading.value = true;

                  try {
                    final newFAQ = FAQ(
                      id: faq?.id ?? const Uuid().v4(),
                      question: questionController.text.trim(),
                      answer: answerController.text.trim(),
                      order: faq?.order ?? 0,
                      isExpanded: faq?.isExpanded ?? false,
                    );

                    await ref
                        .read(instructorProfileNotifierProvider.notifier)
                        .handleEditAction(
                      faq == null
                          ? ProfileEditAction.addFAQ
                          : ProfileEditAction.editFAQ,
                      data: {'faq': newFAQ},
                    );

                    if (context.mounted) {
                      Navigator.of(context).pop();
                      ScaffoldMessenger.of(context).showSnackBar(
                        SnackBar(
                          content: Text(faq == null
                              ? 'FAQ added successfully'
                              : 'FAQ updated successfully'),
                          backgroundColor: const Color(0xFF10B981),
                        ),
                      );
                    }
                  } catch (e) {
                    // Handle error
                    debugPrint('Error saving FAQ: $e');
                    if (context.mounted) {
                      ScaffoldMessenger.of(context).showSnackBar(
                        SnackBar(
                          content: Text('Error: ${e.toString()}'),
                          backgroundColor: Colors.red,
                        ),
                      );
                    }
                  } finally {
                    if (context.mounted) {
                      isLoading.value = false;
                    }
                  }
                },
          style: ElevatedButton.styleFrom(
            backgroundColor: const Color(0xFFFACC15),
            foregroundColor: Colors.black,
          ),
          child: isLoading.value
              ? const SizedBox(
                  width: 20,
                  height: 20,
                  child: CircularProgressIndicator(
                    strokeWidth: 2,
                    valueColor: AlwaysStoppedAnimation<Color>(Colors.black),
                  ),
                )
              : Text(faq == null ? 'Add' : 'Save'),
        ),
      ],
    );
  }

  Widget _buildTextField({
    required TextEditingController controller,
    required String label,
    required String hint,
    int maxLines = 1,
    String? Function(String?)? validator,
  }) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(label, style: ATextStyle.medium.copyWith(color: Colors.white)),
        const SizedBox(height: 8),
        TextFormField(
          controller: controller,
          maxLines: maxLines,
          validator: validator,
          style: const TextStyle(color: Colors.white),
          decoration: InputDecoration(
            hintText: hint,
            hintStyle: const TextStyle(color: Colors.grey),
            border: OutlineInputBorder(
              borderSide: const BorderSide(color: Color(0xFF374151)),
              borderRadius: BorderRadius.circular(8),
            ),
            enabledBorder: OutlineInputBorder(
              borderSide: const BorderSide(color: Color(0xFF374151)),
              borderRadius: BorderRadius.circular(8),
            ),
            focusedBorder: OutlineInputBorder(
              borderSide: const BorderSide(color: Color(0xFFFACC15)),
              borderRadius: BorderRadius.circular(8),
            ),
            filled: true,
            fillColor: const Color(0xFF374151),
          ),
        ),
      ],
    );
  }
}
