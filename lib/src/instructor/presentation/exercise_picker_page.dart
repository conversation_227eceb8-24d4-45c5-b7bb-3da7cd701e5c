import 'package:flutter/material.dart';
import 'package:flutter_hooks/flutter_hooks.dart';
import 'package:hooks_riverpod/hooks_riverpod.dart';
import '../domain/workout_plan_template.dart';
import '../application/workout_plan_template_provider.dart';

/// Exercise Picker page for selecting and customizing exercises for student plans
class ExercisePickerPage extends HookConsumerWidget {
  final int planIndex;
  final String planName;

  const ExercisePickerPage({
    super.key,
    required this.planIndex,
    required this.planName,
  });

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final searchController = useTextEditingController();
    final selectedFilter = useState<String>('All');
    final selectedExercise = useState<Exercise?>(null);
    final setsController = useTextEditingController(text: '3');
    final repsController = useTextEditingController(text: '10');
    final restController = useTextEditingController(text: '60');
    final notesController = useTextEditingController();

    final exercisesAsync = ref.watch(exercisesProvider(const ExerciseFilters()));

    // Filter exercises based on search and category
    final filteredExercises = useMemoized(() {
      return exercisesAsync.when(
        data: (exercises) {
          var filtered = exercises;
          
          // Apply search filter
          if (searchController.text.isNotEmpty) {
            filtered = filtered.where((exercise) =>
              exercise.name.toLowerCase().contains(searchController.text.toLowerCase())
            ).toList();
          }
          
          // Apply category filter
          if (selectedFilter.value != 'All') {
            filtered = filtered.where((exercise) =>
              exercise.muscleGroups.any((group) => 
                group.toLowerCase().contains(selectedFilter.value.toLowerCase())
              )
            ).toList();
          }
          
          return filtered;
        },
        loading: () => <Exercise>[],
        error: (_, __) => <Exercise>[],
      );
    }, [exercisesAsync, searchController.text, selectedFilter.value]);

    return Scaffold(
      backgroundColor: const Color(0xFF111827),
      appBar: AppBar(
        backgroundColor: const Color(0xFF111827),
        elevation: 0,
        leading: IconButton(
          icon: const Icon(Icons.arrow_back, color: Colors.white),
          onPressed: () => Navigator.pop(context),
        ),
        title: const Text(
          'Add Exercise',
          style: TextStyle(
            color: Colors.white,
            fontSize: 20,
            fontWeight: FontWeight.w600,
          ),
        ),
      ),
      body: Column(
        children: [
          // Plan Info Header
          Container(
            padding: const EdgeInsets.all(16),
            decoration: const BoxDecoration(
              color: Color(0xFF1F2937),
              border: Border(
                bottom: BorderSide(color: Color(0xFF374151), width: 1),
              ),
            ),
            child: Row(
              children: [
                const Icon(Icons.fitness_center, color: Color(0xFFFACC15), size: 20),
                const SizedBox(width: 8),
                Text(
                  'Adding to: $planName',
                  style: const TextStyle(
                    color: Color(0xFFFACC15),
                    fontSize: 16,
                    fontWeight: FontWeight.w600,
                  ),
                ),
              ],
            ),
          ),

          // Search and Filters
          Padding(
            padding: const EdgeInsets.all(16),
            child: Column(
              children: [
                // Search Bar
                TextField(
                  controller: searchController,
                  style: const TextStyle(color: Colors.white),
                  decoration: InputDecoration(
                    hintText: 'Search exercises...',
                    hintStyle: const TextStyle(color: Color(0xFF6B7280)),
                    prefixIcon: const Icon(Icons.search, color: Color(0xFF6B7280)),
                    filled: true,
                    fillColor: const Color(0xFF1F2937),
                    border: OutlineInputBorder(
                      borderRadius: BorderRadius.circular(12),
                      borderSide: const BorderSide(color: Color(0xFF374151)),
                    ),
                    enabledBorder: OutlineInputBorder(
                      borderRadius: BorderRadius.circular(12),
                      borderSide: const BorderSide(color: Color(0xFF374151)),
                    ),
                    focusedBorder: OutlineInputBorder(
                      borderRadius: BorderRadius.circular(12),
                      borderSide: const BorderSide(color: Color(0xFFFACC15)),
                    ),
                  ),
                  onChanged: (value) {
                    // Trigger rebuild to filter exercises
                  },
                ),
                const SizedBox(height: 16),

                // Filter Chips
                SizedBox(
                  height: 40,
                  child: ListView(
                    scrollDirection: Axis.horizontal,
                    children: [
                      'All',
                      'Chest',
                      'Shoulders',
                      'Arms',
                      'Back',
                      'Legs',
                      'Core',
                      'Cardio',
                    ].map((filter) => _buildFilterChip(filter, selectedFilter)).toList(),
                  ),
                ),
              ],
            ),
          ),

          // Exercise List or Selected Exercise
          Expanded(
            child: selectedExercise.value == null
                ? _buildExerciseList(filteredExercises, selectedExercise)
                : _buildSelectedExerciseView(
                    selectedExercise.value!,
                    setsController,
                    repsController,
                    restController,
                    notesController,
                    () => selectedExercise.value = null,
                  ),
          ),
        ],
      ),
      bottomNavigationBar: selectedExercise.value != null
          ? Container(
              padding: const EdgeInsets.all(16),
              decoration: const BoxDecoration(
                color: Color(0xFF1F2937),
                border: Border(
                  top: BorderSide(color: Color(0xFF374151), width: 1),
                ),
              ),
              child: SafeArea(
                child: SizedBox(
                  width: double.infinity,
                  height: 48,
                  child: ElevatedButton(
                    onPressed: () => _addExerciseToplan(
                      context,
                      selectedExercise.value!,
                      setsController.text,
                      repsController.text,
                      restController.text,
                      notesController.text,
                    ),
                    style: ElevatedButton.styleFrom(
                      backgroundColor: const Color(0xFFFACC15),
                      foregroundColor: const Color(0xFF111827),
                      elevation: 0,
                      shape: RoundedRectangleBorder(
                        borderRadius: BorderRadius.circular(12),
                      ),
                    ),
                    child: const Text(
                      '+ Add Exercise',
                      style: TextStyle(
                        fontSize: 16,
                        fontWeight: FontWeight.w600,
                      ),
                    ),
                  ),
                ),
              ),
            )
          : null,
    );
  }

  Widget _buildFilterChip(String filter, ValueNotifier<String> selectedFilter) {
    final isSelected = selectedFilter.value == filter;
    
    return Padding(
      padding: const EdgeInsets.only(right: 8),
      child: FilterChip(
        label: Text(filter),
        selected: isSelected,
        onSelected: (selected) {
          selectedFilter.value = filter;
        },
        backgroundColor: const Color(0xFF1F2937),
        selectedColor: const Color(0xFFFACC15).withValues(alpha: 0.2),
        labelStyle: TextStyle(
          color: isSelected ? const Color(0xFFFACC15) : Colors.grey[300],
          fontWeight: isSelected ? FontWeight.w600 : FontWeight.normal,
        ),
        side: BorderSide(
          color: isSelected ? const Color(0xFFFACC15) : const Color(0xFF374151),
        ),
        showCheckmark: false,
      ),
    );
  }

  Widget _buildExerciseList(List<Exercise> exercises, ValueNotifier<Exercise?> selectedExercise) {
    if (exercises.isEmpty) {
      return const Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              Icons.search_off,
              color: Color(0xFF6B7280),
              size: 64,
            ),
            SizedBox(height: 16),
            Text(
              'No exercises found',
              style: TextStyle(
                color: Color(0xFF9CA3AF),
                fontSize: 18,
                fontWeight: FontWeight.w500,
              ),
            ),
            SizedBox(height: 8),
            Text(
              'Try adjusting your search or filters',
              style: TextStyle(
                color: Color(0xFF6B7280),
                fontSize: 14,
              ),
            ),
          ],
        ),
      );
    }

    return ListView.builder(
      padding: const EdgeInsets.symmetric(horizontal: 16),
      itemCount: exercises.length,
      itemBuilder: (context, index) {
        final exercise = exercises[index];
        return _buildExerciseCard(exercise, selectedExercise);
      },
    );
  }

  Widget _buildExerciseCard(Exercise exercise, ValueNotifier<Exercise?> selectedExercise) {
    return Container(
      margin: const EdgeInsets.only(bottom: 12),
      decoration: BoxDecoration(
        color: const Color(0xFF1F2937),
        borderRadius: BorderRadius.circular(12),
        border: Border.all(color: const Color(0xFF374151)),
      ),
      child: InkWell(
        onTap: () => selectedExercise.value = exercise,
        borderRadius: BorderRadius.circular(12),
        child: Padding(
          padding: const EdgeInsets.all(16),
          child: Row(
            children: [
              // Exercise Image/Icon
              Container(
                width: 60,
                height: 60,
                decoration: BoxDecoration(
                  color: const Color(0xFF374151),
                  borderRadius: BorderRadius.circular(8),
                ),
                child: exercise.imageUrl != null
                    ? ClipRRect(
                        borderRadius: BorderRadius.circular(8),
                        child: Image.network(
                          exercise.imageUrl!,
                          fit: BoxFit.cover,
                          errorBuilder: (context, error, stackTrace) => const Icon(
                            Icons.fitness_center,
                            color: Color(0xFF6B7280),
                            size: 24,
                          ),
                        ),
                      )
                    : const Icon(
                        Icons.fitness_center,
                        color: Color(0xFF6B7280),
                        size: 24,
                      ),
              ),
              const SizedBox(width: 16),

              // Exercise Info
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      exercise.name,
                      style: const TextStyle(
                        color: Colors.white,
                        fontSize: 16,
                        fontWeight: FontWeight.w600,
                      ),
                    ),
                    const SizedBox(height: 4),
                    Text(
                      exercise.description ?? 'No description available',
                      style: const TextStyle(
                        color: Color(0xFF9CA3AF),
                        fontSize: 14,
                      ),
                      maxLines: 2,
                      overflow: TextOverflow.ellipsis,
                    ),
                    const SizedBox(height: 8),
                    Wrap(
                      spacing: 4,
                      runSpacing: 4,
                      children: exercise.muscleGroups.take(3).map((group) => Container(
                        padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 2),
                        decoration: BoxDecoration(
                          color: const Color(0xFF6B7280).withValues(alpha: 0.2),
                          borderRadius: BorderRadius.circular(12),
                        ),
                        child: Text(
                          group,
                          style: const TextStyle(
                            color: Color(0xFF9CA3AF),
                            fontSize: 10,
                            fontWeight: FontWeight.w500,
                          ),
                        ),
                      )).toList(),
                    ),
                  ],
                ),
              ),

              // Select Button
              Container(
                padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
                decoration: BoxDecoration(
                  color: const Color(0xFFFACC15).withValues(alpha: 0.2),
                  borderRadius: BorderRadius.circular(8),
                  border: Border.all(color: const Color(0xFFFACC15)),
                ),
                child: const Text(
                  'Select',
                  style: TextStyle(
                    color: Color(0xFFFACC15),
                    fontSize: 12,
                    fontWeight: FontWeight.w600,
                  ),
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildSelectedExerciseView(
    Exercise exercise,
    TextEditingController setsController,
    TextEditingController repsController,
    TextEditingController restController,
    TextEditingController notesController,
    VoidCallback onBack,
  ) {
    return SingleChildScrollView(
      padding: const EdgeInsets.all(16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Back Button
          Row(
            children: [
              IconButton(
                onPressed: onBack,
                icon: const Icon(Icons.arrow_back, color: Color(0xFFFACC15)),
              ),
              const Text(
                'Back to exercises',
                style: TextStyle(
                  color: Color(0xFFFACC15),
                  fontSize: 16,
                  fontWeight: FontWeight.w500,
                ),
              ),
            ],
          ),
          const SizedBox(height: 16),

          // Exercise Preview
          Container(
            width: double.infinity,
            height: 200,
            decoration: BoxDecoration(
              color: const Color(0xFF1F2937),
              borderRadius: BorderRadius.circular(12),
              border: Border.all(color: const Color(0xFF374151)),
            ),
            child: exercise.imageUrl != null
                ? ClipRRect(
                    borderRadius: BorderRadius.circular(12),
                    child: Image.network(
                      exercise.imageUrl!,
                      fit: BoxFit.cover,
                      errorBuilder: (context, error, stackTrace) => const Center(
                        child: Icon(
                          Icons.fitness_center,
                          color: Color(0xFF6B7280),
                          size: 64,
                        ),
                      ),
                    ),
                  )
                : const Center(
                    child: Icon(
                      Icons.fitness_center,
                      color: Color(0xFF6B7280),
                      size: 64,
                    ),
                  ),
          ),
          const SizedBox(height: 16),

          // Exercise Info
          Text(
            exercise.name,
            style: const TextStyle(
              color: Colors.white,
              fontSize: 24,
              fontWeight: FontWeight.w700,
            ),
          ),
          const SizedBox(height: 8),
          Text(
            exercise.description ?? 'No description available',
            style: const TextStyle(
              color: Color(0xFF9CA3AF),
              fontSize: 16,
            ),
          ),
          const SizedBox(height: 16),

          // Muscle Groups
          Wrap(
            spacing: 8,
            runSpacing: 8,
            children: exercise.muscleGroups.map((group) => Container(
              padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
              decoration: BoxDecoration(
                color: const Color(0xFFFACC15).withValues(alpha: 0.2),
                borderRadius: BorderRadius.circular(16),
                border: Border.all(color: const Color(0xFFFACC15).withValues(alpha: 0.3)),
              ),
              child: Text(
                group,
                style: const TextStyle(
                  color: Color(0xFFFACC15),
                  fontSize: 12,
                  fontWeight: FontWeight.w500,
                ),
              ),
            )).toList(),
          ),
          const SizedBox(height: 24),

          // Customization Fields
          const Text(
            'Customize Exercise',
            style: TextStyle(
              color: Colors.white,
              fontSize: 18,
              fontWeight: FontWeight.w600,
            ),
          ),
          const SizedBox(height: 16),

          // Sets and Reps Row
          Row(
            children: [
              Expanded(
                child: _buildNumberField('Sets', setsController),
              ),
              const SizedBox(width: 16),
              Expanded(
                child: _buildNumberField('Reps', repsController),
              ),
            ],
          ),
          const SizedBox(height: 16),

          // Rest Time
          _buildNumberField('Rest Time (seconds)', restController),
          const SizedBox(height: 16),

          // Notes
          _buildNotesField(notesController),
          const SizedBox(height: 100), // Space for bottom button
        ],
      ),
    );
  }

  Widget _buildNumberField(String label, TextEditingController controller) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          label,
          style: const TextStyle(
            color: Colors.white,
            fontSize: 14,
            fontWeight: FontWeight.w500,
          ),
        ),
        const SizedBox(height: 8),
        TextField(
          controller: controller,
          keyboardType: TextInputType.number,
          style: const TextStyle(color: Colors.white),
          decoration: InputDecoration(
            hintText: '0',
            hintStyle: const TextStyle(color: Color(0xFF6B7280)),
            filled: true,
            fillColor: const Color(0xFF1F2937),
            border: OutlineInputBorder(
              borderRadius: BorderRadius.circular(8),
              borderSide: const BorderSide(color: Color(0xFF374151)),
            ),
            enabledBorder: OutlineInputBorder(
              borderRadius: BorderRadius.circular(8),
              borderSide: const BorderSide(color: Color(0xFF374151)),
            ),
            focusedBorder: OutlineInputBorder(
              borderRadius: BorderRadius.circular(8),
              borderSide: const BorderSide(color: Color(0xFFFACC15)),
            ),
            contentPadding: const EdgeInsets.symmetric(horizontal: 12, vertical: 16),
          ),
        ),
      ],
    );
  }

  Widget _buildNotesField(TextEditingController controller) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        const Text(
          'Notes (Optional)',
          style: TextStyle(
            color: Colors.white,
            fontSize: 14,
            fontWeight: FontWeight.w500,
          ),
        ),
        const SizedBox(height: 8),
        TextField(
          controller: controller,
          maxLines: 3,
          style: const TextStyle(color: Colors.white),
          decoration: InputDecoration(
            hintText: 'Add any special instructions or notes...',
            hintStyle: const TextStyle(color: Color(0xFF6B7280)),
            filled: true,
            fillColor: const Color(0xFF1F2937),
            border: OutlineInputBorder(
              borderRadius: BorderRadius.circular(8),
              borderSide: const BorderSide(color: Color(0xFF374151)),
            ),
            enabledBorder: OutlineInputBorder(
              borderRadius: BorderRadius.circular(8),
              borderSide: const BorderSide(color: Color(0xFF374151)),
            ),
            focusedBorder: OutlineInputBorder(
              borderRadius: BorderRadius.circular(8),
              borderSide: const BorderSide(color: Color(0xFFFACC15)),
            ),
            contentPadding: const EdgeInsets.all(12),
          ),
        ),
      ],
    );
  }

  void _addExerciseToplan(
    BuildContext context,
    Exercise exercise,
    String sets,
    String reps,
    String restTime,
    String notes,
  ) {
    // Validate inputs
    if (sets.isEmpty || reps.isEmpty || restTime.isEmpty) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(
          content: Text('Please fill in sets, reps, and rest time'),
          backgroundColor: Colors.red,
        ),
      );
      return;
    }

    // Create exercise data
    final exerciseData = {
      'id': exercise.id,
      'name': exercise.name,
      'description': exercise.description,
      'sets': int.tryParse(sets) ?? 0,
      'reps': int.tryParse(reps) ?? 0,
      'rest_time': int.tryParse(restTime) ?? 0,
      'notes': notes.isNotEmpty ? notes : null,
      'muscle_groups': exercise.muscleGroups,
      'equipment': exercise.equipment,
      'difficulty_level': exercise.difficultyLevel,
      'image_url': exercise.imageUrl,
    };

    // Return the exercise data to the previous screen
    Navigator.pop(context, exerciseData);
  }
}
