import 'package:supabase_flutter/supabase_flutter.dart';
import '../domain/supplement_models.dart';
import '../../shared/utils/app_logger.dart';

class SupplementRepository {
  final SupabaseClient _supabase = Supabase.instance.client;

  /// Get all available supplements
  Future<List<Supplement>> getSupplements({
    SupplementType? type,
    SupplementCategory? category,
    String? searchQuery,
  }) async {
    try {
      var query = _supabase
          .from('supplements')
          .select()
          .eq('is_active', true);

      if (type != null) {
        query = query.eq('type', type.name);
      }

      if (category != null) {
        query = query.eq('category', category.name);
      }

      if (searchQuery != null && searchQuery.isNotEmpty) {
        query = query.or('name.ilike.%$searchQuery%,brand.ilike.%$searchQuery%');
      }

      final response = await query.order('name');

      return (response as List)
          .map((json) => Supplement.fromJson(json))
          .toList();
    } catch (e) {
      AppLogger.error('Failed to fetch supplements: $e', tag: 'SUPPLEMENT_REPO');
      rethrow;
    }
  }

  /// Get supplement by ID
  Future<Supplement?> getSupplementById(String supplementId) async {
    try {
      final response = await _supabase
          .from('supplements')
          .select()
          .eq('id', supplementId)
          .eq('is_active', true)
          .maybeSingle();

      if (response == null) return null;
      
      return Supplement.fromJson(response);
    } catch (e) {
      AppLogger.error('Failed to fetch supplement: $e', tag: 'SUPPLEMENT_REPO');
      rethrow;
    }
  }

  /// Get student's assigned supplements
  Future<List<StudentSupplementAssignment>> getStudentSupplements(
    String studentId,
    String instructorId,
  ) async {
    try {
      final response = await _supabase
          .from('student_supplement_assignments')
          .select('''
            *,
            supplements (
              id,
              name,
              brand,
              description,
              type,
              category,
              image_url,
              benefits,
              ingredients,
              warnings
            )
          ''')
          .eq('student_id', studentId)
          .eq('instructor_id', instructorId)
          .order('assigned_at', ascending: false);

      return (response as List)
          .map((json) => StudentSupplementAssignment.fromJson(json))
          .toList();
    } catch (e) {
      AppLogger.error('Failed to fetch student supplements: $e', tag: 'SUPPLEMENT_REPO');
      rethrow;
    }
  }

  /// Assign supplement to student
  Future<StudentSupplementAssignment> assignSupplementToStudent({
    required String studentId,
    required String instructorId,
    required String supplementId,
    required int quantity,
    String? notes,
  }) async {
    try {
      final assignmentData = {
        'student_id': studentId,
        'instructor_id': instructorId,
        'supplement_id': supplementId,
        'quantity': quantity,
        'notes': notes,
        'status': SupplementAssignmentStatus.active.name,
        'assigned_at': DateTime.now().toIso8601String(),
      };

      final response = await _supabase
          .from('student_supplement_assignments')
          .insert(assignmentData)
          .select('''
            *,
            supplements (
              id,
              name,
              brand,
              description,
              type,
              category,
              image_url,
              benefits,
              ingredients,
              warnings
            )
          ''')
          .single();

      AppLogger.success(
        'Supplement assigned successfully: $supplementId to student $studentId',
        tag: 'SUPPLEMENT_REPO',
      );

      return StudentSupplementAssignment.fromJson(response);
    } catch (e) {
      AppLogger.error('Failed to assign supplement: $e', tag: 'SUPPLEMENT_REPO');
      rethrow;
    }
  }

  /// Update supplement assignment
  Future<StudentSupplementAssignment> updateSupplementAssignment({
    required String assignmentId,
    int? quantity,
    String? notes,
    SupplementAssignmentStatus? status,
  }) async {
    try {
      final updateData = <String, dynamic>{};

      if (quantity != null) updateData['quantity'] = quantity;
      if (notes != null) updateData['notes'] = notes;
      if (status != null) updateData['status'] = status.name;
      
      updateData['updated_at'] = DateTime.now().toIso8601String();

      final response = await _supabase
          .from('student_supplement_assignments')
          .update(updateData)
          .eq('id', assignmentId)
          .select('''
            *,
            supplements (
              id,
              name,
              brand,
              description,
              type,
              category,
              image_url,
              benefits,
              ingredients,
              warnings
            )
          ''')
          .single();

      AppLogger.success(
        'Supplement assignment updated: $assignmentId',
        tag: 'SUPPLEMENT_REPO',
      );

      return StudentSupplementAssignment.fromJson(response);
    } catch (e) {
      AppLogger.error('Failed to update supplement assignment: $e', tag: 'SUPPLEMENT_REPO');
      rethrow;
    }
  }

  /// Remove supplement assignment
  Future<void> removeSupplementAssignment(String assignmentId) async {
    try {
      await _supabase
          .from('student_supplement_assignments')
          .delete()
          .eq('id', assignmentId);

      AppLogger.success(
        'Supplement assignment removed: $assignmentId',
        tag: 'SUPPLEMENT_REPO',
      );
    } catch (e) {
      AppLogger.error('Failed to remove supplement assignment: $e', tag: 'SUPPLEMENT_REPO');
      rethrow;
    }
  }

  /// Create custom supplement
  Future<Supplement> createCustomSupplement({
    required String name,
    required String brand,
    required String description,
    required SupplementType type,
    required SupplementCategory category,
    String? imageUrl,
    List<String>? benefits,
    List<String>? ingredients,
    String? warnings,
  }) async {
    try {
      final supplementData = {
        'name': name,
        'brand': brand,
        'description': description,
        'type': type.name,
        'category': category.name,
        'image_url': imageUrl,
        'benefits': benefits ?? [],
        'ingredients': ingredients ?? [],
        'warnings': warnings,
        'is_active': true,
        'created_at': DateTime.now().toIso8601String(),
        'updated_at': DateTime.now().toIso8601String(),
      };

      final response = await _supabase
          .from('supplements')
          .insert(supplementData)
          .select()
          .single();

      AppLogger.success(
        'Custom supplement created: $name',
        tag: 'SUPPLEMENT_REPO',
      );

      return Supplement.fromJson(response);
    } catch (e) {
      AppLogger.error('Failed to create custom supplement: $e', tag: 'SUPPLEMENT_REPO');
      rethrow;
    }
  }

  /// Get supplement statistics for instructor
  Future<Map<String, int>> getSupplementStats(String instructorId) async {
    try {
      final response = await _supabase
          .from('student_supplement_assignments')
          .select('status')
          .eq('instructor_id', instructorId);

      final stats = <String, int>{
        'total': 0,
        'active': 0,
        'paused': 0,
        'completed': 0,
        'discontinued': 0,
      };

      for (final assignment in response) {
        stats['total'] = (stats['total'] ?? 0) + 1;
        final status = assignment['status'] as String;
        stats[status] = (stats[status] ?? 0) + 1;
      }

      return stats;
    } catch (e) {
      AppLogger.error('Failed to fetch supplement stats: $e', tag: 'SUPPLEMENT_REPO');
      return {};
    }
  }
}
