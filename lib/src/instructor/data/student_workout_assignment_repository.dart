import 'package:supabase_flutter/supabase_flutter.dart';
import '../domain/student_workout_assignment.dart';
import '../domain/workout_plan_template.dart';

class StudentWorkoutAssignmentRepository {
  final SupabaseClient _supabase;

  StudentWorkoutAssignmentRepository(this._supabase);

  /// Assign a workout template to a student
  Future<StudentWorkoutAssignment> assignWorkoutTemplateToStudent({
    required String templateId,
    required String studentId,
    required String instructorId,
  }) async {
    try {
      print('🏋️ Assigning workout template $templateId to student $studentId');

      // First, get the template data
      final templateResponse = await _supabase
          .from('workout_plan_templates')
          .select('''
            *,
            workout_template_plans!inner(
              *,
              workout_template_plan_exercises(
                *,
                exercises(
                  id,
                  name,
                  description,
                  muscle_groups,
                  equipment,
                  difficulty_level,
                  image_url
                )
              )
            )
          ''')
          .eq('id', templateId)
          .eq('instructor_id', instructorId)
          .single();

      // Convert template to plan data
      final planData = _convertTemplateToPlanData(templateResponse);

      // Create the assignment
      final assignmentRequest = CreateWorkoutAssignmentRequest(
        studentId: studentId,
        instructorId: instructorId,
        templateId: templateId,
        planName: templateResponse['name'] as String,
        description: templateResponse['description'] as String?,
        planData: planData,
      );

      // Insert into student_workout_plans table
      final response = await _supabase
          .from('student_workout_plans')
          .insert(assignmentRequest.toJson())
          .select()
          .single();

      print('✅ Workout template assigned successfully');
      return StudentWorkoutAssignment.fromJson(response);
    } catch (e) {
      print('❌ Error assigning workout template: $e');
      throw Exception('Failed to assign workout template: $e');
    }
  }

  /// Create a custom workout plan for a student
  Future<StudentWorkoutAssignment> createCustomWorkoutPlan({
    required String studentId,
    required String instructorId,
    required String planName,
    String? description,
  }) async {
    try {
      print('🏋️ Creating custom workout plan for student $studentId');

      // First check if a plan already exists for this student-instructor pair
      final existingResponse = await _supabase
          .from('student_workout_plans')
          .select()
          .eq('student_id', studentId)
          .eq('instructor_id', instructorId)
          .maybeSingle();

      if (existingResponse != null) {
        print('🔄 Found existing plan, updating instead of creating new one');

        // Update existing plan
        final updateData = {
          'title': planName,
          'description': description,
          'plan_data': {
            'type': 'custom',
            'updated_at': DateTime.now().toIso8601String(),
          },
          'updated_at': DateTime.now().toIso8601String(),
        };

        final response = await _supabase
            .from('student_workout_plans')
            .update(updateData)
            .eq('student_id', studentId)
            .eq('instructor_id', instructorId)
            .select()
            .single();

        print('✅ Updated existing workout plan');
        return StudentWorkoutAssignment.fromJson(response);
      } else {
        print('🆕 No existing plan found, creating new one');

        // Create new plan
        final assignmentRequest = CreateWorkoutAssignmentRequest(
          studentId: studentId,
          instructorId: instructorId,
          templateId: null, // Custom plan
          planName: planName,
          description: description,
          planData: {
            'type': 'custom',
            'created_at': DateTime.now().toIso8601String(),
          },
        );

        final response = await _supabase
            .from('student_workout_plans')
            .insert(assignmentRequest.toJson())
            .select()
            .single();

        print('✅ Custom workout plan created successfully');
        return StudentWorkoutAssignment.fromJson(response);
      }
    } catch (e) {
      print('❌ Error creating custom workout plan: $e');
      throw Exception('Failed to create custom workout plan: $e');
    }
  }

  /// Get student's current workout assignment
  Future<StudentWorkoutAssignment?> getStudentWorkoutAssignment({
    required String studentId,
    required String instructorId,
  }) async {
    try {
      final response = await _supabase
          .from('student_workout_plans')
          .select()
          .eq('student_id', studentId)
          .eq('instructor_id', instructorId)
          .eq('is_active', true)
          .maybeSingle();

      if (response == null) return null;

      return StudentWorkoutAssignment.fromJson(response);
    } catch (e) {
      print('❌ Error getting student workout assignment: $e');
      throw Exception('Failed to get student workout assignment: $e');
    }
  }

  /// Update workout assignment plan data
  Future<StudentWorkoutAssignment> updateWorkoutAssignment({
    required String assignmentId,
    String? planName,
    String? description,
    Map<String, dynamic>? planData,
  }) async {
    try {
      final updateData = <String, dynamic>{
        'updated_at': DateTime.now().toIso8601String(),
      };

      if (planName != null) updateData['title'] = planName;
      if (description != null) updateData['description'] = description;
      if (planData != null) updateData['plan_data'] = planData;

      final response = await _supabase
          .from('student_workout_plans')
          .update(updateData)
          .eq('id', assignmentId)
          .select()
          .single();

      return StudentWorkoutAssignment.fromJson(response);
    } catch (e) {
      print('❌ Error updating workout assignment: $e');
      throw Exception('Failed to update workout assignment: $e');
    }
  }

  /// Convert workout template to plan data format
  Map<String, dynamic> _convertTemplateToPlanData(Map<String, dynamic> template) {
    final plans = template['workout_template_plans'] as List<dynamic>;
    
    return {
      'template_id': template['id'],
      'template_name': template['name'],
      'plans': plans.map((plan) {
        final exercises = plan['workout_template_plan_exercises'] as List<dynamic>? ?? [];
        
        return {
          'id': plan['id'],
          'name': plan['name'],
          'description': plan['description'],
          'order_index': plan['order_index'],
          'exercises': exercises.map((exercise) {
            final exerciseDetails = exercise['exercises'] as Map<String, dynamic>;
            
            return {
              'id': exercise['id'],
              'exercise_id': exercise['exercise_id'],
              'order_index': exercise['order_index'],
              'sets': exercise['sets'],
              'reps': exercise['reps'],
              'weight': exercise['weight'],
              'duration': exercise['duration'],
              'rest_time': exercise['rest_time'],
              'notes': exercise['notes'],
              'exercise_name': exerciseDetails['name'],
              'exercise_description': exerciseDetails['description'],
              'muscle_groups': exerciseDetails['muscle_groups'],
              'equipment': exerciseDetails['equipment'],
              'difficulty_level': exerciseDetails['difficulty_level'],
              'image_url': exerciseDetails['image_url'],
            };
          }).toList(),
        };
      }).toList(),
    };
  }
}
