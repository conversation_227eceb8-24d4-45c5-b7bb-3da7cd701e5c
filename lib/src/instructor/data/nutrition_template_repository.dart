import 'package:supabase_flutter/supabase_flutter.dart';
import '../domain/nutrition_template.dart';

class NutritionTemplateRepository {
  final SupabaseClient _supabase = Supabase.instance.client;

  // Get all nutrition templates for an instructor
  Future<List<NutritionTemplate>> getNutritionTemplates({
    required String instructorId,
    String? searchQuery,
    List<String>? tags,
    NutritionTemplateSortOption? sortOption,
  }) async {
    try {
      print('🔍 Fetching nutrition templates for instructor: $instructorId');
      
      var query = _supabase
          .from('nutrition_templates')
          .select('''
            *,
            nutrition_template_meals(
              *,
              nutrition_template_meal_items(*)
            )
          ''')
          .eq('instructor_id', instructorId);

      // Apply search filter
      if (searchQuery != null && searchQuery.isNotEmpty) {
        query = query.or('name.ilike.%$searchQuery%,description.ilike.%$searchQuery%');
      }

      // Apply tag filter
      if (tags != null && tags.isNotEmpty) {
        for (String tag in tags) {
          query = query.contains('tags', [tag]);
        }
      }

      // Apply sorting and execute query
      final List<Map<String, dynamic>> response;
      switch (sortOption) {
        case NutritionTemplateSortOption.latest:
          response = await query.order('created_at', ascending: false);
          break;
        case NutritionTemplateSortOption.oldest:
          response = await query.order('created_at', ascending: true);
          break;
        case NutritionTemplateSortOption.alphabetical:
          response = await query.order('name', ascending: true);
          break;
        default:
          response = await query.order('created_at', ascending: false);
      }
      print('✅ Fetched ${response.length} nutrition templates');

      return response.map((data) => _mapToNutritionTemplate(data)).toList();
    } catch (e) {
      print('❌ Error fetching nutrition templates: $e');
      rethrow;
    }
  }

  // Get a single nutrition template by ID
  Future<NutritionTemplate?> getNutritionTemplate(String templateId) async {
    try {
      print('🔍 Fetching nutrition template: $templateId');
      
      final response = await _supabase
          .from('nutrition_templates')
          .select('''
            *,
            nutrition_template_meals(
              *,
              nutrition_template_meal_items(*)
            )
          ''')
          .eq('id', templateId)
          .single();

      print('✅ Fetched nutrition template: ${response['name']}');
      return _mapToNutritionTemplate(response);
    } catch (e) {
      print('❌ Error fetching nutrition template: $e');
      return null;
    }
  }

  // Create a new nutrition template
  Future<NutritionTemplate> createNutritionTemplate(NutritionTemplate template) async {
    try {
      print('📝 Creating nutrition template: ${template.name}');

      final templateData = {
        'instructor_id': template.instructorId,
        'name': template.name,
        'description': template.description,
        'tags': template.tags,
        'macros': {
          'protein_percentage': template.macros.proteinPercentage,
          'carbs_percentage': template.macros.carbsPercentage,
          'fats_percentage': template.macros.fatsPercentage,
        },
        'created_at': DateTime.now().toIso8601String(),
        'updated_at': DateTime.now().toIso8601String(),
      };

      // Insert template
      final templateResponse = await _supabase
          .from('nutrition_templates')
          .insert(templateData)
          .select()
          .single();

      final templateId = templateResponse['id'];
      print('✅ Created nutrition template: ${templateResponse['name']} (ID: $templateId)');

      // Insert meals if any
      print('🔍 Template has ${template.meals.length} meals to create');
      if (template.meals.isNotEmpty) {
        await _createMealsForTemplate(templateId, template.meals);
      } else {
        print('⚠️ No meals to create for template');
      }

      // Fetch the complete template with meals and items
      return await getNutritionTemplate(templateId) ??
             _mapToNutritionTemplate(templateResponse);
    } catch (e) {
      print('❌ Error creating nutrition template: $e');
      rethrow;
    }
  }

  // Update a nutrition template
  Future<NutritionTemplate> updateNutritionTemplate(NutritionTemplate template) async {
    try {
      print('📝 Updating nutrition template: ${template.name}');

      final templateData = {
        'name': template.name,
        'description': template.description,
        'tags': template.tags,
        'macros': {
          'protein_percentage': template.macros.proteinPercentage,
          'carbs_percentage': template.macros.carbsPercentage,
          'fats_percentage': template.macros.fatsPercentage,
        },
        'updated_at': DateTime.now().toIso8601String(),
      };

      // Update template
      final templateResponse = await _supabase
          .from('nutrition_templates')
          .update(templateData)
          .eq('id', template.id)
          .select()
          .single();

      print('✅ Updated nutrition template: ${templateResponse['name']}');

      // Update meals
      await _updateMealsForTemplate(template.id, template.meals);

      // Fetch the complete template with meals and items
      return await getNutritionTemplate(template.id) ??
             _mapToNutritionTemplate(templateResponse);
    } catch (e) {
      print('❌ Error updating nutrition template: $e');
      rethrow;
    }
  }

  // Delete a nutrition template
  Future<void> deleteNutritionTemplate(String templateId) async {
    try {
      print('🗑️ Deleting nutrition template: $templateId');
      
      await _supabase
          .from('nutrition_templates')
          .delete()
          .eq('id', templateId);

      print('✅ Deleted nutrition template');
    } catch (e) {
      print('❌ Error deleting nutrition template: $e');
      rethrow;
    }
  }

  // Helper method to map database response to NutritionTemplate
  NutritionTemplate _mapToNutritionTemplate(Map<String, dynamic> data) {
    final macrosData = data['macros'] as Map<String, dynamic>? ?? {};

    return NutritionTemplate(
      id: data['id'],
      instructorId: data['instructor_id'],
      name: data['name'],
      description: data['description'],
      tags: List<String>.from(data['tags'] ?? []),
      macros: MacronutrientBreakdown(
        proteinPercentage: (macrosData['protein_percentage'] ?? 0.0).toDouble(),
        carbsPercentage: (macrosData['carbs_percentage'] ?? 0.0).toDouble(),
        fatsPercentage: (macrosData['fats_percentage'] ?? 0.0).toDouble(),
      ),
      createdAt: DateTime.parse(data['created_at']),
      updatedAt: DateTime.parse(data['updated_at']),
      meals: (data['nutrition_template_meals'] as List<dynamic>?)
              ?.map((mealData) => _mapToNutritionTemplateMeal(mealData))
              .toList() ??
          [],
    );
  }

  // Helper method to map meal data
  NutritionTemplateMeal _mapToNutritionTemplateMeal(Map<String, dynamic> data) {
    return NutritionTemplateMeal(
      id: data['id'],
      templateId: data['template_id'],
      name: data['name'],
      mealType: data['meal_type'],
      orderIndex: data['order_index'],
      description: data['description'],
      createdAt: DateTime.parse(data['created_at']),
      updatedAt: DateTime.parse(data['updated_at']),
      items: (data['nutrition_template_meal_items'] as List<dynamic>?)
              ?.map((itemData) => _mapToNutritionTemplateMealItem(itemData))
              .toList() ??
          [],
    );
  }

  // Helper method to map meal item data
  NutritionTemplateMealItem _mapToNutritionTemplateMealItem(Map<String, dynamic> data) {
    return NutritionTemplateMealItem(
      id: data['id'],
      mealId: data['meal_id'],
      foodName: data['food_name'],
      quantity: (data['quantity'] ?? 0.0).toDouble(),
      unit: data['unit'],
      notes: data['notes'],
      createdAt: DateTime.parse(data['created_at']),
      updatedAt: DateTime.parse(data['updated_at']),
      calories: data['calories']?.toDouble(),
      protein: data['protein']?.toDouble(),
      carbs: data['carbs']?.toDouble(),
      fats: data['fats']?.toDouble(),
    );
  }

  // Helper method to create meals for a template
  Future<void> _createMealsForTemplate(String templateId, List<NutritionTemplateMeal> meals) async {
    if (meals.isEmpty) return;

    print('📝 Creating ${meals.length} meals for template: $templateId');

    for (int i = 0; i < meals.length; i++) {
      final meal = meals[i];
      final mealData = {
        'template_id': templateId,
        'name': meal.name,
        'meal_type': meal.mealType,
        'order_index': i, // Use index for proper ordering
        'description': meal.description,
        'created_at': DateTime.now().toIso8601String(),
        'updated_at': DateTime.now().toIso8601String(),
      };

      final mealResponse = await _supabase
          .from('nutrition_template_meals')
          .insert(mealData)
          .select()
          .single();

      final mealId = mealResponse['id'];
      print('✅ Created meal: ${meal.name} (ID: $mealId)');

      // Create food items for this meal
      if (meal.items.isNotEmpty) {
        await _createFoodItemsForMeal(mealId, meal.items);
      }
    }
  }

  // Helper method to update meals for a template
  Future<void> _updateMealsForTemplate(String templateId, List<NutritionTemplateMeal> meals) async {
    print('📝 Updating meals for template: $templateId');

    // Delete existing meals and their items
    await _supabase
        .from('nutrition_template_meals')
        .delete()
        .eq('template_id', templateId);

    // Create new meals
    await _createMealsForTemplate(templateId, meals);
  }

  // Helper method to create food items for a meal
  Future<void> _createFoodItemsForMeal(String mealId, List<NutritionTemplateMealItem> items) async {
    if (items.isEmpty) return;

    print('📝 Creating ${items.length} food items for meal: $mealId');

    final itemsData = items.map((item) => {
      'meal_id': mealId,
      'food_name': item.foodName,
      'quantity': item.quantity,
      'unit': item.unit,
      'notes': item.notes,
      'calories': item.calories,
      'protein': item.protein,
      'carbs': item.carbs,
      'fats': item.fats,
      'created_at': DateTime.now().toIso8601String(),
      'updated_at': DateTime.now().toIso8601String(),
    }).toList();

    await _supabase
        .from('nutrition_template_meal_items')
        .insert(itemsData);

    print('✅ Created ${items.length} food items');
  }
}
