class WorkoutPlanTemplate {
  final String id;
  final String instructorId;
  final String name;
  final String? description;
  final List<String> tags;
  final String? notes;
  final bool isDraft;
  final DateTime createdAt;
  final DateTime updatedAt;
  final List<WorkoutTemplatePlan> plans;

  const WorkoutPlanTemplate({
    required this.id,
    required this.instructorId,
    required this.name,
    this.description,
    this.tags = const [],
    this.notes,
    this.isDraft = true,
    required this.createdAt,
    required this.updatedAt,
    this.plans = const [],
  });

  factory WorkoutPlanTemplate.fromJson(Map<String, dynamic> json) {
    return WorkoutPlanTemplate(
      id: json['id'] as String,
      instructorId: json['instructor_id'] as String,
      name: json['name'] as String,
      description: json['description'] as String?,
      tags: (json['tags'] as List<dynamic>?)?.cast<String>() ?? [],
      notes: json['notes'] as String?,
      isDraft: json['is_draft'] as bool? ?? true,
      createdAt: DateTime.parse(json['created_at'] as String),
      updatedAt: DateTime.parse(json['updated_at'] as String),
      plans: const [],
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'instructor_id': instructorId,
      'name': name,
      'description': description,
      'tags': tags,
      'notes': notes,
      'is_draft': isDraft,
      'created_at': createdAt.toIso8601String(),
      'updated_at': updatedAt.toIso8601String(),
    };
  }
}

class WorkoutTemplatePlan {
  final String id;
  final String templateId;
  final String name;
  final String? description;
  final int orderIndex;
  final DateTime createdAt;
  final DateTime updatedAt;
  final List<WorkoutTemplatePlanExercise> exercises;

  const WorkoutTemplatePlan({
    required this.id,
    required this.templateId,
    required this.name,
    this.description,
    this.orderIndex = 0,
    required this.createdAt,
    required this.updatedAt,
    this.exercises = const [],
  });

  factory WorkoutTemplatePlan.fromJson(Map<String, dynamic> json) {
    return WorkoutTemplatePlan(
      id: json['id'] as String,
      templateId: json['template_id'] as String,
      name: json['name'] as String,
      description: json['description'] as String?,
      orderIndex: json['order_index'] as int? ?? 0,
      createdAt: DateTime.parse(json['created_at'] as String),
      updatedAt: DateTime.parse(json['updated_at'] as String),
      exercises: const [],
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'template_id': templateId,
      'name': name,
      'description': description,
      'order_index': orderIndex,
      'created_at': createdAt.toIso8601String(),
      'updated_at': updatedAt.toIso8601String(),
    };
  }
}

class WorkoutTemplatePlanExercise {
  final String id;
  final String planId;
  final String exerciseId;
  final int orderIndex;
  final int? sets;
  final int? reps;
  final double? weight;
  final int? duration; // in seconds
  final int? restTime; // in seconds
  final String? notes;
  final DateTime createdAt;
  // Exercise details (joined from exercises table)
  final String? exerciseName;
  final String? exerciseDescription;
  final List<String>? muscleGroups;
  final List<String>? equipment;
  final int? difficultyLevel;
  final String? imageUrl;

  const WorkoutTemplatePlanExercise({
    required this.id,
    required this.planId,
    required this.exerciseId,
    this.orderIndex = 0,
    this.sets,
    this.reps,
    this.weight,
    this.duration,
    this.restTime,
    this.notes,
    required this.createdAt,
    this.exerciseName,
    this.exerciseDescription,
    this.muscleGroups,
    this.equipment,
    this.difficultyLevel,
    this.imageUrl,
  });

  factory WorkoutTemplatePlanExercise.fromJson(Map<String, dynamic> json) {
    return WorkoutTemplatePlanExercise(
      id: json['id'] as String,
      planId: json['plan_id'] as String,
      exerciseId: json['exercise_id'] as String,
      orderIndex: json['order_index'] as int? ?? 0,
      sets: json['sets'] as int?,
      reps: json['reps'] as int?,
      weight: (json['weight'] as num?)?.toDouble(),
      duration: json['duration'] as int?,
      restTime: json['rest_time'] as int?,
      notes: json['notes'] as String?,
      createdAt: DateTime.parse(json['created_at'] as String),
      exerciseName: json['exercise_name'] as String?,
      exerciseDescription: json['exercise_description'] as String?,
      muscleGroups: (json['muscle_groups'] as List<dynamic>?)?.cast<String>(),
      equipment: (json['equipment'] as List<dynamic>?)?.cast<String>(),
      difficultyLevel: json['difficulty_level'] as int?,
      imageUrl: json['image_url'] as String?,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'plan_id': planId,
      'exercise_id': exerciseId,
      'order_index': orderIndex,
      'sets': sets,
      'reps': reps,
      'weight': weight,
      'duration': duration,
      'rest_time': restTime,
      'notes': notes,
      'created_at': createdAt.toIso8601String(),
    };
  }
}

class Exercise {
  final String id;
  final String name;
  final String? description;
  final String? instructions;
  final String? categoryId;
  final List<String> muscleGroups;
  final List<String> equipment;
  final int? difficultyLevel;
  final String? videoUrl;
  final String? imageUrl;
  final double? caloriesPerMinute;
  final DateTime createdAt;
  final DateTime updatedAt;

  const Exercise({
    required this.id,
    required this.name,
    this.description,
    this.instructions,
    this.categoryId,
    this.muscleGroups = const [],
    this.equipment = const [],
    this.difficultyLevel,
    this.videoUrl,
    this.imageUrl,
    this.caloriesPerMinute,
    required this.createdAt,
    required this.updatedAt,
  });

  factory Exercise.fromJson(Map<String, dynamic> json) {
    return Exercise(
      id: json['id'] as String,
      name: json['name'] as String,
      description: json['description'] as String?,
      instructions: json['instructions'] as String?,
      categoryId: json['category_id'] as String?,
      muscleGroups: (json['muscle_groups'] as List<dynamic>?)?.cast<String>() ?? [],
      equipment: (json['equipment'] as List<dynamic>?)?.cast<String>() ?? [],
      difficultyLevel: json['difficulty_level'] as int?,
      videoUrl: json['video_url'] as String?,
      imageUrl: json['image_url'] as String?,
      caloriesPerMinute: (json['calories_per_minute'] as num?)?.toDouble(),
      createdAt: DateTime.parse(json['created_at'] as String),
      updatedAt: DateTime.parse(json['updated_at'] as String),
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'name': name,
      'description': description,
      'instructions': instructions,
      'category_id': categoryId,
      'muscle_groups': muscleGroups,
      'equipment': equipment,
      'difficulty_level': difficultyLevel,
      'video_url': videoUrl,
      'image_url': imageUrl,
      'calories_per_minute': caloriesPerMinute,
      'created_at': createdAt.toIso8601String(),
      'updated_at': updatedAt.toIso8601String(),
    };
  }
}

class WorkoutCategory {
  final String id;
  final String name;
  final String? description;
  final String? iconUrl;
  final DateTime createdAt;

  const WorkoutCategory({
    required this.id,
    required this.name,
    this.description,
    this.iconUrl,
    required this.createdAt,
  });

  factory WorkoutCategory.fromJson(Map<String, dynamic> json) {
    return WorkoutCategory(
      id: json['id'] as String,
      name: json['name'] as String,
      description: json['description'] as String?,
      iconUrl: json['icon_url'] as String?,
      createdAt: DateTime.parse(json['created_at'] as String),
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'name': name,
      'description': description,
      'icon_url': iconUrl,
      'created_at': createdAt.toIso8601String(),
    };
  }
}
