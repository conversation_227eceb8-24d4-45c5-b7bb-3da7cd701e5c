// Domain models for instructor features

/// Instructor dashboard data model
class InstructorDashboard {
  final InstructorProfile profile;
  final InstructorStats stats;
  final List<PriorityAlert> priorityAlerts;
  final TaskOverview taskOverview;
  final List<NewStudent> newStudents;
  final WeeklyProgress weeklyProgress;

  const InstructorDashboard({
    required this.profile,
    required this.stats,
    required this.priorityAlerts,
    required this.taskOverview,
    required this.newStudents,
    required this.weeklyProgress,
  });
}

/// Instructor profile information
class InstructorProfile {
  final String id;
  final String name;
  final String title;
  final String? avatarUrl;
  final String email;
  final int experienceYears;

  const InstructorProfile({
    required this.id,
    required this.name,
    required this.title,
    required this.avatarUrl,
    required this.email,
    required this.experienceYears,
  });
}

/// Instructor statistics
class InstructorStats {
  final int totalStudents;
  final int newStudentsToday;
  final int newStudentsChange;
  final double averageRating;
  final int totalReviews;

  const InstructorStats({
    required this.totalStudents,
    required this.newStudentsToday,
    required this.newStudentsChange,
    required this.averageRating,
    required this.totalReviews,
  });
}

/// Priority alert for instructor attention
class PriorityAlert {
  final String id;
  final String studentId;
  final String studentName;
  final String message;
  final AlertType type;
  final String actionLabel;
  final DateTime createdAt;

  const PriorityAlert({
    required this.id,
    required this.studentId,
    required this.studentName,
    required this.message,
    required this.type,
    required this.actionLabel,
    required this.createdAt,
  });
}

/// Alert types for priority alerts
enum AlertType {
  inactivity,
  workoutUpdate,
  nutritionUpdate,
  feedback,
  question,
  emergency;

  String get displayName {
    switch (this) {
      case AlertType.inactivity:
        return 'Inactivity';
      case AlertType.workoutUpdate:
        return 'Workout Update';
      case AlertType.nutritionUpdate:
        return 'Nutrition Update';
      case AlertType.feedback:
        return 'Feedback Required';
      case AlertType.question:
        return 'Student Question';
      case AlertType.emergency:
        return 'Emergency';
    }
  }
}

/// Task overview for instructor
class TaskOverview {
  final int workouts;
  final int cardio;
  final int nutrition;
  final int feedback;
  final int questions;
  final int supplements;

  const TaskOverview({
    required this.workouts,
    required this.cardio,
    required this.nutrition,
    required this.feedback,
    required this.questions,
    required this.supplements,
  });
}

/// New student information
class NewStudent {
  final String id;
  final String name;
  final String? avatarUrl;
  final DateTime joinedAt;
  final String programType;
  final bool hasCompletedProfile;

  const NewStudent({
    required this.id,
    required this.name,
    required this.avatarUrl,
    required this.joinedAt,
    required this.programType,
    required this.hasCompletedProfile,
  });
}

/// Weekly progress summary
class WeeklyProgress {
  final int totalCompletedTasks;
  final int missedWorkouts;
  final int inactiveStudents;
  final double completionRate;

  const WeeklyProgress({
    required this.totalCompletedTasks,
    required this.missedWorkouts,
    required this.inactiveStudents,
    required this.completionRate,
  });
}

/// Task type enumeration
enum TaskType {
  workout,
  cardio,
  nutrition,
  feedback,
  question,
  supplement;

  String get displayName {
    switch (this) {
      case TaskType.workout:
        return 'Workouts';
      case TaskType.cardio:
        return 'Cardio';
      case TaskType.nutrition:
        return 'Nutrition';
      case TaskType.feedback:
        return 'Feedback';
      case TaskType.question:
        return 'Questions';
      case TaskType.supplement:
        return 'Supplements';
    }
  }

  String get icon {
    switch (this) {
      case TaskType.workout:
        return '💪';
      case TaskType.cardio:
        return '🏃';
      case TaskType.nutrition:
        return '🥗';
      case TaskType.feedback:
        return '💬';
      case TaskType.question:
        return '❓';
      case TaskType.supplement:
        return '💊';
    }
  }
}
