// Domain models for student workout assignments

/// Student workout plan assignment
class StudentWorkoutAssignment {
  final String id;
  final String studentId;
  final String instructorId;
  final String? templateId; // null for custom plans
  final String planName;
  final String? description;
  final Map<String, dynamic>? planData; // JSON data containing the workout plan
  final DateTime assignedAt;
  final DateTime? completedAt;
  final bool isActive;
  final bool isCompleted;
  final DateTime createdAt;
  final DateTime updatedAt;

  const StudentWorkoutAssignment({
    required this.id,
    required this.studentId,
    required this.instructorId,
    this.templateId,
    required this.planName,
    this.description,
    this.planData,
    required this.assignedAt,
    this.completedAt,
    this.isActive = true,
    this.isCompleted = false,
    required this.createdAt,
    required this.updatedAt,
  });

  factory StudentWorkoutAssignment.fromJson(Map<String, dynamic> json) {
    return StudentWorkoutAssignment(
      id: json['id'] as String,
      studentId: json['student_id'] as String,
      instructorId: json['instructor_id'] as String,
      templateId: json['template_id'] as String?, // This might be null for custom plans
      planName: json['title'] as String,
      description: json['description'] as String?,
      planData: json['plan_data'] as Map<String, dynamic>?,
      assignedAt: json['assigned_at'] != null
          ? DateTime.parse(json['assigned_at'] as String)
          : DateTime.now(), // Default to now if not set
      completedAt: json['completed_at'] != null
          ? DateTime.parse(json['completed_at'] as String)
          : null,
      isActive: json['is_active'] as bool? ?? true,
      isCompleted: json['is_completed'] as bool? ?? false,
      createdAt: json['created_at'] != null
          ? DateTime.parse(json['created_at'] as String)
          : DateTime.now(), // Default to now if not set
      updatedAt: json['updated_at'] != null
          ? DateTime.parse(json['updated_at'] as String)
          : DateTime.now(), // Default to now if not set
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'student_id': studentId,
      'instructor_id': instructorId,
      'template_id': templateId,
      'title': planName,
      'description': description,
      'plan_data': planData,
      'assigned_at': assignedAt.toIso8601String(),
      'completed_at': completedAt?.toIso8601String(),
      'is_active': isActive,
      'is_completed': isCompleted,
      'created_at': createdAt.toIso8601String(),
      'updated_at': updatedAt.toIso8601String(),
    };
  }

  StudentWorkoutAssignment copyWith({
    String? id,
    String? studentId,
    String? instructorId,
    String? templateId,
    String? planName,
    String? description,
    Map<String, dynamic>? planData,
    DateTime? assignedAt,
    DateTime? completedAt,
    bool? isActive,
    bool? isCompleted,
    DateTime? createdAt,
    DateTime? updatedAt,
  }) {
    return StudentWorkoutAssignment(
      id: id ?? this.id,
      studentId: studentId ?? this.studentId,
      instructorId: instructorId ?? this.instructorId,
      templateId: templateId ?? this.templateId,
      planName: planName ?? this.planName,
      description: description ?? this.description,
      planData: planData ?? this.planData,
      assignedAt: assignedAt ?? this.assignedAt,
      completedAt: completedAt ?? this.completedAt,
      isActive: isActive ?? this.isActive,
      isCompleted: isCompleted ?? this.isCompleted,
      createdAt: createdAt ?? this.createdAt,
      updatedAt: updatedAt ?? this.updatedAt,
    );
  }

  /// Check if this assignment has plan data
  bool get hasPlanData => planData != null && planData!.isNotEmpty;

  /// Check if this assignment is pending (no plan data assigned yet)
  bool get isPending => !hasPlanData && isActive && !isCompleted;
}

/// Assignment creation request
class CreateWorkoutAssignmentRequest {
  final String studentId;
  final String instructorId;
  final String? templateId; // null for custom plans
  final String planName;
  final String? description;
  final Map<String, dynamic>? planData;

  const CreateWorkoutAssignmentRequest({
    required this.studentId,
    required this.instructorId,
    this.templateId,
    required this.planName,
    this.description,
    this.planData,
  });

  Map<String, dynamic> toJson() {
    final json = {
      'student_id': studentId,
      'instructor_id': instructorId,
      'title': planName,
      'description': description,
      'plan_data': planData,
      'assigned_at': DateTime.now().toIso8601String(),
      'is_active': true,
      'is_completed': false,
    };

    // Only add template_id if it's not null (for template-based assignments)
    // For custom plans, we don't include template_id
    if (templateId != null) {
      json['template_id'] = templateId;
    }

    return json;
  }
}
