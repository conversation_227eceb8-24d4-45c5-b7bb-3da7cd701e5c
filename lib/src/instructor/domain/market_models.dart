import 'package:flutter/material.dart';

/// Product category for FitGo Market
enum ProductCategory {
  protein,
  supplements,
  snacks,
  equipment,
  accessories,
}

extension ProductCategoryExtension on ProductCategory {
  String get displayName {
    switch (this) {
      case ProductCategory.protein:
        return 'Protein';
      case ProductCategory.supplements:
        return 'Supplements';
      case ProductCategory.snacks:
        return 'Healthy Snacks';
      case ProductCategory.equipment:
        return 'Equipment';
      case ProductCategory.accessories:
        return 'Accessories';
    }
  }

  IconData get icon {
    switch (this) {
      case ProductCategory.protein:
        return Icons.fitness_center;
      case ProductCategory.supplements:
        return Icons.medication;
      case ProductCategory.snacks:
        return Icons.restaurant;
      case ProductCategory.equipment:
        return Icons.sports_gymnastics;
      case ProductCategory.accessories:
        return Icons.watch;
    }
  }

  Color get color {
    switch (this) {
      case ProductCategory.protein:
        return const Color(0xFF3B82F6); // Blue
      case ProductCategory.supplements:
        return const Color(0xFF10B981); // Green
      case ProductCategory.snacks:
        return const Color(0xFFF59E0B); // Orange
      case ProductCategory.equipment:
        return const Color(0xFF8B5CF6); // Purple
      case ProductCategory.accessories:
        return const Color(0xFFEF4444); // Red
    }
  }
}

/// Market product model
class MarketProduct {
  final String id;
  final String name;
  final String description;
  final String imageUrl;
  final double price;
  final String currency;
  final ProductCategory category;
  final List<String> benefits;
  final double rating;
  final int reviewCount;
  final bool isPopular;
  final bool isNew;
  final String? discountText;

  const MarketProduct({
    required this.id,
    required this.name,
    required this.description,
    required this.imageUrl,
    required this.price,
    this.currency = 'TL',
    required this.category,
    this.benefits = const [],
    this.rating = 0.0,
    this.reviewCount = 0,
    this.isPopular = false,
    this.isNew = false,
    this.discountText,
  });

  /// Get formatted price string
  String get formattedPrice {
    return '${price.toStringAsFixed(0)} $currency';
  }

  /// Get formatted rating string
  String get formattedRating {
    return '${rating.toStringAsFixed(1)} (${reviewCount})';
  }
}

/// Explore section model for navigation
class ExploreSection {
  final String id;
  final String title;
  final String subtitle;
  final IconData icon;
  final Color color;
  final Color backgroundColor;
  final VoidCallback? onTap;

  const ExploreSection({
    required this.id,
    required this.title,
    required this.subtitle,
    required this.icon,
    required this.color,
    required this.backgroundColor,
    this.onTap,
  });
}

/// Mock data for FitGo Market products
class MockMarketData {
  static List<MarketProduct> getProducts() {
    return [
      const MarketProduct(
        id: '1',
        name: 'Premium Whey Protein',
        description: 'High-quality whey protein for muscle building',
        imageUrl: 'https://images.unsplash.com/photo-1593095948071-474c5cc2989d?w=400',
        price: 299,
        category: ProductCategory.protein,
        benefits: ['25g Protein', 'Fast Absorption', 'Great Taste'],
        rating: 4.8,
        reviewCount: 1250,
        isPopular: true,
      ),
      const MarketProduct(
        id: '2',
        name: 'BCAA Energy Boost',
        description: 'Essential amino acids for recovery',
        imageUrl: 'https://images.unsplash.com/photo-1544947950-fa07a98d237f?w=400',
        price: 149,
        category: ProductCategory.supplements,
        benefits: ['Muscle Recovery', 'Energy Boost', 'Zero Sugar'],
        rating: 4.6,
        reviewCount: 890,
        isNew: true,
      ),
      const MarketProduct(
        id: '3',
        name: 'Protein Bars Pack',
        description: 'Delicious high-protein snack bars',
        imageUrl: 'https://images.unsplash.com/photo-1571019613454-1cb2f99b2d8b?w=400',
        price: 89,
        category: ProductCategory.snacks,
        benefits: ['20g Protein', 'Low Carb', 'Natural Ingredients'],
        rating: 4.4,
        reviewCount: 567,
        discountText: '20% OFF',
      ),
      const MarketProduct(
        id: '4',
        name: 'Resistance Bands Set',
        description: 'Complete resistance training kit',
        imageUrl: 'https://images.unsplash.com/photo-1571019613454-1cb2f99b2d8b?w=400',
        price: 199,
        category: ProductCategory.equipment,
        benefits: ['5 Resistance Levels', 'Portable', 'Full Body Workout'],
        rating: 4.7,
        reviewCount: 432,
      ),
      const MarketProduct(
        id: '5',
        name: 'Smart Fitness Tracker',
        description: 'Track your workouts and health metrics',
        imageUrl: 'https://images.unsplash.com/photo-**********-1dfe5d97d256?w=400',
        price: 599,
        category: ProductCategory.accessories,
        benefits: ['Heart Rate Monitor', 'Sleep Tracking', '7-Day Battery'],
        rating: 4.5,
        reviewCount: 1100,
        isPopular: true,
      ),
      const MarketProduct(
        id: '6',
        name: 'Creatine Monohydrate',
        description: 'Pure creatine for strength and power',
        imageUrl: 'https://images.unsplash.com/photo-1594737625785-a6cbdabd333c?w=400',
        price: 119,
        category: ProductCategory.supplements,
        benefits: ['Increased Strength', 'Muscle Growth', 'Proven Formula'],
        rating: 4.9,
        reviewCount: 2100,
        isPopular: true,
      ),
    ];
  }

  static List<ProductCategory> getCategories() {
    return ProductCategory.values;
  }
}
