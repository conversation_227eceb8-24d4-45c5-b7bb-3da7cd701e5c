// Domain models for supplement management

import 'package:equatable/equatable.dart';

/// Supplement entity representing a supplement product
class Supplement extends Equatable {
  final String id;
  final String name;
  final String brand;
  final String description;
  final SupplementType type;
  final SupplementCategory category;
  final String? imageUrl;
  final List<String> benefits;
  final List<String> ingredients;
  final String? warnings;
  final bool isActive;
  final DateTime createdAt;
  final DateTime updatedAt;

  const Supplement({
    required this.id,
    required this.name,
    required this.brand,
    required this.description,
    required this.type,
    required this.category,
    this.imageUrl,
    required this.benefits,
    required this.ingredients,
    this.warnings,
    this.isActive = true,
    required this.createdAt,
    required this.updatedAt,
  });

  @override
  List<Object?> get props => [
        id,
        name,
        brand,
        description,
        type,
        category,
        imageUrl,
        benefits,
        ingredients,
        warnings,
        isActive,
        createdAt,
        updatedAt,
      ];

  factory Supplement.fromJson(Map<String, dynamic> json) {
    return Supplement(
      id: json['id'] as String,
      name: json['name'] as String,
      brand: json['brand'] as String,
      description: json['description'] as String,
      type: SupplementType.values.firstWhere(
        (e) => e.name == json['type'],
        orElse: () => SupplementType.other,
      ),
      category: SupplementCategory.values.firstWhere(
        (e) => e.name == json['category'],
        orElse: () => SupplementCategory.general,
      ),
      imageUrl: json['image_url'] as String?,
      benefits: (json['benefits'] as List<dynamic>?)?.cast<String>() ?? [],
      ingredients: (json['ingredients'] as List<dynamic>?)?.cast<String>() ?? [],
      warnings: json['warnings'] as String?,
      isActive: json['is_active'] as bool? ?? true,
      createdAt: json['created_at'] != null
          ? (json['created_at'] is String
              ? DateTime.parse(json['created_at'] as String)
              : DateTime.parse(json['created_at'].toString()))
          : DateTime.now(),
      updatedAt: json['updated_at'] != null
          ? (json['updated_at'] is String
              ? DateTime.parse(json['updated_at'] as String)
              : DateTime.parse(json['updated_at'].toString()))
          : DateTime.now(),
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'name': name,
      'brand': brand,
      'description': description,
      'type': type.name,
      'category': category.name,
      'image_url': imageUrl,
      'benefits': benefits,
      'ingredients': ingredients,
      'warnings': warnings,
      'is_active': isActive,
      'created_at': createdAt.toIso8601String(),
      'updated_at': updatedAt.toIso8601String(),
    };
  }
}

/// Student supplement assignment
class StudentSupplementAssignment extends Equatable {
  final String id;
  final String studentId;
  final String instructorId;
  final String supplementId;
  final Supplement? supplement; // Populated when fetched with join
  final int quantity;
  final String? notes;
  final SupplementAssignmentStatus status;
  final DateTime assignedAt;

  const StudentSupplementAssignment({
    required this.id,
    required this.studentId,
    required this.instructorId,
    required this.supplementId,
    this.supplement,
    required this.quantity,
    this.notes,
    required this.status,
    required this.assignedAt,
  });

  @override
  List<Object?> get props => [
        id,
        studentId,
        instructorId,
        supplementId,
        supplement,
        quantity,
        notes,
        status,
        assignedAt,
      ];

  factory StudentSupplementAssignment.fromJson(Map<String, dynamic> json) {
    return StudentSupplementAssignment(
      id: json['id'] as String,
      studentId: json['student_id'] as String,
      instructorId: json['instructor_id'] as String,
      supplementId: json['supplement_id'] as String,
      supplement: json['supplements'] != null
          ? Supplement.fromJson(json['supplements'] as Map<String, dynamic>)
          : null,
      quantity: json['quantity'] as int? ?? 1,
      notes: json['notes'] as String?,
      status: SupplementAssignmentStatus.values.firstWhere(
        (e) => e.name == json['status'],
        orElse: () => SupplementAssignmentStatus.active,
      ),
      assignedAt: json['assigned_at'] != null
          ? (json['assigned_at'] is String
              ? DateTime.parse(json['assigned_at'] as String)
              : DateTime.parse(json['assigned_at'].toString()))
          : DateTime.now(),
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'student_id': studentId,
      'instructor_id': instructorId,
      'supplement_id': supplementId,
      'quantity': quantity,
      'notes': notes,
      'status': status.name,
      'assigned_at': assignedAt.toIso8601String(),
    };
  }
}

/// Supplement dosage information
class SupplementDosage extends Equatable {
  final double amount;
  final DosageUnit unit;
  final int frequency; // times per day
  final String? specificInstructions;

  const SupplementDosage({
    required this.amount,
    required this.unit,
    required this.frequency,
    this.specificInstructions,
  });

  @override
  List<Object?> get props => [amount, unit, frequency, specificInstructions];

  factory SupplementDosage.fromJson(Map<String, dynamic> json) {
    return SupplementDosage(
      amount: (json['amount'] as num).toDouble(),
      unit: DosageUnit.values.firstWhere(
        (e) => e.name == json['unit'],
        orElse: () => DosageUnit.grams,
      ),
      frequency: json['frequency'] as int,
      specificInstructions: json['specific_instructions'] as String?,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'amount': amount,
      'unit': unit.name,
      'frequency': frequency,
      'specific_instructions': specificInstructions,
    };
  }

  String get displayText {
    final unitText = unit.displayName;
    return '$amount $unitText, $frequency times daily';
  }
}

/// Supplement schedule for specific times
class SupplementSchedule extends Equatable {
  final String timeOfDay; // morning, afternoon, evening, night
  final String? specificTime; // HH:mm format
  final SupplementTiming timing; // before_meal, after_meal, with_meal, empty_stomach
  final String? notes;

  const SupplementSchedule({
    required this.timeOfDay,
    this.specificTime,
    required this.timing,
    this.notes,
  });

  @override
  List<Object?> get props => [timeOfDay, specificTime, timing, notes];

  factory SupplementSchedule.fromJson(Map<String, dynamic> json) {
    return SupplementSchedule(
      timeOfDay: json['time_of_day'] as String,
      specificTime: json['specific_time'] as String?,
      timing: SupplementTiming.values.firstWhere(
        (e) => e.name == json['timing'],
        orElse: () => SupplementTiming.withMeal,
      ),
      notes: json['notes'] as String?,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'time_of_day': timeOfDay,
      'specific_time': specificTime,
      'timing': timing.name,
      'notes': notes,
    };
  }

  String get displayText {
    final timeText = specificTime ?? timeOfDay;
    return '$timeText - ${timing.displayName}';
  }
}

/// Supplement types
enum SupplementType {
  protein,
  creatine,
  vitamins,
  minerals,
  preworkout,
  postworkout,
  fatburner,
  bcaa,
  glutamine,
  omega3,
  multivitamin,
  other;

  String get displayName {
    switch (this) {
      case SupplementType.protein:
        return 'Protein Powder';
      case SupplementType.creatine:
        return 'Creatine';
      case SupplementType.vitamins:
        return 'Vitamins';
      case SupplementType.minerals:
        return 'Minerals';
      case SupplementType.preworkout:
        return 'Pre-Workout';
      case SupplementType.postworkout:
        return 'Post-Workout';
      case SupplementType.fatburner:
        return 'Fat Burner';
      case SupplementType.bcaa:
        return 'BCAA';
      case SupplementType.glutamine:
        return 'Glutamine';
      case SupplementType.omega3:
        return 'Omega-3';
      case SupplementType.multivitamin:
        return 'Multivitamin';
      case SupplementType.other:
        return 'Other';
    }
  }
}

/// Supplement categories
enum SupplementCategory {
  performance,
  recovery,
  health,
  weightLoss,
  muscleGain,
  general;

  String get displayName {
    switch (this) {
      case SupplementCategory.performance:
        return 'Performance';
      case SupplementCategory.recovery:
        return 'Recovery';
      case SupplementCategory.health:
        return 'Health & Wellness';
      case SupplementCategory.weightLoss:
        return 'Weight Loss';
      case SupplementCategory.muscleGain:
        return 'Muscle Gain';
      case SupplementCategory.general:
        return 'General';
    }
  }
}

/// Dosage units
enum DosageUnit {
  grams,
  milligrams,
  capsules,
  tablets,
  scoops,
  drops,
  milliliters;

  String get displayName {
    switch (this) {
      case DosageUnit.grams:
        return 'g';
      case DosageUnit.milligrams:
        return 'mg';
      case DosageUnit.capsules:
        return 'capsules';
      case DosageUnit.tablets:
        return 'tablets';
      case DosageUnit.scoops:
        return 'scoops';
      case DosageUnit.drops:
        return 'drops';
      case DosageUnit.milliliters:
        return 'ml';
    }
  }
}

/// Supplement timing relative to meals
enum SupplementTiming {
  beforeMeal,
  withMeal,
  afterMeal,
  emptyStomach,
  anytime;

  String get displayName {
    switch (this) {
      case SupplementTiming.beforeMeal:
        return 'Before Meal';
      case SupplementTiming.withMeal:
        return 'With Meal';
      case SupplementTiming.afterMeal:
        return 'After Meal';
      case SupplementTiming.emptyStomach:
        return 'Empty Stomach';
      case SupplementTiming.anytime:
        return 'Anytime';
    }
  }
}

/// Assignment status
enum SupplementAssignmentStatus {
  active,
  paused,
  completed,
  discontinued;

  String get displayName {
    switch (this) {
      case SupplementAssignmentStatus.active:
        return 'Active';
      case SupplementAssignmentStatus.paused:
        return 'Paused';
      case SupplementAssignmentStatus.completed:
        return 'Completed';
      case SupplementAssignmentStatus.discontinued:
        return 'Discontinued';
    }
  }
}
