// Domain models for instructor profile features

/// Complete instructor profile data
class InstructorProfileData {
  final InstructorBasicInfo basicInfo;
  final List<WorkExperience> workHistory;
  final List<Certification> certifications;
  final List<ClientReview> reviews;
  final List<FAQ> faqs;
  final SubscriptionInfo? subscriptionInfo;

  const InstructorProfileData({
    required this.basicInfo,
    required this.workHistory,
    required this.certifications,
    required this.reviews,
    required this.faqs,
    this.subscriptionInfo,
  });
}

/// Basic instructor information
class InstructorBasicInfo {
  final String id;
  final String name;
  final String title;
  final String? bio;
  final String? profilePictureUrl;
  final double averageRating;
  final int totalReviews;
  final int experienceYears;
  final int totalStudents;
  final String? primaryCertification;
  final bool canEdit;

  // Capacity management
  final int maxStudents;
  final int currentStudents;

  const InstructorBasicInfo({
    required this.id,
    required this.name,
    required this.title,
    this.bio,
    this.profilePictureUrl,
    required this.averageRating,
    required this.totalReviews,
    required this.experienceYears,
    required this.totalStudents,
    this.primaryCertification,
    this.canEdit = true,
    this.maxStudents = 5,
    this.currentStudents = 0,
  });

  /// Check if instructor has available capacity
  bool get hasAvailableCapacity => currentStudents < maxStudents;

  /// Get remaining capacity
  int get remainingCapacity => maxStudents - currentStudents;

  /// Get capacity percentage (0.0 to 1.0)
  double get capacityPercentage =>
      maxStudents > 0 ? currentStudents / maxStudents : 0.0;

  /// Get capacity status text
  String get capacityStatus => '$currentStudents/$maxStudents students';

  /// Check if capacity is nearly full (>80%)
  bool get isNearlyFull => capacityPercentage > 0.8;

  /// Check if capacity is full
  bool get isFull => currentStudents >= maxStudents;

  /// Create a copy with updated values
  InstructorBasicInfo copyWith({
    String? id,
    String? name,
    String? title,
    String? bio,
    String? profilePictureUrl,
    double? averageRating,
    int? totalReviews,
    int? experienceYears,
    int? totalStudents,
    String? primaryCertification,
    bool? canEdit,
    int? maxStudents,
    int? currentStudents,
  }) {
    return InstructorBasicInfo(
      id: id ?? this.id,
      name: name ?? this.name,
      title: title ?? this.title,
      bio: bio ?? this.bio,
      profilePictureUrl: profilePictureUrl ?? this.profilePictureUrl,
      averageRating: averageRating ?? this.averageRating,
      totalReviews: totalReviews ?? this.totalReviews,
      experienceYears: experienceYears ?? this.experienceYears,
      totalStudents: totalStudents ?? this.totalStudents,
      primaryCertification: primaryCertification ?? this.primaryCertification,
      canEdit: canEdit ?? this.canEdit,
      maxStudents: maxStudents ?? this.maxStudents,
      currentStudents: currentStudents ?? this.currentStudents,
    );
  }
}

/// Work experience entry
class WorkExperience {
  final String id;
  final String companyName;
  final String role;
  final String startYear;
  final String? endYear;
  final String description;
  final bool isCurrent;

  const WorkExperience({
    required this.id,
    required this.companyName,
    required this.role,
    required this.startYear,
    this.endYear,
    required this.description,
    this.isCurrent = false,
  });

  String get duration {
    final end = endYear ?? 'Present';
    return '$startYear - $end';
  }
}

/// Certification information
class Certification {
  final String id;
  final String name;
  final String issuer;
  final String year;
  final String? externalLink;
  final bool isVerified;

  const Certification({
    required this.id,
    required this.name,
    required this.issuer,
    required this.year,
    this.externalLink,
    this.isVerified = false,
  });
}

/// Client review
class ClientReview {
  final String id;
  final String clientName;
  final String? clientAvatarUrl;
  final double rating;
  final String comment;
  final DateTime createdAt;
  final bool canModerate;

  const ClientReview({
    required this.id,
    required this.clientName,
    this.clientAvatarUrl,
    required this.rating,
    required this.comment,
    required this.createdAt,
    this.canModerate = false,
  });
}

/// Frequently Asked Question
class FAQ {
  final String id;
  final String question;
  final String answer;
  final int order;
  final bool isExpanded;

  const FAQ({
    required this.id,
    required this.question,
    required this.answer,
    this.order = 0,
    this.isExpanded = false,
  });

  FAQ copyWith({
    String? id,
    String? question,
    String? answer,
    int? order,
    bool? isExpanded,
  }) {
    return FAQ(
      id: id ?? this.id,
      question: question ?? this.question,
      answer: answer ?? this.answer,
      order: order ?? this.order,
      isExpanded: isExpanded ?? this.isExpanded,
    );
  }
}

/// Subscription information
class SubscriptionInfo {
  final String planName;
  final String status;
  final DateTime? expiresAt;
  final bool autoRenew;
  final double monthlyPrice;

  const SubscriptionInfo({
    required this.planName,
    required this.status,
    this.expiresAt,
    this.autoRenew = false,
    required this.monthlyPrice,
  });
}

/// Badge information for header
class InstructorBadge {
  final String label;
  final String value;
  final String icon;
  final BadgeType type;

  const InstructorBadge({
    required this.label,
    required this.value,
    required this.icon,
    required this.type,
  });
}

/// Badge types for styling
enum BadgeType {
  experience,
  students,
  certification,
  rating;

  String get color {
    switch (this) {
      case BadgeType.experience:
        return '#10B981'; // Green
      case BadgeType.students:
        return '#3B82F6'; // Blue
      case BadgeType.certification:
        return '#F59E0B'; // Amber
      case BadgeType.rating:
        return '#FACC15'; // Gold
    }
  }
}

/// Profile section types
enum ProfileSection {
  header,
  workHistory,
  certifications,
  reviews,
  faqs,
  subscription;

  String get title {
    switch (this) {
      case ProfileSection.header:
        return '';
      case ProfileSection.workHistory:
        return 'Work History';
      case ProfileSection.certifications:
        return 'Certifications';
      case ProfileSection.reviews:
        return 'Client Reviews';
      case ProfileSection.faqs:
        return 'Frequently Asked Questions';
      case ProfileSection.subscription:
        return 'Subscription';
    }
  }
}

/// Edit actions for profile sections
enum ProfileEditAction {
  editBasicInfo,
  addWorkExperience,
  editWorkExperience,
  deleteWorkExperience,
  addCertification,
  editCertification,
  deleteCertification,
  moderateReview,
  deleteReview,
  addFAQ,
  editFAQ,
  deleteFAQ,
  manageSubscription,
}
