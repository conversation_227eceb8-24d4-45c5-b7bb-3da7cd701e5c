/// Capacity upgrade package options
class CapacityUpgradePackage {
  final String id;
  final String name;
  final String description;
  final int additionalCapacity;
  final double price;
  final String duration; // "monthly", "yearly", "lifetime"
  final List<String> features;
  final bool isPopular;
  final String? discountText;
  final double? originalPrice;

  const CapacityUpgradePackage({
    required this.id,
    required this.name,
    required this.description,
    required this.additionalCapacity,
    required this.price,
    required this.duration,
    required this.features,
    this.isPopular = false,
    this.discountText,
    this.originalPrice,
  });

  bool get hasDiscount => originalPrice != null && originalPrice! > price;
  double get discountPercentage => hasDiscount ? ((originalPrice! - price) / originalPrice! * 100) : 0;
  String get pricePerStudent => (price / additionalCapacity).toStringAsFixed(2);
}

/// Current capacity status
class InstructorCapacityStatus {
  final int currentCapacity;
  final int maxCapacity;
  final int usedCapacity;
  final double utilizationPercentage;
  final bool canUpgrade;
  final String? upgradeRestriction;
  final DateTime? lastUpgradeDate;
  final String? currentPlan;

  const InstructorCapacityStatus({
    required this.currentCapacity,
    required this.maxCapacity,
    required this.usedCapacity,
    required this.utilizationPercentage,
    required this.canUpgrade,
    this.upgradeRestriction,
    this.lastUpgradeDate,
    this.currentPlan,
  });

  int get availableCapacity => maxCapacity - usedCapacity;
  bool get isNearCapacity => utilizationPercentage > 80;
  bool get isAtCapacity => usedCapacity >= maxCapacity;
  
  String get statusText {
    if (isAtCapacity) return 'Kapasite Dolu';
    if (isNearCapacity) return 'Kapasiteye Yakın';
    return 'Kapasite Uygun';
  }
  
  String get statusDescription {
    if (isAtCapacity) return 'Yeni öğrenci kabul edemezsiniz. Kapasiteyi arttırın.';
    if (isNearCapacity) return 'Kapasiteye yaklaşıyorsunuz. Yükseltme düşünün.';
    return 'Daha fazla öğrenci kabul edebilirsiniz.';
  }
}

/// Capacity upgrade transaction
class CapacityUpgradeTransaction {
  final String id;
  final String instructorId;
  final String packageId;
  final String packageName;
  final int additionalCapacity;
  final double amount;
  final String status; // "pending", "completed", "failed"
  final DateTime createdAt;
  final DateTime? completedAt;
  final String? paymentMethod;
  final String? transactionId;

  const CapacityUpgradeTransaction({
    required this.id,
    required this.instructorId,
    required this.packageId,
    required this.packageName,
    required this.additionalCapacity,
    required this.amount,
    required this.status,
    required this.createdAt,
    this.completedAt,
    this.paymentMethod,
    this.transactionId,
  });

  bool get isPending => status == 'pending';
  bool get isCompleted => status == 'completed';
  bool get isFailed => status == 'failed';
}

/// Predefined capacity upgrade packages
class CapacityUpgradePackages {
  static const List<CapacityUpgradePackage> packages = [
    CapacityUpgradePackage(
      id: 'starter_10',
      name: 'Starter Boost',
      description: '10 ek öğrenci kapasitesi',
      additionalCapacity: 10,
      price: 99.99,
      duration: 'monthly',
      features: [
        '10 ek öğrenci kapasitesi',
        'Aylık yenileme',
        'Temel destek',
        'İstatistik raporları',
      ],
    ),
    
    CapacityUpgradePackage(
      id: 'professional_25',
      name: 'Professional',
      description: '25 ek öğrenci kapasitesi',
      additionalCapacity: 25,
      price: 199.99,
      originalPrice: 249.99,
      duration: 'monthly',
      features: [
        '25 ek öğrenci kapasitesi',
        'Aylık yenileme',
        'Öncelikli destek',
        'Gelişmiş istatistikler',
        'Özel raporlar',
        'Pazarlama araçları',
      ],
      isPopular: true,
      discountText: '20% İndirim',
    ),
    
    CapacityUpgradePackage(
      id: 'enterprise_50',
      name: 'Enterprise',
      description: '50 ek öğrenci kapasitesi',
      additionalCapacity: 50,
      price: 349.99,
      originalPrice: 499.99,
      duration: 'monthly',
      features: [
        '50 ek öğrenci kapasitesi',
        'Aylık yenileme',
        '7/24 premium destek',
        'Tüm premium özellikler',
        'API erişimi',
        'Özel entegrasyonlar',
        'Kişisel hesap yöneticisi',
      ],
      discountText: '30% İndirim',
    ),
    
    CapacityUpgradePackage(
      id: 'unlimited_yearly',
      name: 'Unlimited Pro',
      description: 'Sınırsız öğrenci kapasitesi',
      additionalCapacity: 1000, // Practically unlimited
      price: 2999.99,
      originalPrice: 4999.99,
      duration: 'yearly',
      features: [
        'Sınırsız öğrenci kapasitesi',
        'Yıllık ödeme',
        'Tüm premium özellikler',
        'Özel branding',
        'Beyaz etiket çözümü',
        'Özel geliştirme desteği',
      ],
      discountText: '40% İndirim - En Popüler',
    ),
  ];

  static CapacityUpgradePackage? getPackageById(String id) {
    try {
      return packages.firstWhere((package) => package.id == id);
    } catch (e) {
      return null;
    }
  }

  static List<CapacityUpgradePackage> getRecommendedPackages(int currentUsage) {
    // Recommend packages based on current usage
    if (currentUsage < 30) {
      return packages.where((p) => p.additionalCapacity <= 25).toList();
    } else if (currentUsage < 60) {
      return packages.where((p) => p.additionalCapacity <= 50).toList();
    } else {
      return packages;
    }
  }
}

/// Capacity upgrade result
class CapacityUpgradeResult {
  final bool success;
  final String? error;
  final String? transactionId;
  final int? newCapacity;

  const CapacityUpgradeResult({
    required this.success,
    this.error,
    this.transactionId,
    this.newCapacity,
  });

  factory CapacityUpgradeResult.success({
    required String transactionId,
    required int newCapacity,
  }) {
    return CapacityUpgradeResult(
      success: true,
      transactionId: transactionId,
      newCapacity: newCapacity,
    );
  }

  factory CapacityUpgradeResult.failure(String error) {
    return CapacityUpgradeResult(
      success: false,
      error: error,
    );
  }
}
