class NutritionTemplate {
  final String id;
  final String instructorId;
  final String name;
  final String? description;
  final List<String> tags;
  final MacronutrientBreakdown macros;
  final DateTime createdAt;
  final DateTime updatedAt;
  final List<NutritionTemplateMeal> meals;

  const NutritionTemplate({
    required this.id,
    required this.instructorId,
    required this.name,
    this.description,
    this.tags = const [],
    required this.macros,
    required this.createdAt,
    required this.updatedAt,
    this.meals = const [],
  });

  factory NutritionTemplate.fromJson(Map<String, dynamic> json) {
    return NutritionTemplate(
      id: json['id'] as String,
      instructorId: json['instructor_id'] as String,
      name: json['name'] as String,
      description: json['description'] as String?,
      tags: List<String>.from(json['tags'] ?? []),
      macros: MacronutrientBreakdown.fromJson(json['macros'] as Map<String, dynamic>),
      createdAt: DateTime.parse(json['created_at'] as String),
      updatedAt: DateTime.parse(json['updated_at'] as String),
      meals: const [], // TODO: Parse meals if needed
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'instructor_id': instructorId,
      'name': name,
      'description': description,
      'tags': tags,
      'macros': macros.toJson(),
      'created_at': createdAt.toIso8601String(),
      'updated_at': updatedAt.toIso8601String(),
    };
  }
}

class MacronutrientBreakdown {
  final double proteinPercentage;
  final double carbsPercentage;
  final double fatsPercentage;

  const MacronutrientBreakdown({
    required this.proteinPercentage,
    required this.carbsPercentage,
    required this.fatsPercentage,
  });

  factory MacronutrientBreakdown.fromJson(Map<String, dynamic> json) {
    return MacronutrientBreakdown(
      proteinPercentage: (json['proteinPercentage'] ?? json['protein_percentage'] ?? 0.0).toDouble(),
      carbsPercentage: (json['carbsPercentage'] ?? json['carbs_percentage'] ?? 0.0).toDouble(),
      fatsPercentage: (json['fatsPercentage'] ?? json['fats_percentage'] ?? 0.0).toDouble(),
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'proteinPercentage': proteinPercentage,
      'carbsPercentage': carbsPercentage,
      'fatsPercentage': fatsPercentage,
    };
  }
}

class NutritionTemplateMeal {
  final String id;
  final String templateId;
  final String name;
  final String mealType; // breakfast, lunch, dinner, snack
  final int orderIndex;
  final String? description;
  final DateTime createdAt;
  final DateTime updatedAt;
  final List<NutritionTemplateMealItem> items;

  const NutritionTemplateMeal({
    required this.id,
    required this.templateId,
    required this.name,
    required this.mealType,
    required this.orderIndex,
    this.description,
    required this.createdAt,
    required this.updatedAt,
    this.items = const [],
  });

  factory NutritionTemplateMeal.fromJson(Map<String, dynamic> json) {
    return NutritionTemplateMeal(
      id: json['id'] as String,
      templateId: json['template_id'] as String,
      name: json['name'] as String,
      mealType: json['meal_type'] as String,
      orderIndex: json['order_index'] as int,
      description: json['description'] as String?,
      createdAt: DateTime.parse(json['created_at'] as String),
      updatedAt: DateTime.parse(json['updated_at'] as String),
      items: const [], // TODO: Parse items if needed
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'template_id': templateId,
      'name': name,
      'meal_type': mealType,
      'order_index': orderIndex,
      'description': description,
      'created_at': createdAt.toIso8601String(),
      'updated_at': updatedAt.toIso8601String(),
    };
  }
}

class NutritionTemplateMealItem {
  final String id;
  final String mealId;
  final String foodName;
  final double quantity;
  final String unit; // grams, cups, pieces, etc.
  final String? notes;
  final DateTime createdAt;
  final DateTime updatedAt;
  // Nutritional info
  final double? calories;
  final double? protein;
  final double? carbs;
  final double? fats;

  const NutritionTemplateMealItem({
    required this.id,
    required this.mealId,
    required this.foodName,
    required this.quantity,
    required this.unit,
    this.notes,
    required this.createdAt,
    required this.updatedAt,
    this.calories,
    this.protein,
    this.carbs,
    this.fats,
  });

  factory NutritionTemplateMealItem.fromJson(Map<String, dynamic> json) {
    return NutritionTemplateMealItem(
      id: json['id'] as String,
      mealId: json['meal_id'] as String,
      foodName: json['food_name'] as String,
      quantity: (json['quantity'] as num).toDouble(),
      unit: json['unit'] as String,
      notes: json['notes'] as String?,
      createdAt: DateTime.parse(json['created_at'] as String),
      updatedAt: DateTime.parse(json['updated_at'] as String),
      calories: (json['calories'] as num?)?.toDouble(),
      protein: (json['protein'] as num?)?.toDouble(),
      carbs: (json['carbs'] as num?)?.toDouble(),
      fats: (json['fats'] as num?)?.toDouble(),
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'meal_id': mealId,
      'food_name': foodName,
      'quantity': quantity,
      'unit': unit,
      'notes': notes,
      'created_at': createdAt.toIso8601String(),
      'updated_at': updatedAt.toIso8601String(),
      'calories': calories,
      'protein': protein,
      'carbs': carbs,
      'fats': fats,
    };
  }
}

// Available nutrition tags
class NutritionTags {
  static const List<String> all = [
    'Vegan',
    'Vegetarian',
    'Keto',
    'High Protein',
    'Low Carb',
    'Weight Loss',
    'Bulking',
    'Cutting',
    'Gluten Free',
    'Dairy Free',
    'Paleo',
    'Mediterranean',
    'Intermittent Fasting',
    'Balanced',
  ];
}

// Sorting options
enum NutritionTemplateSortOption {
  latest,
  oldest,
  alphabetical,
}

extension NutritionTemplateSortOptionExtension on NutritionTemplateSortOption {
  String get displayName {
    switch (this) {
      case NutritionTemplateSortOption.latest:
        return 'Latest';
      case NutritionTemplateSortOption.oldest:
        return 'Oldest';
      case NutritionTemplateSortOption.alphabetical:
        return 'Alphabetical';
    }
  }
}
