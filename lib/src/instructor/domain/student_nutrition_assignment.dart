import 'nutrition_template.dart';

/// Student nutrition assignment model
class StudentNutritionAssignment {
  final String id;
  final String studentId;
  final String instructorId;
  final String? templateId; // null for custom plans
  final String planName;
  final String? description;
  final String? notes; // Trainer's notes
  final List<StudentNutritionMeal> meals;
  final MacronutrientBreakdown macros;
  final DateTime assignedAt;
  final DateTime? startDate;
  final DateTime? endDate;
  final bool isActive;
  final DateTime createdAt;
  final DateTime updatedAt;

  const StudentNutritionAssignment({
    required this.id,
    required this.studentId,
    required this.instructorId,
    this.templateId,
    required this.planName,
    this.description,
    this.notes,
    required this.meals,
    required this.macros,
    required this.assignedAt,
    this.startDate,
    this.endDate,
    this.isActive = true,
    required this.createdAt,
    required this.updatedAt,
  });

  factory StudentNutritionAssignment.fromJson(Map<String, dynamic> json) {
    final macrosData = json['macros'] as Map<String, dynamic>? ?? {};
    
    return StudentNutritionAssignment(
      id: json['id'] as String,
      studentId: json['student_id'] as String,
      instructorId: json['instructor_id'] as String,
      templateId: json['template_id'] as String?,
      planName: json['plan_name'] as String,
      description: json['description'] as String?,
      notes: json['notes'] as String?,
      meals: (json['meals'] as List<dynamic>?)
              ?.map((mealData) => StudentNutritionMeal.fromJson(mealData))
              .toList() ??
          [],
      macros: MacronutrientBreakdown(
        proteinPercentage: (macrosData['protein_percentage'] ?? 0.0).toDouble(),
        carbsPercentage: (macrosData['carbs_percentage'] ?? 0.0).toDouble(),
        fatsPercentage: (macrosData['fats_percentage'] ?? 0.0).toDouble(),
      ),
      assignedAt: DateTime.parse(json['assigned_at'] as String),
      startDate: json['start_date'] != null ? DateTime.parse(json['start_date'] as String) : null,
      endDate: json['end_date'] != null ? DateTime.parse(json['end_date'] as String) : null,
      isActive: json['is_active'] as bool? ?? true,
      createdAt: DateTime.parse(json['created_at'] as String),
      updatedAt: DateTime.parse(json['updated_at'] as String),
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'student_id': studentId,
      'instructor_id': instructorId,
      'template_id': templateId,
      'plan_name': planName,
      'description': description,
      'notes': notes,
      'meals': meals.map((meal) => meal.toJson()).toList(),
      'macros': {
        'protein_percentage': macros.proteinPercentage,
        'carbs_percentage': macros.carbsPercentage,
        'fats_percentage': macros.fatsPercentage,
      },
      'assigned_at': assignedAt.toIso8601String(),
      'start_date': startDate?.toIso8601String(),
      'end_date': endDate?.toIso8601String(),
      'is_active': isActive,
      'created_at': createdAt.toIso8601String(),
      'updated_at': updatedAt.toIso8601String(),
    };
  }

  /// Create assignment from template
  factory StudentNutritionAssignment.fromTemplate({
    required String studentId,
    required String instructorId,
    required NutritionTemplate template,
    String? notes,
  }) {
    return StudentNutritionAssignment(
      id: '',
      studentId: studentId,
      instructorId: instructorId,
      templateId: template.id,
      planName: template.name,
      description: template.description,
      notes: notes,
      meals: template.meals
          .map((templateMeal) => StudentNutritionMeal.fromTemplateMeal(templateMeal))
          .toList(),
      macros: template.macros,
      assignedAt: DateTime.now(),
      createdAt: DateTime.now(),
      updatedAt: DateTime.now(),
    );
  }

  /// Get total calories for the plan
  double get totalCalories {
    return meals.fold(0.0, (sum, meal) => sum + meal.totalCalories);
  }

  /// Get total protein for the plan
  double get totalProtein {
    return meals.fold(0.0, (sum, meal) => sum + meal.totalProtein);
  }

  /// Get total carbs for the plan
  double get totalCarbs {
    return meals.fold(0.0, (sum, meal) => sum + meal.totalCarbs);
  }

  /// Get total fats for the plan
  double get totalFats {
    return meals.fold(0.0, (sum, meal) => sum + meal.totalFats);
  }
}

/// Student nutrition meal model
class StudentNutritionMeal {
  final String id;
  final String assignmentId;
  final String name;
  final String mealType; // breakfast, lunch, dinner, snack
  final int orderIndex;
  final String? description;
  final List<StudentNutritionMealItem> items;
  final DateTime createdAt;
  final DateTime updatedAt;

  const StudentNutritionMeal({
    required this.id,
    required this.assignmentId,
    required this.name,
    required this.mealType,
    required this.orderIndex,
    this.description,
    required this.items,
    required this.createdAt,
    required this.updatedAt,
  });

  factory StudentNutritionMeal.fromJson(Map<String, dynamic> json) {
    return StudentNutritionMeal(
      id: json['id'] as String,
      assignmentId: json['assignment_id'] as String,
      name: json['name'] as String,
      mealType: json['meal_type'] as String,
      orderIndex: json['order_index'] as int,
      description: json['description'] as String?,
      items: (json['items'] as List<dynamic>?)
              ?.map((itemData) => StudentNutritionMealItem.fromJson(itemData))
              .toList() ??
          [],
      createdAt: DateTime.parse(json['created_at'] as String),
      updatedAt: DateTime.parse(json['updated_at'] as String),
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'assignment_id': assignmentId,
      'name': name,
      'meal_type': mealType,
      'order_index': orderIndex,
      'description': description,
      'items': items.map((item) => item.toJson()).toList(),
      'created_at': createdAt.toIso8601String(),
      'updated_at': updatedAt.toIso8601String(),
    };
  }

  /// Create meal from template meal
  factory StudentNutritionMeal.fromTemplateMeal(NutritionTemplateMeal templateMeal) {
    return StudentNutritionMeal(
      id: '',
      assignmentId: '',
      name: templateMeal.name,
      mealType: templateMeal.mealType,
      orderIndex: templateMeal.orderIndex,
      description: templateMeal.description,
      items: templateMeal.items
          .map((templateItem) => StudentNutritionMealItem.fromTemplateItem(templateItem))
          .toList(),
      createdAt: DateTime.now(),
      updatedAt: DateTime.now(),
    );
  }

  /// Get total calories for this meal
  double get totalCalories {
    return items.fold(0.0, (sum, item) => sum + (item.calories ?? 0.0));
  }

  /// Get total protein for this meal
  double get totalProtein {
    return items.fold(0.0, (sum, item) => sum + (item.protein ?? 0.0));
  }

  /// Get total carbs for this meal
  double get totalCarbs {
    return items.fold(0.0, (sum, item) => sum + (item.carbs ?? 0.0));
  }

  /// Get total fats for this meal
  double get totalFats {
    return items.fold(0.0, (sum, item) => sum + (item.fats ?? 0.0));
  }
}

/// Student nutrition meal item model
class StudentNutritionMealItem {
  final String id;
  final String mealId;
  final String foodName;
  final double quantity;
  final String unit; // grams, cups, pieces, etc.
  final String? notes;
  final DateTime createdAt;
  final DateTime updatedAt;
  // Nutritional info
  final double? calories;
  final double? protein;
  final double? carbs;
  final double? fats;

  const StudentNutritionMealItem({
    required this.id,
    required this.mealId,
    required this.foodName,
    required this.quantity,
    required this.unit,
    this.notes,
    required this.createdAt,
    required this.updatedAt,
    this.calories,
    this.protein,
    this.carbs,
    this.fats,
  });

  factory StudentNutritionMealItem.fromJson(Map<String, dynamic> json) {
    return StudentNutritionMealItem(
      id: json['id'] as String,
      mealId: json['meal_id'] as String,
      foodName: json['food_name'] as String,
      quantity: (json['quantity'] as num).toDouble(),
      unit: json['unit'] as String,
      notes: json['notes'] as String?,
      createdAt: DateTime.parse(json['created_at'] as String),
      updatedAt: DateTime.parse(json['updated_at'] as String),
      calories: (json['calories'] as num?)?.toDouble(),
      protein: (json['protein'] as num?)?.toDouble(),
      carbs: (json['carbs'] as num?)?.toDouble(),
      fats: (json['fats'] as num?)?.toDouble(),
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'meal_id': mealId,
      'food_name': foodName,
      'quantity': quantity,
      'unit': unit,
      'notes': notes,
      'created_at': createdAt.toIso8601String(),
      'updated_at': updatedAt.toIso8601String(),
      'calories': calories,
      'protein': protein,
      'carbs': carbs,
      'fats': fats,
    };
  }

  /// Create item from template item
  factory StudentNutritionMealItem.fromTemplateItem(NutritionTemplateMealItem templateItem) {
    return StudentNutritionMealItem(
      id: '',
      mealId: '',
      foodName: templateItem.foodName,
      quantity: templateItem.quantity,
      unit: templateItem.unit,
      notes: templateItem.notes,
      createdAt: DateTime.now(),
      updatedAt: DateTime.now(),
      calories: templateItem.calories,
      protein: templateItem.protein,
      carbs: templateItem.carbs,
      fats: templateItem.fats,
    );
  }
}
