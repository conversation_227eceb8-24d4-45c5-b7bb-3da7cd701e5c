// Domain models for instructor subscription plan management

/// Complete subscription plan configuration
class InstructorSubscriptionConfig {
  final double baseMonthlyPrice;
  final Map<PlanDuration, PlanPricing> basicPlanPricing;
  final Map<PlanDuration, PlanPricing> premiumPlanPricing;
  final List<DiscountCode> discountCodes;
  final SubmissionStatus submissionStatus;
  final DateTime? lastSubmittedAt;
  final String? adminFeedback;

  const InstructorSubscriptionConfig({
    required this.baseMonthlyPrice,
    required this.basicPlanPricing,
    required this.premiumPlanPricing,
    required this.discountCodes,
    this.submissionStatus = SubmissionStatus.draft,
    this.lastSubmittedAt,
    this.adminFeedback,
  });

  InstructorSubscriptionConfig copyWith({
    double? baseMonthlyPrice,
    Map<PlanDuration, PlanPricing>? basicPlanPricing,
    Map<PlanDuration, PlanPricing>? premiumPlanPricing,
    List<DiscountCode>? discountCodes,
    SubmissionStatus? submissionStatus,
    DateTime? lastSubmittedAt,
    String? adminFeedback,
  }) {
    return InstructorSubscriptionConfig(
      baseMonthlyPrice: baseMonthlyPrice ?? this.baseMonthlyPrice,
      basicPlanPricing: basicPlanPricing ?? this.basicPlanPricing,
      premiumPlanPricing: premiumPlanPricing ?? this.premiumPlanPricing,
      discountCodes: discountCodes ?? this.discountCodes,
      submissionStatus: submissionStatus ?? this.submissionStatus,
      lastSubmittedAt: lastSubmittedAt ?? this.lastSubmittedAt,
      adminFeedback: adminFeedback ?? this.adminFeedback,
    );
  }
}

/// Plan duration options
enum PlanDuration {
  monthly,
  sixMonth,
  yearly;

  String get displayName {
    switch (this) {
      case PlanDuration.monthly:
        return 'Monthly';
      case PlanDuration.sixMonth:
        return '6-Month';
      case PlanDuration.yearly:
        return 'Yearly';
    }
  }

  int get months {
    switch (this) {
      case PlanDuration.monthly:
        return 1;
      case PlanDuration.sixMonth:
        return 6;
      case PlanDuration.yearly:
        return 12;
    }
  }

  double get discountMultiplier {
    switch (this) {
      case PlanDuration.monthly:
        return 1.0; // No discount
      case PlanDuration.sixMonth:
        return 0.9; // 10% discount
      case PlanDuration.yearly:
        return 0.8; // 20% discount
    }
  }
}

/// Plan type enumeration
enum PlanType {
  basic,
  premium;

  String get displayName {
    switch (this) {
      case PlanType.basic:
        return 'Basic Plan';
      case PlanType.premium:
        return 'Premium Plan';
    }
  }
}

/// Pricing information for a specific plan and duration
class PlanPricing {
  final double monthlyPrice;
  final double totalPrice;
  final PlanDuration duration;
  final List<String> features;
  final bool isCustomPrice;

  const PlanPricing({
    required this.monthlyPrice,
    required this.totalPrice,
    required this.duration,
    required this.features,
    this.isCustomPrice = false,
  });

  PlanPricing copyWith({
    double? monthlyPrice,
    double? totalPrice,
    PlanDuration? duration,
    List<String>? features,
    bool? isCustomPrice,
  }) {
    return PlanPricing(
      monthlyPrice: monthlyPrice ?? this.monthlyPrice,
      totalPrice: totalPrice ?? this.totalPrice,
      duration: duration ?? this.duration,
      features: features ?? this.features,
      isCustomPrice: isCustomPrice ?? this.isCustomPrice,
    );
  }

  /// Calculate savings compared to monthly plan
  double calculateSavings(double baseMonthlyPrice) {
    final monthlyTotal = baseMonthlyPrice * duration.months;
    return monthlyTotal - totalPrice;
  }

  /// Calculate savings percentage
  double calculateSavingsPercentage(double baseMonthlyPrice) {
    final monthlyTotal = baseMonthlyPrice * duration.months;
    if (monthlyTotal == 0) return 0;
    return ((monthlyTotal - totalPrice) / monthlyTotal) * 100;
  }
}

/// Discount code model
class DiscountCode {
  final String id;
  final String code;
  final double discountPercentage;
  final DateTime expiryDate;
  final bool isActive;
  final int usageLimit;
  final int usageCount;
  final List<PlanType> applicablePlans;

  const DiscountCode({
    required this.id,
    required this.code,
    required this.discountPercentage,
    required this.expiryDate,
    this.isActive = true,
    this.usageLimit = 100,
    this.usageCount = 0,
    this.applicablePlans = const [PlanType.basic, PlanType.premium],
  });

  DiscountCode copyWith({
    String? id,
    String? code,
    double? discountPercentage,
    DateTime? expiryDate,
    bool? isActive,
    int? usageLimit,
    int? usageCount,
    List<PlanType>? applicablePlans,
  }) {
    return DiscountCode(
      id: id ?? this.id,
      code: code ?? this.code,
      discountPercentage: discountPercentage ?? this.discountPercentage,
      expiryDate: expiryDate ?? this.expiryDate,
      isActive: isActive ?? this.isActive,
      usageLimit: usageLimit ?? this.usageLimit,
      usageCount: usageCount ?? this.usageCount,
      applicablePlans: applicablePlans ?? this.applicablePlans,
    );
  }

  bool get isExpired => DateTime.now().isAfter(expiryDate);
  bool get isUsageLimitReached => usageCount >= usageLimit;
  bool get isValid => isActive && !isExpired && !isUsageLimitReached;
}

/// Submission status for instructor plans
enum SubmissionStatus {
  draft,
  submitted,
  underReview,
  approved,
  rejected,
  needsRevision;

  String get displayName {
    switch (this) {
      case SubmissionStatus.draft:
        return 'Draft';
      case SubmissionStatus.submitted:
        return 'Submitted';
      case SubmissionStatus.underReview:
        return 'Under Review';
      case SubmissionStatus.approved:
        return 'Approved';
      case SubmissionStatus.rejected:
        return 'Rejected';
      case SubmissionStatus.needsRevision:
        return 'Needs Revision';
    }
  }

  String get description {
    switch (this) {
      case SubmissionStatus.draft:
        return 'Configure your plans and submit for review';
      case SubmissionStatus.submitted:
        return 'Your plans have been submitted for admin review';
      case SubmissionStatus.underReview:
        return 'Admin is currently reviewing your plans';
      case SubmissionStatus.approved:
        return 'Your plans are approved and live on the platform';
      case SubmissionStatus.rejected:
        return 'Your plans were rejected. Please review feedback and resubmit';
      case SubmissionStatus.needsRevision:
        return 'Please make the requested changes and resubmit';
    }
  }

  bool get canEdit => this == SubmissionStatus.draft || 
                     this == SubmissionStatus.rejected || 
                     this == SubmissionStatus.needsRevision;

  bool get canSubmit => this == SubmissionStatus.draft || 
                       this == SubmissionStatus.rejected || 
                       this == SubmissionStatus.needsRevision;
}

/// Plan features for different plan types
class PlanFeatures {
  static const List<String> basicFeatures = [
    'Weekly training sessions',
    'Unlimited questions',
    'Basic support',
    'Progress tracking',
    'Mobile app access',
  ];

  static const List<String> premiumFeatures = [
    'Personalized workout plan',
    'Priority 24/7 support',
    'Exclusive premium content',
    'Weekly progress check-ins',
    'Nutrition guidance',
    'Video call sessions',
    'Custom meal plans',
  ];

  static List<String> getFeaturesForPlan(PlanType planType) {
    switch (planType) {
      case PlanType.basic:
        return basicFeatures;
      case PlanType.premium:
        return premiumFeatures;
    }
  }
}

/// Validation result for subscription config
class ValidationResult {
  final bool isValid;
  final List<String> errors;
  final List<String> warnings;

  const ValidationResult({
    required this.isValid,
    this.errors = const [],
    this.warnings = const [],
  });

  bool get hasErrors => errors.isNotEmpty;
  bool get hasWarnings => warnings.isNotEmpty;
}
