import 'package:flutter/material.dart';
import 'package:hooks_riverpod/hooks_riverpod.dart';

import 'colors.dart';

/// Theme mode provider
final themeModeProvider = StateProvider<ThemeMode>((ref) => ThemeMode.system);

/// Light theme
final lightTheme = ThemeData(
  brightness: Brightness.light,
  primarySwatch: Colors.green,
  primaryColor: AColor.fitgoGreen,
  scaffoldBackgroundColor: Colors.white,
  bottomSheetTheme: const BottomSheetThemeData(
    backgroundColor: Colors.transparent,
  ),
  appBarTheme: const AppBarTheme(
    backgroundColor: Colors.white,
    foregroundColor: Colors.black,
    elevation: 0,
  ),
  elevatedButtonTheme: ElevatedButtonThemeData(
    style: ElevatedButton.styleFrom(
      backgroundColor: AColor.fitgoGreen,
      foregroundColor: Colors.black,
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
    ),
  ),
  colorScheme: const ColorScheme.light(
    primary: AColor.fitgoGreen,
    secondary: AColor.fitgoColor1,
  ),
);

/// Dark theme
final darkTheme = ThemeData(
  brightness: Brightness.dark,
  primarySwatch: Colors.green,
  primaryColor: AColor.fitgoGreen,
  scaffoldBackgroundColor: AColor.onboardingBackgroundColor,
  bottomSheetTheme: const BottomSheetThemeData(
    backgroundColor: Colors.transparent,
  ),
  appBarTheme: const AppBarTheme(
    backgroundColor: AColor.onboardingBackgroundColor,
    foregroundColor: AColor.textColor,
    elevation: 0,
  ),
  elevatedButtonTheme: ElevatedButtonThemeData(
    style: ElevatedButton.styleFrom(
      backgroundColor: AColor.fitgoGreen,
      foregroundColor: Colors.black,
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
    ),
  ),
  colorScheme: const ColorScheme.dark(
    primary: AColor.fitgoGreen,
    secondary: AColor.fitgoColor1,
  ),
);
