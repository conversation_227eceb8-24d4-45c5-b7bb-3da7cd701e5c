import 'dart:io';
import 'package:hooks_riverpod/hooks_riverpod.dart';
import 'package:image_picker/image_picker.dart';
import 'package:fitgo_app/src/profile_form/domain/profile_models.dart';
import 'package:fitgo_app/src/profile_form/application/profile_repository.dart';

// Repository provider
final profileRepositoryProvider = Provider<ProfileRepository>((ref) {
  return ProfileRepository();
});

// Main profile form state notifier
class ProfileFormNotifier extends StateNotifier<ProfileFormState> {
  final ProfileRepository _repository;
  final ImagePicker _imagePicker = ImagePicker();
  bool _mounted = true;

  ProfileFormNotifier({
    required ProfileRepository repository,
  }) : _repository = repository, super(ProfileFormState(
    form: UserProfileForm(
      bodyPhotos: repository.createInitialBodyPhotos(),
    ),
  ));

  // Initialize form (check for existing profile)
  Future<void> initializeForm() async {
    final userId = _repository.getCurrentUserId();
    if (userId != null) {
      final existingProfile = await _repository.getExistingProfile(userId);
      if (existingProfile != null) {
        state = state.copyWith(
          form: existingProfile,
          isSubmitted: true,
        );
      }
    }
  }

  // Update fitness goals
  void updateFitnessGoals(String goals) {
    state = state.copyWith(
      form: state.form.copyWith(fitnessGoals: goals),
      clearError: true,
    );
  }

  // Update activity level
  void updateActivityLevel(ActivityLevel level) {
    state = state.copyWith(
      form: state.form.copyWith(activityLevel: level),
      clearError: true,
    );
  }

  // Toggle dietary restriction
  void toggleDietaryRestriction(DietaryRestriction restriction) {
    final currentRestrictions = Set<DietaryRestriction>.from(state.form.dietaryRestrictions);
    
    if (currentRestrictions.contains(restriction)) {
      currentRestrictions.remove(restriction);
    } else {
      currentRestrictions.add(restriction);
    }

    state = state.copyWith(
      form: state.form.copyWith(dietaryRestrictions: currentRestrictions),
      clearError: true,
    );
  }

  // Update medical conditions
  void updateMedicalConditions(String conditions) {
    state = state.copyWith(
      form: state.form.copyWith(medicalConditions: conditions),
      clearError: true,
    );
  }

  // Update additional notes
  void updateAdditionalNotes(String notes) {
    state = state.copyWith(
      form: state.form.copyWith(additionalNotes: notes),
      clearError: true,
    );
  }

  // Update weight
  void updateWeight(double? weight) {
    state = state.copyWith(
      form: state.form.copyWith(weight: weight),
      clearError: true,
    );
  }

  // Update height
  void updateHeight(double? height) {
    state = state.copyWith(
      form: state.form.copyWith(height: height),
      clearError: true,
    );
  }

  // Pick image from camera
  Future<void> pickImageFromCamera(BodyPhotoType photoType) async {
    try {
      final XFile? image = await _imagePicker.pickImage(
        source: ImageSource.camera,
        imageQuality: 80,
        maxWidth: 1024,
        maxHeight: 1024,
      );

      if (image != null) {
        await _updateBodyPhoto(photoType, File(image.path));
      }
    } catch (e) {
      state = state.copyWith(error: 'Fotoğraf çekilemedi. Kamera izni verildiğinden emin olun.');
    }
  }

  // Pick image from gallery
  Future<void> pickImageFromGallery(BodyPhotoType photoType) async {
    try {
      final XFile? image = await _imagePicker.pickImage(
        source: ImageSource.gallery,
        imageQuality: 80,
        maxWidth: 1024,
        maxHeight: 1024,
      );

      if (image != null) {
        await _updateBodyPhoto(photoType, File(image.path));
      }
    } catch (e) {
      state = state.copyWith(error: 'Fotoğraf seçilemedi. Galeri izni verildiğinden emin olun.');
    }
  }

  // Update body photo
  Future<void> _updateBodyPhoto(BodyPhotoType photoType, File imageFile) async {
    // Validate image
    if (!_repository.isValidImageFile(imageFile)) {
      state = state.copyWith(error: 'Lütfen geçerli bir resim dosyası seçin (JPG, PNG)');
      return;
    }

    if (!_repository.isValidImageSize(imageFile)) {
      state = state.copyWith(error: 'Resim boyutu 5MB\'dan küçük olmalıdır');
      return;
    }

    // Update photo with local file
    final updatedPhoto = BodyPhoto(
      type: photoType,
      localFile: imageFile,
    );

    final updatedPhotos = _repository.updateBodyPhoto(
      state.form.bodyPhotos,
      photoType,
      updatedPhoto,
    );

    state = state.copyWith(
      form: state.form.copyWith(bodyPhotos: updatedPhotos),
      clearError: true,
    );
  }

  // Remove body photo
  void removeBodyPhoto(BodyPhotoType photoType) {
    final clearedPhoto = BodyPhoto(type: photoType);
    
    final updatedPhotos = _repository.updateBodyPhoto(
      state.form.bodyPhotos,
      photoType,
      clearedPhoto,
    );

    state = state.copyWith(
      form: state.form.copyWith(bodyPhotos: updatedPhotos),
      clearError: true,
    );
  }

  // Upload all photos
  Future<void> _uploadPhotos() async {
    final userId = _repository.getCurrentUserId();
    if (userId == null) {
      throw Exception('Kullanıcı girişi yapılmamış');
    }

    state = state.copyWith(isUploadingPhotos: true);

    try {
      final updatedPhotos = <BodyPhoto>[];

      for (final photo in state.form.bodyPhotos) {
        if (photo.localFile != null && photo.uploadedUrl == null) {
          // Upload photo
          final uploadedUrl = await _repository.uploadBodyPhoto(
            imageFile: photo.localFile!,
            photoType: photo.type,
            userId: userId,
            onProgress: (progress) {
              final progressMap = Map<BodyPhotoType, double>.from(state.uploadProgress);
              progressMap[photo.type] = progress;
              state = state.copyWith(uploadProgress: progressMap);
            },
          );

          // Update photo with uploaded URL
          updatedPhotos.add(photo.copyWith(uploadedUrl: uploadedUrl));
        } else {
          updatedPhotos.add(photo);
        }
      }

      state = state.copyWith(
        form: state.form.copyWith(bodyPhotos: updatedPhotos),
        isUploadingPhotos: false,
        uploadProgress: {},
      );
    } catch (e) {
      state = state.copyWith(
        isUploadingPhotos: false,
        uploadProgress: {},
        error: 'Failed to upload photos: ${e.toString()}',
      );
      rethrow;
    }
  }

  // Submit profile form
  Future<ProfileSubmissionResult> submitProfile() async {
    if (!state.canSubmit) {
      return ProfileSubmissionResult.failure('Form geçerli değil veya zaten gönderilmiş');
    }

    // Ensure user is authenticated before starting
    var userId = _repository.getCurrentUserId();
    if (userId == null) {
      // For development, create a mock user session
      await _repository.createMockUserSession();
      userId = _repository.getCurrentUserId();
      if (userId == null) {
        return ProfileSubmissionResult.failure('Kimlik doğrulama gerekli');
      }
    }

    print('User ID for submission: $userId');
    state = state.copyWith(isSubmitting: true, clearError: true);

    try {
      // Upload photos first if any (development mode friendly)
      final photosToUpload = state.form.bodyPhotos
          .where((photo) => photo.localFile != null && photo.uploadedUrl == null)
          .toList();

      print('Photos to upload: ${photosToUpload.length}');

      if (photosToUpload.isNotEmpty) {
        try {
          await _uploadPhotos();
        } catch (uploadError) {
          print('Photo upload error: $uploadError');

          // In development mode, continue without photos if upload fails
          // Show a brief message to user but don't block submission
          state = state.copyWith(
            error: 'Fotoğraflar yüklenemedi: ${uploadError.toString()}',
          );

          // Clear the error after a longer delay to see the error
          Future.delayed(const Duration(seconds: 10), () {
            if (_mounted) {
              state = state.copyWith(clearError: true);
            }
          });

          // Clear any photos that failed to upload to avoid submission errors
          final updatedPhotos = state.form.bodyPhotos.map((photo) =>
              photo.copyWith(localFile: null, uploadedUrl: null)).toList();

          state = state.copyWith(
            form: state.form.copyWith(bodyPhotos: updatedPhotos),
            isUploadingPhotos: false,
            uploadProgress: {},
          );

          // Continue with form submission without photos
        }
      }

      // Submit profile
      final result = await _repository.submitProfile(
        form: state.form,
        userId: _repository.getCurrentUserId()!,
      );

      if (result.isSuccess) {
        state = state.copyWith(
          isSubmitting: false,
          isSubmitted: true,
        );
      } else {
        state = state.copyWith(
          isSubmitting: false,
          error: result.errorMessage,
        );
      }

      return result;
    } catch (e) {
      state = state.copyWith(
        isSubmitting: false,
        error: 'Profil gönderilirken hata oluştu: ${e.toString()}',
      );
      return ProfileSubmissionResult.failure(e.toString());
    }
  }

  // Clear error
  void clearError() {
    state = state.copyWith(clearError: true);
  }

  // Reset form
  void resetForm() {
    state = ProfileFormState(
      form: UserProfileForm(
        bodyPhotos: _repository.createInitialBodyPhotos(),
      ),
    );
  }

  @override
  void dispose() {
    _mounted = false;
    super.dispose();
  }
}

// Main profile form provider
final profileFormProvider = StateNotifierProvider<ProfileFormNotifier, ProfileFormState>(
  (ref) {
    final repository = ref.read(profileRepositoryProvider);
    return ProfileFormNotifier(repository: repository);
  },
);

// Convenience providers
final profileFormDataProvider = Provider<UserProfileForm>((ref) {
  return ref.watch(profileFormProvider).form;
});

final canSubmitProfileProvider = Provider<bool>((ref) {
  return ref.watch(profileFormProvider).canSubmit;
});

final isProfileSubmittedProvider = Provider<bool>((ref) {
  return ref.watch(profileFormProvider).isSubmitted;
});

// Profile submission status provider
final profileSubmissionStatusProvider = FutureProvider<bool>((ref) async {
  final repository = ref.read(profileRepositoryProvider);
  return await repository.isProfileSubmitted();
});
