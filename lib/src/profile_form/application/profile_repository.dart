import 'dart:io';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:supabase_flutter/supabase_flutter.dart';
import 'package:fitgo_app/src/profile_form/domain/profile_models.dart';

class ProfileRepository {
  final SupabaseClient _supabase = Supabase.instance.client;
  static const String _profileSubmittedKey = 'profile_form_submitted';

  // Development mode flag - set to false in production
  static const bool _isDevelopmentMode = true;

  // Check if profile form has been submitted before
  Future<bool> isProfileSubmitted() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final localSubmitted = prefs.getBool(_profileSubmittedKey) ?? false;
      
      if (localSubmitted) return true;

      // Also check Supabase for existing profile
      final user = _supabase.auth.currentUser;
      if (user != null) {
        final response = await _supabase
            .from('user_profiles')
            .select('id')
            .eq('user_id', user.id)
            .maybeSingle();
        
        if (response != null) {
          // Mark as submitted locally for faster future checks
          await prefs.setBool(_profileSubmittedKey, true);
          return true;
        }
      }
      
      return false;
    } catch (e) {
      // If there's an error, assume not submitted to be safe
      return false;
    }
  }

  // Mark profile as submitted locally
  Future<void> markProfileAsSubmitted() async {
    final prefs = await SharedPreferences.getInstance();
    await prefs.setBool(_profileSubmittedKey, true);
  }

  // Check if storage bucket exists and is accessible
  Future<bool> _checkBucketExists() async {
    try {
      // Try to list files in the bucket to check access
      await _supabase.storage.from('profile_images').list();
      return true;
    } catch (e) {
      print('Bucket access error: $e');
      // If listing fails, try to check if bucket exists by getting info
      try {
        final buckets = await _supabase.storage.listBuckets();
        final bucketExists = buckets.any((bucket) => bucket.id == 'profile_images');
        print('Bucket exists: $bucketExists');
        return bucketExists;
      } catch (e2) {
        print('Bucket list error: $e2');
        return false;
      }
    }
  }

  // Upload image to Supabase Storage
  Future<String> uploadBodyPhoto({
    required File imageFile,
    required BodyPhotoType photoType,
    required String userId,
    Function(double)? onProgress,
  }) async {
    try {
      // Check if bucket exists before uploading
      final bucketExists = await _checkBucketExists();
      if (!bucketExists) {
        if (_isDevelopmentMode) {
          throw Exception('Fotoğraf yükleme servisi şu anda kullanılamıyor. Geliştirme modunda fotoğraf olmadan devam edebilirsiniz.');
        } else {
          throw Exception('Fotoğraf yükleme servisi şu anda kullanılamıyor. Lütfen daha sonra tekrar deneyin.');
        }
      }

      // Create organized path: {userId}/body/initial/{photoType}.jpg
      final fileName = '${photoType.name}.jpg';
      final filePath = '$userId/body/initial/$fileName';

      print('Uploading photo to path: $filePath');

      // Upload to Supabase Storage with upsert option
      await _supabase.storage
          .from('profile_images')
          .upload(filePath, imageFile,
            fileOptions: const FileOptions(
              cacheControl: '3600',
              upsert: true, // Allow overwriting existing files
            ),
          );

      // Get public URL
      final publicUrl = _supabase.storage
          .from('profile_images')
          .getPublicUrl(filePath);

      print('Photo uploaded successfully. URL: $publicUrl');
      return publicUrl;
    } on StorageException catch (e) {
      // Handle specific storage errors
      final statusCode = int.tryParse(e.statusCode ?? '0') ?? 0;
      if (statusCode == 404 || e.message.contains('Bucket not found')) {
        throw Exception('Fotoğraf yükleme servisi bulunamadı. Lütfen daha sonra tekrar deneyin.');
      } else if (statusCode == 413 || e.message.contains('too large')) {
        throw Exception('Fotoğraf boyutu çok büyük. Lütfen daha küçük bir fotoğraf seçin.');
      } else if (statusCode == 401 || statusCode == 403 || e.message.contains('permission')) {
        throw Exception('Fotoğraf yükleme izni yok. Lütfen giriş yapın ve tekrar deneyin.');
      } else {
        throw Exception('Fotoğraf yüklenemedi: ${e.message}');
      }
    } catch (e) {
      // Handle network and other errors
      if (e.toString().contains('SocketException') || e.toString().contains('TimeoutException')) {
        throw Exception('İnternet bağlantınızı kontrol edin ve tekrar deneyin.');
      }
      throw Exception('Fotoğraf yüklenirken bir hata oluştu: ${e.toString()}');
    }
  }

  // Upload progress photo (for future use)
  Future<String> uploadProgressPhoto({
    required File imageFile,
    required BodyPhotoType photoType,
    required String userId,
    DateTime? captureDate,
    Function(double)? onProgress,
  }) async {
    try {
      // Check if bucket exists first
      final bucketExists = await _checkBucketExists();
      if (!bucketExists) {
        throw Exception('Fotoğraf yükleme servisi hazır değil');
      }

      // Create organized path: {userId}/body/progress/{date}/{photoType}_{timestamp}.jpg
      final date = captureDate ?? DateTime.now();
      final dateStr = '${date.day.toString().padLeft(2, '0')}-${date.month.toString().padLeft(2, '0')}-${date.year}';
      final timestamp = DateTime.now().millisecondsSinceEpoch;
      final fileName = '${photoType.name}_$timestamp.jpg';
      final filePath = '$userId/body/progress/$dateStr/$fileName';

      print('Uploading progress photo to path: $filePath');

      // Upload to Supabase Storage
      await _supabase.storage
          .from('profile_images')
          .upload(filePath, imageFile,
            fileOptions: const FileOptions(
              cacheControl: '3600',
              upsert: false, // Don't overwrite progress photos
            ),
          );

      // Get public URL
      final publicUrl = _supabase.storage
          .from('profile_images')
          .getPublicUrl(filePath);

      print('Progress photo uploaded successfully. URL: $publicUrl');
      return publicUrl;
    } on StorageException catch (e) {
      // Handle specific storage errors
      final statusCode = int.tryParse(e.statusCode ?? '0') ?? 0;
      if (statusCode == 404 || e.message.contains('Bucket not found')) {
        throw Exception('Fotoğraf yükleme servisi bulunamadı.');
      } else if (statusCode == 413 || e.message.contains('too large')) {
        throw Exception('Fotoğraf boyutu çok büyük.');
      } else {
        throw Exception('Fotoğraf yüklenemedi: ${e.message}');
      }
    } catch (e) {
      throw Exception('Fotoğraf yüklenirken hata: ${e.toString()}');
    }
  }

  // Upload avatar/profile photo (for future use)
  Future<String> uploadAvatarPhoto({
    required File imageFile,
    required String userId,
    Function(double)? onProgress,
  }) async {
    try {
      // Check if bucket exists first
      final bucketExists = await _checkBucketExists();
      if (!bucketExists) {
        throw Exception('Fotoğraf yükleme servisi hazır değil');
      }

      // Create organized path: {userId}/profile/avatar.jpg
      final fileName = 'avatar.jpg';
      final filePath = '$userId/profile/$fileName';

      print('Uploading avatar photo to path: $filePath');

      // Upload to Supabase Storage with upsert (overwrite existing avatar)
      await _supabase.storage
          .from('profile_images')
          .upload(filePath, imageFile,
            fileOptions: const FileOptions(
              cacheControl: '3600',
              upsert: true, // Allow overwriting avatar
            ),
          );

      // Get public URL
      final publicUrl = _supabase.storage
          .from('profile_images')
          .getPublicUrl(filePath);

      print('Avatar photo uploaded successfully. URL: $publicUrl');
      return publicUrl;
    } on StorageException catch (e) {
      // Handle specific storage errors
      final statusCode = int.tryParse(e.statusCode ?? '0') ?? 0;
      if (statusCode == 404 || e.message.contains('Bucket not found')) {
        throw Exception('Fotoğraf yükleme servisi bulunamadı.');
      } else if (statusCode == 413 || e.message.contains('too large')) {
        throw Exception('Fotoğraf boyutu çok büyük.');
      } else {
        throw Exception('Fotoğraf yüklenemedi: ${e.message}');
      }
    } catch (e) {
      throw Exception('Fotoğraf yüklenirken hata: ${e.toString()}');
    }
  }

  // Submit complete profile form
  Future<ProfileSubmissionResult> submitProfile({
    required UserProfileForm form,
    required String userId,
  }) async {
    try {
      // Prepare profile data
      final profileData = {
        'user_id': userId,
        'fitness_goals': form.fitnessGoals,
        'activity_level': form.activityLevel?.name,
        'dietary_restrictions': form.dietaryRestrictions.map((e) => e.name).toList(),
        'medical_conditions': form.medicalConditions.isEmpty ? null : form.medicalConditions,
        'additional_notes': form.additionalNotes.isEmpty ? null : form.additionalNotes,
        'weight': form.weight,
        'height': form.height,
        'created_at': DateTime.now().toIso8601String(),
        'updated_at': DateTime.now().toIso8601String(),
      };

      // Add body photo URLs if available
      final photoUrls = <String, String>{};
      for (final photo in form.bodyPhotos) {
        if (photo.uploadedUrl != null) {
          photoUrls['${photo.type.name}_photo_url'] = photo.uploadedUrl!;
        }
      }
      profileData.addAll(photoUrls);

      // Insert into Supabase
      final response = await _supabase
          .from('user_profiles')
          .insert(profileData)
          .select('id')
          .single();

      final profileId = response['id'] as String;

      // Mark as submitted locally
      await markProfileAsSubmitted();

      return ProfileSubmissionResult.success(profileId);
    } catch (e) {
      return ProfileSubmissionResult.failure('Failed to submit profile: ${e.toString()}');
    }
  }

  // Get existing profile (if any)
  Future<UserProfileForm?> getExistingProfile(String userId) async {
    try {
      final response = await _supabase
          .from('user_profiles')
          .select('*')
          .eq('user_id', userId)
          .maybeSingle();

      if (response == null) return null;

      return UserProfileForm.fromJson(response);
    } catch (e) {
      return null;
    }
  }

  // Validate image file
  bool isValidImageFile(File file) {
    final extension = file.path.toLowerCase().split('.').last;
    final validExtensions = ['jpg', 'jpeg', 'png'];
    return validExtensions.contains(extension);
  }

  // Get image file size in MB
  double getImageSizeInMB(File file) {
    final bytes = file.lengthSync();
    return bytes / (1024 * 1024);
  }

  // Validate image size (max 5MB)
  bool isValidImageSize(File file) {
    return getImageSizeInMB(file) <= 5.0;
  }

  // Create initial body photos list
  List<BodyPhoto> createInitialBodyPhotos() {
    return BodyPhotoType.values
        .map((type) => BodyPhoto(type: type))
        .toList();
  }

  // Update body photo in list
  List<BodyPhoto> updateBodyPhoto(
    List<BodyPhoto> photos,
    BodyPhotoType type,
    BodyPhoto updatedPhoto,
  ) {
    return photos.map((photo) {
      if (photo.type == type) {
        return updatedPhoto;
      }
      return photo;
    }).toList();
  }

  // Get current user ID
  String? getCurrentUserId() {
    return _supabase.auth.currentUser?.id;
  }

  // Check if user is authenticated
  bool isUserAuthenticated() {
    return _supabase.auth.currentUser != null;
  }

  // Create mock user session for development
  Future<void> createMockUserSession() async {
    // For development purposes - create a mock user session
    // In production, this would be handled by proper authentication
    try {
      final mockEmail = 'test.user.${DateTime.now().millisecondsSinceEpoch}@fitgo.com';
      final mockPassword = 'testpassword123';
      
      // Try to sign up a mock user
      await _supabase.auth.signUp(
        email: mockEmail,
        password: mockPassword,
      );
    } catch (e) {
      // If signup fails, try to sign in with a default test user
      try {
        await _supabase.auth.signInWithPassword(
          email: '<EMAIL>',
          password: 'testpassword123',
        );
      } catch (signInError) {
        // If both fail, we'll work without authentication for now
        print('Mock authentication failed: $signInError');
      }
    }
  }
}
