import 'package:flutter/material.dart';
import 'package:fitgo_app/src/shared/widgets/text_widget/text_widget.dart';
import 'package:fitgo_app/src/shared/constants/app_text_style.dart';
import 'package:fitgo_app/src/shared/extensions/string_hardcoded.dart';
import 'package:fitgo_app/src/theme/colors.dart';
import 'package:fitgo_app/src/profile_form/domain/profile_models.dart';

class ActivityLevelSelector extends StatelessWidget {
  final ActivityLevel? selectedLevel;
  final ValueChanged<ActivityLevel> onChanged;

  const ActivityLevelSelector({
    super.key,
    required this.selectedLevel,
    required this.onChanged,
  });

  @override
  Widget build(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Row(
          children: [
            Icon(
              Icons.directions_run,
              color: AColor.fitgoGreen,
              size: 20,
            ),
            const SizedBox(width: 8),
            TextWidget(
              'Aktivite Seviyesi'.hardcoded,
              style: ATextStyle.medium.copyWith(
                color: Colors.white,
                fontWeight: FontWeight.w600,
              ),
            ),
            TextWidget(
              ' *',
              style: ATextStyle.medium.copyWith(
                color: Colors.red,
                fontWeight: FontWeight.w600,
              ),
            ),
          ],
        ),
        const SizedBox(height: 8),
        TextWidget(
          'Günlük aktivite seviyeni seç'.hardcoded,
          style: ATextStyle.small.copyWith(
            color: Colors.white70,
          ),
        ),
        const SizedBox(height: 12),
        
        Column(
          children: ActivityLevel.values.map((level) {
            final isSelected = selectedLevel == level;
            return Padding(
              padding: const EdgeInsets.only(bottom: 8),
              child: GestureDetector(
                onTap: () => onChanged(level),
                child: Container(
                  padding: const EdgeInsets.all(16),
                  decoration: BoxDecoration(
                    color: isSelected 
                        ? AColor.fitgoGreen.withOpacity(0.1)
                        : const Color(0xFF1E293B),
                    borderRadius: BorderRadius.circular(12),
                    border: Border.all(
                      color: isSelected 
                          ? AColor.fitgoGreen
                          : const Color(0xFF334155),
                      width: isSelected ? 2 : 1,
                    ),
                  ),
                  child: Row(
                    children: [
                      Container(
                        width: 20,
                        height: 20,
                        decoration: BoxDecoration(
                          shape: BoxShape.circle,
                          border: Border.all(
                            color: isSelected 
                                ? AColor.fitgoGreen
                                : Colors.white54,
                            width: 2,
                          ),
                          color: isSelected 
                              ? AColor.fitgoGreen
                              : Colors.transparent,
                        ),
                        child: isSelected
                            ? const Icon(
                                Icons.check,
                                color: Colors.white,
                                size: 12,
                              )
                            : null,
                      ),
                      const SizedBox(width: 12),
                      Expanded(
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            TextWidget(
                              level.displayName,
                              style: ATextStyle.medium.copyWith(
                                color: isSelected ? AColor.fitgoGreen : Colors.white,
                                fontWeight: FontWeight.w600,
                              ),
                            ),
                            const SizedBox(height: 4),
                            TextWidget(
                              _getActivityDescription(level),
                              style: ATextStyle.small.copyWith(
                                color: Colors.white70,
                              ),
                            ),
                          ],
                        ),
                      ),
                    ],
                  ),
                ),
              ),
            );
          }).toList(),
        ),
      ],
    );
  }

  String _getActivityDescription(ActivityLevel level) {
    switch (level) {
      case ActivityLevel.sedentary:
        return 'Çoğunlukla oturarak çalışıyorum, az hareket ediyorum'.hardcoded;
      case ActivityLevel.lightlyActive:
        return 'Haftada 1-3 gün hafif egzersiz yapıyorum'.hardcoded;
      case ActivityLevel.moderatelyActive:
        return 'Haftada 3-5 gün orta seviye egzersiz yapıyorum'.hardcoded;
    }
  }
}
