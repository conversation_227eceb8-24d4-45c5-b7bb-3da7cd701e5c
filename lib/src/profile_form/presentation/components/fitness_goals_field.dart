import 'package:flutter/material.dart';
import 'package:fitgo_app/src/shared/widgets/text_widget/text_widget.dart';
import 'package:fitgo_app/src/shared/constants/app_text_style.dart';
import 'package:fitgo_app/src/shared/extensions/string_hardcoded.dart';
import 'package:fitgo_app/src/theme/colors.dart';

class FitnessGoalsField extends StatelessWidget {
  final String value;
  final ValueChanged<String> onChanged;

  const FitnessGoalsField({
    super.key,
    required this.value,
    required this.onChanged,
  });

  @override
  Widget build(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Row(
          children: [
            Icon(
              Icons.flag,
              color: AColor.fitgoGreen,
              size: 20,
            ),
            const SizedBox(width: 8),
            TextWidget(
              'Fitness Hedeflerin'.hardcoded,
              style: ATextStyle.medium.copyWith(
                color: Colors.white,
                fontWeight: FontWeight.w600,
              ),
            ),
            TextWidget(
              ' *',
              style: ATextStyle.medium.copyWith(
                color: Colors.red,
                fontWeight: FontWeight.w600,
              ),
            ),
          ],
        ),
        const SizedBox(height: 8),
        TextWidget(
          'Hangi hedeflere ulaşmak istiyorsun? (örn: kilo vermek, kas yapmak, kondisyon artırmak)'.hardcoded,
          style: ATextStyle.small.copyWith(
            color: Colors.white70,
          ),
        ),
        const SizedBox(height: 12),
        TextField(
          onChanged: onChanged,
          maxLines: 3,
          style: ATextStyle.medium.copyWith(color: Colors.white),
          decoration: InputDecoration(
            hintText: 'Fitness hedeflerini buraya yaz...'.hardcoded,
            hintStyle: ATextStyle.medium.copyWith(color: Colors.white54),
            filled: true,
            fillColor: const Color(0xFF1E293B),
            border: OutlineInputBorder(
              borderRadius: BorderRadius.circular(12),
              borderSide: const BorderSide(color: Color(0xFF334155)),
            ),
            enabledBorder: OutlineInputBorder(
              borderRadius: BorderRadius.circular(12),
              borderSide: const BorderSide(color: Color(0xFF334155)),
            ),
            focusedBorder: OutlineInputBorder(
              borderRadius: BorderRadius.circular(12),
              borderSide: BorderSide(color: AColor.fitgoGreen, width: 2),
            ),
            contentPadding: const EdgeInsets.all(16),
          ),
        ),
      ],
    );
  }
}
