import 'package:flutter/material.dart';
import 'package:fitgo_app/src/shared/widgets/text_widget/text_widget.dart';
import 'package:fitgo_app/src/shared/constants/app_text_style.dart';
import 'package:fitgo_app/src/shared/extensions/string_hardcoded.dart';
import 'package:fitgo_app/src/theme/colors.dart';

class SubmitButton extends StatelessWidget {
  final bool canSubmit;
  final bool isSubmitting;
  final bool isUploading;
  final VoidCallback onPressed;

  const SubmitButton({
    super.key,
    required this.canSubmit,
    required this.isSubmitting,
    required this.isUploading,
    required this.onPressed,
  });

  @override
  Widget build(BuildContext context) {
    final isLoading = isSubmitting || isUploading;
    
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: const Color(0xFF0F172A),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withOpacity(0.1),
            blurRadius: 8,
            offset: const Offset(0, -2),
          ),
        ],
      ),
      child: <PERSON><PERSON><PERSON>(
        child: AnimatedContainer(
          duration: const Duration(milliseconds: 300),
          curve: Curves.easeInOut,
          child: ElevatedButton(
            onPressed: (canSubmit && !isLoading) ? onPressed : null,
            style: ElevatedButton.styleFrom(
              backgroundColor: canSubmit && !isLoading 
                  ? AColor.fitgoGreen 
                  : Colors.grey[600],
              foregroundColor: Colors.white,
              disabledBackgroundColor: Colors.grey[600],
              disabledForegroundColor: Colors.grey[400],
              padding: const EdgeInsets.symmetric(vertical: 18),
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(16),
              ),
              elevation: (canSubmit && !isLoading) ? 4 : 0,
              shadowColor: AColor.fitgoGreen.withOpacity(0.3),
            ),
            child: Container(
              width: double.infinity,
              child: Row(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  if (isLoading) ...[
                    SizedBox(
                      height: 20,
                      width: 20,
                      child: CircularProgressIndicator(
                        strokeWidth: 2,
                        valueColor: const AlwaysStoppedAnimation<Color>(Colors.white),
                      ),
                    ),
                    const SizedBox(width: 12),
                    TextWidget(
                      isUploading 
                          ? 'Fotoğraflar Yükleniyor...'.hardcoded
                          : 'Gönderiliyor...'.hardcoded,
                      style: ATextStyle.large.copyWith(
                        color: Colors.white,
                        fontWeight: FontWeight.bold,
                        fontSize: 18,
                      ),
                    ),
                  ] else ...[
                    Icon(
                      Icons.send,
                      color: Colors.white,
                      size: 20,
                    ),
                    const SizedBox(width: 8),
                    
                    TextWidget(
                      'Gönder'.hardcoded,
                      style: ATextStyle.large.copyWith(
                        color: Colors.white,
                        fontWeight: FontWeight.bold,
                        fontSize: 18,
                      ),
                    ),
                    
                    const SizedBox(width: 8),
                    Icon(
                      Icons.arrow_forward,
                      color: Colors.white,
                      size: 20,
                    ),
                  ],
                ],
              ),
            ),
          ),
        ),
      ),
    );
  }
}
