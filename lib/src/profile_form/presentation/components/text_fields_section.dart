import 'package:flutter/material.dart';
import 'package:fitgo_app/src/shared/widgets/text_widget/text_widget.dart';
import 'package:fitgo_app/src/shared/constants/app_text_style.dart';
import 'package:fitgo_app/src/shared/extensions/string_hardcoded.dart';
import 'package:fitgo_app/src/theme/colors.dart';

class TextFieldsSection extends StatelessWidget {
  final String medicalConditions;
  final String additionalNotes;
  final ValueChanged<String> onMedicalConditionsChanged;
  final ValueChanged<String> onAdditionalNotesChanged;

  const TextFieldsSection({
    super.key,
    required this.medicalConditions,
    required this.additionalNotes,
    required this.onMedicalConditionsChanged,
    required this.onAdditionalNotesChanged,
  });

  @override
  Widget build(BuildContext context) {
    return Column(
      children: [
        // Medical conditions field
        _buildTextField(
          icon: Icons.medical_services,
          label: 'Sağlık Durumu'.hardcoded,
          description: 'Eğitmenin bilmesi gereken sağlık sorunları varsa belirt (opsiyonel)'.hardcoded,
          value: medicalConditions,
          onChanged: onMedicalConditionsChanged,
          hintText: 'Örn: Diz problemi, kalp rahatsızlığı, vs.'.hardcoded,
          maxLines: 3,
        ),
        
        const SizedBox(height: 24),
        
        // Additional notes field
        _buildTextField(
          icon: Icons.note,
          label: 'Ek Notlar'.hardcoded,
          description: 'Eğitmenle paylaşmak istediğin diğer bilgiler (opsiyonel)'.hardcoded,
          value: additionalNotes,
          onChanged: onAdditionalNotesChanged,
          hintText: 'Örn: Tercih ettiğin egzersiz türleri, çalışma saatlerin, vs.'.hardcoded,
          maxLines: 4,
        ),
      ],
    );
  }

  Widget _buildTextField({
    required IconData icon,
    required String label,
    required String description,
    required String value,
    required ValueChanged<String> onChanged,
    required String hintText,
    required int maxLines,
  }) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Row(
          children: [
            Icon(
              icon,
              color: AColor.fitgoGreen,
              size: 20,
            ),
            const SizedBox(width: 8),
            TextWidget(
              label,
              style: ATextStyle.medium.copyWith(
                color: Colors.white,
                fontWeight: FontWeight.w600,
              ),
            ),
          ],
        ),
        const SizedBox(height: 8),
        TextWidget(
          description,
          style: ATextStyle.small.copyWith(
            color: Colors.white70,
          ),
        ),
        const SizedBox(height: 12),
        TextField(
          onChanged: onChanged,
          maxLines: maxLines,
          style: ATextStyle.medium.copyWith(color: Colors.white),
          decoration: InputDecoration(
            hintText: hintText,
            hintStyle: ATextStyle.medium.copyWith(color: Colors.white54),
            filled: true,
            fillColor: const Color(0xFF1E293B),
            border: OutlineInputBorder(
              borderRadius: BorderRadius.circular(12),
              borderSide: const BorderSide(color: Color(0xFF334155)),
            ),
            enabledBorder: OutlineInputBorder(
              borderRadius: BorderRadius.circular(12),
              borderSide: const BorderSide(color: Color(0xFF334155)),
            ),
            focusedBorder: OutlineInputBorder(
              borderRadius: BorderRadius.circular(12),
              borderSide: BorderSide(color: AColor.fitgoGreen, width: 2),
            ),
            contentPadding: const EdgeInsets.all(16),
          ),
        ),
      ],
    );
  }
}
