import 'package:flutter/material.dart';
import 'package:fitgo_app/src/shared/widgets/text_widget/text_widget.dart';
import 'package:fitgo_app/src/shared/constants/app_text_style.dart';
import 'package:fitgo_app/src/shared/extensions/string_hardcoded.dart';
import 'package:fitgo_app/src/theme/colors.dart';
import 'package:fitgo_app/src/profile_form/domain/profile_models.dart';

class DietaryRestrictionsSection extends StatelessWidget {
  final Set<DietaryRestriction> selectedRestrictions;
  final ValueChanged<DietaryRestriction> onToggle;

  const DietaryRestrictionsSection({
    super.key,
    required this.selectedRestrictions,
    required this.onToggle,
  });

  @override
  Widget build(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Row(
          children: [
            Icon(
              Icons.restaurant,
              color: AColor.fitgoGreen,
              size: 20,
            ),
            const SizedBox(width: 8),
            TextWidget(
              'Beslenme Kısıtlamaları'.hardcoded,
              style: ATextStyle.medium.copyWith(
                color: Colors.white,
                fontWeight: FontWeight.w600,
              ),
            ),
          ],
        ),
        const SizedBox(height: 8),
        TextWidget(
          'Varsa beslenme kısıtlamalarını seç (opsiyonel)'.hardcoded,
          style: ATextStyle.small.copyWith(
            color: Colors.white70,
          ),
        ),
        const SizedBox(height: 12),
        
        Wrap(
          spacing: 12,
          runSpacing: 8,
          children: DietaryRestriction.values.map((restriction) {
            final isSelected = selectedRestrictions.contains(restriction);
            return GestureDetector(
              onTap: () => onToggle(restriction),
              child: Container(
                padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
                decoration: BoxDecoration(
                  color: isSelected 
                      ? AColor.fitgoGreen.withOpacity(0.1)
                      : const Color(0xFF1E293B),
                  borderRadius: BorderRadius.circular(20),
                  border: Border.all(
                    color: isSelected 
                        ? AColor.fitgoGreen
                        : const Color(0xFF334155),
                    width: isSelected ? 2 : 1,
                  ),
                ),
                child: Row(
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    Container(
                      width: 16,
                      height: 16,
                      decoration: BoxDecoration(
                        color: isSelected 
                            ? AColor.fitgoGreen
                            : Colors.transparent,
                        border: Border.all(
                          color: isSelected 
                              ? AColor.fitgoGreen
                              : Colors.white54,
                          width: 2,
                        ),
                        borderRadius: BorderRadius.circular(4),
                      ),
                      child: isSelected
                          ? const Icon(
                              Icons.check,
                              color: Colors.white,
                              size: 10,
                            )
                          : null,
                    ),
                    const SizedBox(width: 8),
                    TextWidget(
                      restriction.displayName,
                      style: ATextStyle.small.copyWith(
                        color: isSelected ? AColor.fitgoGreen : Colors.white,
                        fontWeight: FontWeight.w500,
                      ),
                    ),
                  ],
                ),
              ),
            );
          }).toList(),
        ),
      ],
    );
  }
}
