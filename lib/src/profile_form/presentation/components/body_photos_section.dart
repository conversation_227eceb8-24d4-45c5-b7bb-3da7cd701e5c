import 'dart:io';
import 'package:flutter/material.dart';
import 'package:fitgo_app/src/shared/widgets/text_widget/text_widget.dart';
import 'package:fitgo_app/src/shared/constants/app_text_style.dart';
import 'package:fitgo_app/src/shared/extensions/string_hardcoded.dart';
import 'package:fitgo_app/src/theme/colors.dart';
import 'package:fitgo_app/src/profile_form/domain/profile_models.dart';

class BodyPhotosSection extends StatelessWidget {
  final List<BodyPhoto> photos;
  final bool isUploading;
  final Map<BodyPhotoType, double> uploadProgress;
  final Function(BodyPhotoType) onPhotoTap;
  final Function(BodyPhotoType) onPhotoRemove;

  const BodyPhotosSection({
    super.key,
    required this.photos,
    required this.isUploading,
    required this.uploadProgress,
    required this.onPhotoTap,
    required this.onPhotoRemove,
  });

  @override
  Widget build(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Row(
          children: [
            Icon(
              Icons.photo_camera,
              color: AColor.fitgoGreen,
              size: 24,
            ),
            const SizedBox(width: 8),
            TextWidget(
              'Vücut Fotoğrafları'.hardcoded,
              style: ATextStyle.large.copyWith(
                color: Colors.white,
                fontWeight: FontWeight.w600,
              ),
            ),
          ],
        ),
        const SizedBox(height: 8),
        TextWidget(
          'Eğitmenin ilerlemenizi takip edebilmesi için vücut fotoğraflarınızı ekleyin (opsiyonel)'.hardcoded,
          style: ATextStyle.small.copyWith(
            color: Colors.white70,
          ),
        ),
        const SizedBox(height: 16),
        
        Row(
          children: photos.map((photo) => Expanded(
            child: Padding(
              padding: EdgeInsets.only(
                right: photo.type != BodyPhotoType.back ? 12 : 0,
              ),
              child: _buildPhotoCard(photo),
            ),
          )).toList(),
        ),
      ],
    );
  }

  Widget _buildPhotoCard(BodyPhoto photo) {
    return GestureDetector(
      onTap: () => onPhotoTap(photo.type),
      child: Container(
        height: 160,
        decoration: BoxDecoration(
          color: const Color(0xFF1E293B),
          borderRadius: BorderRadius.circular(12),
          border: Border.all(
            color: photo.hasPhoto 
                ? AColor.fitgoGreen.withOpacity(0.5)
                : Colors.white24,
            width: photo.hasPhoto ? 2 : 1,
          ),
        ),
        child: Stack(
          children: [
            // Photo content
            ClipRRect(
              borderRadius: BorderRadius.circular(11),
              child: _buildPhotoContent(photo),
            ),
            
            // Upload progress overlay
            if (photo.isUploading || uploadProgress.containsKey(photo.type))
              _buildUploadProgress(photo),
            
            // Remove button
            if (photo.hasPhoto && !photo.isUploading)
              Positioned(
                top: 8,
                right: 8,
                child: GestureDetector(
                  onTap: () => onPhotoRemove(photo.type),
                  child: Container(
                    padding: const EdgeInsets.all(4),
                    decoration: BoxDecoration(
                      color: Colors.red.withOpacity(0.8),
                      shape: BoxShape.circle,
                    ),
                    child: const Icon(
                      Icons.close,
                      color: Colors.white,
                      size: 16,
                    ),
                  ),
                ),
              ),
            
            // Photo type label
            Positioned(
              bottom: 8,
              left: 8,
              right: 8,
              child: Container(
                padding: const EdgeInsets.symmetric(vertical: 4, horizontal: 8),
                decoration: BoxDecoration(
                  color: Colors.black.withOpacity(0.7),
                  borderRadius: BorderRadius.circular(6),
                ),
                child: TextWidget(
                  photo.type.displayName,
                  style: ATextStyle.small.copyWith(
                    color: Colors.white,
                    fontWeight: FontWeight.w500,
                  ),
                  textAlign: TextAlign.center,
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildPhotoContent(BodyPhoto photo) {
    if (photo.localFile != null) {
      // Show local file
      return Container(
        width: double.infinity,
        height: double.infinity,
        child: Image.file(
          photo.localFile!,
          fit: BoxFit.cover,
        ),
      );
    } else if (photo.uploadedUrl != null) {
      // Show uploaded image
      return Container(
        width: double.infinity,
        height: double.infinity,
        child: Image.network(
          photo.uploadedUrl!,
          fit: BoxFit.cover,
          loadingBuilder: (context, child, loadingProgress) {
            if (loadingProgress == null) return child;
            return Center(
              child: CircularProgressIndicator(
                valueColor: AlwaysStoppedAnimation<Color>(AColor.fitgoGreen),
                value: loadingProgress.expectedTotalBytes != null
                    ? loadingProgress.cumulativeBytesLoaded / 
                      loadingProgress.expectedTotalBytes!
                    : null,
              ),
            );
          },
          errorBuilder: (context, error, stackTrace) {
            return _buildPlaceholder(photo.type, hasError: true);
          },
        ),
      );
    } else {
      // Show placeholder
      return _buildPlaceholder(photo.type);
    }
  }

  Widget _buildPlaceholder(BodyPhotoType photoType, {bool hasError = false}) {
    return Container(
      width: double.infinity,
      height: double.infinity,
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(
            hasError ? Icons.error_outline : Icons.add_a_photo,
            color: hasError ? Colors.red : AColor.fitgoGreen,
            size: 32,
          ),
          const SizedBox(height: 8),
          TextWidget(
            hasError ? 'Hata'.hardcoded : 'Ekle'.hardcoded,
            style: ATextStyle.small.copyWith(
              color: hasError ? Colors.red : Colors.white70,
              fontWeight: FontWeight.w500,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildUploadProgress(BodyPhoto photo) {
    final progress = uploadProgress[photo.type] ?? 0.0;
    
    return Container(
      width: double.infinity,
      height: double.infinity,
      decoration: BoxDecoration(
        color: Colors.black.withOpacity(0.7),
        borderRadius: BorderRadius.circular(11),
      ),
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          CircularProgressIndicator(
            value: progress,
            valueColor: AlwaysStoppedAnimation<Color>(AColor.fitgoGreen),
            strokeWidth: 3,
          ),
          const SizedBox(height: 12),
          TextWidget(
            'Yükleniyor...'.hardcoded,
            style: ATextStyle.small.copyWith(
              color: Colors.white,
              fontWeight: FontWeight.w500,
            ),
          ),
          const SizedBox(height: 4),
          TextWidget(
            '${(progress * 100).toInt()}%',
            style: ATextStyle.small.copyWith(
              color: AColor.fitgoGreen,
              fontWeight: FontWeight.w600,
            ),
          ),
        ],
      ),
    );
  }
}
