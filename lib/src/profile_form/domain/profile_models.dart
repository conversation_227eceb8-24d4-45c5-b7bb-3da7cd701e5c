import 'dart:io';

// Activity level enum
enum ActivityLevel {
  sedentary('Sedentary'),
  lightlyActive('Lightly Active'),
  moderatelyActive('Moderately Active');

  const ActivityLevel(this.displayName);
  final String displayName;
}

// Dietary restrictions enum
enum DietaryRestriction {
  vegetarian('Vegetarian'),
  glutenFree('Gluten Free'),
  dairyFree('Dairy Free');

  const DietaryRestriction(this.displayName);
  final String displayName;
}

// Body photo type enum
enum BodyPhotoType {
  front('Front'),
  side('Side'),
  back('Back');

  const BodyPhotoType(this.displayName);
  final String displayName;
}

// Body photo model
class BodyPhoto {
  final BodyPhotoType type;
  final File? localFile;
  final String? uploadedUrl;
  final bool isUploading;

  const BodyPhoto({
    required this.type,
    this.localFile,
    this.uploadedUrl,
    this.isUploading = false,
  });

  bool get hasPhoto => localFile != null || uploadedUrl != null;
  bool get isUploaded => uploadedUrl != null;

  BodyPhoto copyWith({
    File? localFile,
    String? uploadedUrl,
    bool? isUploading,
    bool clearLocalFile = false,
    bool clearUploadedUrl = false,
  }) {
    return BodyPhoto(
      type: type,
      localFile: clearLocalFile ? null : (localFile ?? this.localFile),
      uploadedUrl: clearUploadedUrl ? null : (uploadedUrl ?? this.uploadedUrl),
      isUploading: isUploading ?? this.isUploading,
    );
  }
}

// User profile form data
class UserProfileForm {
  final String fitnessGoals;
  final ActivityLevel? activityLevel;
  final Set<DietaryRestriction> dietaryRestrictions;
  final String medicalConditions;
  final String additionalNotes;
  final List<BodyPhoto> bodyPhotos;
  final double? weight; // kg
  final double? height; // cm

  const UserProfileForm({
    this.fitnessGoals = '',
    this.activityLevel,
    this.dietaryRestrictions = const {},
    this.medicalConditions = '',
    this.additionalNotes = '',
    this.bodyPhotos = const [],
    this.weight,
    this.height,
  });

  UserProfileForm copyWith({
    String? fitnessGoals,
    ActivityLevel? activityLevel,
    Set<DietaryRestriction>? dietaryRestrictions,
    String? medicalConditions,
    String? additionalNotes,
    List<BodyPhoto>? bodyPhotos,
    double? weight,
    double? height,
    bool clearActivityLevel = false,
    bool clearWeight = false,
    bool clearHeight = false,
  }) {
    return UserProfileForm(
      fitnessGoals: fitnessGoals ?? this.fitnessGoals,
      activityLevel: clearActivityLevel ? null : (activityLevel ?? this.activityLevel),
      dietaryRestrictions: dietaryRestrictions ?? this.dietaryRestrictions,
      medicalConditions: medicalConditions ?? this.medicalConditions,
      additionalNotes: additionalNotes ?? this.additionalNotes,
      bodyPhotos: bodyPhotos ?? this.bodyPhotos,
      weight: clearWeight ? null : (weight ?? this.weight),
      height: clearHeight ? null : (height ?? this.height),
    );
  }

  bool get isValid {
    return fitnessGoals.isNotEmpty && 
           activityLevel != null;
  }

  bool get hasAllPhotos {
    return bodyPhotos.length == 3 && 
           bodyPhotos.every((photo) => photo.hasPhoto);
  }

  Map<String, dynamic> toJson() {
    return {
      'fitness_goals': fitnessGoals,
      'activity_level': activityLevel?.name,
      'dietary_restrictions': dietaryRestrictions.map((e) => e.name).toList(),
      'medical_conditions': medicalConditions,
      'additional_notes': additionalNotes,
      'weight': weight,
      'height': height,
      'body_photos': bodyPhotos.map((photo) => {
        'type': photo.type.name,
        'uploaded_url': photo.uploadedUrl,
      }).toList(),
    };
  }

  factory UserProfileForm.fromJson(Map<String, dynamic> json) {
    return UserProfileForm(
      fitnessGoals: json['fitness_goals'] as String? ?? '',
      activityLevel: json['activity_level'] != null
          ? ActivityLevel.values.firstWhere((e) => e.name == json['activity_level'])
          : null,
      dietaryRestrictions: (json['dietary_restrictions'] as List<dynamic>?)
          ?.map((e) => DietaryRestriction.values.firstWhere((dr) => dr.name == e))
          .toSet() ?? {},
      medicalConditions: json['medical_conditions'] as String? ?? '',
      additionalNotes: json['additional_notes'] as String? ?? '',
      weight: json['weight'] != null ? (json['weight'] as num).toDouble() : null,
      height: json['height'] != null ? (json['height'] as num).toDouble() : null,
      bodyPhotos: (json['body_photos'] as List<dynamic>?)
          ?.map((photoJson) => BodyPhoto(
            type: BodyPhotoType.values.firstWhere((t) => t.name == photoJson['type']),
            uploadedUrl: photoJson['uploaded_url'] as String?,
          ))
          .toList() ?? [],
    );
  }
}

// Profile form state
class ProfileFormState {
  final UserProfileForm form;
  final bool isSubmitting;
  final bool isSubmitted;
  final String? error;
  final bool isUploadingPhotos;
  final Map<BodyPhotoType, double> uploadProgress;

  const ProfileFormState({
    this.form = const UserProfileForm(),
    this.isSubmitting = false,
    this.isSubmitted = false,
    this.error,
    this.isUploadingPhotos = false,
    this.uploadProgress = const {},
  });

  ProfileFormState copyWith({
    UserProfileForm? form,
    bool? isSubmitting,
    bool? isSubmitted,
    String? error,
    bool? isUploadingPhotos,
    Map<BodyPhotoType, double>? uploadProgress,
    bool clearError = false,
  }) {
    return ProfileFormState(
      form: form ?? this.form,
      isSubmitting: isSubmitting ?? this.isSubmitting,
      isSubmitted: isSubmitted ?? this.isSubmitted,
      error: clearError ? null : (error ?? this.error),
      isUploadingPhotos: isUploadingPhotos ?? this.isUploadingPhotos,
      uploadProgress: uploadProgress ?? this.uploadProgress,
    );
  }

  bool get canSubmit {
    return form.isValid && 
           !isSubmitting && 
           !isUploadingPhotos && 
           !isSubmitted;
  }
}

// Profile submission result
class ProfileSubmissionResult {
  final bool isSuccess;
  final String? errorMessage;
  final String? profileId;

  const ProfileSubmissionResult({
    required this.isSuccess,
    this.errorMessage,
    this.profileId,
  });

  factory ProfileSubmissionResult.success(String profileId) {
    return ProfileSubmissionResult(
      isSuccess: true,
      profileId: profileId,
    );
  }

  factory ProfileSubmissionResult.failure(String errorMessage) {
    return ProfileSubmissionResult(
      isSuccess: false,
      errorMessage: errorMessage,
    );
  }
}
