import 'package:flutter/material.dart';
import 'package:hooks_riverpod/hooks_riverpod.dart';

import '../../theme/colors.dart';

class HomePageView extends HookConsumerWidget {
  const HomePageView({super.key});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    return Scaffold(
      backgroundColor: AColor.onboardingBackgroundColor,
      appBar: AppBar(
        title: const Text(
          'FitGo',
          style: TextStyle(
            color: AColor.fitgoGreen,
            fontWeight: FontWeight.bold,
          ),
        ),
        backgroundColor: AColor.onboardingBackgroundColor,
        elevation: 0,
      ),
      body: const Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              Icons.fitness_center,
              color: AColor.fitgoGreen,
              size: 64,
            ),
            Sized<PERSON><PERSON>(height: 24),
            Text(
              'Welcome to FitGo!',
              style: TextStyle(
                color: AColor.textColor,
                fontSize: 24,
                fontWeight: FontWeight.bold,
              ),
            ),
            SizedBox(height: 16),
            Text(
              'Your fitness journey starts here',
              style: TextStyle(
                color: AColor.textSecondaryColor,
                fontSize: 16,
              ),
            ),
          ],
        ),
      ),
    );
  }
}
