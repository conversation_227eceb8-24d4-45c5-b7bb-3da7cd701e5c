import 'package:fitgo_app/core/services/onboarding_service.dart';
import 'package:fitgo_app/src/shared/enums/user_type.dart';
import 'package:flutter/material.dart';
import 'package:hooks_riverpod/hooks_riverpod.dart';

/// Application state providers following Riverpod best practices

/// Current user type selection state with Hive persistence
final currentUserTypeProvider =
    StateNotifierProvider<UserTypeNotifier, UserType?>((ref) {
      final onboardingService = ref.watch(onboardingServiceProvider);
      return UserTypeNotifier(onboardingService);
    });

/// Onboarding completion state with Hive persistence
final onboardingCompletedProvider =
    StateNotifierProvider<OnboardingCompletedNotifier, bool>((ref) {
      final onboardingService = ref.watch(onboardingServiceProvider);
      return OnboardingCompletedNotifier(onboardingService);
    });

/// Landing page seen state with Hive persistence
final landingPageSeenProvider =
    StateNotifierProvider<LandingPageSeenNotifier, bool>((ref) {
      final onboardingService = ref.watch(onboardingServiceProvider);
      return LandingPageSeenNotifier(onboardingService);
    });

/// Authentication state provider
final isAuthenticatedProvider = StateProvider<bool>((ref) => false);

/// User session provider
final userSessionProvider = StateProvider<Map<String, dynamic>?>((ref) => null);

/// App theme mode provider
final themeModeProvider = StateProvider<ThemeMode>((ref) => ThemeMode.system);

/// Student enrollment status provider (deprecated - use studentWorkflowProvider instead)
@Deprecated('Use studentWorkflowProvider instead')
final studentEnrollmentStatusProvider = StateProvider<bool>((ref) => false);

/// StateNotifier classes for persistent state management

/// User type state notifier with Hive persistence
class UserTypeNotifier extends StateNotifier<UserType?> {
  final OnboardingService _onboardingService;

  UserTypeNotifier(this._onboardingService) : super(null) {
    _loadUserType();
  }

  void _loadUserType() {
    state = _onboardingService.getSavedUserType();
  }

  Future<void> setUserType(UserType userType) async {
    state = userType;
    await _onboardingService.saveUserType(userType);
  }

  Future<void> clearUserType() async {
    state = null;
    await _onboardingService.clearOnboardingData();
  }
}

/// Onboarding completion state notifier with Hive persistence
class OnboardingCompletedNotifier extends StateNotifier<bool> {
  final OnboardingService _onboardingService;

  OnboardingCompletedNotifier(this._onboardingService) : super(false) {
    _loadOnboardingStatus();
  }

  void _loadOnboardingStatus() {
    state = _onboardingService.hasCompletedOnboarding();
  }

  Future<void> markCompleted() async {
    state = true;
    await _onboardingService.markOnboardingCompleted();
  }

  Future<void> reset() async {
    state = false;
    await _onboardingService.resetOnboardingCompletion();
  }
}

/// Landing page seen state notifier with Hive persistence
class LandingPageSeenNotifier extends StateNotifier<bool> {
  final OnboardingService _onboardingService;

  LandingPageSeenNotifier(this._onboardingService) : super(false) {
    _loadLandingPageStatus();
  }

  void _loadLandingPageStatus() {
    state = _onboardingService.hasSeenLandingPage();
  }

  Future<void> markSeen() async {
    state = true;
    await _onboardingService.markLandingPageSeen();
  }
}
