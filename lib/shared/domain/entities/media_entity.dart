import 'package:equatable/equatable.dart';

/// Media entity for handling images, videos, and other media files
class MediaEntity extends Equatable {
  final String id;
  final String url;
  final String? thumbnailUrl;
  final MediaType type;
  final String? title;
  final String? description;
  final int? duration; // in seconds for videos
  final int? fileSize; // in bytes
  final String? mimeType;
  final DateTime createdAt;
  final DateTime updatedAt;

  const MediaEntity({
    required this.id,
    required this.url,
    this.thumbnailUrl,
    required this.type,
    this.title,
    this.description,
    this.duration,
    this.fileSize,
    this.mimeType,
    required this.createdAt,
    required this.updatedAt,
  });

  /// Check if media is a video
  bool get isVideo => type == MediaType.video;

  /// Check if media is an image
  bool get isImage => type == MediaType.image;

  /// Get formatted file size
  String get formattedFileSize {
    if (fileSize == null) return 'Unknown size';
    
    const int kb = 1024;
    const int mb = kb * 1024;
    const int gb = mb * 1024;

    if (fileSize! >= gb) {
      return '${(fileSize! / gb).toStringAsFixed(1)} GB';
    } else if (fileSize! >= mb) {
      return '${(fileSize! / mb).toStringAsFixed(1)} MB';
    } else if (fileSize! >= kb) {
      return '${(fileSize! / kb).toStringAsFixed(1)} KB';
    } else {
      return '$fileSize B';
    }
  }

  /// Get formatted duration for videos
  String get formattedDuration {
    if (duration == null) return '';
    
    final int hours = duration! ~/ 3600;
    final int minutes = (duration! % 3600) ~/ 60;
    final int seconds = duration! % 60;

    if (hours > 0) {
      return '${hours.toString().padLeft(2, '0')}:${minutes.toString().padLeft(2, '0')}:${seconds.toString().padLeft(2, '0')}';
    } else {
      return '${minutes.toString().padLeft(2, '0')}:${seconds.toString().padLeft(2, '0')}';
    }
  }

  @override
  List<Object?> get props => [
        id,
        url,
        thumbnailUrl,
        type,
        title,
        description,
        duration,
        fileSize,
        mimeType,
        createdAt,
        updatedAt,
      ];
}

/// Media type enumeration
enum MediaType {
  image,
  video,
  audio,
  document;

  String get displayName {
    switch (this) {
      case MediaType.image:
        return 'Image';
      case MediaType.video:
        return 'Video';
      case MediaType.audio:
        return 'Audio';
      case MediaType.document:
        return 'Document';
    }
  }
}
