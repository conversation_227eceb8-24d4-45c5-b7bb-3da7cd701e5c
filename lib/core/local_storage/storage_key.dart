enum StorageKey {
  /// theme mode
  /// stored type is [int] ,ThemeMode index
  themeMode,

  /// language id
  /// stored type is [int], Language model id
  languageId,

  /// onboarding completion status
  /// stored type is [bool]
  onboardingCompleted,

  /// landing page seen status
  /// stored type is [bool]
  landingPageSeen,

  /// selected user type
  /// stored type is [String], UserType enum name
  selectedUserType,

  /// student workflow state
  /// stored type is [String], StudentWorkflowState enum name
  studentWorkflowState,

  /// student payment completion status
  /// stored type is [bool]
  studentPaymentCompleted,

  /// student profile form completion status
  /// stored type is [bool]
  studentProfileFormCompleted,

  /// student plan assignment status
  /// stored type is [bool]
  studentPlanAssigned,

  /// student instructor id
  /// stored type is [String]
  studentInstructorId,

  /// student enrollment id
  /// stored type is [String]
  studentEnrollmentId,
}
