import 'dart:io';

import 'package:connectivity_plus/connectivity_plus.dart';

/// Network service for handling connectivity and network operations
class NetworkService {
  final Connectivity _connectivity = Connectivity();

  /// Check if device is connected to internet
  Future<bool> get isConnected async {
    final connectivityResult = await _connectivity.checkConnectivity();

    if (connectivityResult == ConnectivityResult.none) {
      return false;
    }

    // Additional check by pinging a reliable server
    return await _hasInternetConnection();
  }

  /// Stream of connectivity changes
  Stream<bool> get connectivityStream {
    return _connectivity.onConnectivityChanged.asyncMap(
      (result) async => await isConnected,
    );
  }

  /// Check actual internet connection by pinging
  Future<bool> _hasInternetConnection() async {
    try {
      final result = await InternetAddress.lookup('google.com');
      return result.isNotEmpty && result[0].rawAddress.isNotEmpty;
    } on SocketException catch (_) {
      return false;
    }
  }

  /// Get current connectivity type
  Future<List<ConnectivityResult>> get connectivityType async {
    return await _connectivity.checkConnectivity();
  }
}
