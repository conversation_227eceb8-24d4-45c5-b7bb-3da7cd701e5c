/// Application constants
class AppConstants {
  // App Info
  static const String appName = 'FitGo';
  static const String appVersion = '1.0.0';
  
  // API & Backend
  static const String apiVersion = 'v1';
  static const Duration apiTimeout = Duration(seconds: 30);
  static const Duration cacheTimeout = Duration(hours: 24);
  
  // Storage Keys
  static const String userTokenKey = 'user_token';
  static const String userProfileKey = 'user_profile';
  static const String onboardingCompletedKey = 'onboarding_completed';
  static const String themeKey = 'theme_mode';
  static const String languageKey = 'language_code';
  static const String notificationSettingsKey = 'notification_settings';
  
  // Pagination
  static const int defaultPageSize = 20;
  static const int maxPageSize = 100;
  
  // Media
  static const int maxImageSizeMB = 10;
  static const int maxVideoSizeMB = 100;
  static const List<String> supportedImageFormats = ['jpg', 'jpeg', 'png', 'webp'];
  static const List<String> supportedVideoFormats = ['mp4', 'mov', 'avi'];
  
  // Workout
  static const int maxWorkoutDurationMinutes = 300; // 5 hours
  static const int minRestTimeSeconds = 10;
  static const int maxRestTimeSeconds = 600; // 10 minutes
  
  // Nutrition
  static const int maxDailyCalories = 10000;
  static const int minDailyCalories = 800;
  
  // Social
  static const int maxFriendsCount = 1000;
  static const int maxLeaderboardSize = 100;
  
  // Subscription
  static const List<String> subscriptionTypes = ['basic', 'premium', 'pro'];
  
  // AI Features
  static const int maxAIRequestsPerDay = 50; // For free users
  static const int maxPremiumAIRequestsPerDay = 500;
  
  // Validation
  static const int minPasswordLength = 6;
  static const int maxPasswordLength = 128;
  static const int minUsernameLength = 3;
  static const int maxUsernameLength = 30;
  static const int maxBioLength = 500;
  
  // URLs
  static const String privacyPolicyUrl = 'https://fitgo.app/privacy';
  static const String termsOfServiceUrl = 'https://fitgo.app/terms';
  static const String supportUrl = 'https://fitgo.app/support';
  
  // Error Messages
  static const String networkErrorMessage = 'Please check your internet connection';
  static const String unknownErrorMessage = 'Something went wrong. Please try again';
  static const String sessionExpiredMessage = 'Your session has expired. Please login again';
}
