import 'dart:async';
import 'package:app_links/app_links.dart';
import 'package:go_router/go_router.dart';
import 'package:hooks_riverpod/hooks_riverpod.dart';

class DeepLinkService {
  static final _instance = DeepLinkService._internal();
  factory DeepLinkService() => _instance;
  DeepLinkService._internal();

  final _appLinks = AppLinks();
  StreamSubscription<Uri>? _linkSubscription;

  /// Initialize deep link handling
  Future<void> initialize(GoRouter router) async {
    // Handle app launch from deep link
    final initialUri = await _appLinks.getInitialLink();
    if (initialUri != null) {
      _handleDeepLink(initialUri, router);
    }

    // Handle deep links while app is running
    _linkSubscription = _appLinks.uriLinkStream.listen(
      (uri) => _handleDeepLink(uri, router),
      onError: (err) {
        print('Deep link error: $err');
      },
    );
  }

  /// Handle incoming deep link
  void _handleDeepLink(Uri uri, GoRouter router) {
    print('Received deep link: $uri');

    if (uri.scheme == 'fitgo') {
      switch (uri.host) {
        case 'reset-password':
          _handlePasswordReset(uri, router);
          break;
        default:
          print('Unknown deep link host: ${uri.host}');
      }
    }
  }

  /// Handle password reset deep link
  void _handlePasswordReset(Uri uri, GoRouter router) {
    // Extract parameters from the URI
    final fragment = uri.fragment;
    final params = <String, String>{};

    if (fragment.isNotEmpty) {
      // Parse fragment parameters (Supabase sends tokens in fragment)
      final fragmentParams = fragment.split('&');
      for (final param in fragmentParams) {
        final keyValue = param.split('=');
        if (keyValue.length == 2) {
          params[keyValue[0]] = Uri.decodeComponent(keyValue[1]);
        }
      }
    }

    // Also check query parameters
    params.addAll(uri.queryParameters);

    final accessToken = params['access_token'];
    final refreshToken = params['refresh_token'];
    final email = params['email'];
    final type = params['type'];
    final token = params['token']; // Verification token from email

    print('Password reset params: $params');

    // Check if we have access token (from ConfirmationURL)
    if (accessToken != null && type == 'recovery') {
      router.go(
        '/reset-password?access_token=$accessToken&refresh_token=${refreshToken ?? ''}&email=${email ?? ''}',
      );
    }
    // Check if we have verification token (legacy support)
    else if (token != null && type == 'recovery') {
      // Navigate to a special screen that will exchange the token
      router.go('/reset-password-verify?token=$token&email=${email ?? ''}');
    } else {
      print('Missing required parameters for password reset');
      print(
        'Available params: accessToken=$accessToken, token=$token, type=$type',
      );
      // Navigate to login or show error
      router.go('/auth');
    }
  }

  /// Dispose resources
  void dispose() {
    _linkSubscription?.cancel();
  }
}

/// Provider for deep link service
final deepLinkServiceProvider = Provider<DeepLinkService>((ref) {
  return DeepLinkService();
});
