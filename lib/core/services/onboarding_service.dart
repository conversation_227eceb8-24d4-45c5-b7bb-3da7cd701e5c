import 'package:fitgo_app/core/local_storage/hive_helper.dart';
import 'package:fitgo_app/core/local_storage/hive_provider.dart';
import 'package:fitgo_app/core/local_storage/storage_key.dart';
import 'package:fitgo_app/src/shared/enums/user_type.dart';
import 'package:hooks_riverpod/hooks_riverpod.dart';

/// Service for managing onboarding and landing page state persistence
class OnboardingService {
  final HiveHelper _hiveHelper;

  OnboardingService(this._hiveHelper);

  /// Check if landing page has been seen
  bool hasSeenLandingPage() {
    return _hiveHelper.readBool(
      key: StorageKey.landingPageSeen,
      defaultValue: false,
    );
  }

  /// Mark landing page as seen
  Future<void> markLandingPageSeen() async {
    await _hiveHelper.writeBool(key: StorageKey.landingPageSeen, value: true);
  }

  /// Check if onboarding has been completed
  bool hasCompletedOnboarding() {
    return _hiveHelper.readBool(
      key: StorageKey.onboardingCompleted,
      defaultValue: false,
    );
  }

  /// Mark onboarding as completed
  Future<void> markOnboardingCompleted() async {
    await _hiveHelper.writeBool(
      key: StorageKey.onboardingCompleted,
      value: true,
    );
  }

  /// Get saved user type
  UserType? getSavedUserType() {
    final userTypeString = _hiveHelper.readString(
      key: StorageKey.selectedUserType,
    );

    if (userTypeString == null) return null;

    try {
      return UserType.values.firstWhere((type) => type.name == userTypeString);
    } catch (e) {
      return null;
    }
  }

  /// Save selected user type
  Future<void> saveUserType(UserType userType) async {
    await _hiveHelper.writeString(
      key: StorageKey.selectedUserType,
      value: userType.name,
    );
  }

  /// Clear all onboarding data (for logout or reset)
  Future<void> clearOnboardingData() async {
    await Future.wait([
      _hiveHelper.delete(key: StorageKey.landingPageSeen),
      _hiveHelper.delete(key: StorageKey.onboardingCompleted),
      _hiveHelper.delete(key: StorageKey.selectedUserType),
    ]);
  }

  /// Reset only onboarding completion (keep user type and landing seen status)
  Future<void> resetOnboardingCompletion() async {
    await _hiveHelper.delete(key: StorageKey.onboardingCompleted);
  }
}

/// Provider for OnboardingService
final onboardingServiceProvider = Provider<OnboardingService>((ref) {
  final hiveHelper = ref.watch(hiveProvider);
  return OnboardingService(hiveHelper);
});
