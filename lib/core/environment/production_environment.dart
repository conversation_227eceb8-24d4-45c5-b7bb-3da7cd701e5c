part of 'environment.dart';

final class _ProductionEnvironment extends _EnvironmentBase {
  @override
  Color get bannerColor => Colors.green;

  @override
  String get bannerName => 'PROD';

  @override
  String get apiBaseUrl => 'https://your-prod-api.com';

  @override
  String get supabaseUrl => 'https://wrevdlggsevlckprjrwm.supabase.co';

  @override
  String get supabaseAnonKey =>
      'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6IndyZXZkbGdnc2V2bGNrcHJqcndtIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDEwMjk3MTcsImV4cCI6MjA1NjYwNTcxN30.KRdBVWPAqDSCsyvYBE3ntrPqpH09KzUmnzbYONAFtzY';

  @override
  BoxName get boxName => BoxName.productionBox;

  @override
  String get firebaseFunctionsUrl => 'https://your-prod-functions.com';

  @override
  bool get enableDioLogs => false;
}
