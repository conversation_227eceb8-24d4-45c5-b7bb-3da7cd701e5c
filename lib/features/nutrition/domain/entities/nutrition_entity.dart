import 'package:equatable/equatable.dart';

import '../../../../shared/domain/entities/media_entity.dart';

/// Meal entity representing a complete meal
class MealEntity extends Equatable {
  final String id;
  final String name;
  final String description;
  final MealType type;
  final List<FoodItemEntity> foodItems;
  final MacroNutrients macros;
  final List<MediaEntity> media;
  final String? recipe;
  final int preparationTimeMinutes;
  final int cookingTimeMinutes;
  final MealDifficulty difficulty;
  final List<String> tags;
  final List<String> allergens;
  final bool isVegetarian;
  final bool isVegan;
  final bool isGlutenFree;
  final bool isDairyFree;
  final double? rating;
  final int ratingsCount;
  final DateTime createdAt;
  final DateTime updatedAt;

  const MealEntity({
    required this.id,
    required this.name,
    required this.description,
    required this.type,
    required this.foodItems,
    required this.macros,
    required this.media,
    this.recipe,
    required this.preparationTimeMinutes,
    required this.cookingTimeMinutes,
    required this.difficulty,
    required this.tags,
    required this.allergens,
    required this.isVegetarian,
    required this.isVegan,
    required this.isGlutenFree,
    required this.isDairyFree,
    this.rating,
    required this.ratingsCount,
    required this.createdAt,
    required this.updatedAt,
  });

  /// Get total preparation and cooking time
  int get totalTimeMinutes => preparationTimeMinutes + cookingTimeMinutes;

  /// Get formatted total time
  String get formattedTotalTime {
    final hours = totalTimeMinutes ~/ 60;
    final minutes = totalTimeMinutes % 60;
    
    if (hours > 0) {
      return '${hours}h ${minutes}m';
    } else {
      return '${minutes}m';
    }
  }

  /// Get dietary restrictions as list
  List<String> get dietaryRestrictions {
    List<String> restrictions = [];
    if (isVegetarian) restrictions.add('Vegetarian');
    if (isVegan) restrictions.add('Vegan');
    if (isGlutenFree) restrictions.add('Gluten-Free');
    if (isDairyFree) restrictions.add('Dairy-Free');
    return restrictions;
  }

  @override
  List<Object?> get props => [
        id,
        name,
        description,
        type,
        foodItems,
        macros,
        media,
        recipe,
        preparationTimeMinutes,
        cookingTimeMinutes,
        difficulty,
        tags,
        allergens,
        isVegetarian,
        isVegan,
        isGlutenFree,
        isDairyFree,
        rating,
        ratingsCount,
        createdAt,
        updatedAt,
      ];
}

/// Food item entity
class FoodItemEntity extends Equatable {
  final String id;
  final String name;
  final String brand;
  final double quantity;
  final String unit;
  final MacroNutrients macrosPerUnit;
  final String? barcode;
  final bool isVerified;

  const FoodItemEntity({
    required this.id,
    required this.name,
    required this.brand,
    required this.quantity,
    required this.unit,
    required this.macrosPerUnit,
    this.barcode,
    required this.isVerified,
  });

  /// Calculate total macros for this food item
  MacroNutrients get totalMacros {
    return MacroNutrients(
      calories: macrosPerUnit.calories * quantity,
      protein: macrosPerUnit.protein * quantity,
      carbs: macrosPerUnit.carbs * quantity,
      fat: macrosPerUnit.fat * quantity,
      fiber: macrosPerUnit.fiber * quantity,
      sugar: macrosPerUnit.sugar * quantity,
      sodium: macrosPerUnit.sodium * quantity,
    );
  }

  @override
  List<Object?> get props => [
        id,
        name,
        brand,
        quantity,
        unit,
        macrosPerUnit,
        barcode,
        isVerified,
      ];
}

/// Macro nutrients entity
class MacroNutrients extends Equatable {
  final double calories;
  final double protein; // grams
  final double carbs; // grams
  final double fat; // grams
  final double fiber; // grams
  final double sugar; // grams
  final double sodium; // milligrams

  const MacroNutrients({
    required this.calories,
    required this.protein,
    required this.carbs,
    required this.fat,
    required this.fiber,
    required this.sugar,
    required this.sodium,
  });

  /// Calculate protein calories (4 cal per gram)
  double get proteinCalories => protein * 4;

  /// Calculate carb calories (4 cal per gram)
  double get carbCalories => carbs * 4;

  /// Calculate fat calories (9 cal per gram)
  double get fatCalories => fat * 9;

  /// Get protein percentage of total calories
  double get proteinPercentage => calories > 0 ? (proteinCalories / calories) * 100 : 0;

  /// Get carb percentage of total calories
  double get carbPercentage => calories > 0 ? (carbCalories / calories) * 100 : 0;

  /// Get fat percentage of total calories
  double get fatPercentage => calories > 0 ? (fatCalories / calories) * 100 : 0;

  /// Add two macro nutrients together
  MacroNutrients operator +(MacroNutrients other) {
    return MacroNutrients(
      calories: calories + other.calories,
      protein: protein + other.protein,
      carbs: carbs + other.carbs,
      fat: fat + other.fat,
      fiber: fiber + other.fiber,
      sugar: sugar + other.sugar,
      sodium: sodium + other.sodium,
    );
  }

  /// Multiply macros by a factor
  MacroNutrients operator *(double factor) {
    return MacroNutrients(
      calories: calories * factor,
      protein: protein * factor,
      carbs: carbs * factor,
      fat: fat * factor,
      fiber: fiber * factor,
      sugar: sugar * factor,
      sodium: sodium * factor,
    );
  }

  @override
  List<Object?> get props => [
        calories,
        protein,
        carbs,
        fat,
        fiber,
        sugar,
        sodium,
      ];
}

/// Supplement entity
class SupplementEntity extends Equatable {
  final String id;
  final String name;
  final String brand;
  final String description;
  final SupplementType type;
  final String dosage;
  final String instructions;
  final List<String> benefits;
  final List<String> sideEffects;
  final List<MediaEntity> media;
  final double? rating;
  final int ratingsCount;
  final bool isPrescriptionRequired;

  const SupplementEntity({
    required this.id,
    required this.name,
    required this.brand,
    required this.description,
    required this.type,
    required this.dosage,
    required this.instructions,
    required this.benefits,
    required this.sideEffects,
    required this.media,
    this.rating,
    required this.ratingsCount,
    required this.isPrescriptionRequired,
  });

  @override
  List<Object?> get props => [
        id,
        name,
        brand,
        description,
        type,
        dosage,
        instructions,
        benefits,
        sideEffects,
        media,
        rating,
        ratingsCount,
        isPrescriptionRequired,
      ];
}

/// Meal type enumeration
enum MealType {
  breakfast,
  lunch,
  dinner,
  snack,
  preworkout,
  postworkout;

  String get displayName {
    switch (this) {
      case MealType.breakfast:
        return 'Breakfast';
      case MealType.lunch:
        return 'Lunch';
      case MealType.dinner:
        return 'Dinner';
      case MealType.snack:
        return 'Snack';
      case MealType.preworkout:
        return 'Pre-Workout';
      case MealType.postworkout:
        return 'Post-Workout';
    }
  }
}

/// Meal difficulty enumeration
enum MealDifficulty {
  easy,
  medium,
  hard;

  String get displayName {
    switch (this) {
      case MealDifficulty.easy:
        return 'Easy';
      case MealDifficulty.medium:
        return 'Medium';
      case MealDifficulty.hard:
        return 'Hard';
    }
  }
}

/// Supplement type enumeration
enum SupplementType {
  protein,
  creatine,
  vitamins,
  minerals,
  preworkout,
  postworkout,
  fatburner,
  other;

  String get displayName {
    switch (this) {
      case SupplementType.protein:
        return 'Protein';
      case SupplementType.creatine:
        return 'Creatine';
      case SupplementType.vitamins:
        return 'Vitamins';
      case SupplementType.minerals:
        return 'Minerals';
      case SupplementType.preworkout:
        return 'Pre-Workout';
      case SupplementType.postworkout:
        return 'Post-Workout';
      case SupplementType.fatburner:
        return 'Fat Burner';
      case SupplementType.other:
        return 'Other';
    }
  }
}
