import 'package:equatable/equatable.dart';

/// AI recommendation entity
class AIRecommendationEntity extends Equatable {
  final String id;
  final String userId;
  final RecommendationType type;
  final String title;
  final String description;
  final Map<String, dynamic> data; // workout plan, meal plan, etc.
  final double confidenceScore;
  final List<String> reasons;
  final RecommendationStatus status;
  final DateTime createdAt;
  final DateTime? acceptedAt;
  final DateTime? rejectedAt;
  final String? userFeedback;

  const AIRecommendationEntity({
    required this.id,
    required this.userId,
    required this.type,
    required this.title,
    required this.description,
    required this.data,
    required this.confidenceScore,
    required this.reasons,
    required this.status,
    required this.createdAt,
    this.acceptedAt,
    this.rejectedAt,
    this.userFeedback,
  });

  /// Check if recommendation is pending
  bool get isPending => status == RecommendationStatus.pending;

  /// Check if recommendation is accepted
  bool get isAccepted => status == RecommendationStatus.accepted;

  /// Check if recommendation is rejected
  bool get isRejected => status == RecommendationStatus.rejected;

  /// Get confidence percentage
  int get confidencePercentage => (confidenceScore * 100).round();

  @override
  List<Object?> get props => [
        id,
        userId,
        type,
        title,
        description,
        data,
        confidenceScore,
        reasons,
        status,
        createdAt,
        acceptedAt,
        rejectedAt,
        userFeedback,
      ];
}

/// AI chat session entity
class AIChatSessionEntity extends Equatable {
  final String id;
  final String userId;
  final String title;
  final ChatType type;
  final List<ChatMessageEntity> messages;
  final SessionStatus status;
  final DateTime createdAt;
  final DateTime updatedAt;
  final DateTime? endedAt;

  const AIChatSessionEntity({
    required this.id,
    required this.userId,
    required this.title,
    required this.type,
    required this.messages,
    required this.status,
    required this.createdAt,
    required this.updatedAt,
    this.endedAt,
  });

  /// Check if session is active
  bool get isActive => status == SessionStatus.active;

  /// Get last message
  ChatMessageEntity? get lastMessage {
    return messages.isNotEmpty ? messages.last : null;
  }

  /// Get message count
  int get messageCount => messages.length;

  @override
  List<Object?> get props => [
        id,
        userId,
        title,
        type,
        messages,
        status,
        createdAt,
        updatedAt,
        endedAt,
      ];
}

/// Chat message entity
class ChatMessageEntity extends Equatable {
  final String id;
  final String sessionId;
  final MessageSender sender;
  final String content;
  final MessageType type;
  final Map<String, dynamic>? metadata;
  final DateTime createdAt;
  final bool isRead;

  const ChatMessageEntity({
    required this.id,
    required this.sessionId,
    required this.sender,
    required this.content,
    required this.type,
    this.metadata,
    required this.createdAt,
    required this.isRead,
  });

  /// Check if message is from user
  bool get isFromUser => sender == MessageSender.user;

  /// Check if message is from AI
  bool get isFromAI => sender == MessageSender.ai;

  @override
  List<Object?> get props => [
        id,
        sessionId,
        sender,
        content,
        type,
        metadata,
        createdAt,
        isRead,
      ];
}

/// AI analysis entity
class AIAnalysisEntity extends Equatable {
  final String id;
  final String userId;
  final AnalysisType type;
  final Map<String, dynamic> inputData;
  final Map<String, dynamic> results;
  final List<String> insights;
  final List<String> recommendations;
  final double accuracyScore;
  final AnalysisStatus status;
  final DateTime createdAt;
  final DateTime? completedAt;

  const AIAnalysisEntity({
    required this.id,
    required this.userId,
    required this.type,
    required this.inputData,
    required this.results,
    required this.insights,
    required this.recommendations,
    required this.accuracyScore,
    required this.status,
    required this.createdAt,
    this.completedAt,
  });

  /// Check if analysis is completed
  bool get isCompleted => status == AnalysisStatus.completed;

  /// Check if analysis is in progress
  bool get isInProgress => status == AnalysisStatus.processing;

  /// Get accuracy percentage
  int get accuracyPercentage => (accuracyScore * 100).round();

  @override
  List<Object?> get props => [
        id,
        userId,
        type,
        inputData,
        results,
        insights,
        recommendations,
        accuracyScore,
        status,
        createdAt,
        completedAt,
      ];
}

/// AI usage tracking entity
class AIUsageEntity extends Equatable {
  final String id;
  final String userId;
  final AIFeatureType featureType;
  final DateTime usedAt;
  final int creditsUsed;
  final Map<String, dynamic>? metadata;

  const AIUsageEntity({
    required this.id,
    required this.userId,
    required this.featureType,
    required this.usedAt,
    required this.creditsUsed,
    this.metadata,
  });

  @override
  List<Object?> get props => [
        id,
        userId,
        featureType,
        usedAt,
        creditsUsed,
        metadata,
      ];
}

/// Recommendation type enumeration
enum RecommendationType {
  workout,
  nutrition,
  recovery,
  goal,
  schedule;

  String get displayName {
    switch (this) {
      case RecommendationType.workout:
        return 'Workout Recommendation';
      case RecommendationType.nutrition:
        return 'Nutrition Recommendation';
      case RecommendationType.recovery:
        return 'Recovery Recommendation';
      case RecommendationType.goal:
        return 'Goal Recommendation';
      case RecommendationType.schedule:
        return 'Schedule Recommendation';
    }
  }
}

/// Recommendation status enumeration
enum RecommendationStatus {
  pending,
  accepted,
  rejected,
  expired;

  String get displayName {
    switch (this) {
      case RecommendationStatus.pending:
        return 'Pending';
      case RecommendationStatus.accepted:
        return 'Accepted';
      case RecommendationStatus.rejected:
        return 'Rejected';
      case RecommendationStatus.expired:
        return 'Expired';
    }
  }
}

/// Chat type enumeration
enum ChatType {
  general,
  workout,
  nutrition,
  motivation,
  technical;

  String get displayName {
    switch (this) {
      case ChatType.general:
        return 'General Chat';
      case ChatType.workout:
        return 'Workout Assistant';
      case ChatType.nutrition:
        return 'Nutrition Coach';
      case ChatType.motivation:
        return 'Motivation Coach';
      case ChatType.technical:
        return 'Technical Support';
    }
  }
}

/// Message sender enumeration
enum MessageSender {
  user,
  ai;

  String get displayName {
    switch (this) {
      case MessageSender.user:
        return 'You';
      case MessageSender.ai:
        return 'AI Coach';
    }
  }
}

/// Message type enumeration
enum MessageType {
  text,
  image,
  workout,
  meal,
  recommendation;

  String get displayName {
    switch (this) {
      case MessageType.text:
        return 'Text';
      case MessageType.image:
        return 'Image';
      case MessageType.workout:
        return 'Workout';
      case MessageType.meal:
        return 'Meal';
      case MessageType.recommendation:
        return 'Recommendation';
    }
  }
}

/// Session status enumeration
enum SessionStatus {
  active,
  paused,
  ended;

  String get displayName {
    switch (this) {
      case SessionStatus.active:
        return 'Active';
      case SessionStatus.paused:
        return 'Paused';
      case SessionStatus.ended:
        return 'Ended';
    }
  }
}

/// Analysis type enumeration
enum AnalysisType {
  progressAnalysis,
  formAnalysis,
  nutritionAnalysis,
  performanceAnalysis,
  injuryRisk;

  String get displayName {
    switch (this) {
      case AnalysisType.progressAnalysis:
        return 'Progress Analysis';
      case AnalysisType.formAnalysis:
        return 'Form Analysis';
      case AnalysisType.nutritionAnalysis:
        return 'Nutrition Analysis';
      case AnalysisType.performanceAnalysis:
        return 'Performance Analysis';
      case AnalysisType.injuryRisk:
        return 'Injury Risk Assessment';
    }
  }
}

/// Analysis status enumeration
enum AnalysisStatus {
  pending,
  processing,
  completed,
  failed;

  String get displayName {
    switch (this) {
      case AnalysisStatus.pending:
        return 'Pending';
      case AnalysisStatus.processing:
        return 'Processing';
      case AnalysisStatus.completed:
        return 'Completed';
      case AnalysisStatus.failed:
        return 'Failed';
    }
  }
}

/// AI feature type enumeration
enum AIFeatureType {
  workoutGeneration,
  mealPlanning,
  progressAnalysis,
  chatAssistant,
  formAnalysis,
  injuryPrevention;

  String get displayName {
    switch (this) {
      case AIFeatureType.workoutGeneration:
        return 'Workout Generation';
      case AIFeatureType.mealPlanning:
        return 'Meal Planning';
      case AIFeatureType.progressAnalysis:
        return 'Progress Analysis';
      case AIFeatureType.chatAssistant:
        return 'Chat Assistant';
      case AIFeatureType.formAnalysis:
        return 'Form Analysis';
      case AIFeatureType.injuryPrevention:
        return 'Injury Prevention';
    }
  }

  int get creditCost {
    switch (this) {
      case AIFeatureType.workoutGeneration:
        return 5;
      case AIFeatureType.mealPlanning:
        return 3;
      case AIFeatureType.progressAnalysis:
        return 2;
      case AIFeatureType.chatAssistant:
        return 1;
      case AIFeatureType.formAnalysis:
        return 10;
      case AIFeatureType.injuryPrevention:
        return 8;
    }
  }
}
