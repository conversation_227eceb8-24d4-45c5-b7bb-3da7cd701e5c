import 'package:equatable/equatable.dart';

import '../../../../shared/domain/entities/media_entity.dart';

/// Social post entity for sharing progress and achievements
class SocialPostEntity extends Equatable {
  final String id;
  final String userId;
  final String userName;
  final String? userAvatarUrl;
  final String content;
  final PostType type;
  final List<MediaEntity> media;
  final Map<String, dynamic>? metadata; // workout data, progress data, etc.
  final int likesCount;
  final int commentsCount;
  final int sharesCount;
  final bool isLikedByCurrentUser;
  final List<String> tags;
  final PostVisibility visibility;
  final DateTime createdAt;
  final DateTime updatedAt;

  const SocialPostEntity({
    required this.id,
    required this.userId,
    required this.userName,
    this.userAvatarUrl,
    required this.content,
    required this.type,
    required this.media,
    this.metadata,
    required this.likesCount,
    required this.commentsCount,
    required this.sharesCount,
    required this.isLikedByCurrentUser,
    required this.tags,
    required this.visibility,
    required this.createdAt,
    required this.updatedAt,
  });

  @override
  List<Object?> get props => [
        id,
        userId,
        userName,
        userAvatarUrl,
        content,
        type,
        media,
        metadata,
        likesCount,
        commentsCount,
        sharesCount,
        isLikedByCurrentUser,
        tags,
        visibility,
        createdAt,
        updatedAt,
      ];
}

/// Comment entity for social posts
class CommentEntity extends Equatable {
  final String id;
  final String postId;
  final String userId;
  final String userName;
  final String? userAvatarUrl;
  final String content;
  final int likesCount;
  final bool isLikedByCurrentUser;
  final String? parentCommentId; // for replies
  final List<CommentEntity> replies;
  final DateTime createdAt;
  final DateTime updatedAt;

  const CommentEntity({
    required this.id,
    required this.postId,
    required this.userId,
    required this.userName,
    this.userAvatarUrl,
    required this.content,
    required this.likesCount,
    required this.isLikedByCurrentUser,
    this.parentCommentId,
    required this.replies,
    required this.createdAt,
    required this.updatedAt,
  });

  /// Check if this is a reply to another comment
  bool get isReply => parentCommentId != null;

  @override
  List<Object?> get props => [
        id,
        postId,
        userId,
        userName,
        userAvatarUrl,
        content,
        likesCount,
        isLikedByCurrentUser,
        parentCommentId,
        replies,
        createdAt,
        updatedAt,
      ];
}

/// Friendship entity
class FriendshipEntity extends Equatable {
  final String id;
  final String userId;
  final String friendId;
  final String friendName;
  final String? friendAvatarUrl;
  final FriendshipStatus status;
  final DateTime createdAt;
  final DateTime? acceptedAt;

  const FriendshipEntity({
    required this.id,
    required this.userId,
    required this.friendId,
    required this.friendName,
    this.friendAvatarUrl,
    required this.status,
    required this.createdAt,
    this.acceptedAt,
  });

  /// Check if friendship is accepted
  bool get isAccepted => status == FriendshipStatus.accepted;

  /// Check if friendship is pending
  bool get isPending => status == FriendshipStatus.pending;

  @override
  List<Object?> get props => [
        id,
        userId,
        friendId,
        friendName,
        friendAvatarUrl,
        status,
        createdAt,
        acceptedAt,
      ];
}

/// Challenge entity for friend challenges
class ChallengeEntity extends Equatable {
  final String id;
  final String creatorId;
  final String creatorName;
  final String? creatorAvatarUrl;
  final String title;
  final String description;
  final ChallengeType type;
  final Map<String, dynamic> rules; // challenge-specific rules
  final DateTime startDate;
  final DateTime endDate;
  final List<ChallengeParticipantEntity> participants;
  final ChallengeStatus status;
  final String? winnerId;
  final Map<String, dynamic>? prize;
  final DateTime createdAt;

  const ChallengeEntity({
    required this.id,
    required this.creatorId,
    required this.creatorName,
    this.creatorAvatarUrl,
    required this.title,
    required this.description,
    required this.type,
    required this.rules,
    required this.startDate,
    required this.endDate,
    required this.participants,
    required this.status,
    this.winnerId,
    this.prize,
    required this.createdAt,
  });

  /// Check if challenge is active
  bool get isActive => status == ChallengeStatus.active;

  /// Check if challenge is completed
  bool get isCompleted => status == ChallengeStatus.completed;

  /// Get challenge duration in days
  int get durationDays => endDate.difference(startDate).inDays;

  @override
  List<Object?> get props => [
        id,
        creatorId,
        creatorName,
        creatorAvatarUrl,
        title,
        description,
        type,
        rules,
        startDate,
        endDate,
        participants,
        status,
        winnerId,
        prize,
        createdAt,
      ];
}

/// Challenge participant entity
class ChallengeParticipantEntity extends Equatable {
  final String id;
  final String challengeId;
  final String userId;
  final String userName;
  final String? userAvatarUrl;
  final DateTime joinedAt;
  final Map<String, dynamic> progress; // challenge-specific progress
  final double score;
  final int rank;
  final bool isCompleted;

  const ChallengeParticipantEntity({
    required this.id,
    required this.challengeId,
    required this.userId,
    required this.userName,
    this.userAvatarUrl,
    required this.joinedAt,
    required this.progress,
    required this.score,
    required this.rank,
    required this.isCompleted,
  });

  @override
  List<Object?> get props => [
        id,
        challengeId,
        userId,
        userName,
        userAvatarUrl,
        joinedAt,
        progress,
        score,
        rank,
        isCompleted,
      ];
}

/// Leaderboard entity
class LeaderboardEntity extends Equatable {
  final String id;
  final LeaderboardType type;
  final LeaderboardPeriod period;
  final List<LeaderboardEntryEntity> entries;
  final DateTime generatedAt;

  const LeaderboardEntity({
    required this.id,
    required this.type,
    required this.period,
    required this.entries,
    required this.generatedAt,
  });

  @override
  List<Object?> get props => [
        id,
        type,
        period,
        entries,
        generatedAt,
      ];
}

/// Leaderboard entry entity
class LeaderboardEntryEntity extends Equatable {
  final String userId;
  final String userName;
  final String? userAvatarUrl;
  final int rank;
  final double score;
  final Map<String, dynamic> metadata;

  const LeaderboardEntryEntity({
    required this.userId,
    required this.userName,
    this.userAvatarUrl,
    required this.rank,
    required this.score,
    required this.metadata,
  });

  @override
  List<Object?> get props => [
        userId,
        userName,
        userAvatarUrl,
        rank,
        score,
        metadata,
      ];
}

/// Post type enumeration
enum PostType {
  workout,
  progress,
  achievement,
  general,
  nutrition;

  String get displayName {
    switch (this) {
      case PostType.workout:
        return 'Workout';
      case PostType.progress:
        return 'Progress';
      case PostType.achievement:
        return 'Achievement';
      case PostType.general:
        return 'General';
      case PostType.nutrition:
        return 'Nutrition';
    }
  }
}

/// Post visibility enumeration
enum PostVisibility {
  public,
  friends,
  private;

  String get displayName {
    switch (this) {
      case PostVisibility.public:
        return 'Public';
      case PostVisibility.friends:
        return 'Friends Only';
      case PostVisibility.private:
        return 'Private';
    }
  }
}

/// Friendship status enumeration
enum FriendshipStatus {
  pending,
  accepted,
  blocked;

  String get displayName {
    switch (this) {
      case FriendshipStatus.pending:
        return 'Pending';
      case FriendshipStatus.accepted:
        return 'Friends';
      case FriendshipStatus.blocked:
        return 'Blocked';
    }
  }
}

/// Challenge type enumeration
enum ChallengeType {
  workoutCount,
  caloriesBurned,
  distanceRun,
  weightLifted,
  streakDays,
  custom;

  String get displayName {
    switch (this) {
      case ChallengeType.workoutCount:
        return 'Workout Count';
      case ChallengeType.caloriesBurned:
        return 'Calories Burned';
      case ChallengeType.distanceRun:
        return 'Distance Run';
      case ChallengeType.weightLifted:
        return 'Weight Lifted';
      case ChallengeType.streakDays:
        return 'Streak Days';
      case ChallengeType.custom:
        return 'Custom';
    }
  }
}

/// Challenge status enumeration
enum ChallengeStatus {
  draft,
  active,
  completed,
  cancelled;

  String get displayName {
    switch (this) {
      case ChallengeStatus.draft:
        return 'Draft';
      case ChallengeStatus.active:
        return 'Active';
      case ChallengeStatus.completed:
        return 'Completed';
      case ChallengeStatus.cancelled:
        return 'Cancelled';
    }
  }
}

/// Leaderboard type enumeration
enum LeaderboardType {
  workouts,
  calories,
  streaks,
  challenges;

  String get displayName {
    switch (this) {
      case LeaderboardType.workouts:
        return 'Workouts';
      case LeaderboardType.calories:
        return 'Calories Burned';
      case LeaderboardType.streaks:
        return 'Workout Streaks';
      case LeaderboardType.challenges:
        return 'Challenges Won';
    }
  }
}

/// Leaderboard period enumeration
enum LeaderboardPeriod {
  daily,
  weekly,
  monthly,
  allTime;

  String get displayName {
    switch (this) {
      case LeaderboardPeriod.daily:
        return 'Today';
      case LeaderboardPeriod.weekly:
        return 'This Week';
      case LeaderboardPeriod.monthly:
        return 'This Month';
      case LeaderboardPeriod.allTime:
        return 'All Time';
    }
  }
}
