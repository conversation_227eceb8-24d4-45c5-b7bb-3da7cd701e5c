import 'package:equatable/equatable.dart';

/// Subscription plan entity
class SubscriptionPlanEntity extends Equatable {
  final String id;
  final String name;
  final String description;
  final SubscriptionTier tier;
  final double price;
  final String currency;
  final SubscriptionPeriod period;
  final List<String> features;
  final bool isPopular;
  final bool isActive;
  final String? discountPercentage;
  final DateTime? discountExpiresAt;
  final Map<String, String> platformProductIds; // iOS, Android product IDs
  final DateTime createdAt;
  final DateTime updatedAt;

  const SubscriptionPlanEntity({
    required this.id,
    required this.name,
    required this.description,
    required this.tier,
    required this.price,
    required this.currency,
    required this.period,
    required this.features,
    required this.isPopular,
    required this.isActive,
    this.discountPercentage,
    this.discountExpiresAt,
    required this.platformProductIds,
    required this.createdAt,
    required this.updatedAt,
  });

  /// Get formatted price
  String get formattedPrice {
    return '$currency ${price.toStringAsFixed(2)}';
  }

  /// Get price per month for comparison
  double get pricePerMonth {
    switch (period) {
      case SubscriptionPeriod.monthly:
        return price;
      case SubscriptionPeriod.quarterly:
        return price / 3;
      case SubscriptionPeriod.yearly:
        return price / 12;
      case SubscriptionPeriod.lifetime:
        return 0; // One-time payment
    }
  }

  /// Check if plan has active discount
  bool get hasActiveDiscount {
    return discountPercentage != null && 
           (discountExpiresAt == null || discountExpiresAt!.isAfter(DateTime.now()));
  }

  @override
  List<Object?> get props => [
        id,
        name,
        description,
        tier,
        price,
        currency,
        period,
        features,
        isPopular,
        isActive,
        discountPercentage,
        discountExpiresAt,
        platformProductIds,
        createdAt,
        updatedAt,
      ];
}

/// User subscription entity
class UserSubscriptionEntity extends Equatable {
  final String id;
  final String userId;
  final String planId;
  final String planName;
  final SubscriptionTier tier;
  final SubscriptionStatus status;
  final DateTime startDate;
  final DateTime? endDate;
  final DateTime? nextBillingDate;
  final bool autoRenew;
  final PaymentMethod paymentMethod;
  final String? platformSubscriptionId;
  final String? platformTransactionId;
  final DateTime createdAt;
  final DateTime updatedAt;

  const UserSubscriptionEntity({
    required this.id,
    required this.userId,
    required this.planId,
    required this.planName,
    required this.tier,
    required this.status,
    required this.startDate,
    this.endDate,
    this.nextBillingDate,
    required this.autoRenew,
    required this.paymentMethod,
    this.platformSubscriptionId,
    this.platformTransactionId,
    required this.createdAt,
    required this.updatedAt,
  });

  /// Check if subscription is active
  bool get isActive => status == SubscriptionStatus.active;

  /// Check if subscription is expired
  bool get isExpired => endDate != null && endDate!.isBefore(DateTime.now());

  /// Get days until expiration
  int? get daysUntilExpiration {
    if (endDate == null) return null;
    return endDate!.difference(DateTime.now()).inDays;
  }

  /// Check if subscription is about to expire (within 7 days)
  bool get isAboutToExpire {
    final days = daysUntilExpiration;
    return days != null && days <= 7 && days > 0;
  }

  @override
  List<Object?> get props => [
        id,
        userId,
        planId,
        planName,
        tier,
        status,
        startDate,
        endDate,
        nextBillingDate,
        autoRenew,
        paymentMethod,
        platformSubscriptionId,
        platformTransactionId,
        createdAt,
        updatedAt,
      ];
}

/// Payment transaction entity
class PaymentTransactionEntity extends Equatable {
  final String id;
  final String userId;
  final String? subscriptionId;
  final TransactionType type;
  final double amount;
  final String currency;
  final TransactionStatus status;
  final PaymentMethod paymentMethod;
  final String? platformTransactionId;
  final String? receiptData;
  final String? failureReason;
  final Map<String, dynamic>? metadata;
  final DateTime createdAt;
  final DateTime? processedAt;

  const PaymentTransactionEntity({
    required this.id,
    required this.userId,
    this.subscriptionId,
    required this.type,
    required this.amount,
    required this.currency,
    required this.status,
    required this.paymentMethod,
    this.platformTransactionId,
    this.receiptData,
    this.failureReason,
    this.metadata,
    required this.createdAt,
    this.processedAt,
  });

  /// Get formatted amount
  String get formattedAmount {
    return '$currency ${amount.toStringAsFixed(2)}';
  }

  /// Check if transaction is successful
  bool get isSuccessful => status == TransactionStatus.completed;

  /// Check if transaction failed
  bool get isFailed => status == TransactionStatus.failed;

  /// Check if transaction is pending
  bool get isPending => status == TransactionStatus.pending;

  @override
  List<Object?> get props => [
        id,
        userId,
        subscriptionId,
        type,
        amount,
        currency,
        status,
        paymentMethod,
        platformTransactionId,
        receiptData,
        failureReason,
        metadata,
        createdAt,
        processedAt,
      ];
}

/// In-app purchase entity
class InAppPurchaseEntity extends Equatable {
  final String id;
  final String name;
  final String description;
  final PurchaseType type;
  final double price;
  final String currency;
  final Map<String, String> platformProductIds;
  final bool isActive;
  final Map<String, dynamic>? metadata;
  final DateTime createdAt;
  final DateTime updatedAt;

  const InAppPurchaseEntity({
    required this.id,
    required this.name,
    required this.description,
    required this.type,
    required this.price,
    required this.currency,
    required this.platformProductIds,
    required this.isActive,
    this.metadata,
    required this.createdAt,
    required this.updatedAt,
  });

  /// Get formatted price
  String get formattedPrice {
    return '$currency ${price.toStringAsFixed(2)}';
  }

  @override
  List<Object?> get props => [
        id,
        name,
        description,
        type,
        price,
        currency,
        platformProductIds,
        isActive,
        metadata,
        createdAt,
        updatedAt,
      ];
}

/// Subscription tier enumeration
enum SubscriptionTier {
  free,
  basic,
  premium,
  pro;

  String get displayName {
    switch (this) {
      case SubscriptionTier.free:
        return 'Free';
      case SubscriptionTier.basic:
        return 'Basic';
      case SubscriptionTier.premium:
        return 'Premium';
      case SubscriptionTier.pro:
        return 'Pro';
    }
  }
}

/// Subscription period enumeration
enum SubscriptionPeriod {
  monthly,
  quarterly,
  yearly,
  lifetime;

  String get displayName {
    switch (this) {
      case SubscriptionPeriod.monthly:
        return 'Monthly';
      case SubscriptionPeriod.quarterly:
        return 'Quarterly';
      case SubscriptionPeriod.yearly:
        return 'Yearly';
      case SubscriptionPeriod.lifetime:
        return 'Lifetime';
    }
  }
}

/// Subscription status enumeration
enum SubscriptionStatus {
  active,
  cancelled,
  expired,
  paused,
  pending;

  String get displayName {
    switch (this) {
      case SubscriptionStatus.active:
        return 'Active';
      case SubscriptionStatus.cancelled:
        return 'Cancelled';
      case SubscriptionStatus.expired:
        return 'Expired';
      case SubscriptionStatus.paused:
        return 'Paused';
      case SubscriptionStatus.pending:
        return 'Pending';
    }
  }
}

/// Payment method enumeration
enum PaymentMethod {
  appStore,
  googlePlay,
  stripe,
  paypal,
  other;

  String get displayName {
    switch (this) {
      case PaymentMethod.appStore:
        return 'App Store';
      case PaymentMethod.googlePlay:
        return 'Google Play';
      case PaymentMethod.stripe:
        return 'Credit Card';
      case PaymentMethod.paypal:
        return 'PayPal';
      case PaymentMethod.other:
        return 'Other';
    }
  }
}

/// Transaction type enumeration
enum TransactionType {
  subscription,
  oneTime,
  refund,
  upgrade,
  downgrade;

  String get displayName {
    switch (this) {
      case TransactionType.subscription:
        return 'Subscription';
      case TransactionType.oneTime:
        return 'One-time Purchase';
      case TransactionType.refund:
        return 'Refund';
      case TransactionType.upgrade:
        return 'Upgrade';
      case TransactionType.downgrade:
        return 'Downgrade';
    }
  }
}

/// Transaction status enumeration
enum TransactionStatus {
  pending,
  completed,
  failed,
  cancelled,
  refunded;

  String get displayName {
    switch (this) {
      case TransactionStatus.pending:
        return 'Pending';
      case TransactionStatus.completed:
        return 'Completed';
      case TransactionStatus.failed:
        return 'Failed';
      case TransactionStatus.cancelled:
        return 'Cancelled';
      case TransactionStatus.refunded:
        return 'Refunded';
    }
  }
}

/// Purchase type enumeration
enum PurchaseType {
  aiCredits,
  premiumWorkouts,
  personalTraining,
  nutritionPlan,
  other;

  String get displayName {
    switch (this) {
      case PurchaseType.aiCredits:
        return 'AI Credits';
      case PurchaseType.premiumWorkouts:
        return 'Premium Workouts';
      case PurchaseType.personalTraining:
        return 'Personal Training';
      case PurchaseType.nutritionPlan:
        return 'Nutrition Plan';
      case PurchaseType.other:
        return 'Other';
    }
  }
}
