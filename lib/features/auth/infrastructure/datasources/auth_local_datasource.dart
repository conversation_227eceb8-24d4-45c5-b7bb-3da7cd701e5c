import '../../../../core/storage/storage_service.dart';
import '../../domain/entities/auth_user_entity.dart';

/// Abstract auth local data source
abstract class AuthLocalDataSource {
  Future<void> cacheUser(AuthUserEntity user);
  Future<AuthUserEntity?> getCachedUser();
  Future<void> clearCachedUser();
  Future<void> cacheToken(String token);
  Future<String?> getCachedToken();
  Future<void> clearCachedToken();
}

/// Implementation of auth local data source using storage service
class AuthLocalDataSourceImpl implements AuthLocalDataSource {
  final StorageService _storageService;
  
  static const String _userKey = 'cached_user';
  static const String _tokenKey = 'cached_token';

  AuthLocalDataSourceImpl(this._storageService);

  @override
  Future<void> cacheUser(AuthUserEntity user) async {
    try {
      await _storageService.setObject(
        _userKey,
        user,
        (user) => _userT<PERSON><PERSON><PERSON>(user),
      );
    } catch (e) {
      throw Exception('Cache user failed: $e');
    }
  }

  @override
  Future<AuthUserEntity?> getCachedUser() async {
    try {
      return _storageService.getObject(
        _userKey,
        (json) => _userFromJson(json),
      );
    } catch (e) {
      throw Exception('Get cached user failed: $e');
    }
  }

  @override
  Future<void> clearCachedUser() async {
    try {
      await _storageService.remove(_userKey);
    } catch (e) {
      throw Exception('Clear cached user failed: $e');
    }
  }

  @override
  Future<void> cacheToken(String token) async {
    try {
      await _storageService.setString(_tokenKey, token);
    } catch (e) {
      throw Exception('Cache token failed: $e');
    }
  }

  @override
  Future<String?> getCachedToken() async {
    try {
      return _storageService.getString(_tokenKey);
    } catch (e) {
      throw Exception('Get cached token failed: $e');
    }
  }

  @override
  Future<void> clearCachedToken() async {
    try {
      await _storageService.remove(_tokenKey);
    } catch (e) {
      throw Exception('Clear cached token failed: $e');
    }
  }

  /// Convert AuthUserEntity to JSON
  Map<String, dynamic> _userToJson(AuthUserEntity user) {
    return {
      'id': user.id,
      'email': user.email,
      'name': user.name,
      'surname': user.surname,
      'phone': user.phone,
      'avatarUrl': user.avatarUrl,
      'dateOfBirth': user.dateOfBirth?.toIso8601String(),
      'gender': user.gender?.name,
      'bio': user.bio,
      'role': user.role.name,
      'subscriptionType': user.subscriptionType.name,
      'subscriptionExpiresAt': user.subscriptionExpiresAt?.toIso8601String(),
      'isEmailVerified': user.isEmailVerified,
      'isPhoneVerified': user.isPhoneVerified,
      'createdAt': user.createdAt.toIso8601String(),
      'updatedAt': user.updatedAt.toIso8601String(),
      'lastLoginAt': user.lastLoginAt?.toIso8601String(),
    };
  }

  /// Convert JSON to AuthUserEntity
  AuthUserEntity _userFromJson(Map<String, dynamic> json) {
    return AuthUserEntity(
      id: json['id'],
      email: json['email'],
      name: json['name'],
      surname: json['surname'],
      phone: json['phone'],
      avatarUrl: json['avatarUrl'],
      dateOfBirth: json['dateOfBirth'] != null 
          ? DateTime.parse(json['dateOfBirth']) 
          : null,
      gender: json['gender'] != null 
          ? Gender.values.firstWhere((e) => e.name == json['gender'])
          : null,
      bio: json['bio'],
      role: UserRole.values.firstWhere((e) => e.name == json['role']),
      subscriptionType: SubscriptionType.values.firstWhere(
        (e) => e.name == json['subscriptionType']
      ),
      subscriptionExpiresAt: json['subscriptionExpiresAt'] != null 
          ? DateTime.parse(json['subscriptionExpiresAt']) 
          : null,
      isEmailVerified: json['isEmailVerified'],
      isPhoneVerified: json['isPhoneVerified'],
      createdAt: DateTime.parse(json['createdAt']),
      updatedAt: DateTime.parse(json['updatedAt']),
      lastLoginAt: json['lastLoginAt'] != null 
          ? DateTime.parse(json['lastLoginAt']) 
          : null,
    );
  }
}
