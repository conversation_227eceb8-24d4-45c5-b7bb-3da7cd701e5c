import 'package:supabase_flutter/supabase_flutter.dart';

import '../../domain/entities/auth_user_entity.dart';

/// Abstract auth remote data source
abstract class AuthRemoteDataSource {
  Future<AuthUserEntity> signInWithEmailAndPassword({
    required String email,
    required String password,
  });

  Future<AuthUserEntity> signUpWithEmailAndPassword({
    required String email,
    required String password,
    String? name,
    String? surname,
    String? phone,
  });

  Future<void> signOut();

  Future<AuthUserEntity?> getCurrentUser();

  Future<void> resetPassword({required String email});

  Future<bool> checkEmailExists(String email);

  Future<bool> checkPhoneExists(String phone);

  Stream<AuthUserEntity?> get authStateChanges;
}

/// Implementation of auth remote data source using Supabase
class AuthRemoteDataSourceImpl implements AuthRemoteDataSource {
  final SupabaseClient _client;

  AuthRemoteDataSourceImpl(this._client);

  @override
  Future<AuthUserEntity> signInWithEmailAndPassword({
    required String email,
    required String password,
  }) async {
    try {
      final response = await _client.auth.signInWithPassword(
        email: email,
        password: password,
      );

      if (response.user == null) {
        throw Exception('Sign in failed');
      }

      return _mapUserToEntity(response.user!);
    } catch (e) {
      throw Exception('Sign in failed: $e');
    }
  }

  @override
  Future<AuthUserEntity> signUpWithEmailAndPassword({
    required String email,
    required String password,
    String? name,
    String? surname,
    String? phone,
  }) async {
    try {
      final response = await _client.auth.signUp(
        email: email,
        password: password,
        data: {
          if (name != null) 'name': name,
          if (surname != null) 'surname': surname,
          if (phone != null) 'phone': phone,
        },
      );

      if (response.user == null) {
        throw Exception('Sign up failed');
      }

      return _mapUserToEntity(response.user!);
    } catch (e) {
      throw Exception('Sign up failed: $e');
    }
  }

  @override
  Future<void> signOut() async {
    try {
      await _client.auth.signOut();
    } catch (e) {
      throw Exception('Sign out failed: $e');
    }
  }

  @override
  Future<AuthUserEntity?> getCurrentUser() async {
    try {
      final user = _client.auth.currentUser;
      return user != null ? _mapUserToEntity(user) : null;
    } catch (e) {
      throw Exception('Get current user failed: $e');
    }
  }

  @override
  Future<void> resetPassword({required String email}) async {
    try {
      await _client.auth.resetPasswordForEmail(email);
    } catch (e) {
      throw Exception('Reset password failed: $e');
    }
  }

  @override
  Future<bool> checkEmailExists(String email) async {
    try {
      final response = await _client.rpc('check_email_exists', params: {
        'email_address': email,
      });
      return response as bool;
    } catch (e) {
      throw Exception('Check email exists failed: $e');
    }
  }

  @override
  Future<bool> checkPhoneExists(String phone) async {
    try {
      final response = await _client.rpc('check_phone_exists', params: {
        'phone_number': phone,
      });
      return response as bool;
    } catch (e) {
      throw Exception('Check phone exists failed: $e');
    }
  }

  @override
  Stream<AuthUserEntity?> get authStateChanges {
    return _client.auth.onAuthStateChange.map((state) {
      final user = state.session?.user;
      return user != null ? _mapUserToEntity(user) : null;
    });
  }

  /// Map Supabase User to AuthUserEntity
  AuthUserEntity _mapUserToEntity(User user) {
    return AuthUserEntity(
      id: user.id,
      email: user.email ?? '',
      name: user.userMetadata?['name'],
      surname: user.userMetadata?['surname'],
      phone: user.userMetadata?['phone'],
      avatarUrl: user.userMetadata?['avatar_url'],
      role: UserRole.student, // Default role
      subscriptionType: SubscriptionType.free, // Default subscription
      isEmailVerified: user.emailConfirmedAt != null,
      isPhoneVerified: user.phoneConfirmedAt != null,
      createdAt: DateTime.parse(user.createdAt),
      updatedAt: DateTime.parse(user.updatedAt ?? user.createdAt),
      lastLoginAt: user.lastSignInAt != null ? DateTime.parse(user.lastSignInAt!) : null,
    );
  }
}
