import 'package:dartz/dartz.dart';

import '../../../../core/error/failures.dart';
import '../../../../core/network/network_service.dart';
import '../../domain/entities/auth_user_entity.dart';
import '../../domain/repositories/auth_repository.dart';
import '../../domain/value_objects/email.dart';
import '../../domain/value_objects/password.dart';
import '../datasources/auth_remote_datasource.dart';
import '../datasources/auth_local_datasource.dart';

/// Implementation of auth repository
class AuthRepositoryImpl implements AuthRepository {
  final AuthRemoteDataSource _remoteDataSource;
  final AuthLocalDataSource _localDataSource;
  final NetworkService _networkService;

  AuthRepositoryImpl({
    required AuthRemoteDataSource remoteDataSource,
    required AuthLocalDataSource localDataSource,
    required NetworkService networkService,
  })  : _remoteDataSource = remoteDataSource,
        _localDataSource = localDataSource,
        _networkService = networkService;

  @override
  Future<Either<Failure, AuthUserEntity>> signInWithEmailAndPassword({
    required Email email,
    required Password password,
  }) async {
    try {
      if (!await _networkService.isConnected) {
        return const Left(NetworkFailure('No internet connection'));
      }

      final user = await _remoteDataSource.signInWithEmailAndPassword(
        email: email.value,
        password: password.value,
      );

      await _localDataSource.cacheUser(user);
      return Right(user);
    } catch (e) {
      return Left(AuthFailure(e.toString()));
    }
  }

  @override
  Future<Either<Failure, AuthUserEntity>> signUpWithEmailAndPassword({
    required Email email,
    required Password password,
    String? name,
    String? surname,
    String? phone,
  }) async {
    try {
      if (!await _networkService.isConnected) {
        return const Left(NetworkFailure('No internet connection'));
      }

      final user = await _remoteDataSource.signUpWithEmailAndPassword(
        email: email.value,
        password: password.value,
        name: name,
        surname: surname,
        phone: phone,
      );

      await _localDataSource.cacheUser(user);
      return Right(user);
    } catch (e) {
      return Left(AuthFailure(e.toString()));
    }
  }

  @override
  Future<Either<Failure, void>> signOut() async {
    try {
      await _remoteDataSource.signOut();
      await _localDataSource.clearCachedUser();
      await _localDataSource.clearCachedToken();
      return const Right(null);
    } catch (e) {
      return Left(AuthFailure(e.toString()));
    }
  }

  @override
  Future<Either<Failure, AuthUserEntity?>> getCurrentUser() async {
    try {
      if (await _networkService.isConnected) {
        final user = await _remoteDataSource.getCurrentUser();
        if (user != null) {
          await _localDataSource.cacheUser(user);
        }
        return Right(user);
      } else {
        final cachedUser = await _localDataSource.getCachedUser();
        return Right(cachedUser);
      }
    } catch (e) {
      return Left(AuthFailure(e.toString()));
    }
  }

  @override
  Future<bool> isAuthenticated() async {
    try {
      final result = await getCurrentUser();
      return result.fold(
        (failure) => false,
        (user) => user != null,
      );
    } catch (e) {
      return false;
    }
  }

  @override
  Future<Either<Failure, void>> resetPassword({
    required Email email,
  }) async {
    try {
      if (!await _networkService.isConnected) {
        return const Left(NetworkFailure('No internet connection'));
      }

      await _remoteDataSource.resetPassword(email: email.value);
      return const Right(null);
    } catch (e) {
      return Left(AuthFailure(e.toString()));
    }
  }

  @override
  Future<Either<Failure, AuthUserEntity>> updateProfile({
    required String userId,
    String? name,
    String? surname,
    String? phone,
    String? avatarUrl,
    DateTime? dateOfBirth,
    String? gender,
    String? bio,
  }) async {
    try {
      if (!await _networkService.isConnected) {
        return const Left(NetworkFailure('No internet connection'));
      }

      // TODO: Implement profile update in remote data source
      throw UnimplementedError('Profile update not implemented yet');
    } catch (e) {
      return Left(AuthFailure(e.toString()));
    }
  }

  @override
  Future<Either<Failure, bool>> checkEmailExists(Email email) async {
    try {
      if (!await _networkService.isConnected) {
        return const Left(NetworkFailure('No internet connection'));
      }

      final exists = await _remoteDataSource.checkEmailExists(email.value);
      return Right(exists);
    } catch (e) {
      return Left(AuthFailure(e.toString()));
    }
  }

  @override
  Future<Either<Failure, bool>> checkPhoneExists(String phone) async {
    try {
      if (!await _networkService.isConnected) {
        return const Left(NetworkFailure('No internet connection'));
      }

      final exists = await _remoteDataSource.checkPhoneExists(phone);
      return Right(exists);
    } catch (e) {
      return Left(AuthFailure(e.toString()));
    }
  }

  @override
  Future<Either<Failure, void>> deleteAccount() async {
    try {
      if (!await _networkService.isConnected) {
        return const Left(NetworkFailure('No internet connection'));
      }

      // TODO: Implement account deletion in remote data source
      throw UnimplementedError('Account deletion not implemented yet');
    } catch (e) {
      return Left(AuthFailure(e.toString()));
    }
  }

  @override
  Stream<AuthUserEntity?> get authStateChanges {
    return _remoteDataSource.authStateChanges;
  }

  @override
  Future<Either<Failure, AuthUserEntity>> refreshUser() async {
    try {
      if (!await _networkService.isConnected) {
        return const Left(NetworkFailure('No internet connection'));
      }

      final user = await _remoteDataSource.getCurrentUser();
      if (user == null) {
        return const Left(AuthFailure('No user found'));
      }

      await _localDataSource.cacheUser(user);
      return Right(user);
    } catch (e) {
      return Left(AuthFailure(e.toString()));
    }
  }
}
