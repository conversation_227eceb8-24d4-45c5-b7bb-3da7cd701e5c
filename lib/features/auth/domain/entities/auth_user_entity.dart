import 'package:equatable/equatable.dart';

/// Authentication user entity
class AuthUserEntity extends Equatable {
  final String id;
  final String email;
  final String? name;
  final String? surname;
  final String? phone;
  final String? avatarUrl;
  final DateTime? dateOfBirth;
  final Gender? gender;
  final String? bio;
  final UserRole role;
  final SubscriptionType subscriptionType;
  final DateTime? subscriptionExpiresAt;
  final bool isEmailVerified;
  final bool isPhoneVerified;
  final DateTime createdAt;
  final DateTime updatedAt;
  final DateTime? lastLoginAt;

  const AuthUserEntity({
    required this.id,
    required this.email,
    this.name,
    this.surname,
    this.phone,
    this.avatarUrl,
    this.dateOfBirth,
    this.gender,
    this.bio,
    required this.role,
    required this.subscriptionType,
    this.subscriptionExpiresAt,
    required this.isEmailVerified,
    required this.isPhoneVerified,
    required this.createdAt,
    required this.updatedAt,
    this.lastLoginAt,
  });

  /// Get full name
  String get fullName {
    if (name == null && surname == null) return email;
    return '${name ?? ''} ${surname ?? ''}'.trim();
  }

  /// Get display name
  String get displayName => name ?? email.split('@').first;

  /// Check if profile is complete
  bool get isProfileComplete {
    return name != null && 
           surname != null && 
           dateOfBirth != null &&
           gender != null;
  }

  /// Check if user has premium subscription
  bool get isPremium {
    return subscriptionType != SubscriptionType.free &&
           (subscriptionExpiresAt == null || subscriptionExpiresAt!.isAfter(DateTime.now()));
  }

  /// Check if user is instructor
  bool get isInstructor => role == UserRole.instructor;

  /// Check if user is student
  bool get isStudent => role == UserRole.student;

  /// Check if user is admin
  bool get isAdmin => role == UserRole.admin;

  /// Get age from date of birth
  int? get age {
    if (dateOfBirth == null) return null;
    final now = DateTime.now();
    int age = now.year - dateOfBirth!.year;
    if (now.month < dateOfBirth!.month || 
        (now.month == dateOfBirth!.month && now.day < dateOfBirth!.day)) {
      age--;
    }
    return age;
  }

  @override
  List<Object?> get props => [
        id,
        email,
        name,
        surname,
        phone,
        avatarUrl,
        dateOfBirth,
        gender,
        bio,
        role,
        subscriptionType,
        subscriptionExpiresAt,
        isEmailVerified,
        isPhoneVerified,
        createdAt,
        updatedAt,
        lastLoginAt,
      ];
}

/// User gender enumeration
enum Gender {
  male,
  female,
  other;

  String get displayName {
    switch (this) {
      case Gender.male:
        return 'Male';
      case Gender.female:
        return 'Female';
      case Gender.other:
        return 'Other';
    }
  }
}

/// User role enumeration
enum UserRole {
  student,
  instructor,
  admin;

  String get displayName {
    switch (this) {
      case UserRole.student:
        return 'Student';
      case UserRole.instructor:
        return 'Instructor';
      case UserRole.admin:
        return 'Admin';
    }
  }
}

/// Subscription type enumeration
enum SubscriptionType {
  free,
  basic,
  premium,
  pro;

  String get displayName {
    switch (this) {
      case SubscriptionType.free:
        return 'Free';
      case SubscriptionType.basic:
        return 'Basic';
      case SubscriptionType.premium:
        return 'Premium';
      case SubscriptionType.pro:
        return 'Pro';
    }
  }

  /// Get subscription features
  List<String> get features {
    switch (this) {
      case SubscriptionType.free:
        return [
          'Basic workout plans',
          'Limited AI recommendations',
          'Basic progress tracking',
        ];
      case SubscriptionType.basic:
        return [
          'All free features',
          'Advanced workout plans',
          'Nutrition tracking',
          'Basic analytics',
        ];
      case SubscriptionType.premium:
        return [
          'All basic features',
          'Unlimited AI recommendations',
          'Advanced analytics',
          'Social features',
          'Custom meal plans',
        ];
      case SubscriptionType.pro:
        return [
          'All premium features',
          'Instructor tools',
          'Course creation',
          'Advanced AI coaching',
          'Priority support',
        ];
    }
  }
}
