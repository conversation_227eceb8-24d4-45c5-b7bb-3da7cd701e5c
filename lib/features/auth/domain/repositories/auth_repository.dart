import 'package:dartz/dartz.dart';

import '../../../../core/error/failures.dart';
import '../entities/auth_user_entity.dart';
import '../value_objects/email.dart';
import '../value_objects/password.dart';

/// Auth repository interface defining the contract for authentication operations
abstract class AuthRepository {
  /// Sign in with email and password
  Future<Either<Failure, AuthUserEntity>> signInWithEmailAndPassword({
    required Email email,
    required Password password,
  });

  /// Sign up with email and password
  Future<Either<Failure, AuthUserEntity>> signUpWithEmailAndPassword({
    required Email email,
    required Password password,
    String? name,
    String? surname,
    String? phone,
  });

  /// Sign out current user
  Future<Either<Failure, void>> signOut();

  /// Get current authenticated user
  Future<Either<Failure, AuthUserEntity?>> getCurrentUser();

  /// Check if user is authenticated
  Future<bool> isAuthenticated();

  /// Reset password
  Future<Either<Failure, void>> resetPassword({required Email email});

  /// Update user profile
  Future<Either<Failure, AuthUserEntity>> updateProfile({
    required String userId,
    String? name,
    String? surname,
    String? phone,
    String? avatarUrl,
    DateTime? dateOfBirth,
    String? gender,
    String? bio,
  });

  /// Check if email exists
  Future<Either<Failure, bool>> checkEmailExists(Email email);

  /// Check if phone exists
  Future<Either<Failure, bool>> checkPhoneExists(String phone);

  /// Delete user account
  Future<Either<Failure, void>> deleteAccount();

  /// Stream of authentication state changes
  Stream<AuthUserEntity?> get authStateChanges;

  /// Refresh current user data
  Future<Either<Failure, AuthUserEntity>> refreshUser();
}
