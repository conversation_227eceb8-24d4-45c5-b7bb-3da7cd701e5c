import 'package:equatable/equatable.dart';

/// Progress entry entity for tracking user progress
class ProgressEntity extends Equatable {
  final String id;
  final String userId;
  final ProgressType type;
  final double value;
  final String unit;
  final DateTime recordedAt;
  final String? notes;
  final Map<String, dynamic>? metadata;

  const ProgressEntity({
    required this.id,
    required this.userId,
    required this.type,
    required this.value,
    required this.unit,
    required this.recordedAt,
    this.notes,
    this.metadata,
  });

  @override
  List<Object?> get props => [
        id,
        userId,
        type,
        value,
        unit,
        recordedAt,
        notes,
        metadata,
      ];
}

/// Workout session entity for tracking completed workouts
class WorkoutSessionEntity extends Equatable {
  final String id;
  final String userId;
  final String workoutId;
  final String workoutTitle;
  final DateTime startedAt;
  final DateTime? completedAt;
  final int durationMinutes;
  final SessionStatus status;
  final List<ExerciseSessionEntity> exercises;
  final int caloriesBurned;
  final double? userRating;
  final String? userNotes;
  final Map<String, dynamic>? metadata;

  const WorkoutSessionEntity({
    required this.id,
    required this.userId,
    required this.workoutId,
    required this.workoutTitle,
    required this.startedAt,
    this.completedAt,
    required this.durationMinutes,
    required this.status,
    required this.exercises,
    required this.caloriesBurned,
    this.userRating,
    this.userNotes,
    this.metadata,
  });

  /// Check if session is completed
  bool get isCompleted => status == SessionStatus.completed;

  /// Get completion percentage
  double get completionPercentage {
    if (exercises.isEmpty) return 0.0;
    final completedExercises = exercises.where((e) => e.isCompleted).length;
    return (completedExercises / exercises.length) * 100;
  }

  /// Get total sets completed
  int get totalSetsCompleted {
    return exercises.fold(0, (sum, exercise) => 
        sum + exercise.sets.where((set) => set.isCompleted).length);
  }

  /// Get total sets planned
  int get totalSetsPlanned {
    return exercises.fold(0, (sum, exercise) => sum + exercise.sets.length);
  }

  @override
  List<Object?> get props => [
        id,
        userId,
        workoutId,
        workoutTitle,
        startedAt,
        completedAt,
        durationMinutes,
        status,
        exercises,
        caloriesBurned,
        userRating,
        userNotes,
        metadata,
      ];
}

/// Exercise session entity within a workout session
class ExerciseSessionEntity extends Equatable {
  final String id;
  final String exerciseId;
  final String exerciseName;
  final List<SetSessionEntity> sets;
  final DateTime startedAt;
  final DateTime? completedAt;
  final bool isCompleted;
  final String? notes;

  const ExerciseSessionEntity({
    required this.id,
    required this.exerciseId,
    required this.exerciseName,
    required this.sets,
    required this.startedAt,
    this.completedAt,
    required this.isCompleted,
    this.notes,
  });

  @override
  List<Object?> get props => [
        id,
        exerciseId,
        exerciseName,
        sets,
        startedAt,
        completedAt,
        isCompleted,
        notes,
      ];
}

/// Set session entity within an exercise session
class SetSessionEntity extends Equatable {
  final String id;
  final int setNumber;
  final int? plannedReps;
  final int? actualReps;
  final double? plannedWeight;
  final double? actualWeight;
  final int? plannedDurationSeconds;
  final int? actualDurationSeconds;
  final double? plannedDistance;
  final double? actualDistance;
  final bool isCompleted;
  final DateTime? completedAt;
  final String? notes;

  const SetSessionEntity({
    required this.id,
    required this.setNumber,
    this.plannedReps,
    this.actualReps,
    this.plannedWeight,
    this.actualWeight,
    this.plannedDurationSeconds,
    this.actualDurationSeconds,
    this.plannedDistance,
    this.actualDistance,
    required this.isCompleted,
    this.completedAt,
    this.notes,
  });

  @override
  List<Object?> get props => [
        id,
        setNumber,
        plannedReps,
        actualReps,
        plannedWeight,
        actualWeight,
        plannedDurationSeconds,
        actualDurationSeconds,
        plannedDistance,
        actualDistance,
        isCompleted,
        completedAt,
        notes,
      ];
}

/// Analytics entity for progress reports
class AnalyticsEntity extends Equatable {
  final String id;
  final String userId;
  final AnalyticsType type;
  final AnalyticsPeriod period;
  final DateTime startDate;
  final DateTime endDate;
  final Map<String, dynamic> data;
  final DateTime generatedAt;

  const AnalyticsEntity({
    required this.id,
    required this.userId,
    required this.type,
    required this.period,
    required this.startDate,
    required this.endDate,
    required this.data,
    required this.generatedAt,
  });

  @override
  List<Object?> get props => [
        id,
        userId,
        type,
        period,
        startDate,
        endDate,
        data,
        generatedAt,
      ];
}

/// Progress type enumeration
enum ProgressType {
  weight,
  bodyFat,
  muscleMass,
  height,
  chest,
  waist,
  hips,
  biceps,
  thighs,
  other;

  String get displayName {
    switch (this) {
      case ProgressType.weight:
        return 'Weight';
      case ProgressType.bodyFat:
        return 'Body Fat %';
      case ProgressType.muscleMass:
        return 'Muscle Mass';
      case ProgressType.height:
        return 'Height';
      case ProgressType.chest:
        return 'Chest';
      case ProgressType.waist:
        return 'Waist';
      case ProgressType.hips:
        return 'Hips';
      case ProgressType.biceps:
        return 'Biceps';
      case ProgressType.thighs:
        return 'Thighs';
      case ProgressType.other:
        return 'Other';
    }
  }

  String get defaultUnit {
    switch (this) {
      case ProgressType.weight:
      case ProgressType.muscleMass:
        return 'kg';
      case ProgressType.bodyFat:
        return '%';
      case ProgressType.height:
      case ProgressType.chest:
      case ProgressType.waist:
      case ProgressType.hips:
      case ProgressType.biceps:
      case ProgressType.thighs:
        return 'cm';
      case ProgressType.other:
        return '';
    }
  }
}

/// Session status enumeration
enum SessionStatus {
  started,
  paused,
  completed,
  cancelled;

  String get displayName {
    switch (this) {
      case SessionStatus.started:
        return 'In Progress';
      case SessionStatus.paused:
        return 'Paused';
      case SessionStatus.completed:
        return 'Completed';
      case SessionStatus.cancelled:
        return 'Cancelled';
    }
  }
}

/// Analytics type enumeration
enum AnalyticsType {
  workout,
  nutrition,
  progress,
  overall;

  String get displayName {
    switch (this) {
      case AnalyticsType.workout:
        return 'Workout Analytics';
      case AnalyticsType.nutrition:
        return 'Nutrition Analytics';
      case AnalyticsType.progress:
        return 'Progress Analytics';
      case AnalyticsType.overall:
        return 'Overall Analytics';
    }
  }
}

/// Analytics period enumeration
enum AnalyticsPeriod {
  daily,
  weekly,
  monthly,
  yearly;

  String get displayName {
    switch (this) {
      case AnalyticsPeriod.daily:
        return 'Daily';
      case AnalyticsPeriod.weekly:
        return 'Weekly';
      case AnalyticsPeriod.monthly:
        return 'Monthly';
      case AnalyticsPeriod.yearly:
        return 'Yearly';
    }
  }
}
