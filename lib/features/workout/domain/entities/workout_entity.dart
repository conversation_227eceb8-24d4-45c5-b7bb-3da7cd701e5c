import 'package:equatable/equatable.dart';

import '../../../../shared/domain/entities/media_entity.dart';

/// Workout entity representing a complete workout session
class WorkoutEntity extends Equatable {
  final String id;
  final String title;
  final String description;
  final String instructorId;
  final String instructorName;
  final WorkoutType type;
  final WorkoutDifficulty difficulty;
  final int estimatedDurationMinutes;
  final int actualDurationMinutes;
  final List<String> targetMuscleGroups;
  final List<String> equipment;
  final List<ExerciseEntity> exercises;
  final MediaEntity? thumbnailMedia;
  final List<MediaEntity> media;
  final WorkoutStatus status;
  final double? rating;
  final int ratingsCount;
  final int enrolledCount;
  final bool isPremium;
  final List<String> tags;
  final DateTime createdAt;
  final DateTime updatedAt;
  final DateTime? publishedAt;

  const WorkoutEntity({
    required this.id,
    required this.title,
    required this.description,
    required this.instructorId,
    required this.instructorName,
    required this.type,
    required this.difficulty,
    required this.estimatedDurationMinutes,
    required this.actualDurationMinutes,
    required this.targetMuscleGroups,
    required this.equipment,
    required this.exercises,
    this.thumbnailMedia,
    required this.media,
    required this.status,
    this.rating,
    required this.ratingsCount,
    required this.enrolledCount,
    required this.isPremium,
    required this.tags,
    required this.createdAt,
    required this.updatedAt,
    this.publishedAt,
  });

  /// Check if workout is published
  bool get isPublished => status == WorkoutStatus.published;

  /// Check if workout is draft
  bool get isDraft => status == WorkoutStatus.draft;

  /// Get formatted duration
  String get formattedDuration {
    final hours = estimatedDurationMinutes ~/ 60;
    final minutes = estimatedDurationMinutes % 60;
    
    if (hours > 0) {
      return '${hours}h ${minutes}m';
    } else {
      return '${minutes}m';
    }
  }

  /// Get total exercises count
  int get totalExercises => exercises.length;

  /// Get total sets count
  int get totalSets => exercises.fold(0, (sum, exercise) => sum + exercise.sets.length);

  @override
  List<Object?> get props => [
        id,
        title,
        description,
        instructorId,
        instructorName,
        type,
        difficulty,
        estimatedDurationMinutes,
        actualDurationMinutes,
        targetMuscleGroups,
        equipment,
        exercises,
        thumbnailMedia,
        media,
        status,
        rating,
        ratingsCount,
        enrolledCount,
        isPremium,
        tags,
        createdAt,
        updatedAt,
        publishedAt,
      ];
}

/// Exercise entity within a workout
class ExerciseEntity extends Equatable {
  final String id;
  final String name;
  final String description;
  final String? instructions;
  final List<SetEntity> sets;
  final MediaEntity? demonstrationVideo;
  final List<MediaEntity> media;
  final int restTimeSeconds;
  final String? notes;
  final List<String> targetMuscles;
  final ExerciseType type;

  const ExerciseEntity({
    required this.id,
    required this.name,
    required this.description,
    this.instructions,
    required this.sets,
    this.demonstrationVideo,
    required this.media,
    required this.restTimeSeconds,
    this.notes,
    required this.targetMuscles,
    required this.type,
  });

  /// Get formatted rest time
  String get formattedRestTime {
    final minutes = restTimeSeconds ~/ 60;
    final seconds = restTimeSeconds % 60;
    
    if (minutes > 0) {
      return '${minutes}m ${seconds}s';
    } else {
      return '${seconds}s';
    }
  }

  @override
  List<Object?> get props => [
        id,
        name,
        description,
        instructions,
        sets,
        demonstrationVideo,
        media,
        restTimeSeconds,
        notes,
        targetMuscles,
        type,
      ];
}

/// Set entity representing a single set in an exercise
class SetEntity extends Equatable {
  final String id;
  final int setNumber;
  final SetType type;
  final int? reps;
  final double? weight;
  final int? durationSeconds;
  final double? distance;
  final String? notes;
  final bool isCompleted;

  const SetEntity({
    required this.id,
    required this.setNumber,
    required this.type,
    this.reps,
    this.weight,
    this.durationSeconds,
    this.distance,
    this.notes,
    required this.isCompleted,
  });

  @override
  List<Object?> get props => [
        id,
        setNumber,
        type,
        reps,
        weight,
        durationSeconds,
        distance,
        notes,
        isCompleted,
      ];
}

/// Workout type enumeration
enum WorkoutType {
  strength,
  cardio,
  flexibility,
  hiit,
  yoga,
  pilates,
  crossfit,
  bodyweight,
  powerlifting,
  olympic;

  String get displayName {
    switch (this) {
      case WorkoutType.strength:
        return 'Strength Training';
      case WorkoutType.cardio:
        return 'Cardio';
      case WorkoutType.flexibility:
        return 'Flexibility';
      case WorkoutType.hiit:
        return 'HIIT';
      case WorkoutType.yoga:
        return 'Yoga';
      case WorkoutType.pilates:
        return 'Pilates';
      case WorkoutType.crossfit:
        return 'CrossFit';
      case WorkoutType.bodyweight:
        return 'Bodyweight';
      case WorkoutType.powerlifting:
        return 'Powerlifting';
      case WorkoutType.olympic:
        return 'Olympic Lifting';
    }
  }
}

/// Workout difficulty enumeration
enum WorkoutDifficulty {
  beginner,
  intermediate,
  advanced,
  expert;

  String get displayName {
    switch (this) {
      case WorkoutDifficulty.beginner:
        return 'Beginner';
      case WorkoutDifficulty.intermediate:
        return 'Intermediate';
      case WorkoutDifficulty.advanced:
        return 'Advanced';
      case WorkoutDifficulty.expert:
        return 'Expert';
    }
  }
}

/// Workout status enumeration
enum WorkoutStatus {
  draft,
  published,
  archived;

  String get displayName {
    switch (this) {
      case WorkoutStatus.draft:
        return 'Draft';
      case WorkoutStatus.published:
        return 'Published';
      case WorkoutStatus.archived:
        return 'Archived';
    }
  }
}

/// Exercise type enumeration
enum ExerciseType {
  reps,
  time,
  distance;

  String get displayName {
    switch (this) {
      case ExerciseType.reps:
        return 'Repetitions';
      case ExerciseType.time:
        return 'Time-based';
      case ExerciseType.distance:
        return 'Distance-based';
    }
  }
}

/// Set type enumeration
enum SetType {
  normal,
  warmup,
  dropset,
  superset,
  pyramid,
  rest;

  String get displayName {
    switch (this) {
      case SetType.normal:
        return 'Normal';
      case SetType.warmup:
        return 'Warm-up';
      case SetType.dropset:
        return 'Drop Set';
      case SetType.superset:
        return 'Super Set';
      case SetType.pyramid:
        return 'Pyramid';
      case SetType.rest:
        return 'Rest';
    }
  }
}
