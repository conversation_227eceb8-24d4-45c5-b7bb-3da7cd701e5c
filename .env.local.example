# 🔐 Environment Configuration for E2E Tests
# Copy this file to .env.local and fill in your actual values

# Supabase Configuration
SUPABASE_URL=https://your-project-id.supabase.co
SUPABASE_ANON_KEY=your_anon_key_here
SUPABASE_SERVICE_ROLE_KEY=your_service_role_key_here

# Test Configuration
FLUTTER_TEST_ENV=local
FLUTTER_TEST_CLEANUP=true

# Optional: Test User Configuration (if you want to use specific test users)
TEST_INSTRUCTOR_EMAIL=<EMAIL>
TEST_STUDENT_EMAIL=<EMAIL>

# Optional: Browser Configuration for Web Tests
CHROME_ARGS=--disable-web-security --disable-features=VizDisplayCompositor
FIREFOX_ARGS=--disable-web-security

# Optional: Android Emulator Configuration
ANDROID_EMULATOR_NAME=Pixel_9_Pro
ANDROID_API_LEVEL=33

# Optional: Test Timeouts (in seconds)
TEST_TIMEOUT=300
WIDGET_TIMEOUT=30
NETWORK_TIMEOUT=60
