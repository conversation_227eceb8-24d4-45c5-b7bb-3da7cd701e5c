import 'dart:io';

/// Configuration helper for E2E tests
class E2ETestConfig {
  static const String _defaultSupabaseUrl = 'https://wrevdlggsevlckprjrwm.supabase.co';
  static const String _defaultAnonKey = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6IndyZXZkbGdnc2V2bGNrcHJqcndtIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDEwMjk3MTcsImV4cCI6MjA1NjYwNTcxN30.KRdBVWPAqDSCsyvYBE3ntrPqpH09KzUmnzbYONAFtzY';

  /// Get Supabase URL from environment or use default
  static String get supabaseUrl => 
    Platform.environment['SUPABASE_URL'] ?? _defaultSupabaseUrl;

  /// Get Supabase anonymous key from environment or use default
  static String get supabaseAnonKey => 
    Platform.environment['SUPABASE_ANON_KEY'] ?? _defaultAnonKey;

  /// Get Supabase service role key from environment (for E2E tests)
  static String get supabaseServiceRoleKey => 
    Platform.environment['SUPABASE_SERVICE_ROLE_KEY'] ?? '';

  /// Get the appropriate Supabase key for testing
  /// Uses service role key if available (for E2E tests), otherwise anon key
  static String get supabaseKey => 
    supabaseServiceRoleKey.isNotEmpty ? supabaseServiceRoleKey : supabaseAnonKey;

  /// Check if we're using service role key
  static bool get isUsingServiceRoleKey => supabaseServiceRoleKey.isNotEmpty;

  /// Get test environment (local, staging, production)
  static String get testEnvironment => 
    Platform.environment['FLUTTER_TEST_ENV'] ?? 'local';

  /// Check if cleanup should be performed after tests
  static bool get shouldCleanup => 
    Platform.environment['FLUTTER_TEST_CLEANUP']?.toLowerCase() != 'false';

  /// Get test timeout in seconds
  static int get testTimeout => 
    int.tryParse(Platform.environment['TEST_TIMEOUT'] ?? '300') ?? 300;

  /// Get widget timeout in seconds
  static int get widgetTimeout => 
    int.tryParse(Platform.environment['WIDGET_TIMEOUT'] ?? '30') ?? 30;

  /// Get network timeout in seconds
  static int get networkTimeout => 
    int.tryParse(Platform.environment['NETWORK_TIMEOUT'] ?? '60') ?? 60;

  /// Check if we're running in GitHub Actions
  static bool get isGitHubActions => 
    Platform.environment['GITHUB_ACTIONS'] == 'true';

  /// Check if we're running in CI environment
  static bool get isCIEnvironment => 
    isGitHubActions || Platform.environment['CI'] == 'true';

  /// Get test user email for instructor
  static String get testInstructorEmail => 
    Platform.environment['TEST_INSTRUCTOR_EMAIL'] ?? 
    'test-instructor-${DateTime.now().millisecondsSinceEpoch}@e2etest.com';

  /// Get test user email for student
  static String get testStudentEmail => 
    Platform.environment['TEST_STUDENT_EMAIL'] ?? 
    'test-student-${DateTime.now().millisecondsSinceEpoch}@e2etest.com';

  /// Print configuration summary
  static void printConfig() {
    print('🔧 E2E Test Configuration:');
    print('  Environment: $testEnvironment');
    print('  Supabase URL: $supabaseUrl');
    print('  Using Service Role Key: $isUsingServiceRoleKey');
    print('  CI Environment: $isCIEnvironment');
    print('  GitHub Actions: $isGitHubActions');
    print('  Cleanup Enabled: $shouldCleanup');
    print('  Test Timeout: ${testTimeout}s');
    print('  Widget Timeout: ${widgetTimeout}s');
    print('  Network Timeout: ${networkTimeout}s');
    print('  Instructor Email: $testInstructorEmail');
    print('  Student Email: $testStudentEmail');
  }

  /// Validate configuration
  static void validateConfig() {
    final errors = <String>[];

    if (supabaseUrl.isEmpty) {
      errors.add('SUPABASE_URL is required');
    }

    if (supabaseAnonKey.isEmpty) {
      errors.add('SUPABASE_ANON_KEY is required');
    }

    if (isCIEnvironment && supabaseServiceRoleKey.isEmpty) {
      errors.add('SUPABASE_SERVICE_ROLE_KEY is required for CI environment');
    }

    if (errors.isNotEmpty) {
      throw Exception('Configuration validation failed:\n${errors.join('\n')}');
    }
  }

  /// Get timeout duration for widgets
  static Duration get widgetTimeoutDuration => 
    Duration(seconds: widgetTimeout);

  /// Get timeout duration for network requests
  static Duration get networkTimeoutDuration => 
    Duration(seconds: networkTimeout);

  /// Get timeout duration for tests
  static Duration get testTimeoutDuration => 
    Duration(seconds: testTimeout);
}
