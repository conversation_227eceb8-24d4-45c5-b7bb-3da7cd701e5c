# FitGo App - Development Makefile
# Provides convenient commands for common development tasks

.PHONY: all setup clean build-runner test analyze format run-dev run-prod build-android build-ios build-web help

all: lint format build_runner

# Enhanced help with FitGo-specific commands
help: ## Show this help dialog
	@echo "FitGo App Development Commands:"
	@echo ""
	@echo "Setup & Dependencies:"
	@echo "  make setup          - Initial project setup with Puro"
	@echo "  make setup-flutter  - Setup with standard Flutter"
	@echo "  make clean          - Clean build artifacts"
	@echo "  make upgrade        - Upgrade dependencies"
	@echo ""
	@echo "Code Generation:"
	@echo "  make build-runner   - Generate code (freezed, json_annotation)"
	@echo "  make build_format   - Generate code and format"
	@echo "  make l10n          - Generate localization files"
	@echo "  make icons         - Generate app icons"
	@echo ""
	@echo "Development:"
	@echo "  make run-dev       - Run app in development mode"
	@echo "  make run-prod      - Run app in production mode"
	@echo "  make test          - Run all tests (alias: run_unit)"
	@echo "  make analyze       - Run static analysis (alias: lint)"
	@echo "  make format        - Format code"
	@echo ""
	@echo "Build:"
	@echo "  make build-android - Build Android APK"
	@echo "  make build-ios     - Build iOS app"
	@echo "  make build-web     - Build web app"
	@echo ""
	@echo "Legacy Commands:"
	@echo "  make run_unit      - Run unit tests (legacy)"
	@echo "  make lint          - Lint code (legacy)"

# Setup commands
setup: ## Initial project setup with Puro
	@echo "╠ Setting up FitGo with Puro..."
	puro upgrade stable
	puro use stable
	puro pub get
	@echo "╠ Running code generation..."
	puro pub run build_runner build --delete-conflicting-outputs
	@echo "╠ Setup complete!"

setup-flutter: ## Setup with standard Flutter
	@echo "╠ Setting up FitGo with Flutter..."
	flutter pub get
	@echo "╠ Running code generation..."
	flutter packages pub run build_runner build --delete-conflicting-outputs
	@echo "╠ Setup complete!"

# Development commands
run-dev: ## Run app in development mode
	@echo "╠ Running FitGo in development mode..."
	flutter run --dart-define envConfig=development

run-prod: ## Run app in production mode
	@echo "╠ Running FitGo in production mode..."
	flutter run --dart-define envConfig=production

test: ## Run all tests
	@echo "╠ Running all tests..."
	@flutter test || (echo "Error while running tests"; exit 1)

run_unit: ## Runs unit tests (legacy)
	@echo "╠ Running the tests"
	@flutter test || (echo "Error while running tests"; exit 1)

clean: ## Cleans the environment
	@echo "╠ Cleaning the project..."
	@rm -rf pubspec.lock
	@flutter clean

format: ## Formats the code
	@echo "╠ Formatting the code"
	@dart format .

analyze: ## Run static analysis
	@echo "╠ Running static analysis..."
	@dart analyze . || (echo "Error in project"; exit 1)

lint: ## Lints the code (legacy)
	@echo "╠ Verifying code..."
	@dart analyze . || (echo "Error in project"; exit 1)

upgrade: clean ## Upgrades dependencies
	@echo "╠ Upgrading dependencies..."
	@flutter pub upgrade

# Code generation
l10n: ## Generate localization files
	@echo "╠ Generating localization files..."
	flutter gen-l10n

icons: ## Generate app icons
	@echo "╠ Generating app icons..."
	flutter packages pub run flutter_launcher_icons:main

build_format: ## Generate codes and format
	@echo "╠ Running build to generate files"
	@dart run build_runner build -d
	@make format

build-runner: ## Generate code (freezed, json_annotation)
	@echo "╠ Running build_runner..."
	flutter packages pub run build_runner build --delete-conflicting-outputs

build_runner: ## Generate codes for freezed and json serialize (legacy)
	@echo "╠ Running build to generate files"
	@dart run build_runner watch build -d

# Build commands
build-android: ## Build Android APK
	@echo "╠ Building Android APK..."
	flutter build apk --release --dart-define envConfig=production

build-android-bundle: ## Build Android App Bundle
	@echo "╠ Building Android App Bundle..."
	flutter build appbundle --release --dart-define envConfig=production

build-ios: ## Build iOS app
	@echo "╠ Building iOS app..."
	flutter build ios --release --dart-define envConfig=production

build-web: ## Build web app
	@echo "╠ Building web app..."
	flutter build web --dart-define envConfig=production --release

# Legacy build commands
build_dev_apk: ## Build development APK (legacy)
	@echo "╠  Building the app"
	@echo "╠  flavor = development"
	@flutter build apk --release --flavor development -t lib/main.dart

build_prod: ## Build production bundle (legacy)
	@echo "╠  Building the app"
	@echo "╠  flavor = production"
	@flutter build appbundle --release --flavor production -t lib/main.dart

# Utility commands
delete_locale_branches: ## Remove local branches except main/development/production
	@echo "╠  remove all local branches except main|development|production branches"
	@git branch | egrep -v "(^\*|main|development|production)" | xargs git branch -D

deps: ## Check dependencies
	@echo "╠ Checking dependencies..."
	flutter pub deps

outdated: ## Check for outdated packages
	@echo "╠ Checking for outdated packages..."
	flutter pub outdated

# Firebase deployment (if needed)
deploy_firebase: ## Deploy web app to Firebase
	@echo "╠ Deploying to Firebase"
	@firebase deploy --only hosting

deploy_web: ## Build and deploy web app to Firebase
	@make build-web
	@make deploy_firebase

# Development workflow
dev-setup: setup build-runner l10n ## Complete development setup
	@echo "╠ Complete development setup finished!"

full-clean: clean setup build-runner ## Full clean and setup
	@echo "╠ Full clean and setup complete!"