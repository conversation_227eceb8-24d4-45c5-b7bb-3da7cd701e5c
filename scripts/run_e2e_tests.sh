#!/bin/bash

# 🚀 Local E2E Test Runner for FitGo App
# This script helps you run E2E tests locally with different configurations

set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Default values
DEVICE_TYPE="chrome"
ENVIRONMENT="local"
VERBOSE=false
CLEANUP=true
HEADLESS=false

# Function to print colored output
print_status() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Function to show usage
show_usage() {
    cat << EOF
🚀 FitGo E2E Test Runner

Usage: $0 [OPTIONS]

OPTIONS:
    -d, --device DEVICE     Device type: chrome, firefox, android, ios (default: chrome)
    -e, --env ENV          Environment: local, staging, production (default: local)
    -v, --verbose          Enable verbose output
    -h, --headless         Run in headless mode (web only)
    -n, --no-cleanup       Skip test data cleanup
    --help                 Show this help message

EXAMPLES:
    # Run on Chrome (default)
    $0

    # Run on Android emulator
    $0 -d android

    # Run on Firefox with verbose output
    $0 -d firefox -v

    # Run in headless Chrome
    $0 -d chrome -h

    # Run without cleanup (for debugging)
    $0 -n

ENVIRONMENT SETUP:
    Create a .env.local file in the project root with:
    SUPABASE_URL=your_supabase_url
    SUPABASE_ANON_KEY=your_anon_key
    SUPABASE_SERVICE_ROLE_KEY=your_service_role_key

EOF
}

# Parse command line arguments
while [[ $# -gt 0 ]]; do
    case $1 in
        -d|--device)
            DEVICE_TYPE="$2"
            shift 2
            ;;
        -e|--env)
            ENVIRONMENT="$2"
            shift 2
            ;;
        -v|--verbose)
            VERBOSE=true
            shift
            ;;
        -h|--headless)
            HEADLESS=true
            shift
            ;;
        -n|--no-cleanup)
            CLEANUP=false
            shift
            ;;
        --help)
            show_usage
            exit 0
            ;;
        *)
            print_error "Unknown option: $1"
            show_usage
            exit 1
            ;;
    esac
done

# Function to load environment variables
load_env() {
    if [[ -f ".env.local" ]]; then
        print_status "Loading environment variables from .env.local"
        export $(cat .env.local | grep -v '^#' | xargs)
    else
        print_warning ".env.local file not found. Using default/system environment variables."
    fi
}

# Function to check prerequisites
check_prerequisites() {
    print_status "Checking prerequisites..."
    
    # Check Flutter
    if ! command -v flutter &> /dev/null; then
        print_error "Flutter is not installed or not in PATH"
        exit 1
    fi
    
    # Check Flutter doctor
    print_status "Running Flutter doctor..."
    flutter doctor
    
    # Check if integration test exists
    if [[ ! -f "integration_test/e2e_user_journey_test.dart" ]]; then
        print_error "E2E test file not found: integration_test/e2e_user_journey_test.dart"
        exit 1
    fi
    
    print_success "Prerequisites check passed"
}

# Function to setup device
setup_device() {
    case $DEVICE_TYPE in
        chrome|firefox)
            print_status "Setting up web testing for $DEVICE_TYPE"
            if [[ "$HEADLESS" == true ]]; then
                export CHROME_ARGS="--headless --disable-gpu --no-sandbox"
                export FIREFOX_ARGS="--headless"
            fi
            ;;
        android)
            print_status "Setting up Android testing"
            # Check for running emulators
            RUNNING_EMULATORS=$(flutter devices | grep -c "android" || true)
            if [[ $RUNNING_EMULATORS -eq 0 ]]; then
                print_warning "No Android devices/emulators found"
                print_status "Available emulators:"
                flutter emulators
                print_status "Please start an emulator or connect a device"
                exit 1
            fi
            ;;
        ios)
            print_status "Setting up iOS testing"
            if [[ "$OSTYPE" != "darwin"* ]]; then
                print_error "iOS testing is only available on macOS"
                exit 1
            fi
            # Check for running simulators
            RUNNING_SIMULATORS=$(flutter devices | grep -c "ios" || true)
            if [[ $RUNNING_SIMULATORS -eq 0 ]]; then
                print_warning "No iOS simulators found"
                print_status "Please start an iOS simulator"
                exit 1
            fi
            ;;
        *)
            print_error "Unsupported device type: $DEVICE_TYPE"
            exit 1
            ;;
    esac
}

# Function to run tests
run_tests() {
    print_status "Starting E2E tests on $DEVICE_TYPE..."
    
    # Set environment variables
    export FLUTTER_TEST_ENV="$ENVIRONMENT"
    export FLUTTER_TEST_CLEANUP="$CLEANUP"
    
    # Build test command
    TEST_CMD="flutter test integration_test/e2e_user_journey_test.dart"
    
    if [[ "$DEVICE_TYPE" != "android" && "$DEVICE_TYPE" != "ios" ]]; then
        TEST_CMD="$TEST_CMD -d $DEVICE_TYPE"
    fi
    
    if [[ "$VERBOSE" == true ]]; then
        TEST_CMD="$TEST_CMD --verbose"
    fi
    
    # Add reporter for better output
    TEST_CMD="$TEST_CMD --reporter=compact"
    
    print_status "Running command: $TEST_CMD"
    
    # Run the tests
    if eval $TEST_CMD; then
        print_success "E2E tests completed successfully! 🎉"
    else
        print_error "E2E tests failed! ❌"
        exit 1
    fi
}

# Function to cleanup
cleanup_tests() {
    if [[ "$CLEANUP" == true ]]; then
        print_status "Cleaning up test data..."
        # Add any additional cleanup logic here
        print_success "Cleanup completed"
    else
        print_warning "Skipping cleanup (test data preserved for debugging)"
    fi
}

# Main execution
main() {
    print_status "🚀 Starting FitGo E2E Test Runner"
    print_status "Device: $DEVICE_TYPE | Environment: $ENVIRONMENT | Verbose: $VERBOSE"
    
    load_env
    check_prerequisites
    setup_device
    run_tests
    cleanup_tests
    
    print_success "🎉 E2E test execution completed!"
}

# Run main function
main "$@"
