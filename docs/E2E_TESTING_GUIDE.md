# 🚀 E2E Testing Guide for FitGo App

This guide covers how to set up and run End-to-End (E2E) tests for the FitGo application both locally and in GitHub Actions.

## 📋 Table of Contents

- [Overview](#overview)
- [Prerequisites](#prerequisites)
- [Local Setup](#local-setup)
- [Running Tests Locally](#running-tests-locally)
- [GitHub Actions Setup](#github-actions-setup)
- [Test Configuration](#test-configuration)
- [Troubleshooting](#troubleshooting)
- [Best Practices](#best-practices)

## 🎯 Overview

Our E2E tests validate the complete user journey from registration to feature usage, covering:

### 👨‍🏫 Instructor Flow (19 Phases)
1. **Registration & Profile Setup** - Complete account creation
2. **Work History & Certifications** - Professional background
3. **Pricing Configuration** - Service pricing setup
4. **Approval Workflow** - Rejection → Resubmission → Approval
5. **Template Creation** - Workout and nutrition templates
6. **Student Management** - Template assignment scenarios

### 👨‍🎓 Student Flow (15 Phases)
1. **Registration & Profile Setup** - Account creation
2. **Instructor Selection & Enrollment** - Browse and select instructors
3. **Plan Assignment & Usage** - Access assigned plans
4. **Workout Session Completion** - Complete workout flows
5. **Nutrition Logging** - Food tracking features
6. **Progress Tracking** - Photo uploads and measurements

## 🔧 Prerequisites

### Required Software
- **Flutter SDK** (3.32.7 or later)
- **Dart SDK** (included with Flutter)
- **Git**

### For Android Testing
- **Android Studio** with Android SDK
- **Android Emulator** or physical device
- **Java 17** (for Android builds)

### For Web Testing
- **Chrome** and/or **Firefox** browser
- **Web drivers** (automatically managed by Flutter)

### For iOS Testing (macOS only)
- **Xcode** (latest version)
- **iOS Simulator** or physical device
- **CocoaPods**

## 🏠 Local Setup

### 1. Clone and Setup Project
```bash
git clone https://github.com/FitGo-Co/fitgo_app.git
cd fitgo_app
flutter pub get
dart run build_runner build --delete-conflicting-outputs
```

### 2. Environment Configuration
Create a `.env.local` file in the project root:

```bash
cp .env.local.example .env.local
```

Edit `.env.local` with your actual values:
```env
# Supabase Configuration
SUPABASE_URL=https://your-project-id.supabase.co
SUPABASE_ANON_KEY=your_anon_key_here
SUPABASE_SERVICE_ROLE_KEY=your_service_role_key_here

# Test Configuration
FLUTTER_TEST_ENV=local
FLUTTER_TEST_CLEANUP=true
```

### 3. Verify Setup
```bash
flutter doctor -v
flutter devices
```

## 🧪 Running Tests Locally

### Using the Test Runner Script (Recommended)

```bash
# Run on Chrome (default)
./scripts/run_e2e_tests.sh

# Run on Android emulator
./scripts/run_e2e_tests.sh -d android

# Run on Firefox with verbose output
./scripts/run_e2e_tests.sh -d firefox -v

# Run in headless Chrome
./scripts/run_e2e_tests.sh -d chrome -h

# Run without cleanup (for debugging)
./scripts/run_e2e_tests.sh -n
```

### Manual Test Execution

```bash
# Web (Chrome)
flutter test integration_test/e2e_user_journey_test.dart -d chrome

# Web (Firefox)
flutter test integration_test/e2e_user_journey_test.dart -d firefox

# Android (with emulator running)
flutter test integration_test/e2e_user_journey_test.dart

# With environment variables
SUPABASE_SERVICE_ROLE_KEY=your_key flutter test integration_test/e2e_user_journey_test.dart -d chrome
```

## 🤖 GitHub Actions Setup

### 1. Required Secrets

Add these secrets to your GitHub repository:

1. Go to **Settings** → **Secrets and variables** → **Actions**
2. Add the following secrets:

```
SUPABASE_URL=https://your-project-id.supabase.co
SUPABASE_ANON_KEY=your_anon_key_here
SUPABASE_SERVICE_ROLE_KEY=your_service_role_key_here
```

### 2. Workflow Configuration

The E2E workflow (`.github/workflows/e2e-tests.yml`) runs:

- **Automatically**: Every night at 2 AM UTC
- **On Push**: To `main` or `development` branches
- **On PR**: To `main` branch
- **Manually**: Via workflow dispatch

### 3. Workflow Features

- **Multi-platform**: Android (API 30, 33) and Web (Chrome, Firefox)
- **Parallel execution**: Tests run simultaneously on different platforms
- **Artifact collection**: Screenshots and logs on failure
- **Test reports**: Detailed reports with results summary
- **PR comments**: Automatic test result comments on pull requests

## ⚙️ Test Configuration

### Environment Variables

| Variable | Description | Default |
|----------|-------------|---------|
| `SUPABASE_URL` | Supabase project URL | Required |
| `SUPABASE_ANON_KEY` | Anonymous key | Required |
| `SUPABASE_SERVICE_ROLE_KEY` | Service role key (for E2E) | Required in CI |
| `FLUTTER_TEST_ENV` | Test environment | `local` |
| `FLUTTER_TEST_CLEANUP` | Enable cleanup | `true` |
| `TEST_TIMEOUT` | Test timeout (seconds) | `300` |
| `WIDGET_TIMEOUT` | Widget timeout (seconds) | `30` |
| `NETWORK_TIMEOUT` | Network timeout (seconds) | `60` |

### Test Configuration Helper

The `E2ETestConfig` class provides centralized configuration management:

```dart
// Get configuration values
final url = E2ETestConfig.supabaseUrl;
final key = E2ETestConfig.supabaseKey;
final isCI = E2ETestConfig.isCIEnvironment;

// Validate configuration
E2ETestConfig.validateConfig();

// Print configuration summary
E2ETestConfig.printConfig();
```

## 🔧 Troubleshooting

### Common Issues

#### 1. Plugin Errors (Local)
```
MissingPluginException: No implementation found for method...
```
**Solution**: E2E tests need real devices/emulators. Use web testing for quick local testing.

#### 2. Supabase Connection Errors
```
SocketException: Failed host lookup
```
**Solution**: Check your internet connection and Supabase URL.

#### 3. RLS Policy Errors
```
Row Level Security policy violation
```
**Solution**: Ensure `SUPABASE_SERVICE_ROLE_KEY` is set correctly.

#### 4. Android Emulator Issues
```
No connected devices
```
**Solution**: 
```bash
flutter emulators --launch Pixel_9_Pro
flutter devices
```

#### 5. Timeout Errors
```
Test timed out after 30 seconds
```
**Solution**: Increase timeout values in `.env.local`:
```env
TEST_TIMEOUT=600
WIDGET_TIMEOUT=60
```

### Debug Mode

Run tests with debug information:
```bash
./scripts/run_e2e_tests.sh -v -n  # Verbose + No cleanup
```

### Log Analysis

Check test logs for detailed information:
- **Local**: Console output
- **GitHub Actions**: Workflow logs and artifacts

## 📝 Best Practices

### 1. Test Data Management
- Use unique identifiers (timestamps) for test data
- Always clean up test data after tests
- Use service role key for database operations in tests

### 2. Test Reliability
- Add proper waits for UI elements
- Handle network delays gracefully
- Use retry mechanisms for flaky operations

### 3. Performance
- Run tests in parallel when possible
- Use headless mode for faster web testing
- Optimize test data size

### 4. Maintenance
- Keep test dependencies updated
- Review and update test scenarios regularly
- Monitor test execution times

### 5. CI/CD Integration
- Run E2E tests on critical branches only
- Use matrix builds for multi-platform testing
- Set appropriate timeouts for CI environment

## 📊 Test Metrics

### Coverage Areas
- **User Registration**: 100% flow coverage
- **Authentication**: Login/logout scenarios
- **Database Operations**: CRUD operations
- **UI Interactions**: Form submissions, navigation
- **Business Logic**: Approval workflows, calculations
- **Error Handling**: Network errors, validation errors

### Success Criteria
- **All phases complete**: 19 instructor + 15 student phases
- **Database integrity**: All data relationships maintained
- **UI consistency**: All interactions work as expected
- **Performance**: Tests complete within timeout limits

## 🚀 Next Steps

1. **Set up your environment** using this guide
2. **Run your first E2E test** locally
3. **Configure GitHub Actions** with your secrets
4. **Monitor test results** and iterate on improvements
5. **Expand test coverage** as your app grows

For questions or issues, please check the troubleshooting section or create an issue in the repository.
