PODS:
  - app_links (0.0.2):
    - Flutter
  - AppAuth (1.7.6):
    - AppAuth/Core (= 1.7.6)
    - AppAuth/ExternalUserAgent (= 1.7.6)
  - AppAuth/Core (1.7.6)
  - AppAuth/ExternalUserAgent (1.7.6):
    - AppAuth/Core
  - AppCheckCore (11.2.0):
    - GoogleUtilities/Environment (~> 8.0)
    - GoogleUtilities/UserDefaults (~> 8.0)
    - PromisesObjC (~> 2.4)
  - connectivity_plus (0.0.1):
    - Flutter
  - device_info_plus (0.0.1):
    - Flutter
  - DKImagePickerController/Core (4.3.9):
    - DKImagePickerController/ImageDataManager
    - DKImagePickerController/Resource
  - DKImagePickerController/ImageDataManager (4.3.9)
  - DKImagePickerController/PhotoGallery (4.3.9):
    - DKImagePickerController/Core
    - DKPhotoGallery
  - DKImagePickerController/Resource (4.3.9)
  - DKPhotoGallery (0.0.19):
    - DKPhotoGallery/Core (= 0.0.19)
    - DKPhotoGallery/Model (= 0.0.19)
    - DKPhotoGallery/Preview (= 0.0.19)
    - DKPhotoGallery/Resource (= 0.0.19)
    - SDWebImage
    - SwiftyGif
  - DKPhotoGallery/Core (0.0.19):
    - DKPhotoGallery/Model
    - DKPhotoGallery/Preview
    - SDWebImage
    - SwiftyGif
  - DKPhotoGallery/Model (0.0.19):
    - SDWebImage
    - SwiftyGif
  - DKPhotoGallery/Preview (0.0.19):
    - DKPhotoGallery/Model
    - DKPhotoGallery/Resource
    - SDWebImage
    - SwiftyGif
  - DKPhotoGallery/Resource (0.0.19):
    - SDWebImage
    - SwiftyGif
  - FBAEMKit (18.0.0):
    - FBSDKCoreKit_Basics (= 18.0.0)
  - FBSDKCoreKit (18.0.0):
    - FBAEMKit (= 18.0.0)
    - FBSDKCoreKit_Basics (= 18.0.0)
  - FBSDKCoreKit_Basics (18.0.0)
  - FBSDKLoginKit (18.0.0):
    - FBSDKCoreKit (= 18.0.0)
  - file_picker (0.0.1):
    - DKImagePickerController/PhotoGallery
    - Flutter
  - Flutter (1.0.0)
  - flutter_facebook_auth (7.1.2):
    - FBSDKLoginKit (~> 18.0.0)
    - Flutter
  - flutter_secure_storage (6.0.0):
    - Flutter
  - google_sign_in_ios (0.0.1):
    - AppAuth (>= 1.7.4)
    - Flutter
    - FlutterMacOS
    - GoogleSignIn (~> 8.0)
    - GTMSessionFetcher (>= 3.4.0)
  - GoogleSignIn (8.0.0):
    - AppAuth (< 2.0, >= 1.7.3)
    - AppCheckCore (~> 11.0)
    - GTMAppAuth (< 5.0, >= 4.1.1)
    - GTMSessionFetcher/Core (~> 3.3)
  - GoogleUtilities/Environment (8.1.0):
    - GoogleUtilities/Privacy
  - GoogleUtilities/Logger (8.1.0):
    - GoogleUtilities/Environment
    - GoogleUtilities/Privacy
  - GoogleUtilities/Privacy (8.1.0)
  - GoogleUtilities/UserDefaults (8.1.0):
    - GoogleUtilities/Logger
    - GoogleUtilities/Privacy
  - GTMAppAuth (4.1.1):
    - AppAuth/Core (~> 1.7)
    - GTMSessionFetcher/Core (< 4.0, >= 3.3)
  - GTMSessionFetcher (3.5.0):
    - GTMSessionFetcher/Full (= 3.5.0)
  - GTMSessionFetcher/Core (3.5.0)
  - GTMSessionFetcher/Full (3.5.0):
    - GTMSessionFetcher/Core
  - image_picker_ios (0.0.1):
    - Flutter
  - in_app_purchase_storekit (0.0.1):
    - Flutter
    - FlutterMacOS
  - package_info_plus (0.4.5):
    - Flutter
  - path_provider_foundation (0.0.1):
    - Flutter
    - FlutterMacOS
  - permission_handler_apple (9.3.0):
    - Flutter
  - PromisesObjC (2.4.0)
  - SDWebImage (5.21.1):
    - SDWebImage/Core (= 5.21.1)
  - SDWebImage/Core (5.21.1)
  - share_plus (0.0.1):
    - Flutter
  - shared_preferences_foundation (0.0.1):
    - Flutter
    - FlutterMacOS
  - sign_in_with_apple (0.0.1):
    - Flutter
  - sqflite_darwin (0.0.4):
    - Flutter
    - FlutterMacOS
  - SwiftyGif (5.4.5)
  - url_launcher_ios (0.0.1):
    - Flutter
  - video_player_avfoundation (0.0.1):
    - Flutter
    - FlutterMacOS
  - wakelock_plus (0.0.1):
    - Flutter

DEPENDENCIES:
  - app_links (from `.symlinks/plugins/app_links/ios`)
  - connectivity_plus (from `.symlinks/plugins/connectivity_plus/ios`)
  - device_info_plus (from `.symlinks/plugins/device_info_plus/ios`)
  - file_picker (from `.symlinks/plugins/file_picker/ios`)
  - Flutter (from `Flutter`)
  - flutter_facebook_auth (from `.symlinks/plugins/flutter_facebook_auth/ios`)
  - flutter_secure_storage (from `.symlinks/plugins/flutter_secure_storage/ios`)
  - google_sign_in_ios (from `.symlinks/plugins/google_sign_in_ios/darwin`)
  - image_picker_ios (from `.symlinks/plugins/image_picker_ios/ios`)
  - in_app_purchase_storekit (from `.symlinks/plugins/in_app_purchase_storekit/darwin`)
  - package_info_plus (from `.symlinks/plugins/package_info_plus/ios`)
  - path_provider_foundation (from `.symlinks/plugins/path_provider_foundation/darwin`)
  - permission_handler_apple (from `.symlinks/plugins/permission_handler_apple/ios`)
  - share_plus (from `.symlinks/plugins/share_plus/ios`)
  - shared_preferences_foundation (from `.symlinks/plugins/shared_preferences_foundation/darwin`)
  - sign_in_with_apple (from `.symlinks/plugins/sign_in_with_apple/ios`)
  - sqflite_darwin (from `.symlinks/plugins/sqflite_darwin/darwin`)
  - url_launcher_ios (from `.symlinks/plugins/url_launcher_ios/ios`)
  - video_player_avfoundation (from `.symlinks/plugins/video_player_avfoundation/darwin`)
  - wakelock_plus (from `.symlinks/plugins/wakelock_plus/ios`)

SPEC REPOS:
  trunk:
    - AppAuth
    - AppCheckCore
    - DKImagePickerController
    - DKPhotoGallery
    - FBAEMKit
    - FBSDKCoreKit
    - FBSDKCoreKit_Basics
    - FBSDKLoginKit
    - GoogleSignIn
    - GoogleUtilities
    - GTMAppAuth
    - GTMSessionFetcher
    - PromisesObjC
    - SDWebImage
    - SwiftyGif

EXTERNAL SOURCES:
  app_links:
    :path: ".symlinks/plugins/app_links/ios"
  connectivity_plus:
    :path: ".symlinks/plugins/connectivity_plus/ios"
  device_info_plus:
    :path: ".symlinks/plugins/device_info_plus/ios"
  file_picker:
    :path: ".symlinks/plugins/file_picker/ios"
  Flutter:
    :path: Flutter
  flutter_facebook_auth:
    :path: ".symlinks/plugins/flutter_facebook_auth/ios"
  flutter_secure_storage:
    :path: ".symlinks/plugins/flutter_secure_storage/ios"
  google_sign_in_ios:
    :path: ".symlinks/plugins/google_sign_in_ios/darwin"
  image_picker_ios:
    :path: ".symlinks/plugins/image_picker_ios/ios"
  in_app_purchase_storekit:
    :path: ".symlinks/plugins/in_app_purchase_storekit/darwin"
  package_info_plus:
    :path: ".symlinks/plugins/package_info_plus/ios"
  path_provider_foundation:
    :path: ".symlinks/plugins/path_provider_foundation/darwin"
  permission_handler_apple:
    :path: ".symlinks/plugins/permission_handler_apple/ios"
  share_plus:
    :path: ".symlinks/plugins/share_plus/ios"
  shared_preferences_foundation:
    :path: ".symlinks/plugins/shared_preferences_foundation/darwin"
  sign_in_with_apple:
    :path: ".symlinks/plugins/sign_in_with_apple/ios"
  sqflite_darwin:
    :path: ".symlinks/plugins/sqflite_darwin/darwin"
  url_launcher_ios:
    :path: ".symlinks/plugins/url_launcher_ios/ios"
  video_player_avfoundation:
    :path: ".symlinks/plugins/video_player_avfoundation/darwin"
  wakelock_plus:
    :path: ".symlinks/plugins/wakelock_plus/ios"

SPEC CHECKSUMS:
  app_links: f3e17e4ee5e357b39d8b95290a9b2c299fca71c6
  AppAuth: d4f13a8fe0baf391b2108511793e4b479691fb73
  AppCheckCore: cc8fd0a3a230ddd401f326489c99990b013f0c4f
  connectivity_plus: 2a701ffec2c0ae28a48cf7540e279787e77c447d
  device_info_plus: bf2e3232933866d73fe290f2942f2156cdd10342
  DKImagePickerController: 946cec48c7873164274ecc4624d19e3da4c1ef3c
  DKPhotoGallery: b3834fecb755ee09a593d7c9e389d8b5d6deed60
  FBAEMKit: e34530df538b8eb8aeb53c35867715ba6c63ef0c
  FBSDKCoreKit: d3f479a69127acebb1c6aad91c1a33907bcf6c2f
  FBSDKCoreKit_Basics: 017b6dc2a1862024815a8229e75661e627ac1e29
  FBSDKLoginKit: 5875762d1fe09ddcb05d03365d4f5dc34413843d
  file_picker: b159e0c068aef54932bb15dc9fd1571818edaf49
  Flutter: e0871f40cf51350855a761d2e70bf5af5b9b5de7
  flutter_facebook_auth: fa503a7103e482577bf50694ceefe6b61a78ef1a
  flutter_secure_storage: d33dac7ae2ea08509be337e775f6b59f1ff45f12
  google_sign_in_ios: 7411fab6948df90490dc4620ecbcabdc3ca04017
  GoogleSignIn: ce8c89bb9b37fb624b92e7514cc67335d1e277e4
  GoogleUtilities: 00c88b9a86066ef77f0da2fab05f65d7768ed8e1
  GTMAppAuth: f69bd07d68cd3b766125f7e072c45d7340dea0de
  GTMSessionFetcher: 5aea5ba6bd522a239e236100971f10cb71b96ab6
  image_picker_ios: c560581cceedb403a6ff17f2f816d7fea1421fc1
  in_app_purchase_storekit: a1ce04056e23eecc666b086040239da7619cd783
  package_info_plus: c0502532a26c7662a62a356cebe2692ec5fe4ec4
  path_provider_foundation: 2b6b4c569c0fb62ec74538f866245ac84301af46
  permission_handler_apple: 9878588469a2b0d0fc1e048d9f43605f92e6cec2
  PromisesObjC: f5707f49cb48b9636751c5b2e7d227e43fba9f47
  SDWebImage: f29024626962457f3470184232766516dee8dfea
  share_plus: 8b6f8b3447e494cca5317c8c3073de39b3600d1f
  shared_preferences_foundation: fcdcbc04712aee1108ac7fda236f363274528f78
  sign_in_with_apple: f3bf75217ea4c2c8b91823f225d70230119b8440
  sqflite_darwin: 5a7236e3b501866c1c9befc6771dfd73ffb8702d
  SwiftyGif: 706c60cf65fa2bc5ee0313beece843c8eb8194d4
  url_launcher_ios: 5334b05cef931de560670eeae103fd3e431ac3fe
  video_player_avfoundation: 7c6c11d8470e1675df7397027218274b6d2360b3
  wakelock_plus: 76957ab028e12bfa4e66813c99e46637f367fc7e

PODFILE CHECKSUM: 3b699761a5cb9432aca9a3b37488e7daba5f31a7

COCOAPODS: 1.16.2
