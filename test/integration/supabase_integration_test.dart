import 'dart:io';
import 'package:flutter_test/flutter_test.dart';
import 'package:supabase/supabase.dart';
import 'package:uuid/uuid.dart';

void main() {
  group('Supabase Integration Tests', () {
    late SupabaseClient supabase;
    const uuid = Uuid();
    
    // Use real test user ID from Supabase Auth (<EMAIL>)
    const TEST_USER_ID = '1c82e580-1878-441b-9215-6e0f33f020e8';

    setUpAll(() async {
      // Initialize Supabase client using environment variables
      final supabaseUrl = Platform.environment['SUPABASE_URL'] ?? 
        'https://wrevdlggsevlckprjrwm.supabase.co';

      // For integration tests, use service role key to bypass RLS
      final serviceRoleKey = Platform.environment['SUPABASE_SERVICE_ROLE_KEY'] ?? '';
      final anonKey = Platform.environment['SUPABASE_ANON_KEY'] ?? 
        'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6IndyZXZkbGdnc2V2bGNrcHJqcndtIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDEwMjk3MTcsImV4cCI6MjA1NjYwNTcxN30.KRdBVWPAqDSCsyvYBE3ntrPqpH09KzUmnzbYONAFtzY';
      
      final supabaseKey = serviceRoleKey.isNotEmpty ? serviceRoleKey : anonKey;

      // Debug: Check which key we're using
      print('🔑 Using ${serviceRoleKey.isNotEmpty ? 'SERVICE_ROLE' : 'ANON'} key');
      print('🔑 Key starts with: ${supabaseKey.substring(0, 20)}...');

      supabase = SupabaseClient(supabaseUrl, supabaseKey);
    });

    group('Database Connection', () {
      test('should connect to Supabase successfully', () async {
        final response = await supabase.from('profiles').select('id').limit(1);
        expect(response, isA<List>());
      });

      test('should have required tables', () async {
        // Test that all required tables exist and are accessible
        await supabase.from('profiles').select('id').limit(1);
        await supabase.from('user_profiles').select('id').limit(1);
        await supabase.from('instructors').select('id').limit(1);
        await supabase.from('enrollments').select('id').limit(1);
        await supabase.from('instructor_subscription_configs').select('instructor_id').limit(1);
      });
    });

    group('Instructor Registration Flow', () {
      tearDown(() async {
        // Cleanup test data using TEST_USER_ID
        try {
          await supabase.from('instructor_subscription_configs').delete().eq('instructor_id', TEST_USER_ID);
          await supabase.from('instructor_certifications').delete().eq('instructor_id', TEST_USER_ID);
          await supabase.from('instructor_work_history').delete().eq('instructor_id', TEST_USER_ID);
          await supabase.from('instructors').delete().eq('id', TEST_USER_ID);
          await supabase.from('user_profiles').delete().eq('user_id', TEST_USER_ID);
          // Don't delete profiles as it's the main auth user profile
        } catch (e) {
          // Ignore cleanup errors
        }
      });

      test('should create complete instructor profile', () async {
        // Using real test user from Supabase Auth
        const testEmail = '<EMAIL>'; // Match the auth user email

        try {
          print('🔄 Creating instructor profile with TEST_USER_ID...');

          // Step 1: Upsert profile using fixed TEST_USER_ID
          final profileData = {
            'id': TEST_USER_ID,
            'email': testEmail,
            'name': 'Test',
            'surname': 'Instructor',
            'phone': '+************',
            'role': 'instructor',
          };

          await supabase.from('profiles').upsert(profileData);
          print('✅ Profile upserted successfully');

          // Step 2: Create user profile with actual schema
          final userProfileData = {
            'id': uuid.v4(),
            'user_id': TEST_USER_ID,
            'fitness_goals': 'Test goals',
            'activity_level': 'moderatelyActive', // Use valid enum value from constraint
            'height': 180,
            'weight': 75,
          };

          await supabase.from('user_profiles').upsert(userProfileData);
          print('✅ User profile upserted successfully');

          // Step 3: Create instructor record
          final instructorData = {
            'id': TEST_USER_ID, // Use same ID for instructor
            'profile_id': TEST_USER_ID,
            'title': 'Personal Trainer',
            'experience_years': 5,
            'bio': 'Test instructor bio',
            'is_public': false,
          };

          await supabase.from('instructors').upsert(instructorData);
          print('✅ Instructor upserted successfully');
          
          // Verify record exists
          final result = await supabase
              .from('instructors')
              .select('id, profile_id, title')
              .eq('id', TEST_USER_ID)
              .single();
          
          expect(result['id'], equals(TEST_USER_ID));
          expect(result['profile_id'], equals(TEST_USER_ID));

          print('✅ Complete instructor profile created successfully');
        } catch (e) {
          print('❌ Error: ${e.toString()}');
          fail('Failed to create instructor profile: $e');
        }
      });

      test('should handle instructor approval workflow', () async {
        // Create minimal instructor for approval test
        await _createMinimalInstructor(supabase, TEST_USER_ID);

        // Submit for review - approval_status is in instructor_subscription_configs
        await supabase
            .from('instructor_subscription_configs')
            .update({
              'approval_status': 'pending',
              'submission_status': 'submitted',
              'application_submitted_at': DateTime.now().toIso8601String(),
              'last_submitted_at': DateTime.now().toIso8601String()
            })
            .eq('instructor_id', TEST_USER_ID);

        // Verify pending status
        final pendingCheck = await supabase
            .from('instructor_subscription_configs')
            .select('approval_status')
            .eq('instructor_id', TEST_USER_ID)
            .single();

        expect(pendingCheck['approval_status'], 'pending');

        // Simulate admin approval
        await supabase
            .from('instructor_subscription_configs')
            .update({
              'approval_status': 'approved',
              'submission_status': 'approved',
              'approved_at': DateTime.now().toIso8601String(),
            })
            .eq('instructor_id', TEST_USER_ID);

        await supabase
            .from('instructors')
            .update({'is_public': true})
            .eq('id', TEST_USER_ID);

        // Verify approval
        final approvedCheck = await supabase
            .from('instructor_subscription_configs')
            .select('approval_status')
            .eq('instructor_id', TEST_USER_ID)
            .single();

        final instructorCheck = await supabase
            .from('instructors')
            .select('is_public')
            .eq('id', TEST_USER_ID)
            .single();

        expect(approvedCheck['approval_status'], 'approved');
        expect(instructorCheck['is_public'], true);
      });
    });

    group('Student Enrollment Flow', () {
      tearDown(() async {
        // Cleanup test data
        try {
          await supabase.from('enrollments').delete().eq('student_id', TEST_USER_ID);
          await supabase.from('enrollments').delete().eq('instructor_id', TEST_USER_ID);
        } catch (e) {
          // Ignore cleanup errors
        }
      });

      test('should enroll student with approved instructor', () async {
        // Create approved instructor
        await _createApprovedInstructor(supabase, TEST_USER_ID);

        // Create enrollment
        final enrollmentData = {
          'id': uuid.v4(),
          'student_id': TEST_USER_ID,
          'instructor_id': TEST_USER_ID,
          'plan_type': 'basic',
          'enrolled_at': DateTime.now().toIso8601String(),
          'expires_at': DateTime.now().add(const Duration(days: 30)).toIso8601String(),
          'is_active': true,
        };

        await supabase.from('enrollments').insert(enrollmentData);

        // Verify enrollment
        final enrollmentCheck = await supabase
            .from('enrollments')
            .select('*, instructors(is_public)')
            .eq('student_id', TEST_USER_ID)
            .single();

        expect(enrollmentCheck['instructors']['is_public'], true);
      });
    });
  });
}

// Helper functions for test setup
Future<void> _createMinimalInstructor(SupabaseClient supabase, String instructorId) async {
  // Create profile
  await supabase.from('profiles').upsert({
    'id': instructorId,
    'email': '<EMAIL>',
    'name': 'Test',
    'surname': 'Instructor',
    'role': 'instructor',
  });

  // Create instructor
  await supabase.from('instructors').upsert({
    'id': instructorId,
    'profile_id': instructorId,
    'title': 'Test Trainer',
    'experience_years': 2,
    'is_public': false,
  });

  // Add pricing config with approval_status
  await supabase.from('instructor_subscription_configs').upsert({
    'instructor_id': instructorId,
    'basic_plan_monthly_price': 100.0,
    'premium_plan_monthly_price': 150.0,
    'submission_status': 'draft',
    'approval_status': 'not_submitted',
  });
}

Future<void> _createApprovedInstructor(SupabaseClient supabase, String instructorId) async {
  await supabase.from('profiles').upsert({
    'id': instructorId,
    'email': '<EMAIL>',
    'name': 'Approved',
    'surname': 'Instructor',
    'role': 'instructor',
  });

  await supabase.from('instructors').upsert({
    'id': instructorId,
    'profile_id': instructorId,
    'title': 'Approved Trainer',
    'experience_years': 5,
    'is_public': true,
  });

  // Add approved subscription config
  await supabase.from('instructor_subscription_configs').upsert({
    'instructor_id': instructorId,
    'basic_plan_monthly_price': 100.0,
    'premium_plan_monthly_price': 150.0,
    'submission_status': 'approved',
    'approval_status': 'approved',
  });
}
