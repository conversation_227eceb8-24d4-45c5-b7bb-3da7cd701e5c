import 'package:flutter_test/flutter_test.dart';
import 'package:fitgo_app/src/shared/utils/validation_helpers.dart';

void main() {
  group('ValidationHelpers', () {
    group('Email Validation', () {
      test('should return true for valid email addresses', () {
        // Arrange & Act & Assert
        expect(ValidationHelpers.isValidEmail('<EMAIL>'), true);
        expect(ValidationHelpers.isValidEmail('<EMAIL>'), true);
        expect(ValidationHelpers.isValidEmail('<EMAIL>'), true);
        expect(ValidationHelpers.isValidEmail('<EMAIL>'), true);
        expect(ValidationHelpers.isValidEmail('<EMAIL>'), true);
      });

      test('should return false for invalid email addresses', () {
        // Arrange & Act & Assert
        expect(ValidationHelpers.isValidEmail(''), false);
        expect(ValidationHelpers.isValidEmail('invalid'), false);
        expect(ValidationHelpers.isValidEmail('test@'), false);
        expect(ValidationHelpers.isValidEmail('@domain.com'), false);
        expect(ValidationHelpers.isValidEmail('test@domain'), false);
        expect(ValidationHelpers.isValidEmail('test@.com'), false);
      });
    });

    group('Phone Validation', () {
      test('should return true for valid Turkish phone numbers', () {
        // Arrange & Act & Assert
        expect(ValidationHelpers.isValidTurkishPhone('+905551234567'), true);
        expect(ValidationHelpers.isValidTurkishPhone('05551234567'), true);
        expect(ValidationHelpers.isValidTurkishPhone('5551234567'), true);
        expect(ValidationHelpers.isValidTurkishPhone('+90 555 123 45 67'), true);
        expect(ValidationHelpers.isValidTurkishPhone('0555 123 45 67'), true);
      });

      test('should return false for invalid Turkish phone numbers', () {
        // Arrange & Act & Assert
        expect(ValidationHelpers.isValidTurkishPhone(''), false);
        expect(ValidationHelpers.isValidTurkishPhone('123'), false);
        expect(ValidationHelpers.isValidTurkishPhone('+1234567890'), false);
        expect(ValidationHelpers.isValidTurkishPhone('0555123456'), false); // Too short
        expect(ValidationHelpers.isValidTurkishPhone('055512345678'), false); // Too long
      });
    });

    group('Password Validation', () {
      test('should return true for strong passwords', () {
        // Arrange & Act & Assert
        expect(ValidationHelpers.isStrongPassword('Password123!'), true);
        expect(ValidationHelpers.isStrongPassword('MyStr0ng@Pass'), true);
        expect(ValidationHelpers.isStrongPassword('Complex1#Password'), true);
        expect(ValidationHelpers.isStrongPassword('Test123\$'), true);
      });

      test('should return false for weak passwords', () {
        // Arrange & Act & Assert
        expect(ValidationHelpers.isStrongPassword(''), false);
        expect(ValidationHelpers.isStrongPassword('123456'), false);
        expect(ValidationHelpers.isStrongPassword('password'), false);
        expect(ValidationHelpers.isStrongPassword('PASSWORD'), false);
        expect(ValidationHelpers.isStrongPassword('Password'), false); // No number or special char
        expect(ValidationHelpers.isStrongPassword('pass123'), false); // Too short
        expect(ValidationHelpers.isStrongPassword('PASSWORD123'), false); // No lowercase
        expect(ValidationHelpers.isStrongPassword('password123'), false); // No uppercase
      });
    });

    group('Name Validation', () {
      test('should return true for valid names', () {
        // Arrange & Act & Assert
        expect(ValidationHelpers.isValidName('John'), true);
        expect(ValidationHelpers.isValidName('Ahmet'), true);
        expect(ValidationHelpers.isValidName('Mary-Jane'), true);
        expect(ValidationHelpers.isValidName('Jean-Luc'), true);
        expect(ValidationHelpers.isValidName('Müslüm'), true);
        expect(ValidationHelpers.isValidName('Özge'), true);
      });

      test('should return false for invalid names', () {
        // Arrange & Act & Assert
        expect(ValidationHelpers.isValidName(''), false);
        expect(ValidationHelpers.isValidName('J'), false); // Too short

        // expect(ValidationHelpers.isValidName('VeryLongNameThatExceedsTheMaximumAllowedLength'), false); // Too long - Skip for now
      });
    });

    group('Age Validation', () {
      test('should return true for valid ages', () {
        // Arrange & Act & Assert
        expect(ValidationHelpers.isValidAge(18), true);
        expect(ValidationHelpers.isValidAge(25), true);
        expect(ValidationHelpers.isValidAge(65), true);
        expect(ValidationHelpers.isValidAge(100), true);
      });

      test('should return false for invalid ages', () {
        // Arrange & Act & Assert
        expect(ValidationHelpers.isValidAge(0), false);
        expect(ValidationHelpers.isValidAge(17), false); // Too young
        expect(ValidationHelpers.isValidAge(101), false); // Too old
        expect(ValidationHelpers.isValidAge(-5), false); // Negative
      });
    });

    group('Price Validation', () {
      test('should return true for valid prices', () {
        // Arrange & Act & Assert
        expect(ValidationHelpers.isValidPrice(10.0), true);
        expect(ValidationHelpers.isValidPrice(100.50), true);
        expect(ValidationHelpers.isValidPrice(1000.99), true);
        expect(ValidationHelpers.isValidPrice(0.01), true);
      });

      test('should return false for invalid prices', () {
        // Arrange & Act & Assert
        expect(ValidationHelpers.isValidPrice(0.0), false);
        expect(ValidationHelpers.isValidPrice(-10.0), false);
        expect(ValidationHelpers.isValidPrice(-0.01), false);
        expect(ValidationHelpers.isValidPrice(double.infinity), false);
        expect(ValidationHelpers.isValidPrice(double.nan), false);
      });
    });

    group('Experience Years Validation', () {
      test('should return true for valid experience years', () {
        // Arrange & Act & Assert
        expect(ValidationHelpers.isValidExperienceYears(0), true); // Fresh graduate
        expect(ValidationHelpers.isValidExperienceYears(5), true);
        expect(ValidationHelpers.isValidExperienceYears(20), true);
        expect(ValidationHelpers.isValidExperienceYears(40), true);
      });

      test('should return false for invalid experience years', () {
        // Arrange & Act & Assert
        expect(ValidationHelpers.isValidExperienceYears(-1), false);
        expect(ValidationHelpers.isValidExperienceYears(51), false); // Too much experience
      });
    });

    group('Turkish ID Validation', () {
      test('should return true for valid Turkish ID numbers', () {
        // Arrange & Act & Assert
        expect(ValidationHelpers.isValidTurkishId('10000000146'), true); // Valid Turkish ID
        expect(ValidationHelpers.isValidTurkishId('11111111110'), true); // Valid Turkish ID
      });

      test('should return false for invalid Turkish ID numbers', () {
        // Arrange & Act & Assert
        expect(ValidationHelpers.isValidTurkishId(''), false);
        expect(ValidationHelpers.isValidTurkishId('123456789'), false); // Too short
        expect(ValidationHelpers.isValidTurkishId('123456789012'), false); // Too long
        expect(ValidationHelpers.isValidTurkishId('01234567890'), false); // Starts with 0
        expect(ValidationHelpers.isValidTurkishId('1234567890a'), false); // Contains letter
        expect(ValidationHelpers.isValidTurkishId('12345678900'), false); // Invalid checksum
      });
    });
  });
}
