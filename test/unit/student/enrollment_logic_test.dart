import 'package:flutter_test/flutter_test.dart';

void main() {
  group('Student Enrollment Logic', () {
    test('should validate enrollment eligibility', () {
      final instructor = {
        'id': 'instructor-1',
        'is_public': true,
        'is_active': true,
        'approval_status': 'approved',
        'current_students': 5,
        'max_students': 10,
      };

      final student = {
        'id': 'student-1',
        'is_active': true,
        'current_instructor': null,
      };

      expect(_canEnroll(student, instructor), true);
    });

    test('should prevent enrollment when instructor at capacity', () {
      final instructor = {
        'id': 'instructor-1',
        'is_public': true,
        'is_active': true,
        'approval_status': 'approved',
        'current_students': 10,
        'max_students': 10, // At capacity
      };

      final student = {
        'id': 'student-1',
        'is_active': true,
        'current_instructor': null,
      };

      expect(_canEnroll(student, instructor), false);
    });

    test('should prevent enrollment when instructor not approved', () {
      final instructor = {
        'id': 'instructor-1',
        'is_public': false,
        'is_active': true,
        'approval_status': 'pending', // Not approved
        'current_students': 5,
        'max_students': 10,
      };

      final student = {
        'id': 'student-1',
        'is_active': true,
        'current_instructor': null,
      };

      expect(_canEnroll(student, instructor), false);
    });

    test('should prevent enrollment when student already enrolled', () {
      final instructor = {
        'id': 'instructor-1',
        'is_public': true,
        'is_active': true,
        'approval_status': 'approved',
        'current_students': 5,
        'max_students': 10,
      };

      final student = {
        'id': 'student-1',
        'is_active': true,
        'current_instructor': 'instructor-2', // Already enrolled
      };

      expect(_canEnroll(student, instructor), false);
    });

    test('should calculate enrollment fee correctly', () {
      final pricingConfig = {
        'basic_monthly': 100.0,
        'basic_3_months': 270.0,
        'basic_6_months': 480.0,
        'basic_1_year': 840.0,
        'premium_monthly': 150.0,
        'premium_3_months': 405.0,
        'premium_6_months': 720.0,
        'premium_1_year': 1260.0,
      };

      expect(_calculateFee(pricingConfig, 'basic', 'monthly'), 100.0);
      expect(_calculateFee(pricingConfig, 'premium', '3_months'), 405.0);
      expect(_calculateFee(pricingConfig, 'basic', '1_year'), 840.0);
    });

    test('should validate plan duration options', () {
      const validDurations = ['monthly', '3_months', '6_months', '1_year'];
      
      expect(_isValidDuration('monthly'), true);
      expect(_isValidDuration('3_months'), true);
      expect(_isValidDuration('6_months'), true);
      expect(_isValidDuration('1_year'), true);
      expect(_isValidDuration('weekly'), false);
      expect(_isValidDuration('2_years'), false);
    });

    test('should validate plan type options', () {
      const validTypes = ['basic', 'premium'];
      
      expect(_isValidPlanType('basic'), true);
      expect(_isValidPlanType('premium'), true);
      expect(_isValidPlanType('vip'), false);
      expect(_isValidPlanType('free'), false);
    });

    test('should calculate plan expiry date correctly', () {
      final startDate = DateTime(2025, 1, 1);
      
      expect(_calculateExpiryDate(startDate, 'monthly'), DateTime(2025, 2, 1));
      expect(_calculateExpiryDate(startDate, '3_months'), DateTime(2025, 4, 1));
      expect(_calculateExpiryDate(startDate, '6_months'), DateTime(2025, 7, 1));
      expect(_calculateExpiryDate(startDate, '1_year'), DateTime(2026, 1, 1));
    });

    test('should validate enrollment data completeness', () {
      final completeEnrollment = {
        'student_id': 'student-1',
        'instructor_id': 'instructor-1',
        'plan_type': 'basic',
        'duration': 'monthly',
        'amount_paid': 100.0,
        'payment_method': 'credit_card',
      };

      expect(_isEnrollmentDataComplete(completeEnrollment), true);

      final incompleteEnrollment = {
        'student_id': 'student-1',
        'instructor_id': null, // Missing instructor
        'plan_type': 'basic',
        'duration': 'monthly',
        'amount_paid': 100.0,
        'payment_method': null, // Missing payment method
      };

      expect(_isEnrollmentDataComplete(incompleteEnrollment), false);
    });

    test('should validate instructor availability status', () {
      final availableInstructor = {
        'is_public': true,
        'is_active': true,
        'approval_status': 'approved',
        'is_accepting_students': true,
      };

      expect(_isInstructorAvailable(availableInstructor), true);

      final unavailableInstructor = {
        'is_public': true,
        'is_active': true,
        'approval_status': 'approved',
        'is_accepting_students': false, // Not accepting students
      };

      expect(_isInstructorAvailable(unavailableInstructor), false);
    });
  });
}

// Helper functions for testing enrollment logic
bool _canEnroll(Map<String, dynamic> student, Map<String, dynamic> instructor) {
  // Check if instructor is available
  if (!_isInstructorAvailable(instructor)) return false;
  
  // Check capacity
  if ((instructor['current_students'] as int) >= (instructor['max_students'] as int)) return false;
  
  // Check if student is already enrolled
  if (student['current_instructor'] != null) return false;
  
  // Check if student is active
  if (student['is_active'] != true) return false;
  
  return true;
}

bool _isInstructorAvailable(Map<String, dynamic> instructor) {
  return instructor['is_public'] == true &&
         instructor['is_active'] == true &&
         instructor['approval_status'] == 'approved' &&
         (instructor['is_accepting_students'] ?? true) == true;
}

double _calculateFee(Map<String, double> pricing, String planType, String duration) {
  final key = '${planType}_$duration';
  return pricing[key] ?? 0.0;
}

bool _isValidDuration(String duration) {
  const validDurations = ['monthly', '3_months', '6_months', '1_year'];
  return validDurations.contains(duration);
}

bool _isValidPlanType(String planType) {
  const validTypes = ['basic', 'premium'];
  return validTypes.contains(planType);
}

DateTime _calculateExpiryDate(DateTime startDate, String duration) {
  switch (duration) {
    case 'monthly':
      return DateTime(startDate.year, startDate.month + 1, startDate.day);
    case '3_months':
      return DateTime(startDate.year, startDate.month + 3, startDate.day);
    case '6_months':
      return DateTime(startDate.year, startDate.month + 6, startDate.day);
    case '1_year':
      return DateTime(startDate.year + 1, startDate.month, startDate.day);
    default:
      return startDate;
  }
}

bool _isEnrollmentDataComplete(Map<String, dynamic> enrollment) {
  return enrollment['student_id'] != null &&
         enrollment['instructor_id'] != null &&
         enrollment['plan_type'] != null &&
         enrollment['duration'] != null &&
         enrollment['amount_paid'] != null &&
         enrollment['payment_method'] != null;
}
