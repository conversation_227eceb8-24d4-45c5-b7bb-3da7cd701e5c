import 'package:flutter_test/flutter_test.dart';

void main() {
  group('Instructor Registration Logic', () {
    test('should validate instructor profile completion', () {
      // Test profile completion logic
      final profileData = {
        'name': '<PERSON>',
        'surname': '<PERSON><PERSON>',
        'title': 'Personal Trainer',
        'experience_years': 5,
        'work_history_count': 1,
        'certifications_count': 1,
        'pricing_config_exists': true,
      };

      final isComplete = _isProfileComplete(profileData);
      expect(isComplete, true);
    });

    test('should return false for incomplete profile', () {
      final profileData = {
        'name': '<PERSON>',
        'surname': '<PERSON><PERSON>',
        'title': null, // Missing title
        'experience_years': 5,
        'work_history_count': 0, // No work history
        'certifications_count': 1,
        'pricing_config_exists': false, // No pricing
      };

      final isComplete = _isProfileComplete(profileData);
      expect(isComplete, false);
    });

    test('should validate experience years range', () {
      expect(_isValidExperienceYears(0), true);   // Fresh graduate
      expect(_isValidExperienceYears(10), true);  // Valid experience
      expect(_isValidExperienceYears(50), true);  // Maximum experience
      expect(_isValidExperienceYears(-1), false); // Invalid negative
      expect(_isValidExperienceYears(51), false); // Too much experience
    });

    test('should validate instructor title format', () {
      expect(_isValidTitle('Personal Trainer'), true);
      expect(_isValidTitle('Fitness Coach'), true);
      expect(_isValidTitle('Yoga Instructor'), true);
      expect(_isValidTitle(''), false);           // Empty title
      expect(_isValidTitle('A'), false);         // Too short
      expect(_isValidTitle('A' * 101), false);   // Too long
    });

    test('should calculate pricing configuration validity', () {
      final basicPricing = {'monthly': 100.0, '3_months': 270.0, '6_months': 480.0, '1_year': 840.0};
      final premiumPricing = {'monthly': 150.0, '3_months': 405.0, '6_months': 720.0, '1_year': 1260.0};

      expect(_isPricingValid(basicPricing, premiumPricing), true);

      // Invalid: Premium not higher than basic
      final invalidPremium = {'monthly': 90.0, '3_months': 240.0, '6_months': 400.0, '1_year': 700.0};
      expect(_isPricingValid(basicPricing, invalidPremium), false);
    });

    test('should validate submission readiness', () {
      final completeInstructor = {
        'profile_complete': true,
        'work_history_count': 2,
        'certifications_count': 1,
        'pricing_configured': true,
        'approval_status': 'not_submitted',
      };

      expect(_isReadyForSubmission(completeInstructor), true);

      final incompleteInstructor = {
        'profile_complete': false,
        'work_history_count': 0,
        'certifications_count': 0,
        'pricing_configured': false,
        'approval_status': 'not_submitted',
      };

      expect(_isReadyForSubmission(incompleteInstructor), false);
    });

    test('should validate approval status transitions', () {
      // Valid transitions
      expect(_canTransitionStatus('not_submitted', 'pending'), true);
      expect(_canTransitionStatus('pending', 'approved'), true);
      expect(_canTransitionStatus('pending', 'rejected'), true);
      expect(_canTransitionStatus('rejected', 'pending'), true); // Resubmission

      // Invalid transitions
      expect(_canTransitionStatus('approved', 'pending'), false);
      expect(_canTransitionStatus('not_submitted', 'approved'), false);
      expect(_canTransitionStatus('approved', 'rejected'), false);
    });
  });
}

// Helper functions for testing business logic
bool _isProfileComplete(Map<String, dynamic> profile) {
  return profile['name'] != null &&
         profile['surname'] != null &&
         profile['title'] != null &&
         profile['experience_years'] != null &&
         (profile['work_history_count'] as int) > 0 &&
         (profile['certifications_count'] as int) > 0 &&
         profile['pricing_config_exists'] == true;
}

bool _isValidExperienceYears(int years) {
  return years >= 0 && years <= 50;
}

bool _isValidTitle(String title) {
  return title.isNotEmpty && title.length >= 2 && title.length <= 100;
}

bool _isPricingValid(Map<String, double> basic, Map<String, double> premium) {
  return premium['monthly']! > basic['monthly']! &&
         premium['3_months']! > basic['3_months']! &&
         premium['6_months']! > basic['6_months']! &&
         premium['1_year']! > basic['1_year']!;
}

bool _isReadyForSubmission(Map<String, dynamic> instructor) {
  return instructor['profile_complete'] == true &&
         (instructor['work_history_count'] as int) > 0 &&
         (instructor['certifications_count'] as int) > 0 &&
         instructor['pricing_configured'] == true &&
         instructor['approval_status'] == 'not_submitted';
}

bool _canTransitionStatus(String from, String to) {
  final validTransitions = {
    'not_submitted': ['pending'],
    'pending': ['approved', 'rejected'],
    'rejected': ['pending'], // Allow resubmission
    'approved': [], // No transitions from approved
  };

  return validTransitions[from]?.contains(to) ?? false;
}
