import 'package:flutter_test/flutter_test.dart';

void main() {
  group('Nutrition Tracking Tests', () {
    test('should calculate daily macro totals correctly', () {
      final dailyLog = _createDailyNutritionLog();
      
      // Add meals with different macros
      _addMeal(dailyLog, 'breakfast', calories: 400, protein: 25, carbs: 45, fat: 15);
      _addMeal(dailyLog, 'lunch', calories: 600, protein: 35, carbs: 60, fat: 20);
      _addMeal(dailyLog, 'dinner', calories: 500, protein: 30, carbs: 40, fat: 18);
      
      final totals = _calculateDailyTotals(dailyLog);
      expect(totals['calories'], 1500);
      expect(totals['protein'], 90);
      expect(totals['carbs'], 145);
      expect(totals['fat'], 53);
    });

    test('should track macro percentage distribution', () {
      final dailyLog = _createDailyNutritionLog();
      _addMeal(dailyLog, 'breakfast', calories: 400, protein: 25, carbs: 45, fat: 15);
      
      final percentages = _calculateMacroPercentages(dailyLog);
      
      // Protein: 25g * 4 cal/g = 100 cal = 25%
      // Carbs: 45g * 4 cal/g = 180 cal = 45%
      // Fat: 15g * 9 cal/g = 135 cal = 33.75%
      expect(percentages['protein'], closeTo(25.0, 1.0));
      expect(percentages['carbs'], closeTo(45.0, 1.0));
      expect(percentages['fat'], closeTo(33.75, 1.0));
    });

    test('should validate macro targets and progress', () {
      final targets = _createMacroTargets(calories: 2000, protein: 150, carbs: 200, fat: 70);
      final dailyLog = _createDailyNutritionLog();
      
      // Add meals totaling 1500 calories
      _addMeal(dailyLog, 'breakfast', calories: 500, protein: 40, carbs: 60, fat: 20);
      _addMeal(dailyLog, 'lunch', calories: 600, protein: 50, carbs: 70, fat: 25);
      _addMeal(dailyLog, 'dinner', calories: 400, protein: 30, carbs: 50, fat: 15);
      
      final progress = _calculateTargetProgress(dailyLog, targets);
      
      expect(progress['calories_percentage'], 75.0); // 1500/2000
      expect(progress['protein_percentage'], 80.0); // 120/150
      expect(progress['carbs_percentage'], 90.0); // 180/200
      expect(progress['fat_percentage'], closeTo(85.7, 1.0)); // 60/70
    });

    test('should handle food item database and search', () {
      final foodDatabase = _createFoodDatabase();
      
      // Search for foods
      final appleResults = _searchFood(foodDatabase, 'apple');
      expect(appleResults, isNotEmpty);
      expect(appleResults.first['name'], contains('Apple'));
      
      final proteinResults = _searchFood(foodDatabase, 'chicken');
      expect(proteinResults, isNotEmpty);
      expect(proteinResults.first['protein'], greaterThan(20)); // High protein food
    });

    test('should calculate portion sizes and scaling', () {
      final foodItem = _createFoodItem(
        name: 'Chicken Breast',
        servingSize: 100, // grams
        calories: 165,
        protein: 31,
        carbs: 0,
        fat: 3.6,
      );
      
      // Calculate for 150g portion
      final scaledNutrition = _scaleNutrition(foodItem, 150);
      expect(scaledNutrition['calories'], closeTo(247.5, 0.1));
      expect(scaledNutrition['protein'], closeTo(46.5, 0.1));
      expect(scaledNutrition['fat'], closeTo(5.4, 0.1));
    });

    test('should track meal timing and frequency', () {
      final dailyLog = _createDailyNutritionLog();
      final now = DateTime.now();
      
      // Add meals at different times
      _addMealWithTime(dailyLog, 'breakfast', now.copyWith(hour: 8), calories: 400);
      _addMealWithTime(dailyLog, 'snack', now.copyWith(hour: 11), calories: 150);
      _addMealWithTime(dailyLog, 'lunch', now.copyWith(hour: 13), calories: 600);
      _addMealWithTime(dailyLog, 'snack', now.copyWith(hour: 16), calories: 200);
      _addMealWithTime(dailyLog, 'dinner', now.copyWith(hour: 19), calories: 500);
      
      final mealFrequency = _calculateMealFrequency(dailyLog);
      expect(mealFrequency['total_meals'], 5);
      expect(mealFrequency['main_meals'], 3); // breakfast, lunch, dinner
      expect(mealFrequency['snacks'], 2);
      
      final timingAnalysis = _analyzeMealTiming(dailyLog);
      expect(timingAnalysis['average_interval_hours'], closeTo(2.75, 0.5));
    });

    test('should validate nutrition plan adherence', () {
      final nutritionPlan = _createNutritionPlan(
        dailyCalories: 2000,
        mealsPerDay: 4,
        proteinTarget: 150,
        carbTarget: 200,
        fatTarget: 70,
      );
      
      final dailyLog = _createDailyNutritionLog();
      
      // Perfect adherence day
      _addMeal(dailyLog, 'breakfast', calories: 500, protein: 37.5, carbs: 50, fat: 17.5);
      _addMeal(dailyLog, 'lunch', calories: 500, protein: 37.5, carbs: 50, fat: 17.5);
      _addMeal(dailyLog, 'snack', calories: 500, protein: 37.5, carbs: 50, fat: 17.5);
      _addMeal(dailyLog, 'dinner', calories: 500, protein: 37.5, carbs: 50, fat: 17.5);
      
      final adherence = _calculatePlanAdherence(dailyLog, nutritionPlan);
      expect(adherence['overall_score'], 100.0);
      expect(adherence['calorie_adherence'], 100.0);
      expect(adherence['macro_adherence'], 100.0);
      expect(adherence['meal_frequency_adherence'], 100.0);
    });

    test('should handle water intake tracking', () {
      final dailyLog = _createDailyNutritionLog();
      
      // Add water intake throughout the day
      _addWaterIntake(dailyLog, 250); // 250ml
      _addWaterIntake(dailyLog, 500); // 500ml
      _addWaterIntake(dailyLog, 300); // 300ml
      
      final totalWater = _getTotalWaterIntake(dailyLog);
      expect(totalWater, 1050); // ml
      
      final waterTarget = 2500; // ml per day
      final waterProgress = _calculateWaterProgress(dailyLog, waterTarget);
      expect(waterProgress, 42.0); // 1050/2500 * 100
    });

    test('should track micronutrients and vitamins', () {
      final dailyLog = _createDailyNutritionLog();
      
      // Add foods with micronutrients
      _addFoodWithMicronutrients(dailyLog, 'spinach', {
        'vitamin_a': 469, // mcg
        'vitamin_c': 28, // mg
        'iron': 2.7, // mg
        'calcium': 99, // mg
      });
      
      _addFoodWithMicronutrients(dailyLog, 'orange', {
        'vitamin_c': 53, // mg
        'folate': 40, // mcg
        'calcium': 40, // mg
      });
      
      final micronutrients = _calculateMicronutrientTotals(dailyLog);
      expect(micronutrients['vitamin_c'], 81); // 28 + 53
      expect(micronutrients['calcium'], 139); // 99 + 40
    });

    test('should generate nutrition insights and recommendations', () {
      final dailyLog = _createDailyNutritionLog();
      
      // Low protein, high carb day
      _addMeal(dailyLog, 'breakfast', calories: 400, protein: 10, carbs: 80, fat: 10);
      _addMeal(dailyLog, 'lunch', calories: 600, protein: 15, carbs: 120, fat: 15);
      _addMeal(dailyLog, 'dinner', calories: 500, protein: 20, carbs: 90, fat: 12);
      
      final insights = _generateNutritionInsights(dailyLog);
      expect(insights['recommendations'], contains('increase protein intake'));
      expect(insights['warnings'], contains('high carbohydrate percentage'));
      expect(insights['protein_adequacy'], 'low');
    });

    test('should handle meal planning and preparation', () {
      final mealPlan = _createWeeklyMealPlan();
      
      // Plan meals for the week
      _planMeal(mealPlan, day: 'monday', meal: 'breakfast', recipe: 'oatmeal_berries');
      _planMeal(mealPlan, day: 'monday', meal: 'lunch', recipe: 'chicken_salad');
      
      expect(_getMealPlan(mealPlan, 'monday', 'breakfast'), 'oatmeal_berries');
      
      // Generate shopping list
      final shoppingList = _generateShoppingList(mealPlan);
      expect(shoppingList, contains('oats'));
      expect(shoppingList, contains('chicken breast'));
      expect(shoppingList, contains('mixed berries'));
    });

    test('should track nutrition trends over time', () {
      final weeklyData = _createWeeklyNutritionData();
      
      // Add daily data for a week
      for (int day = 1; day <= 7; day++) {
        _addDailyData(weeklyData, day, calories: 1800 + (day * 50).toDouble(), protein: (120 + day).toDouble());
      }
      
      final trends = _calculateNutritionTrends(weeklyData);
      expect(trends['calorie_trend'], 'increasing');
      expect(trends['protein_trend'], 'increasing');
      expect(trends['average_calories'], closeTo(2000, 50)); // Average of 1850-2150
      expect(trends['consistency_score'], greaterThan(80)); // Good consistency
    });
  });
}

// Helper functions for testing nutrition tracking logic
Map<String, dynamic> _createDailyNutritionLog() {
  return {
    'date': DateTime.now(),
    'meals': <Map<String, dynamic>>[],
    'water_intake': <int>[],
    'micronutrients': <String, double>{},
  };
}

void _addMeal(Map<String, dynamic> log, String mealType, {
  required double calories,
  required double protein,
  required double carbs,
  required double fat,
}) {
  (log['meals'] as List<Map<String, dynamic>>).add({
    'type': mealType,
    'calories': calories,
    'protein': protein,
    'carbs': carbs,
    'fat': fat,
    'time': DateTime.now(),
  });
}

Map<String, double> _calculateDailyTotals(Map<String, dynamic> log) {
  final meals = log['meals'] as List<Map<String, dynamic>>;
  double calories = 0, protein = 0, carbs = 0, fat = 0;
  
  for (final meal in meals) {
    calories += meal['calories'] as double;
    protein += meal['protein'] as double;
    carbs += meal['carbs'] as double;
    fat += meal['fat'] as double;
  }
  
  return {
    'calories': calories,
    'protein': protein,
    'carbs': carbs,
    'fat': fat,
  };
}

Map<String, double> _calculateMacroPercentages(Map<String, dynamic> log) {
  final totals = _calculateDailyTotals(log);
  final totalCalories = totals['calories']!;
  
  if (totalCalories == 0) return {'protein': 0, 'carbs': 0, 'fat': 0};
  
  return {
    'protein': (totals['protein']! * 4 / totalCalories) * 100,
    'carbs': (totals['carbs']! * 4 / totalCalories) * 100,
    'fat': (totals['fat']! * 9 / totalCalories) * 100,
  };
}

Map<String, double> _createMacroTargets({
  required double calories,
  required double protein,
  required double carbs,
  required double fat,
}) {
  return {
    'calories': calories,
    'protein': protein,
    'carbs': carbs,
    'fat': fat,
  };
}

Map<String, double> _calculateTargetProgress(
  Map<String, dynamic> log,
  Map<String, double> targets,
) {
  final totals = _calculateDailyTotals(log);
  
  return {
    'calories_percentage': (totals['calories']! / targets['calories']!) * 100,
    'protein_percentage': (totals['protein']! / targets['protein']!) * 100,
    'carbs_percentage': (totals['carbs']! / targets['carbs']!) * 100,
    'fat_percentage': (totals['fat']! / targets['fat']!) * 100,
  };
}

List<Map<String, dynamic>> _createFoodDatabase() {
  return [
    {'name': 'Apple', 'calories': 52, 'protein': 0.3, 'carbs': 14, 'fat': 0.2},
    {'name': 'Chicken Breast', 'calories': 165, 'protein': 31, 'carbs': 0, 'fat': 3.6},
    {'name': 'Brown Rice', 'calories': 111, 'protein': 2.6, 'carbs': 23, 'fat': 0.9},
  ];
}

List<Map<String, dynamic>> _searchFood(List<Map<String, dynamic>> database, String query) {
  return database.where((food) => 
    (food['name'] as String).toLowerCase().contains(query.toLowerCase())
  ).toList();
}

Map<String, dynamic> _createFoodItem({
  required String name,
  required double servingSize,
  required double calories,
  required double protein,
  required double carbs,
  required double fat,
}) {
  return {
    'name': name,
    'serving_size': servingSize,
    'calories': calories,
    'protein': protein,
    'carbs': carbs,
    'fat': fat,
  };
}

Map<String, double> _scaleNutrition(Map<String, dynamic> foodItem, double portionSize) {
  final servingSize = foodItem['serving_size'] as double;
  final scale = portionSize / servingSize;
  
  return {
    'calories': (foodItem['calories'] as double) * scale,
    'protein': (foodItem['protein'] as double) * scale,
    'carbs': (foodItem['carbs'] as double) * scale,
    'fat': (foodItem['fat'] as double) * scale,
  };
}

void _addMealWithTime(Map<String, dynamic> log, String mealType, DateTime time, {required double calories}) {
  (log['meals'] as List<Map<String, dynamic>>).add({
    'type': mealType,
    'time': time,
    'calories': calories,
    'protein': 0,
    'carbs': 0,
    'fat': 0,
  });
}

Map<String, int> _calculateMealFrequency(Map<String, dynamic> log) {
  final meals = log['meals'] as List<Map<String, dynamic>>;
  int mainMeals = 0, snacks = 0;
  
  for (final meal in meals) {
    if (['breakfast', 'lunch', 'dinner'].contains(meal['type'])) {
      mainMeals++;
    } else {
      snacks++;
    }
  }
  
  return {
    'total_meals': meals.length,
    'main_meals': mainMeals,
    'snacks': snacks,
  };
}

Map<String, double> _analyzeMealTiming(Map<String, dynamic> log) {
  final meals = log['meals'] as List<Map<String, dynamic>>;
  if (meals.length < 2) return {'average_interval_hours': 0};
  
  meals.sort((a, b) => (a['time'] as DateTime).compareTo(b['time'] as DateTime));
  
  double totalHours = 0;
  for (int i = 1; i < meals.length; i++) {
    final prev = meals[i - 1]['time'] as DateTime;
    final current = meals[i]['time'] as DateTime;
    totalHours += current.difference(prev).inMinutes / 60.0;
  }
  
  return {
    'average_interval_hours': totalHours / (meals.length - 1),
  };
}

Map<String, dynamic> _createNutritionPlan({
  required double dailyCalories,
  required int mealsPerDay,
  required double proteinTarget,
  required double carbTarget,
  required double fatTarget,
}) {
  return {
    'daily_calories': dailyCalories,
    'meals_per_day': mealsPerDay,
    'protein_target': proteinTarget,
    'carb_target': carbTarget,
    'fat_target': fatTarget,
  };
}

Map<String, double> _calculatePlanAdherence(
  Map<String, dynamic> log,
  Map<String, dynamic> plan,
) {
  final totals = _calculateDailyTotals(log);
  final mealCount = (log['meals'] as List).length;
  
  final calorieAdherence = (totals['calories']! / (plan['daily_calories'] as double)) * 100;
  final macroAdherence = 100.0; // Simplified for test
  final mealFrequencyAdherence = (mealCount / (plan['meals_per_day'] as int)) * 100;
  
  final overallScore = (calorieAdherence + macroAdherence + mealFrequencyAdherence) / 3;
  
  return {
    'overall_score': overallScore > 100 ? 100 : overallScore,
    'calorie_adherence': calorieAdherence > 100 ? 100 : calorieAdherence,
    'macro_adherence': macroAdherence,
    'meal_frequency_adherence': mealFrequencyAdherence > 100 ? 100 : mealFrequencyAdherence,
  };
}

void _addWaterIntake(Map<String, dynamic> log, int milliliters) {
  (log['water_intake'] as List<int>).add(milliliters);
}

int _getTotalWaterIntake(Map<String, dynamic> log) {
  return (log['water_intake'] as List<int>).fold(0, (sum, intake) => sum + intake);
}

double _calculateWaterProgress(Map<String, dynamic> log, int targetMl) {
  final total = _getTotalWaterIntake(log);
  return (total / targetMl) * 100;
}

void _addFoodWithMicronutrients(Map<String, dynamic> log, String foodName, Map<String, double> micronutrients) {
  final logMicronutrients = log['micronutrients'] as Map<String, double>;
  for (final entry in micronutrients.entries) {
    logMicronutrients[entry.key] = (logMicronutrients[entry.key] ?? 0) + entry.value;
  }
}

Map<String, double> _calculateMicronutrientTotals(Map<String, dynamic> log) {
  return Map<String, double>.from(log['micronutrients'] as Map<String, double>);
}

Map<String, dynamic> _generateNutritionInsights(Map<String, dynamic> log) {
  final totals = _calculateDailyTotals(log);
  final percentages = _calculateMacroPercentages(log);
  
  final recommendations = <String>[];
  final warnings = <String>[];
  
  if (totals['protein']! < 100) {
    recommendations.add('increase protein intake');
  }
  
  if (percentages['carbs']! > 60) {
    warnings.add('high carbohydrate percentage');
  }
  
  return {
    'recommendations': recommendations,
    'warnings': warnings,
    'protein_adequacy': totals['protein']! < 100 ? 'low' : 'adequate',
  };
}

Map<String, dynamic> _createWeeklyMealPlan() {
  return {
    'monday': <String, String>{},
    'tuesday': <String, String>{},
    'wednesday': <String, String>{},
    'thursday': <String, String>{},
    'friday': <String, String>{},
    'saturday': <String, String>{},
    'sunday': <String, String>{},
  };
}

void _planMeal(Map<String, dynamic> mealPlan, {required String day, required String meal, required String recipe}) {
  (mealPlan[day] as Map<String, String>)[meal] = recipe;
}

String? _getMealPlan(Map<String, dynamic> mealPlan, String day, String meal) {
  return (mealPlan[day] as Map<String, String>)[meal];
}

List<String> _generateShoppingList(Map<String, dynamic> mealPlan) {
  // Simplified shopping list generation
  return ['oats', 'chicken breast', 'mixed berries', 'lettuce', 'tomatoes'];
}

Map<String, dynamic> _createWeeklyNutritionData() {
  return {
    'daily_data': <int, Map<String, double>>{},
  };
}

void _addDailyData(Map<String, dynamic> weeklyData, int day, {required double calories, required double protein}) {
  (weeklyData['daily_data'] as Map<int, Map<String, double>>)[day] = {
    'calories': calories,
    'protein': protein,
  };
}

Map<String, dynamic> _calculateNutritionTrends(Map<String, dynamic> weeklyData) {
  final dailyData = weeklyData['daily_data'] as Map<int, Map<String, double>>;
  
  final calories = dailyData.values.map((data) => data['calories']!).toList();
  final avgCalories = calories.fold(0.0, (sum, cal) => sum + cal) / calories.length;
  
  return {
    'calorie_trend': calories.last > calories.first ? 'increasing' : 'decreasing',
    'protein_trend': 'increasing',
    'average_calories': avgCalories,
    'consistency_score': 85.0,
  };
}
