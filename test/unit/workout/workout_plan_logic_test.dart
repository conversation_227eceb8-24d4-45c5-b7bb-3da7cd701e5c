import 'package:flutter_test/flutter_test.dart';

void main() {
  group('Workout Plan Logic', () {
    test('should validate workout plan structure', () {
      final workoutPlan = {
        'id': 'plan-1',
        'name': 'Beginner Strength Training',
        'description': 'A comprehensive beginner workout plan',
        'instructor_id': 'instructor-1',
        'difficulty_level': 'beginner',
        'duration_weeks': 8,
        'exercises_count': 12,
        'is_draft': false,
      };

      expect(_isValidWorkoutPlan(workoutPlan), true);
    });

    test('should reject invalid workout plan', () {
      final invalidPlan = {
        'id': 'plan-1',
        'name': '', // Empty name
        'description': null,
        'instructor_id': null, // Missing instructor
        'difficulty_level': 'expert', // Invalid difficulty
        'duration_weeks': 0, // Invalid duration
        'exercises_count': 0, // No exercises
        'is_draft': false,
      };

      expect(_isValidWorkoutPlan(invalidPlan), false);
    });

    test('should validate exercise structure', () {
      final exercise = {
        'id': 'exercise-1',
        'name': 'Push-ups',
        'description': 'Standard push-up exercise',
        'muscle_groups': ['chest', 'triceps', 'shoulders'],
        'equipment_needed': ['none'],
        'difficulty_level': 'beginner',
        'sets': 3,
        'reps': 10,
        'rest_seconds': 60,
        'instructions': 'Keep your body straight and lower yourself down',
      };

      expect(_isValidExercise(exercise), true);
    });

    test('should validate difficulty levels', () {
      const validLevels = ['beginner', 'intermediate', 'advanced'];
      
      expect(_isValidDifficultyLevel('beginner'), true);
      expect(_isValidDifficultyLevel('intermediate'), true);
      expect(_isValidDifficultyLevel('advanced'), true);
      expect(_isValidDifficultyLevel('expert'), false);
      expect(_isValidDifficultyLevel('novice'), false);
    });

    test('should validate workout duration', () {
      expect(_isValidDuration(1), true);   // 1 week minimum
      expect(_isValidDuration(12), true);  // 12 weeks valid
      expect(_isValidDuration(52), true);  // 1 year maximum
      expect(_isValidDuration(0), false);  // Invalid
      expect(_isValidDuration(53), false); // Too long
    });

    test('should calculate workout completion percentage', () {
      final workoutSession = {
        'total_exercises': 10,
        'completed_exercises': 7,
        'skipped_exercises': 1,
        'remaining_exercises': 2,
      };

      expect(_calculateCompletionPercentage(workoutSession), 70.0);
    });

    test('should validate exercise sets and reps', () {
      expect(_isValidSetsAndReps(3, 10), true);   // Normal workout
      expect(_isValidSetsAndReps(5, 5), true);    // Strength training
      expect(_isValidSetsAndReps(1, 20), true);   // Endurance
      expect(_isValidSetsAndReps(0, 10), false);  // Invalid sets
      expect(_isValidSetsAndReps(3, 0), false);   // Invalid reps
      expect(_isValidSetsAndReps(20, 100), false); // Unrealistic
    });

    test('should validate rest time between sets', () {
      expect(_isValidRestTime(30), true);   // 30 seconds
      expect(_isValidRestTime(120), true);  // 2 minutes
      expect(_isValidRestTime(300), true);  // 5 minutes max
      expect(_isValidRestTime(0), false);   // No rest
      expect(_isValidRestTime(600), false); // Too long
    });

    test('should validate muscle group targeting', () {
      final muscleGroups = ['chest', 'triceps', 'shoulders'];
      expect(_isValidMuscleGroups(muscleGroups), true);

      final invalidGroups = ['chest', 'invalid_muscle'];
      expect(_isValidMuscleGroups(invalidGroups), false);

      final emptyGroups = <String>[];
      expect(_isValidMuscleGroups(emptyGroups), false);
    });

    test('should calculate estimated workout time', () {
      final exercises = [
        {'sets': 3, 'reps': 10, 'rest_seconds': 60},
        {'sets': 3, 'reps': 12, 'rest_seconds': 90},
        {'sets': 4, 'reps': 8, 'rest_seconds': 120},
      ];

      // Rough calculation: (sets * reps * 2 seconds per rep) + (sets * rest time)
      final estimatedTime = _calculateWorkoutTime(exercises);
      expect(estimatedTime, greaterThan(0));
      expect(estimatedTime, lessThan(3600)); // Less than 1 hour
    });

    test('should validate workout plan assignment to student', () {
      final assignment = {
        'student_id': 'student-1',
        'instructor_id': 'instructor-1',
        'workout_plan_id': 'plan-1',
        'start_date': DateTime.now(),
        'is_active': true,
        'student_level': 'beginner',
        'plan_difficulty': 'beginner',
      };

      expect(_isValidAssignment(assignment), true);

      // Mismatched difficulty levels
      final mismatchedAssignment = {
        'student_id': 'student-1',
        'instructor_id': 'instructor-1',
        'workout_plan_id': 'plan-1',
        'start_date': DateTime.now(),
        'is_active': true,
        'student_level': 'beginner',
        'plan_difficulty': 'advanced', // Mismatch
      };

      expect(_isValidAssignment(mismatchedAssignment), false);
    });
  });
}

// Helper functions for testing workout plan logic
bool _isValidWorkoutPlan(Map<String, dynamic> plan) {
  return plan['name'] != null &&
         (plan['name'] as String).isNotEmpty &&
         plan['instructor_id'] != null &&
         _isValidDifficultyLevel(plan['difficulty_level']) &&
         _isValidDuration(plan['duration_weeks']) &&
         (plan['exercises_count'] as int) > 0;
}

bool _isValidExercise(Map<String, dynamic> exercise) {
  return exercise['name'] != null &&
         (exercise['name'] as String).isNotEmpty &&
         exercise['muscle_groups'] != null &&
         (exercise['muscle_groups'] as List).isNotEmpty &&
         _isValidSetsAndReps(exercise['sets'], exercise['reps']) &&
         _isValidRestTime(exercise['rest_seconds']);
}

bool _isValidDifficultyLevel(String? level) {
  const validLevels = ['beginner', 'intermediate', 'advanced'];
  return level != null && validLevels.contains(level);
}

bool _isValidDuration(int? weeks) {
  return weeks != null && weeks >= 1 && weeks <= 52;
}

double _calculateCompletionPercentage(Map<String, dynamic> session) {
  final total = session['total_exercises'] as int;
  final completed = session['completed_exercises'] as int;
  return (completed / total) * 100;
}

bool _isValidSetsAndReps(int? sets, int? reps) {
  return sets != null &&
         reps != null &&
         sets > 0 &&
         reps > 0 &&
         sets <= 10 &&
         reps <= 50;
}

bool _isValidRestTime(int? seconds) {
  return seconds != null && seconds > 0 && seconds <= 300; // Max 5 minutes
}

bool _isValidMuscleGroups(List<String> groups) {
  if (groups.isEmpty) return false;
  
  const validMuscleGroups = [
    'chest', 'back', 'shoulders', 'biceps', 'triceps', 'forearms',
    'abs', 'obliques', 'lower_back', 'glutes', 'quadriceps', 
    'hamstrings', 'calves', 'cardio', 'full_body'
  ];
  
  return groups.every((group) => validMuscleGroups.contains(group));
}

int _calculateWorkoutTime(List<Map<String, dynamic>> exercises) {
  int totalTime = 0;
  
  for (final exercise in exercises) {
    final sets = exercise['sets'] as int;
    final reps = exercise['reps'] as int;
    final restSeconds = exercise['rest_seconds'] as int;
    
    // Estimate 2 seconds per rep + rest time between sets
    final exerciseTime = (sets * reps * 2) + (sets * restSeconds);
    totalTime += exerciseTime;
  }
  
  return totalTime;
}

bool _isValidAssignment(Map<String, dynamic> assignment) {
  return assignment['student_id'] != null &&
         assignment['instructor_id'] != null &&
         assignment['workout_plan_id'] != null &&
         assignment['start_date'] != null &&
         assignment['student_level'] == assignment['plan_difficulty'];
}
