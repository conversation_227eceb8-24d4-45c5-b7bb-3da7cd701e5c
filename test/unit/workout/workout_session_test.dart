import 'package:flutter_test/flutter_test.dart';

void main() {
  group('Workout Session Tests', () {
    test('should track exercise completion correctly', () {
      final session = _createWorkoutSession();
      
      // Initially no exercises completed
      expect(_getCompletionPercentage(session), 0.0);
      
      // Complete first exercise
      _completeExercise(session, 'exercise-1');
      expect(_getCompletionPercentage(session), closeTo(33.33, 0.1));
      
      // Complete second exercise
      _completeExercise(session, 'exercise-2');
      expect(_getCompletionPercentage(session), closeTo(66.67, 0.1));
      
      // Complete all exercises
      _completeExercise(session, 'exercise-3');
      expect(_getCompletionPercentage(session), 100.0);
    });

    test('should handle exercise skipping', () {
      final session = _createWorkoutSession();
      
      // Skip an exercise
      _skipExercise(session, 'exercise-1');
      expect(_getSkippedCount(session), 1);
      expect(_getCompletionPercentage(session), 0.0); // Skipped doesn't count as completed
      
      // Complete remaining exercises
      _completeExercise(session, 'exercise-2');
      _completeExercise(session, 'exercise-3');
      expect(_getCompletionPercentage(session), closeTo(66.67, 0.1)); // 2 out of 3 completed
    });

    test('should track workout session timing', () {
      final session = _createWorkoutSession();
      final startTime = DateTime.now();
      
      _startSession(session, startTime);
      expect(_getSessionDuration(session), Duration.zero);
      
      // Simulate 30 minutes of workout
      final endTime = startTime.add(const Duration(minutes: 30));
      _endSession(session, endTime);
      
      expect(_getSessionDuration(session), const Duration(minutes: 30));
      expect(_isSessionCompleted(session), true);
    });

    test('should validate rest timer functionality', () {
      final session = _createWorkoutSession();
      
      // Start rest timer
      _startRestTimer(session, 60); // 60 seconds rest
      expect(_getRestTimeRemaining(session), 60);
      
      // Simulate 30 seconds passed
      _updateRestTimer(session, 30);
      expect(_getRestTimeRemaining(session), 30);
      
      // Rest timer completed
      _updateRestTimer(session, 60);
      expect(_getRestTimeRemaining(session), 0);
      expect(_isRestCompleted(session), true);
    });

    test('should track set completion within exercises', () {
      final exercise = _createExercise('Push-ups', sets: 3, reps: 10);
      
      // Complete first set
      _completeSet(exercise, setNumber: 1, actualReps: 10);
      expect(_getCompletedSets(exercise), 1);
      expect(_getSetCompletionPercentage(exercise), closeTo(33.33, 0.1));
      
      // Complete second set with fewer reps
      _completeSet(exercise, setNumber: 2, actualReps: 8);
      expect(_getCompletedSets(exercise), 2);
      expect(_getTotalActualReps(exercise), 18);
      
      // Complete all sets
      _completeSet(exercise, setNumber: 3, actualReps: 12);
      expect(_isExerciseCompleted(exercise), true);
      expect(_getTotalActualReps(exercise), 30);
    });

    test('should calculate workout performance metrics', () {
      final session = _createWorkoutSession();
      
      // Complete exercises with different performance
      _completeExerciseWithPerformance(session, 'exercise-1', 
          targetReps: 30, actualReps: 32); // 106.7% performance
      _completeExerciseWithPerformance(session, 'exercise-2', 
          targetReps: 20, actualReps: 18); // 90% performance
      _completeExerciseWithPerformance(session, 'exercise-3', 
          targetReps: 15, actualReps: 15); // 100% performance
      
      final avgPerformance = _calculateAveragePerformance(session);
      expect(avgPerformance, closeTo(98.9, 0.1)); // Average of 106.7, 90, 100
    });

    test('should handle workout session pausing and resuming', () {
      final session = _createWorkoutSession();
      final startTime = DateTime.now();
      
      _startSession(session, startTime);
      
      // Pause after 10 minutes
      final pauseTime = startTime.add(const Duration(minutes: 10));
      _pauseSession(session, pauseTime);
      expect(_isSessionPaused(session), true);
      // Active time should be 0 during pause (no end time set yet)
      expect(_getActiveWorkoutTime(session), Duration.zero);
      
      // Resume after 5 minutes break
      final resumeTime = pauseTime.add(const Duration(minutes: 5));
      _resumeSession(session, resumeTime);
      expect(_isSessionPaused(session), false);
      
      // End session after another 15 minutes
      final endTime = resumeTime.add(const Duration(minutes: 15));
      _endSession(session, endTime);
      
      // Total active time should be 25 minutes (10 + 15), not 30
      expect(_getActiveWorkoutTime(session), const Duration(minutes: 25));
      expect(_getTotalSessionTime(session), const Duration(minutes: 30));
    });

    test('should validate workout plan adherence', () {
      final session = _createWorkoutSession();
      
      // Complete all exercises as planned
      _completeAllExercisesAsPlanned(session);
      expect(_getPlanAdherence(session), 100.0);
      
      // Skip one exercise
      final sessionWithSkip = _createWorkoutSession();
      _skipExercise(sessionWithSkip, 'exercise-1');
      _completeExercise(sessionWithSkip, 'exercise-2');
      _completeExercise(sessionWithSkip, 'exercise-3');
      expect(_getPlanAdherence(sessionWithSkip), closeTo(66.67, 0.1)); // 2 out of 3 completed
    });

    test('should handle exercise modifications during session', () {
      final session = _createWorkoutSession();
      final exercise = _getExercise(session, 'exercise-1');
      
      // Modify exercise difficulty mid-session
      _modifyExercise(exercise, newReps: 8, reason: 'too_difficult');
      expect(_getTargetReps(exercise), 8);
      expect(_hasModifications(exercise), true);
      expect(_getModificationReason(exercise), 'too_difficult');
      
      // Track modification in session
      (session['modifications'] as Map<String, Map<String, dynamic>>)['exercise-1'] = {
        'original_reps': 10,
        'new_reps': 8,
        'reason': 'too_difficult',
      };

      // Complete modified exercise
      _completeExercise(session, 'exercise-1');
      expect(_wasExerciseModified(session, 'exercise-1'), true);
    });

    test('should track workout session notes and feedback', () {
      final session = _createWorkoutSession();
      
      // Add notes during workout
      _addSessionNote(session, 'Feeling strong today');
      _addExerciseNote(session, 'exercise-1', 'Form felt good');
      
      expect(_getSessionNotes(session), contains('Feeling strong today'));
      expect(_getExerciseNotes(session, 'exercise-1'), contains('Form felt good'));
      
      // Rate workout difficulty
      _rateWorkoutDifficulty(session, 7); // 1-10 scale
      expect(_getWorkoutDifficultyRating(session), 7);
    });

    test('should validate session data persistence', () {
      final session = _createWorkoutSession();
      
      // Complete partial workout
      _completeExercise(session, 'exercise-1');
      _pauseSession(session, DateTime.now());
      
      // Save session state
      final sessionData = _saveSessionState(session);
      expect(sessionData['completed_exercises'], contains('exercise-1'));
      expect(sessionData['is_paused'], true);
      
      // Restore session state
      final restoredSession = _restoreSessionState(sessionData);
      expect(_getCompletedExercises(restoredSession), contains('exercise-1'));
      expect(_isSessionPaused(restoredSession), true);
    });
  });
}

// Helper functions for testing workout session logic
Map<String, dynamic> _createWorkoutSession() {
  return {
    'id': 'session-1',
    'exercises': ['exercise-1', 'exercise-2', 'exercise-3'],
    'completed_exercises': <String>[],
    'skipped_exercises': <String>[],
    'start_time': null,
    'end_time': null,
    'pause_time': null,
    'resume_time': null,
    'total_pause_duration': Duration.zero,
    'rest_timer': {'remaining': 0, 'is_active': false},
    'notes': <String>[],
    'exercise_notes': <String, List<String>>{},
    'difficulty_rating': null,
    'modifications': <String, Map<String, dynamic>>{},
  };
}

Map<String, dynamic> _createExercise(String name, {required int sets, required int reps}) {
  return {
    'name': name,
    'target_sets': sets,
    'target_reps': reps,
    'completed_sets': 0,
    'actual_reps': <int>[],
    'is_completed': false,
  };
}

double _getCompletionPercentage(Map<String, dynamic> session) {
  final total = (session['exercises'] as List).length;
  final completed = (session['completed_exercises'] as List).length;
  return total > 0 ? (completed / total * 100) : 0.0;
}

void _completeExercise(Map<String, dynamic> session, String exerciseId) {
  (session['completed_exercises'] as List<String>).add(exerciseId);
}

void _skipExercise(Map<String, dynamic> session, String exerciseId) {
  (session['skipped_exercises'] as List<String>).add(exerciseId);
}

int _getSkippedCount(Map<String, dynamic> session) {
  return (session['skipped_exercises'] as List).length;
}

void _startSession(Map<String, dynamic> session, DateTime startTime) {
  session['start_time'] = startTime;
}

void _endSession(Map<String, dynamic> session, DateTime endTime) {
  session['end_time'] = endTime;
}

Duration _getSessionDuration(Map<String, dynamic> session) {
  final start = session['start_time'] as DateTime?;
  final end = session['end_time'] as DateTime?;
  if (start != null && end != null) {
    return end.difference(start);
  }
  return Duration.zero;
}

bool _isSessionCompleted(Map<String, dynamic> session) {
  return session['end_time'] != null;
}

void _startRestTimer(Map<String, dynamic> session, int seconds) {
  session['rest_timer'] = {'remaining': seconds, 'is_active': true};
}

void _updateRestTimer(Map<String, dynamic> session, int elapsedSeconds) {
  final timer = session['rest_timer'] as Map<String, dynamic>;
  final remaining = (timer['remaining'] as int) - elapsedSeconds;
  timer['remaining'] = remaining > 0 ? remaining : 0;
}

int _getRestTimeRemaining(Map<String, dynamic> session) {
  return (session['rest_timer'] as Map<String, dynamic>)['remaining'] as int;
}

bool _isRestCompleted(Map<String, dynamic> session) {
  return _getRestTimeRemaining(session) == 0;
}

void _completeSet(Map<String, dynamic> exercise, {required int setNumber, required int actualReps}) {
  exercise['completed_sets'] = setNumber;
  (exercise['actual_reps'] as List<int>).add(actualReps);
}

int _getCompletedSets(Map<String, dynamic> exercise) {
  return exercise['completed_sets'] as int;
}

double _getSetCompletionPercentage(Map<String, dynamic> exercise) {
  final completed = exercise['completed_sets'] as int;
  final total = exercise['target_sets'] as int;
  return total > 0 ? (completed / total * 100) : 0.0;
}

int _getTotalActualReps(Map<String, dynamic> exercise) {
  return (exercise['actual_reps'] as List<int>).fold(0, (sum, reps) => sum + reps);
}

bool _isExerciseCompleted(Map<String, dynamic> exercise) {
  return exercise['completed_sets'] == exercise['target_sets'];
}

void _completeExerciseWithPerformance(Map<String, dynamic> session, String exerciseId, 
    {required int targetReps, required int actualReps}) {
  _completeExercise(session, exerciseId);
  // Store performance data
  session['performance_data'] ??= <String, Map<String, int>>{};
  (session['performance_data'] as Map<String, Map<String, int>>)[exerciseId] = {
    'target': targetReps,
    'actual': actualReps,
  };
}

double _calculateAveragePerformance(Map<String, dynamic> session) {
  final performanceData = session['performance_data'] as Map<String, Map<String, int>>?;
  if (performanceData == null || performanceData.isEmpty) return 0.0;
  
  double totalPerformance = 0.0;
  for (final data in performanceData.values) {
    final target = data['target']!;
    final actual = data['actual']!;
    totalPerformance += (actual / target) * 100;
  }
  
  return totalPerformance / performanceData.length;
}

void _pauseSession(Map<String, dynamic> session, DateTime pauseTime) {
  session['pause_time'] = pauseTime;
  session['is_paused'] = true;
}

void _resumeSession(Map<String, dynamic> session, DateTime resumeTime) {
  final pauseTime = session['pause_time'] as DateTime?;
  if (pauseTime != null) {
    final pauseDuration = resumeTime.difference(pauseTime);
    session['total_pause_duration'] = (session['total_pause_duration'] as Duration) + pauseDuration;
  }
  session['is_paused'] = false;
  session['pause_time'] = null;
}

bool _isSessionPaused(Map<String, dynamic> session) {
  return session['is_paused'] == true;
}

Duration _getActiveWorkoutTime(Map<String, dynamic> session) {
  final start = session['start_time'] as DateTime?;
  final end = session['end_time'] as DateTime?;
  if (start == null || end == null) return Duration.zero;

  final totalDuration = end.difference(start);
  final pauseDuration = session['total_pause_duration'] as Duration? ?? Duration.zero;
  return totalDuration - pauseDuration;
}

Duration _getTotalSessionTime(Map<String, dynamic> session) {
  return _getSessionDuration(session);
}

void _completeAllExercisesAsPlanned(Map<String, dynamic> session) {
  final exercises = session['exercises'] as List<String>;
  for (final exercise in exercises) {
    _completeExercise(session, exercise);
  }
}

double _getPlanAdherence(Map<String, dynamic> session) {
  final total = (session['exercises'] as List).length;
  final completed = (session['completed_exercises'] as List).length;
  return total > 0 ? (completed / total * 100) : 0.0;
}

Map<String, dynamic> _getExercise(Map<String, dynamic> session, String exerciseId) {
  return _createExercise('Test Exercise', sets: 3, reps: 10);
}

void _modifyExercise(Map<String, dynamic> exercise, {required int newReps, required String reason}) {
  exercise['target_reps'] = newReps;
  exercise['modification_reason'] = reason;
  exercise['is_modified'] = true;
}

int _getTargetReps(Map<String, dynamic> exercise) {
  return exercise['target_reps'] as int;
}

bool _hasModifications(Map<String, dynamic> exercise) {
  return exercise['is_modified'] == true;
}

String _getModificationReason(Map<String, dynamic> exercise) {
  return exercise['modification_reason'] as String? ?? '';
}

bool _wasExerciseModified(Map<String, dynamic> session, String exerciseId) {
  final modifications = session['modifications'] as Map<String, Map<String, dynamic>>?;
  return modifications?[exerciseId] != null;
}

void _addSessionNote(Map<String, dynamic> session, String note) {
  (session['notes'] as List<String>).add(note);
}

void _addExerciseNote(Map<String, dynamic> session, String exerciseId, String note) {
  final exerciseNotes = session['exercise_notes'] as Map<String, List<String>>;
  exerciseNotes[exerciseId] ??= <String>[];
  exerciseNotes[exerciseId]!.add(note);
}

List<String> _getSessionNotes(Map<String, dynamic> session) {
  return session['notes'] as List<String>;
}

List<String> _getExerciseNotes(Map<String, dynamic> session, String exerciseId) {
  return (session['exercise_notes'] as Map<String, List<String>>)[exerciseId] ?? <String>[];
}

void _rateWorkoutDifficulty(Map<String, dynamic> session, int rating) {
  session['difficulty_rating'] = rating;
}

int? _getWorkoutDifficultyRating(Map<String, dynamic> session) {
  return session['difficulty_rating'] as int?;
}

Map<String, dynamic> _saveSessionState(Map<String, dynamic> session) {
  return Map<String, dynamic>.from(session);
}

Map<String, dynamic> _restoreSessionState(Map<String, dynamic> sessionData) {
  return Map<String, dynamic>.from(sessionData);
}

List<String> _getCompletedExercises(Map<String, dynamic> session) {
  return session['completed_exercises'] as List<String>;
}
