import 'package:flutter/material.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:fitgo_app/src/shared/extensions/string_extensions.dart';

void main() {
  group('Date Validation', () {
    testWidgets('should validate birth date correctly', (
      WidgetTester tester,
    ) async {
      late BuildContext context;

      await tester.pumpWidget(
        MaterialApp(
          home: Builder(
            builder: (BuildContext ctx) {
              context = ctx;
              return Container();
            },
          ),
        ),
      );

      // Test valid dates
      expect('25/12/1990'.isValidBirthDate(context), null);
      expect('01/01/2000'.isValidBirthDate(context), null);
      expect('29/02/2000'.isValidBirthDate(context), null); // Leap year

      // Test invalid format
      expect('25-12-1990'.isValidBirthDate(context), isNotNull);
      expect('25/12/90'.isValidBirthDate(context), isNotNull);
      expect('25/12'.isValidBirthDate(context), isNotNull);
      expect('invalid'.isValidBirthDate(context), isNotNull);

      // Test invalid dates
      expect('32/12/1990'.isValidBirthDate(context), isNotNull); // Invalid day
      expect(
        '25/13/1990'.isValidBirthDate(context),
        isNotNull,
      ); // Invalid month
      expect(
        '29/02/1999'.isValidBirthDate(context),
        isNotNull,
      ); // Not a leap year

      // Test future dates
      final futureDate = DateTime.now().add(const Duration(days: 1));
      final futureDateString =
          '${futureDate.day.toString().padLeft(2, '0')}/${futureDate.month.toString().padLeft(2, '0')}/${futureDate.year}';
      expect(futureDateString.isValidBirthDate(context), isNotNull);

      // Test empty/null
      expect(''.isValidBirthDate(context), isNotNull);
      expect(null.isValidBirthDate(context), isNotNull);
    });
  });
}
