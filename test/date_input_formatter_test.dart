import 'package:flutter/services.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:fitgo_app/src/shared/utils/date_input_formatter.dart';

void main() {
  group('DateInputFormatter', () {
    late DateInputFormatter formatter;

    setUp(() {
      formatter = DateInputFormatter();
    });

    test('should format input correctly', () {
      // Test empty input
      var result = formatter.formatEditUpdate(
        const TextEditingValue(),
        const TextEditingValue(text: ''),
      );
      expect(result.text, '');

      // Test single digit
      result = formatter.formatEditUpdate(
        const TextEditingValue(),
        const TextEditingValue(text: '1'),
      );
      expect(result.text, '1');

      // Test two digits (day)
      result = formatter.formatEditUpdate(
        const TextEditingValue(),
        const TextEditingValue(text: '12'),
      );
      expect(result.text, '12');

      // Test three digits (should add first slash)
      result = formatter.formatEditUpdate(
        const TextEditingValue(),
        const TextEditingValue(text: '123'),
      );
      expect(result.text, '12/3');

      // Test four digits (day and month)
      result = formatter.formatEditUpdate(
        const TextEditingValue(),
        const TextEditingValue(text: '1234'),
      );
      expect(result.text, '12/34');

      // Test five digits (should add second slash)
      result = formatter.formatEditUpdate(
        const TextEditingValue(),
        const TextEditingValue(text: '12345'),
      );
      expect(result.text, '12/34/5');

      // Test full date
      result = formatter.formatEditUpdate(
        const TextEditingValue(),
        const TextEditingValue(text: '12345678'),
      );
      expect(result.text, '12/34/5678');

      // Test more than 8 digits (should be limited)
      result = formatter.formatEditUpdate(
        const TextEditingValue(),
        const TextEditingValue(text: '123456789'),
      );
      expect(result.text, '12/34/5678');
    });

    test('should handle non-digit characters', () {
      // Test input with letters and special characters
      var result = formatter.formatEditUpdate(
        const TextEditingValue(),
        const TextEditingValue(text: '1a2b3c4d5e6f7g8h'),
      );
      expect(result.text, '12/34/5678');

      // Test input with existing slashes
      result = formatter.formatEditUpdate(
        const TextEditingValue(),
        const TextEditingValue(text: '12/34/5678'),
      );
      expect(result.text, '12/34/5678');
    });

    test('should handle realistic user input', () {
      // Test typical birth date input
      var result = formatter.formatEditUpdate(
        const TextEditingValue(),
        const TextEditingValue(text: '25121990'),
      );
      expect(result.text, '25/12/1990');

      // Test partial input
      result = formatter.formatEditUpdate(
        const TextEditingValue(),
        const TextEditingValue(text: '2512'),
      );
      expect(result.text, '25/12');
    });
  });
}
