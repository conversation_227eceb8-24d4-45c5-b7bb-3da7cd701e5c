# 🏋️ FitGo - Your Personal Fitness Companion

[![Flutter](https://img.shields.io/badge/Flutter-3.7.0-blue.svg)](https://flutter.dev/)
[![Dart](https://img.shields.io/badge/Dart-3.7.0-blue.svg)](https://dart.dev/)
[![Supabase](https://img.shields.io/badge/Supabase-Backend-green.svg)](https://supabase.com/)
[![Riverpod](https://img.shields.io/badge/Riverpod-State%20Management-purple.svg)](https://riverpod.dev/)

FitGo is a comprehensive fitness application built with Flutter, featuring AI-powered personalized recommendations, social features, and professional instructor tools. The app connects fitness enthusiasts with certified instructors and provides a complete ecosystem for workout planning, nutrition tracking, and progress monitoring.

## 🌟 Features

### 👥 User Types & Authentication
- **Students**: Access courses, track progress, nutrition monitoring
- **Instructors**: Create courses, manage students, track performance
- **Admin Panel**: Web-based instructor approval and management system
- **Multi-platform Authentication**: Email, Google, Facebook, Apple Sign-In
- **Secure Password Reset**: Domain-based reset links with Wix integration
- **Onboarding Flow**: Persistent local storage with Hive
- **User-Friendly Error Handling**: Turkish error messages with technical details hidden
- **Unified Auth System**: Consistent authentication flow for all user types
- **Database Integrity**: Restructured schema with normalized instructor data across specialized tables
- **Instructor Approval Workflow**: Automated application system with admin review process
- **Enhanced Data Architecture**: Separated capacity, pricing, and profile data for better organization
- **Commission-Based Pricing**: Admin-configurable commission rates with multi-duration plans
- **Platform Configuration**: Global settings management without code deployment
- **JSON-Free Architecture**: Migrated from JSON fields to individual columns for better performance

### 🎯 Core Functionality
- **Course Management**: Browse, filter, and enroll in fitness courses
- **Instructor Approval System**: Automated workflow for instructor verification
- **Course Profile Management**: Separate course profiles with approval process
- **Video Player Integration**: Chewie-based video streaming with caching
- **Progress Analytics**: FL Chart and Syncfusion charts for data visualization
- **Nutrition Tracking**: Macro breakdown and meal planning
- **Feedback System**: Student-instructor feedback with photo uploads and response management
- **Course Visibility Management**: Instructors can hide courses from new students while serving existing ones
- **Social Features**: Leaderboards and community interaction
- **Payment Integration**: In-app purchases and subscription management
- **Push Notifications**: Real-time updates and reminders
- **Plan Waiting System**: Students wait for instructor-assigned workout plans
- **Purchase History**: Access to transaction history during plan preparation
- **Admin Dashboard**: Web panel for instructor application management

### 🤖 AI & Advanced Features
- **Personalized Recommendations**: AI-powered workout and nutrition suggestions
- **Smart Matching**: Instructor-student compatibility algorithms
- **Progress Prediction**: Machine learning-based fitness forecasting
- **Adaptive Workouts**: Dynamic difficulty adjustment based on performance

### 🎨 User Experience
- **Multi-language Support**: English and Turkish localization
- **Dark/Light Theme**: Adaptive UI with custom theming
- **Responsive Design**: Optimized for all screen sizes
- **Offline Support**: Local data caching and sync
- **Accessibility**: Full accessibility compliance
- **Cultural Elements**: Inspirational Atatürk quotes with Turkish flag red design celebrating heritage and athletic values

## 🚀 Recent Updates (January 2025)

### 📊 Database Schema Restructuring
- **Normalized Instructor Data**: Separated instructor information across specialized tables for better organization
- **Enhanced Capacity Management**: Dedicated `instructor_capacity` table for student enrollment tracking
- **Pricing Configuration**: Specialized `instructor_subscription_configs` table for subscription plans
- **Professional Profiles**: Separate tables for work history, certifications, and FAQs
- **Improved Performance**: Better query optimization and data retrieval

### 💻 Flutter Codebase Migration
- **Domain Model Updates**: Migrated from Trainer* to Instructor* models throughout the application
- **Provider Enhancements**: Updated all Riverpod providers to use new database schema
- **Backward Compatibility**: Maintained API compatibility with type aliases and parameter mapping
- **Enhanced Enrollment**: Improved student enrollment validation and capacity checking

## 🏗️ Architecture

FitGo follows **Domain-Driven Design (DDD)** principles with **Clean Architecture** patterns, implementing best practices from Remi Rousselet and Andrea Bizzotto.

### 📁 Project Structure

```
lib/
├── main.dart                          # App entry point
├── bootstrap.dart                     # App initialization
├── app.dart                          # Main app widget
├── core/                             # Core infrastructure
│   ├── constants/                    # App-wide constants
│   ├── environment/                  # Environment configuration
│   ├── local_storage/               # Hive storage abstraction
│   ├── network/                     # Network utilities
│   ├── router/                      # GoRouter configuration
│   └── services/                    # Core services
├── src/                             # Main application code
│   ├── app_provider.dart            # Global state providers
│   ├── auth/                        # Authentication module
│   │   ├── application/             # Business logic (Riverpod)
│   │   ├── domain/                  # Domain models
│   │   ├── repository/              # Data access layer
│   │   └── presentation/            # UI components
│   ├── student/                     # Student-specific features
│   ├── instructor/                  # Instructor-specific features
│   ├── shared/                      # Shared components
│   │   ├── widgets/                 # Reusable UI components
│   │   ├── providers/               # Shared providers
│   │   ├── utils/                   # Utility functions
│   │   └── extensions/              # Dart extensions
│   └── theme/                       # App theming
├── features/                        # Feature modules (DDD)
│   ├── auth/                        # Authentication feature
│   ├── workout/                     # Workout management
│   ├── nutrition/                   # Nutrition tracking
│   ├── progress/                    # Progress analytics
│   ├── social/                      # Social features
│   ├── payments/                    # Payment processing
│   └── ai/                          # AI recommendations
├── start/                           # App startup flow
│   ├── splash/                      # Splash screen
│   └── onboarding/                  # User onboarding
└── localization/                    # i18n support
```

### 🔧 Key Architectural Patterns

- **State Management**: Riverpod with StateNotifier pattern
- **Dependency Injection**: Provider-based DI with proper scoping
- **Navigation**: GoRouter with type-safe routing
- **Data Layer**: Repository pattern with Supabase integration
- **Error Handling**: Comprehensive error boundaries and logging
- **Local Storage**: Hive for persistent data with type safety

## 🚀 Getting Started

### Prerequisites

- **Flutter SDK**: 3.7.0 or higher
- **Dart SDK**: 3.7.0 or higher
- **Puro**: Flutter version management (recommended)
- **Supabase Account**: For backend services
- **IDE**: VS Code or Android Studio with Flutter plugins

### 🛠️ Installation

1. **Clone the repository**
   ```bash
   git clone https://github.com/yourusername/fitgo_app.git
   cd fitgo_app
   ```

2. **Setup Flutter with Puro (Recommended)**
   ```bash
   puro upgrade stable
   puro use stable
   puro pub get
   ```

   Or with standard Flutter:
   ```bash
   flutter pub get
   ```

3. **Environment Configuration**

   Update environment files with your Supabase credentials:
   - `lib/core/environment/development_environment.dart`
   - `lib/core/environment/production_environment.dart`

   ```dart
   // Example configuration
   static const String supabaseUrl = 'YOUR_SUPABASE_URL';
   static const String supabaseAnonKey = 'YOUR_SUPABASE_ANON_KEY';
   ```

4. **Database Setup**

   Execute the database migrations in order:
   ```sql
   -- Run these in your Supabase SQL editor
   database_schema/migration_001_unified_profiles.sql
   database_schema/migration_002_data_migration.sql
   database_schema/migration_003_cleanup.sql
   ```

5. **Generate Code**
   ```bash
   flutter packages pub run build_runner build --delete-conflicting-outputs
   ```

### 🏃‍♂️ Running the App

**Development Environment:**
```bash
flutter run --dart-define envConfig=development
```

**Production Environment:**
```bash
flutter run --dart-define envConfig=production --release
```

**With Device Selection:**
```bash
flutter run --dart-define envConfig=development -d chrome
flutter run --dart-define envConfig=development -d android
flutter run --dart-define envConfig=development -d ios
```

## 📱 App Navigation & User Flow

### 🎯 User Journey

1. **First Launch**: Splash screen → Landing page (user type selection)
2. **Onboarding**: Multi-step introduction → Authentication
3. **Authentication**: Login/Register with multiple providers
4. **Role-based Navigation**:
   - **Students**: Course list → Course enrollment → Dashboard
   - **Instructors**: Instructor dashboard → Student management

### 🧭 Navigation Structure

```
/splash                    # App initialization
├── /landing              # User type selection (Student/Instructor)
├── /onboarding          # Multi-step onboarding flow
├── /auth                # Authentication (Login/Register)
│   ├── /forgot-password # Password reset flow
│   ├── /reset-password  # Password reset form
│   └── /reset-confirm   # Reset confirmation
├── /course-list         # Student: Browse available courses
├── /trainer-detail/:id  # Student: View instructor details
├── /dashboard           # Student: Main dashboard with tabs
│   ├── Exercise Tab     # Workout tracking
│   ├── Nutrition Tab    # Meal planning & tracking
│   ├── Progress Tab     # Analytics & charts
│   └── Explore Tab      # Discover new content
├── /instructor-main     # Instructor: Management dashboard
│   ├── Homepage Tab     # Overview & statistics
│   ├── Members Tab      # Student management
│   ├── Profile Tab      # Instructor profile
│   └── Explore Tab      # Content discovery
├── /profile-form        # User profile setup
├── /form-approval       # Profile approval waiting
└── /dashboard           # Unified dashboard (context-aware)
```

## 🔧 Development

### 🧪 Testing

Run the comprehensive test suite:

```bash
# Unit tests
flutter test

# Integration tests
flutter test integration_test/

# Specific test files
flutter test test/trainer_detail_test.dart
flutter test test/date_input_formatter_test.dart
```

### 🔍 Code Quality

The project uses strict linting and analysis:

```bash
# Run static analysis
flutter analyze

# Check for unused dependencies
flutter pub deps

# Format code
dart format .
```

**Linting Configuration:**
- `very_good_analysis`: Strict Flutter/Dart linting
- `riverpod_lint`: Riverpod-specific rules
- `flutter_lints`: Official Flutter linting

### 🏗️ Build & Deployment

**Android Build:**
```bash
# Debug APK
flutter build apk --dart-define envConfig=production

# Release APK
flutter build apk --release --dart-define envConfig=production

# App Bundle (for Play Store)
flutter build appbundle --release --dart-define envConfig=production
```

**iOS Build:**
```bash
# Debug
flutter build ios --dart-define envConfig=production

# Release
flutter build ios --release --dart-define envConfig=production
```

**Web Build:**
```bash
flutter build web --dart-define envConfig=production --release
```

## 🗄️ Database Schema

### 📊 Core Tables

**User Management:**
- `profiles`: Base user information (id, email, name, role)
- `user_profiles`: Extended profile data (fitness goals, photos)
- `instructors`: Instructor-specific data (unified from teachers/trainers)
- `students`: Student-specific data and enrollments

**Content & Courses:**
- `courses`: Course information and metadata
- `exercises`: Exercise library and instructions
- `workout_plans`: Structured workout programs
- `nutrition_plans`: Meal plans and dietary guidelines

**Progress & Analytics:**
- `workout_sessions`: Individual workout tracking
- `progress_photos`: Before/after photo tracking
- `measurements`: Body measurements over time
- `nutrition_logs`: Daily food intake tracking

**Social & Payments:**
- `payments`: Transaction history and subscriptions
- `social_interactions`: Likes, comments, follows
- `leaderboards`: Competition and ranking data

### 🔐 Security Features

- **Row Level Security (RLS)**: All tables protected with user-specific policies
- **JWT Authentication**: Secure token-based authentication
- **Role-based Access**: Granular permissions for students/instructors
- **Data Encryption**: Sensitive data encrypted at rest
- **API Rate Limiting**: Protection against abuse

## 🎨 UI/UX Design System

### 🎨 Theme System

**Colors:**
- Primary: Custom fitness-focused color palette
- Dark/Light modes with automatic switching
- Accessibility-compliant contrast ratios

**Typography:**
- **Poppins**: Primary UI font
- **Roboto**: Secondary text
- **Lato**: Body text
- **Oswald**: Headers and emphasis

**Components:**
- `CustomTextFormField`: Consistent form inputs
- `RaisedButtonWidget`: Primary action buttons
- `AppDrawer`: Reusable navigation drawer
- `OnboardingScaffold`: Consistent onboarding layout

### 📱 Responsive Design

- **Breakpoints**: Mobile, tablet, desktop optimized
- **Context Extensions**: `context.height`, `context.width`
- **Adaptive Layouts**: Platform-specific UI patterns
- **Accessibility**: Screen reader support, semantic labels

## 🔌 Integrations

### 🔐 Authentication Providers
- **Supabase Auth**: Primary authentication service
- **Google Sign-In**: OAuth integration
- **Facebook Login**: Social authentication
- **Apple Sign-In**: iOS-specific authentication

### 💳 Payment Processing
- **In-App Purchases**: iOS/Android native payments
- **Subscription Management**: Recurring billing
- **Payment History**: Transaction tracking

### 📊 Analytics & Monitoring
- **Custom Analytics**: User behavior tracking
- **Performance Monitoring**: App performance metrics
- **Error Reporting**: Comprehensive error logging

### 🌐 External Services
- **Wix Integration**: Website password reset handling
- **Deep Linking**: App-to-app navigation
- **Push Notifications**: Real-time user engagement

## 🛠️ Development Tools & Utilities

### 📦 Key Dependencies

**State Management & Architecture:**
```yaml
hooks_riverpod: ^2.6.1          # State management
flutter_hooks: ^0.21.2          # React-like hooks
dartz: ^0.10.1                  # Functional programming
equatable: ^2.0.7               # Value equality
freezed_annotation: ^3.0.0      # Immutable classes
json_annotation: ^4.9.0         # JSON serialization
```

**Backend & Authentication:**
```yaml
supabase_flutter: ^2.9.0        # Backend as a service
google_sign_in: ^6.2.1          # Google authentication
flutter_facebook_auth: ^7.1.1   # Facebook authentication
sign_in_with_apple: ^6.1.3      # Apple authentication
```

**UI & Media:**
```yaml
cached_network_image: ^3.4.1    # Image caching
video_player: ^2.9.2            # Video playback
chewie: ^1.8.5                  # Video player UI
fl_chart: ^1.0.0                # Charts and graphs
syncfusion_flutter_charts: ^28.1.35  # Advanced charts
flutter_svg: ^2.1.0             # SVG support
lottie: ^3.1.3                  # Animations
shimmer: ^3.0.0                 # Loading effects
```

**Storage & Utilities:**
```yaml
hive: ^2.2.3                    # Local database
hive_flutter: ^1.1.0            # Flutter Hive integration
go_router: ^14.6.2              # Navigation
country_code_picker: ^3.3.0     # Phone input
shared_preferences: ^2.4.1      # Simple key-value storage
logger: ^2.4.0                  # Logging utility
uuid: ^4.5.1                    # UUID generation
```

### 🔧 Development Scripts

**Makefile Commands:**
```bash
make setup          # Initial project setup
make clean          # Clean build artifacts
make build-runner   # Generate code
make test           # Run all tests
make analyze        # Static analysis
make format         # Format code
```

**Custom Scripts:**
```bash
# Generate app icons
flutter packages pub run flutter_launcher_icons:main

# Generate localization
flutter gen-l10n

# Build runner with conflict resolution
flutter packages pub run build_runner build --delete-conflicting-outputs
```

## 📚 Documentation

### 📖 Additional Resources

- **[ARCHITECTURE.md](ARCHITECTURE.md)**: Detailed architecture documentation
- **[FITGO_PROJECT_STATUS.md](FITGO_PROJECT_STATUS.md)**: Current project status and database analysis
- **[CHANGELOG.md](CHANGELOG.md)**: Version history and updates
- **[database_schema/](database_schema/)**: Database migration scripts and documentation

### 🎓 Learning Resources

**Flutter & Dart:**
- [Flutter Documentation](https://docs.flutter.dev/)
- [Dart Language Tour](https://dart.dev/guides/language/language-tour)
- [Flutter Widget Catalog](https://docs.flutter.dev/development/ui/widgets)

**Riverpod State Management:**
- [Riverpod Documentation](https://riverpod.dev/)
- [Remi Rousselet's Best Practices](https://github.com/rrousselGit/riverpod)
- [Andrea Bizzotto's Flutter Course](https://codewithandrea.com/)

**Supabase Backend:**
- [Supabase Documentation](https://supabase.com/docs)
- [Supabase Flutter Guide](https://supabase.com/docs/guides/getting-started/tutorials/with-flutter)

## 🤝 Contributing

### 🔄 Development Workflow

1. **Fork the repository**
2. **Create a feature branch**: `git checkout -b feature/amazing-feature`
3. **Follow coding standards**: Use provided linting rules
4. **Write tests**: Ensure good test coverage
5. **Update documentation**: Keep README and docs current
6. **Submit a pull request**: Detailed description required

### 📋 Code Standards

- **Naming Conventions**: Follow Dart naming conventions
- **File Organization**: Maintain DDD structure
- **Comments**: Document complex business logic
- **Testing**: Minimum 80% test coverage for new features
- **Localization**: Add translations for new UI text

### 🐛 Bug Reports

When reporting bugs, please include:
- Device information (OS, version, model)
- Steps to reproduce
- Expected vs actual behavior
- Screenshots or video if applicable
- Relevant logs or error messages

## 📄 License

This project is licensed under the MIT License - see the [LICENSE](LICENSE) file for details.

## 🙏 Acknowledgments

- **Flutter Team**: For the amazing framework
- **Riverpod Community**: For excellent state management
- **Supabase Team**: For the powerful backend platform
- **Open Source Contributors**: For the incredible packages used

## 📞 Support & Contact

- **Website**: [https://www.fitgo.online/](https://www.fitgo.online/)
- **Email**: <EMAIL>
- **Documentation**: Check ARCHITECTURE.md and FITGO_PROJECT_STATUS.md for detailed guides
- **Issues**: Use GitHub Issues for bug reports and feature requests

---

**Made with ❤️ by the FitGo Team**

*Transform your fitness journey with AI-powered personalization and professional guidance.*