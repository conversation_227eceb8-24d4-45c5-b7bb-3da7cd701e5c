-- =====================================================
-- FITGO SOCIAL: INSTRUCTOR REVIEWS TABLE
-- =====================================================
-- Reviews and ratings for instructors
-- Renamed from trainer_reviews with proper column updates

-- Drop table if exists (for development only)
-- DROP TABLE IF EXISTS instructor_reviews CASCADE;

-- Create instructor_reviews table
CREATE TABLE IF NOT EXISTS instructor_reviews (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    instructor_id UUID NOT NULL REFERENCES instructors(profile_id) ON DELETE CASCADE,
    student_id UUID NOT NULL REFERENCES profiles(id) ON DELETE CASCADE,
    enrollment_id UUID REFERENCES enrollments(id) ON DELETE SET NULL,
    
    -- Review content
    rating INTEGER NOT NULL CHECK (rating >= 1 AND rating <= 5),
    title TEXT,
    comment TEXT,
    
    -- Review metadata
    is_verified BOOLEAN DEFAULT false, -- Whether review is from verified enrollment
    is_public BOOLEAN DEFAULT true,
    is_featured BOOLEAN DEFAULT false, -- Featured reviews for instructor profile
    
    -- Moderation
    is_approved BOOLEAN DEFAULT true,
    moderated_by UUID REFERENCES profiles(id),
    moderated_at TIMESTAMP WITH TIME ZONE,
    moderation_notes TEXT,
    
    -- Response from instructor
    instructor_response TEXT,
    instructor_response_date TIMESTAMP WITH TIME ZONE,
    
    -- Helpful votes
    helpful_votes INTEGER DEFAULT 0,
    total_votes INTEGER DEFAULT 0,
    
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    UNIQUE(instructor_id, student_id, enrollment_id) -- One review per enrollment
);

-- Create indexes for better performance
CREATE INDEX IF NOT EXISTS idx_instructor_reviews_instructor_id ON instructor_reviews(instructor_id);
CREATE INDEX IF NOT EXISTS idx_instructor_reviews_student_id ON instructor_reviews(student_id);
CREATE INDEX IF NOT EXISTS idx_instructor_reviews_enrollment_id ON instructor_reviews(enrollment_id);
CREATE INDEX IF NOT EXISTS idx_instructor_reviews_rating ON instructor_reviews(rating);
CREATE INDEX IF NOT EXISTS idx_instructor_reviews_is_public ON instructor_reviews(is_public);
CREATE INDEX IF NOT EXISTS idx_instructor_reviews_is_approved ON instructor_reviews(is_approved);
CREATE INDEX IF NOT EXISTS idx_instructor_reviews_created_at ON instructor_reviews(created_at);

-- Enable Row Level Security
ALTER TABLE instructor_reviews ENABLE ROW LEVEL SECURITY;

-- RLS Policies for instructor_reviews table
-- Students can view public and approved reviews
CREATE POLICY "Students can view public reviews" ON instructor_reviews
    FOR SELECT USING (
        is_public = true 
        AND is_approved = true
        AND EXISTS (
            SELECT 1 FROM profiles p 
            WHERE p.id = auth.uid() 
            AND p.role = 'student'
        )
    );

-- Students can create reviews for their enrollments
CREATE POLICY "Students can create reviews for their enrollments" ON instructor_reviews
    FOR INSERT WITH CHECK (
        auth.uid() = student_id
        AND EXISTS (
            SELECT 1 FROM enrollments e 
            WHERE e.id = instructor_reviews.enrollment_id 
            AND e.student_id = auth.uid()
            AND e.status IN ('completed', 'active')
        )
    );

-- Students can update their own reviews
CREATE POLICY "Students can update own reviews" ON instructor_reviews
    FOR UPDATE USING (auth.uid() = student_id);

-- Instructors can view reviews for their services
CREATE POLICY "Instructors can view their reviews" ON instructor_reviews
    FOR SELECT USING (auth.uid() = instructor_id);

-- Instructors can respond to their reviews
CREATE POLICY "Instructors can respond to their reviews" ON instructor_reviews
    FOR UPDATE USING (auth.uid() = instructor_id);

-- Public can view public and approved reviews
CREATE POLICY "Public can view public reviews" ON instructor_reviews
    FOR SELECT USING (is_public = true AND is_approved = true);

-- Admin can manage all reviews
CREATE POLICY "Admin can manage all reviews" ON instructor_reviews
    FOR ALL USING (
        EXISTS (
            SELECT 1 FROM profiles p 
            WHERE p.id = auth.uid() 
            AND p.role = 'admin'
        )
    );

-- Create trigger for updated_at
CREATE TRIGGER update_instructor_reviews_updated_at 
    BEFORE UPDATE ON instructor_reviews 
    FOR EACH ROW 
    EXECUTE FUNCTION update_updated_at_column();

-- Create function to calculate instructor rating
CREATE OR REPLACE FUNCTION calculate_instructor_rating(instructor_uuid UUID)
RETURNS TABLE (
    average_rating DECIMAL(3,2),
    total_reviews BIGINT,
    rating_distribution JSONB
) AS $$
DECLARE
    rating_dist JSONB;
BEGIN
    -- Calculate rating distribution
    SELECT jsonb_object_agg(rating::TEXT, count) INTO rating_dist
    FROM (
        SELECT rating, COUNT(*) as count
        FROM instructor_reviews 
        WHERE instructor_id = instructor_uuid 
        AND is_public = true 
        AND is_approved = true
        GROUP BY rating
    ) t;
    
    RETURN QUERY
    SELECT 
        ROUND(AVG(rating)::NUMERIC, 2)::DECIMAL(3,2) as average_rating,
        COUNT(*) as total_reviews,
        COALESCE(rating_dist, '{}'::jsonb) as rating_distribution
    FROM instructor_reviews
    WHERE instructor_id = instructor_uuid 
    AND is_public = true 
    AND is_approved = true;
END;
$$ LANGUAGE plpgsql;

-- Create function to get featured reviews
CREATE OR REPLACE FUNCTION get_featured_reviews(instructor_uuid UUID, limit_count INTEGER DEFAULT 3)
RETURNS TABLE (
    id UUID,
    rating INTEGER,
    title TEXT,
    comment TEXT,
    student_name TEXT,
    created_at TIMESTAMP WITH TIME ZONE,
    instructor_response TEXT,
    helpful_votes INTEGER
) AS $$
BEGIN
    RETURN QUERY
    SELECT 
        ir.id,
        ir.rating,
        ir.title,
        ir.comment,
        CONCAT(p.name, ' ', SUBSTRING(p.surname FROM 1 FOR 1), '.') as student_name,
        ir.created_at,
        ir.instructor_response,
        ir.helpful_votes
    FROM instructor_reviews ir
    JOIN profiles p ON p.id = ir.student_id
    WHERE ir.instructor_id = instructor_uuid
    AND ir.is_public = true
    AND ir.is_approved = true
    AND (ir.is_featured = true OR ir.rating >= 4)
    ORDER BY 
        ir.is_featured DESC,
        ir.helpful_votes DESC,
        ir.created_at DESC
    LIMIT limit_count;
END;
$$ LANGUAGE plpgsql;

-- Create function to moderate review
CREATE OR REPLACE FUNCTION moderate_review(
    review_uuid UUID,
    approved BOOLEAN,
    moderator_uuid UUID,
    notes TEXT DEFAULT NULL
) RETURNS VOID AS $$
BEGIN
    UPDATE instructor_reviews 
    SET 
        is_approved = approved,
        moderated_by = moderator_uuid,
        moderated_at = NOW(),
        moderation_notes = notes,
        updated_at = NOW()
    WHERE id = review_uuid;
END;
$$ LANGUAGE plpgsql;

-- Create function to add instructor response
CREATE OR REPLACE FUNCTION add_instructor_response(
    review_uuid UUID,
    response_text TEXT
) RETURNS VOID AS $$
BEGIN
    UPDATE instructor_reviews 
    SET 
        instructor_response = response_text,
        instructor_response_date = NOW(),
        updated_at = NOW()
    WHERE id = review_uuid
    AND instructor_id = auth.uid();
END;
$$ LANGUAGE plpgsql;

-- Create function to vote on review helpfulness
CREATE OR REPLACE FUNCTION vote_review_helpful(
    review_uuid UUID,
    is_helpful BOOLEAN
) RETURNS VOID AS $$
BEGIN
    UPDATE instructor_reviews 
    SET 
        helpful_votes = CASE 
            WHEN is_helpful THEN helpful_votes + 1 
            ELSE helpful_votes 
        END,
        total_votes = total_votes + 1,
        updated_at = NOW()
    WHERE id = review_uuid;
END;
$$ LANGUAGE plpgsql;

-- Comments for documentation
COMMENT ON TABLE instructor_reviews IS 'Reviews and ratings for instructors (renamed from trainer_reviews with proper column updates)';
COMMENT ON COLUMN instructor_reviews.instructor_id IS 'Foreign key to instructors table (now uses instructor_id instead of trainer_id)';
COMMENT ON COLUMN instructor_reviews.student_id IS 'Foreign key to profiles table (reviewer)';
COMMENT ON COLUMN instructor_reviews.is_verified IS 'Whether review is from verified enrollment';
COMMENT ON COLUMN instructor_reviews.is_featured IS 'Whether review is featured on instructor profile';
COMMENT ON COLUMN instructor_reviews.helpful_votes IS 'Number of helpful votes from other users';
COMMENT ON FUNCTION calculate_instructor_rating IS 'Function to calculate average rating and distribution for an instructor';
COMMENT ON FUNCTION get_featured_reviews IS 'Function to get featured reviews for an instructor';
COMMENT ON FUNCTION moderate_review IS 'Function for admin to moderate reviews';
COMMENT ON FUNCTION add_instructor_response IS 'Function for instructor to respond to reviews';
COMMENT ON FUNCTION vote_review_helpful IS 'Function to vote on review helpfulness';
