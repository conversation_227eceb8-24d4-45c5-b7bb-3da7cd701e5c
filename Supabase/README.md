# 🗄️ Fitgo Supabase Database Structure

## 📋 Overview

This directory contains all SQL scripts and database schema definitions for the Fitgo fitness application. The structure is organized by functional domains to maintain clarity and ease of maintenance.

## 🏗️ Database Architecture

### Core Principles
- **Row Level Security (RLS)** enabled on all tables
- **UUID primary keys** for all user-related tables
- **Consistent foreign key relationships** using proper constraints
- **Audit trails** with created_at/updated_at timestamps
- **Soft deletes** where applicable for data integrity

### Schema Organization
```
supabase/
├── 01_core/              # Core user and authentication tables
├── 02_instructors/       # Instructor-specific tables and features
├── 03_students/          # Student-specific tables and features
├── 04_content/           # Exercises, workouts, nutrition content
├── 05_payments/          # Payment and subscription management
├── 06_social/            # Reviews, ratings, social features
├── 07_feedback/          # Student-instructor feedback system
├── 07_analytics/         # Analytics and tracking tables
├── 08_utilities/         # Helper functions and triggers
├── 99_migrations/        # Database migration scripts
└── README.md            # This file
```

## 🚀 Quick Start

### 1. Initial Setup
Run these scripts in order in your Supabase SQL Editor:

```sql
-- 1. Core tables (users, profiles, authentication)
\i 01_core/01_profiles.sql
\i 01_core/02_user_profiles.sql
\i 01_core/03_auth_policies.sql

-- 2. Instructor system
\i 02_instructors/01_instructors.sql
\i 02_instructors/02_instructor_certifications.sql
\i 02_instructors/03_instructor_work_history.sql
\i 02_instructors/04_instructor_faqs.sql
\i 02_instructors/05_instructor_subscription_configs.sql
\i 02_instructors/06_instructor_capacity.sql

-- 3. Student system
\i 03_students/01_students.sql
\i 03_students/02_enrollments.sql

-- 4. Content management
\i 04_content/01_exercises.sql
\i 04_content/02_workout_plans.sql
\i 04_content/03_nutrition_plans.sql

-- 5. Payment system
\i 05_payments/01_payments.sql
\i 05_payments/02_user_subscriptions.sql

-- 6. Social features
\i 06_social/01_instructor_reviews.sql

-- 7. Feedback system
\i 07_feedback/01_feedback.sql

-- 8. Utilities and functions
\i 08_utilities/01_functions.sql
\i 08_utilities/02_triggers.sql
```

### 2. Apply RLS Policies
```sql
-- Enable RLS on all tables
\i 08_utilities/03_enable_rls.sql
```

### 3. Create Storage Buckets
```sql
-- Setup file storage
\i 08_utilities/04_storage_setup.sql
```

## 📊 Database Schema Changes (Major Refactor)

### 🗂 1. profiles table
**Changes Applied:**
- ✅ Removed: `bio`, `avatar_url` (moved to user_profiles)
- ❌ **MISSING**: `gender` field needs to be re-added

**Action Required:**
```sql
-- Re-add gender field to profiles table
ALTER TABLE profiles ADD COLUMN gender VARCHAR(10) CHECK (gender IN ('male', 'female', 'other'));
```

### 🗂 2. instructors table
**Major Cleanup Completed:**
- ✅ Removed legacy columns: `bio`, `specializations`, `department`, `gym_affiliation`
- ✅ Removed pricing columns: `price_per_session`, `price_monthly`, `price_3_months`, `price_6_months`, `price_1_year`
- ✅ Removed metrics: `client_count`, `total_students`, `review_count`
- ✅ Removed links: `linkedin_url`, `instagram_url`, `website_url`
- ✅ Removed location: `address`, `city`, `district`, `postal_code`
- ✅ Removed capacity: `max_course_capacity`, `available_spots`, `max_students`
- ✅ Removed status: `is_active`

**Fields to Migrate:**
- `approval_status` → move to `instructor_subscription_configs`
- `approved_by` → move to `instructor_subscription_configs`
- `approved_at` → move to `instructor_subscription_configs`
- `application_submitted_at` → move to `instructor_subscription_configs`
- `current_students` → move to new `instructor_capacity` table

### 🗂 3. instructor_certifications table
**Changes Applied:**
- ✅ Removed: `is_verified`, `external_link`

### 🗂 4. instructor_subscription_configs table
**Refactoring Required:**
- Move JSON pricing to individual columns for better admin panel updates
- `base_monthly_price` → rename to `user_income_share`
- `premium_plan_pricing` → extract from JSON to column
- `basic_plan_pricing` → extract from JSON to column

### 🗂 5. New Tables Required
- `instructor_capacity` - Track current student counts per instructor
- Enhanced `instructor_subscription_configs` with approval workflow

### 🗂 6. Deleted Tables ✅
- `audit_log` - Removed (orphaned data)
- `capacity_upgrade_transactions` - Removed
- `notification_tokens` - Removed

## 🔄 Backup Strategy

### Automated Backups
- **Point-in-time recovery**: Enabled (7 days retention)
- **Daily snapshots**: Automated at 2:00 AM UTC
- **Weekly full backups**: Sundays at 1:00 AM UTC

### Manual Backup Commands
```sql
-- Export specific table data
COPY (SELECT * FROM profiles) TO '/tmp/profiles_backup.csv' WITH CSV HEADER;

-- Export schema only
pg_dump --schema-only --no-owner --no-privileges fitgo_db > schema_backup.sql

-- Export data only
pg_dump --data-only --no-owner --no-privileges fitgo_db > data_backup.sql
```

### Restore Procedures
```sql
-- Restore from point-in-time (via Supabase Dashboard)
-- 1. Go to Database > Backups
-- 2. Select restore point
-- 3. Confirm restoration

-- Restore from SQL file
\i backup_file.sql
```

## 🛠️ Development Workflow

### Adding New Tables
1. Create SQL file in appropriate domain folder
2. Include RLS policies in the same file
3. Add foreign key constraints
4. Update this README with table documentation
5. Test with sample data

### Modifying Existing Tables
1. Create migration script in `99_migrations/`
2. Test migration on development database
3. Document changes in this README
4. Apply to production during maintenance window

### Testing Database Changes
```sql
-- Check table exists
SELECT EXISTS (
    SELECT FROM information_schema.tables
    WHERE table_schema = 'public'
    AND table_name = 'your_table_name'
);

-- Verify RLS is enabled
SELECT schemaname, tablename, rowsecurity
FROM pg_tables
WHERE schemaname = 'public'
AND rowsecurity = true;

-- Check foreign key constraints
SELECT
    tc.table_name,
    kcu.column_name,
    ccu.table_name AS foreign_table_name,
    ccu.column_name AS foreign_column_name
FROM information_schema.table_constraints AS tc
JOIN information_schema.key_column_usage AS kcu
    ON tc.constraint_name = kcu.constraint_name
    AND tc.table_schema = kcu.table_schema
JOIN information_schema.constraint_column_usage AS ccu
    ON ccu.constraint_name = tc.constraint_name
    AND ccu.table_schema = tc.table_schema
WHERE tc.constraint_type = 'FOREIGN KEY'
AND tc.table_schema='public';
```

## 📈 Performance Optimization

### Indexes
- All foreign keys have indexes
- Frequently queried columns have composite indexes
- Full-text search indexes on content tables

### Query Optimization
- Use prepared statements in application code
- Implement proper pagination with LIMIT/OFFSET
- Use connection pooling (handled by Supabase)

## 🔐 Security

### Row Level Security (RLS)
- All tables have RLS enabled
- Users can only access their own data
- Instructors can access their students' data
- Admin roles have elevated permissions

### API Security
- Never expose service key in client code
- Use JWT tokens for authentication
- Implement proper role-based access control

## 📞 Support

### Common Issues
1. **Table not found**: Check if table exists and RLS policies allow access
2. **Permission denied**: Verify user role and RLS policies
3. **Foreign key constraint**: Ensure referenced records exist

### Debugging
```sql
-- Check current user and role
SELECT auth.uid(), auth.role();

-- Test RLS policy
SET row_security = off; -- Admin only
SELECT * FROM your_table;
SET row_security = on;
```

---

**📝 Last Updated:** 2025-07-16
**🔄 Schema Version:** 2.0
**📊 Total Tables:** 28 (optimized from 58)
**🎯 Database Health:** Excellent (96% active usage)