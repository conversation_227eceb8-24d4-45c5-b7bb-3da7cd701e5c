-- =====================================================
-- FITGO STUDENTS: STUDENTS TABLE
-- =====================================================
-- Student-specific data and enrollments
-- Core table for student management

-- Drop table if exists (for development only)
-- DROP TABLE IF EXISTS students CASCADE;

-- Create students table
CREATE TABLE IF NOT EXISTS students (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    student_id UUID NOT NULL REFERENCES profiles(id) ON DELETE CASCADE,
    instructor_id UUID REFERENCES instructors(profile_id) ON DELETE SET NULL,
    program_type TEXT CHECK (program_type IN ('basic', 'premium', 'trial')),
    enrollment_date TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    status TEXT DEFAULT 'active' CHECK (status IN ('active', 'inactive', 'suspended', 'completed')),
    payment_status TEXT DEFAULT 'pending' CHECK (payment_status IN ('pending', 'paid', 'overdue', 'cancelled')),
    subscription_end_date TIMESTAMP WITH TIME ZONE,
    notes TEXT, -- Instructor notes about the student
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    UNIQUE(student_id, instructor_id) -- A student can only be enrolled once with each instructor
);

-- Create indexes for better performance
CREATE INDEX IF NOT EXISTS idx_students_student_id ON students(student_id);
CREATE INDEX IF NOT EXISTS idx_students_instructor_id ON students(instructor_id);
CREATE INDEX IF NOT EXISTS idx_students_program_type ON students(program_type);
CREATE INDEX IF NOT EXISTS idx_students_status ON students(status);
CREATE INDEX IF NOT EXISTS idx_students_payment_status ON students(payment_status);
CREATE INDEX IF NOT EXISTS idx_students_enrollment_date ON students(enrollment_date);

-- Enable Row Level Security
ALTER TABLE students ENABLE ROW LEVEL SECURITY;

-- RLS Policies for students table
-- Students can view their own enrollment records
CREATE POLICY "Students can view own enrollments" ON students
    FOR SELECT USING (auth.uid() = student_id);

-- Students can update their own enrollment records (limited fields)
CREATE POLICY "Students can update own enrollments" ON students
    FOR UPDATE USING (auth.uid() = student_id);

-- Instructors can view their students
CREATE POLICY "Instructors can view their students" ON students
    FOR SELECT USING (auth.uid() = instructor_id);

-- Instructors can update their students (notes, status)
CREATE POLICY "Instructors can update their students" ON students
    FOR UPDATE USING (auth.uid() = instructor_id);

-- Instructors can create new student enrollments
CREATE POLICY "Instructors can create student enrollments" ON students
    FOR INSERT WITH CHECK (auth.uid() = instructor_id);

-- Admin can view and manage all student records
CREATE POLICY "Admin can manage all students" ON students
    FOR ALL USING (
        EXISTS (
            SELECT 1 FROM profiles p 
            WHERE p.id = auth.uid() 
            AND p.role = 'admin'
        )
    );

-- Create trigger for updated_at
CREATE TRIGGER update_students_updated_at 
    BEFORE UPDATE ON students 
    FOR EACH ROW 
    EXECUTE FUNCTION update_updated_at_column();

-- Create function to handle student enrollment
CREATE OR REPLACE FUNCTION enroll_student(
    student_uuid UUID,
    instructor_uuid UUID,
    program_type_param TEXT DEFAULT 'basic'
) RETURNS UUID AS $$
DECLARE
    enrollment_id UUID;
    instructor_capacity_check INTEGER;
BEGIN
    -- Check if instructor has capacity
    SELECT available_spots INTO instructor_capacity_check
    FROM instructor_capacity 
    WHERE instructor_id = instructor_uuid;
    
    IF instructor_capacity_check IS NULL OR instructor_capacity_check <= 0 THEN
        RAISE EXCEPTION 'Instructor has no available capacity';
    END IF;
    
    -- Create enrollment
    INSERT INTO students (student_id, instructor_id, program_type)
    VALUES (student_uuid, instructor_uuid, program_type_param)
    RETURNING id INTO enrollment_id;
    
    -- Update instructor capacity
    PERFORM update_instructor_student_count(instructor_uuid, 1);
    
    RETURN enrollment_id;
END;
$$ LANGUAGE plpgsql;

-- Create function to handle student unenrollment
CREATE OR REPLACE FUNCTION unenroll_student(
    student_uuid UUID,
    instructor_uuid UUID
) RETURNS VOID AS $$
BEGIN
    -- Update enrollment status
    UPDATE students 
    SET 
        status = 'completed',
        updated_at = NOW()
    WHERE student_id = student_uuid 
    AND instructor_id = instructor_uuid
    AND status = 'active';
    
    -- Update instructor capacity
    PERFORM update_instructor_student_count(instructor_uuid, -1);
END;
$$ LANGUAGE plpgsql;

-- Create function to get student dashboard data
CREATE OR REPLACE FUNCTION get_student_dashboard(student_uuid UUID)
RETURNS TABLE (
    instructor_name TEXT,
    instructor_id UUID,
    program_type TEXT,
    enrollment_date TIMESTAMP WITH TIME ZONE,
    status TEXT,
    subscription_end_date TIMESTAMP WITH TIME ZONE
) AS $$
BEGIN
    RETURN QUERY
    SELECT 
        CONCAT(p.name, ' ', p.surname) as instructor_name,
        s.instructor_id,
        s.program_type,
        s.enrollment_date,
        s.status,
        s.subscription_end_date
    FROM students s
    JOIN profiles p ON p.id = s.instructor_id
    WHERE s.student_id = student_uuid
    AND s.status = 'active'
    ORDER BY s.enrollment_date DESC;
END;
$$ LANGUAGE plpgsql;

-- Comments for documentation
COMMENT ON TABLE students IS 'Student-specific data and enrollments - core table for student management';
COMMENT ON COLUMN students.student_id IS 'Foreign key to profiles table (student)';
COMMENT ON COLUMN students.instructor_id IS 'Foreign key to instructors table (assigned instructor)';
COMMENT ON COLUMN students.program_type IS 'Type of program: basic, premium, trial';
COMMENT ON COLUMN students.status IS 'Enrollment status: active, inactive, suspended, completed';
COMMENT ON COLUMN students.payment_status IS 'Payment status: pending, paid, overdue, cancelled';
COMMENT ON FUNCTION enroll_student IS 'Function to enroll a student with an instructor (checks capacity)';
COMMENT ON FUNCTION unenroll_student IS 'Function to unenroll a student from an instructor';
COMMENT ON FUNCTION get_student_dashboard IS 'Function to get student dashboard data with instructor info';
