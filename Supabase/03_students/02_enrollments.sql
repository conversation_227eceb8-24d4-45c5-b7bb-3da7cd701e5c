-- =====================================================
-- FITGO STUDENTS: ENROLLMENTS TABLE
-- =====================================================
-- Detailed enrollment tracking and history
-- Separate from students table for better data organization

-- Drop table if exists (for development only)
-- DROP TABLE IF EXISTS enrollments CASCADE;

-- Create enrollments table
CREATE TABLE IF NOT EXISTS enrollments (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    student_id UUID NOT NULL REFERENCES profiles(id) ON DELETE CASCADE,
    instructor_id UUID NOT NULL REFERENCES instructors(profile_id) ON DELETE CASCADE,
    course_id UUID, -- Future reference to courses table
    
    -- Enrollment details
    enrollment_type TEXT NOT NULL CHECK (enrollment_type IN ('course', 'personal_training', 'group_session')),
    program_level TEXT CHECK (program_level IN ('beginner', 'intermediate', 'advanced')),
    enrollment_date TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    start_date TIMESTAMP WITH TIME ZONE,
    end_date TIMESTAMP WITH TIME ZONE,
    
    -- Status tracking
    status TEXT DEFAULT 'pending' CHECK (status IN ('pending', 'confirmed', 'active', 'completed', 'cancelled', 'suspended')),
    completion_percentage DECIMAL(5,2) DEFAULT 0.00 CHECK (completion_percentage >= 0 AND completion_percentage <= 100),
    
    -- Payment information
    payment_amount DECIMAL(10,2),
    payment_currency TEXT DEFAULT 'TRY',
    payment_status TEXT DEFAULT 'pending' CHECK (payment_status IN ('pending', 'paid', 'partial', 'refunded', 'failed')),
    payment_date TIMESTAMP WITH TIME ZONE,
    
    -- Additional information
    enrollment_notes TEXT,
    instructor_notes TEXT,
    cancellation_reason TEXT,
    
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Create indexes for better performance
CREATE INDEX IF NOT EXISTS idx_enrollments_student_id ON enrollments(student_id);
CREATE INDEX IF NOT EXISTS idx_enrollments_instructor_id ON enrollments(instructor_id);
CREATE INDEX IF NOT EXISTS idx_enrollments_course_id ON enrollments(course_id);
CREATE INDEX IF NOT EXISTS idx_enrollments_enrollment_type ON enrollments(enrollment_type);
CREATE INDEX IF NOT EXISTS idx_enrollments_status ON enrollments(status);
CREATE INDEX IF NOT EXISTS idx_enrollments_payment_status ON enrollments(payment_status);
CREATE INDEX IF NOT EXISTS idx_enrollments_enrollment_date ON enrollments(enrollment_date);

-- Enable Row Level Security
ALTER TABLE enrollments ENABLE ROW LEVEL SECURITY;

-- RLS Policies for enrollments table
-- Students can view their own enrollments
CREATE POLICY "Students can view own enrollments" ON enrollments
    FOR SELECT USING (auth.uid() = student_id);

-- Students can update their own enrollments (limited fields)
CREATE POLICY "Students can update own enrollments" ON enrollments
    FOR UPDATE USING (
        auth.uid() = student_id 
        AND status IN ('pending', 'confirmed')
    );

-- Instructors can view enrollments for their courses
CREATE POLICY "Instructors can view their enrollments" ON enrollments
    FOR SELECT USING (auth.uid() = instructor_id);

-- Instructors can update enrollments for their courses
CREATE POLICY "Instructors can update their enrollments" ON enrollments
    FOR UPDATE USING (auth.uid() = instructor_id);

-- Instructors can create new enrollments
CREATE POLICY "Instructors can create enrollments" ON enrollments
    FOR INSERT WITH CHECK (auth.uid() = instructor_id);

-- Admin can view and manage all enrollments
CREATE POLICY "Admin can manage all enrollments" ON enrollments
    FOR ALL USING (
        EXISTS (
            SELECT 1 FROM profiles p 
            WHERE p.id = auth.uid() 
            AND p.role = 'admin'
        )
    );

-- Create trigger for updated_at
CREATE TRIGGER update_enrollments_updated_at 
    BEFORE UPDATE ON enrollments 
    FOR EACH ROW 
    EXECUTE FUNCTION update_updated_at_column();

-- Create function to confirm enrollment
CREATE OR REPLACE FUNCTION confirm_enrollment(enrollment_uuid UUID)
RETURNS VOID AS $$
BEGIN
    UPDATE enrollments 
    SET 
        status = 'confirmed',
        start_date = COALESCE(start_date, NOW()),
        updated_at = NOW()
    WHERE id = enrollment_uuid
    AND status = 'pending';
END;
$$ LANGUAGE plpgsql;

-- Create function to cancel enrollment
CREATE OR REPLACE FUNCTION cancel_enrollment(
    enrollment_uuid UUID,
    reason TEXT DEFAULT NULL
) RETURNS VOID AS $$
BEGIN
    UPDATE enrollments 
    SET 
        status = 'cancelled',
        cancellation_reason = reason,
        updated_at = NOW()
    WHERE id = enrollment_uuid
    AND status IN ('pending', 'confirmed', 'active');
END;
$$ LANGUAGE plpgsql;

-- Create function to update completion percentage
CREATE OR REPLACE FUNCTION update_enrollment_progress(
    enrollment_uuid UUID,
    progress_percentage DECIMAL(5,2)
) RETURNS VOID AS $$
BEGIN
    UPDATE enrollments 
    SET 
        completion_percentage = progress_percentage,
        status = CASE 
            WHEN progress_percentage >= 100 THEN 'completed'
            WHEN progress_percentage > 0 THEN 'active'
            ELSE status
        END,
        updated_at = NOW()
    WHERE id = enrollment_uuid;
END;
$$ LANGUAGE plpgsql;

-- Create function to get enrollment statistics for instructor
CREATE OR REPLACE FUNCTION get_instructor_enrollment_stats(instructor_uuid UUID)
RETURNS TABLE (
    total_enrollments BIGINT,
    active_enrollments BIGINT,
    completed_enrollments BIGINT,
    pending_enrollments BIGINT,
    cancelled_enrollments BIGINT,
    average_completion_rate DECIMAL(5,2),
    total_revenue DECIMAL(10,2)
) AS $$
BEGIN
    RETURN QUERY
    SELECT 
        COUNT(*) as total_enrollments,
        COUNT(*) FILTER (WHERE status = 'active') as active_enrollments,
        COUNT(*) FILTER (WHERE status = 'completed') as completed_enrollments,
        COUNT(*) FILTER (WHERE status = 'pending') as pending_enrollments,
        COUNT(*) FILTER (WHERE status = 'cancelled') as cancelled_enrollments,
        AVG(completion_percentage) FILTER (WHERE status IN ('active', 'completed')) as average_completion_rate,
        SUM(payment_amount) FILTER (WHERE payment_status = 'paid') as total_revenue
    FROM enrollments
    WHERE instructor_id = instructor_uuid;
END;
$$ LANGUAGE plpgsql;

-- Comments for documentation
COMMENT ON TABLE enrollments IS 'Detailed enrollment tracking and history (separate from students table for better organization)';
COMMENT ON COLUMN enrollments.student_id IS 'Foreign key to profiles table (student)';
COMMENT ON COLUMN enrollments.instructor_id IS 'Foreign key to instructors table';
COMMENT ON COLUMN enrollments.course_id IS 'Future reference to courses table';
COMMENT ON COLUMN enrollments.enrollment_type IS 'Type: course, personal_training, group_session';
COMMENT ON COLUMN enrollments.completion_percentage IS 'Progress percentage (0-100)';
COMMENT ON COLUMN enrollments.payment_currency IS 'Payment currency (default TRY for Turkish Lira)';
COMMENT ON FUNCTION confirm_enrollment IS 'Function to confirm a pending enrollment';
COMMENT ON FUNCTION cancel_enrollment IS 'Function to cancel an enrollment with reason';
COMMENT ON FUNCTION update_enrollment_progress IS 'Function to update enrollment completion progress';
COMMENT ON FUNCTION get_instructor_enrollment_stats IS 'Function to get enrollment statistics for an instructor';
