-- =====================================================
-- FITGO MIGRATION 002: PRICING STRUCTURE REFACTOR
-- =====================================================
-- Refactor instructor subscription configs from JSON to columns
-- Add admin settings for commission management
-- Update pricing calculation logic

BEGIN;

-- =====================================================
-- 1. CREATE ADMIN SETTINGS TABLE
-- =====================================================

-- Create admin_settings table if not exists
CREATE TABLE IF NOT EXISTS admin_settings (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    setting_key TEXT NOT NULL UNIQUE,
    setting_value TEXT NOT NULL,
    setting_type TEXT NOT NULL CHECK (setting_type IN ('string', 'number', 'boolean', 'json')),
    description TEXT,
    category TEXT DEFAULT 'general',
    is_editable BOOLEAN DEFAULT true,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_by UUID REFERENCES profiles(id)
);

-- Create indexes
CREATE INDEX IF NOT EXISTS idx_admin_settings_key ON admin_settings(setting_key);
CREATE INDEX IF NOT EXISTS idx_admin_settings_category ON admin_settings(category);

-- Enable RLS
ALTER TABLE admin_settings ENABLE ROW LEVEL SECURITY;

-- =====================================================
-- 2. CLEAN UP AUDIT_LOG TABLE
-- =====================================================

-- Drop audit_log table as it's no longer needed
DROP TABLE IF EXISTS audit_log CASCADE;

-- =====================================================
-- 3. MIGRATE INSTRUCTOR SUBSCRIPTION CONFIGS
-- =====================================================

-- Add new columns to instructor_subscription_configs
DO $$
BEGIN
    -- Add desired_monthly_earnings column
    IF NOT EXISTS (
        SELECT 1 FROM information_schema.columns
        WHERE table_name = 'instructor_subscription_configs'
        AND column_name = 'desired_monthly_earnings'
    ) THEN
        ALTER TABLE instructor_subscription_configs
        ADD COLUMN desired_monthly_earnings DECIMAL(10,2) DEFAULT 0.00;
    END IF;

    -- Add basic plan pricing columns (monthly, 6-month, yearly)
    IF NOT EXISTS (
        SELECT 1 FROM information_schema.columns
        WHERE table_name = 'instructor_subscription_configs'
        AND column_name = 'basic_plan_monthly_price'
    ) THEN
        ALTER TABLE instructor_subscription_configs
        ADD COLUMN basic_plan_monthly_price DECIMAL(10,2) DEFAULT 0.00;
    END IF;

    IF NOT EXISTS (
        SELECT 1 FROM information_schema.columns
        WHERE table_name = 'instructor_subscription_configs'
        AND column_name = 'basic_plan_6month_price'
    ) THEN
        ALTER TABLE instructor_subscription_configs
        ADD COLUMN basic_plan_6month_price DECIMAL(10,2) DEFAULT 0.00;
    END IF;

    IF NOT EXISTS (
        SELECT 1 FROM information_schema.columns
        WHERE table_name = 'instructor_subscription_configs'
        AND column_name = 'basic_plan_yearly_price'
    ) THEN
        ALTER TABLE instructor_subscription_configs
        ADD COLUMN basic_plan_yearly_price DECIMAL(10,2) DEFAULT 0.00;
    END IF;

    -- Add premium plan pricing columns (monthly, 6-month, yearly)
    IF NOT EXISTS (
        SELECT 1 FROM information_schema.columns
        WHERE table_name = 'instructor_subscription_configs'
        AND column_name = 'premium_plan_monthly_price'
    ) THEN
        ALTER TABLE instructor_subscription_configs
        ADD COLUMN premium_plan_monthly_price DECIMAL(10,2) DEFAULT 0.00;
    END IF;

    IF NOT EXISTS (
        SELECT 1 FROM information_schema.columns
        WHERE table_name = 'instructor_subscription_configs'
        AND column_name = 'premium_plan_6month_price'
    ) THEN
        ALTER TABLE instructor_subscription_configs
        ADD COLUMN premium_plan_6month_price DECIMAL(10,2) DEFAULT 0.00;
    END IF;

    IF NOT EXISTS (
        SELECT 1 FROM information_schema.columns
        WHERE table_name = 'instructor_subscription_configs'
        AND column_name = 'premium_plan_yearly_price'
    ) THEN
        ALTER TABLE instructor_subscription_configs
        ADD COLUMN premium_plan_yearly_price DECIMAL(10,2) DEFAULT 0.00;
    END IF;
    
    -- Add plan description columns
    IF NOT EXISTS (
        SELECT 1 FROM information_schema.columns 
        WHERE table_name = 'instructor_subscription_configs' 
        AND column_name = 'basic_plan_description'
    ) THEN
        ALTER TABLE instructor_subscription_configs 
        ADD COLUMN basic_plan_description TEXT;
    END IF;
    
    IF NOT EXISTS (
        SELECT 1 FROM information_schema.columns 
        WHERE table_name = 'instructor_subscription_configs' 
        AND column_name = 'premium_plan_description'
    ) THEN
        ALTER TABLE instructor_subscription_configs 
        ADD COLUMN premium_plan_description TEXT;
    END IF;
    
    -- Add submission tracking columns if not exists
    IF NOT EXISTS (
        SELECT 1 FROM information_schema.columns 
        WHERE table_name = 'instructor_subscription_configs' 
        AND column_name = 'submission_status'
    ) THEN
        ALTER TABLE instructor_subscription_configs 
        ADD COLUMN submission_status TEXT DEFAULT 'draft' CHECK (submission_status IN ('draft', 'submitted', 'approved', 'rejected', 'needs_revision'));
    END IF;
    
    IF NOT EXISTS (
        SELECT 1 FROM information_schema.columns 
        WHERE table_name = 'instructor_subscription_configs' 
        AND column_name = 'last_submitted_at'
    ) THEN
        ALTER TABLE instructor_subscription_configs 
        ADD COLUMN last_submitted_at TIMESTAMP WITH TIME ZONE;
    END IF;
    
    IF NOT EXISTS (
        SELECT 1 FROM information_schema.columns 
        WHERE table_name = 'instructor_subscription_configs' 
        AND column_name = 'admin_feedback'
    ) THEN
        ALTER TABLE instructor_subscription_configs 
        ADD COLUMN admin_feedback TEXT;
    END IF;
END;
$$;

-- =====================================================
-- 4. MIGRATE DATA FROM JSON TO COLUMNS AND CLEANUP
-- =====================================================

-- Migrate existing JSON data to new columns and calculate prices
DO $$
DECLARE
    config_record RECORD;
    basic_pricing JSONB;
    premium_pricing JSONB;
    desired_earnings DECIMAL(10,2);
BEGIN
    -- First, migrate data from JSON columns to individual columns (if they exist)
    FOR config_record IN
        SELECT id, instructor_id, base_monthly_price
        FROM instructor_subscription_configs
        WHERE base_monthly_price IS NOT NULL AND base_monthly_price > 0
    LOOP
        -- Migrate base_monthly_price to desired_monthly_earnings and calculate all prices
        desired_earnings := config_record.base_monthly_price;

        -- Use the new function to calculate all plan prices
        PERFORM calculate_plan_prices_from_earnings(config_record.instructor_id, desired_earnings);

        RAISE NOTICE 'Migrated pricing for instructor: %, desired earnings: %', config_record.instructor_id, desired_earnings;
    END LOOP;

    RAISE NOTICE 'Data migration completed successfully';
END;
$$;

-- =====================================================
-- 5. CLEANUP UNUSED JSON COLUMNS
-- =====================================================

-- Remove unused JSON pricing columns
DO $$
BEGIN
    -- Drop basic_plan_pricing column if exists
    IF EXISTS (
        SELECT 1 FROM information_schema.columns
        WHERE table_name = 'instructor_subscription_configs'
        AND column_name = 'basic_plan_pricing'
    ) THEN
        ALTER TABLE instructor_subscription_configs DROP COLUMN basic_plan_pricing;
        RAISE NOTICE 'Dropped basic_plan_pricing column';
    END IF;

    -- Drop premium_plan_pricing column if exists
    IF EXISTS (
        SELECT 1 FROM information_schema.columns
        WHERE table_name = 'instructor_subscription_configs'
        AND column_name = 'premium_plan_pricing'
    ) THEN
        ALTER TABLE instructor_subscription_configs DROP COLUMN premium_plan_pricing;
        RAISE NOTICE 'Dropped premium_plan_pricing column';
    END IF;
END;
$$;

-- =====================================================
-- 4. INSERT DEFAULT ADMIN SETTINGS
-- =====================================================

INSERT INTO admin_settings (setting_key, setting_value, setting_type, description, category, is_editable) VALUES
-- Commission and pricing settings
('platform_commission_rate', '0.15', 'number', 'Platform commission rate (0.15 = 15%)', 'pricing', true),
('instructor_commission_rate', '0.85', 'number', 'Instructor commission rate (0.85 = 85%)', 'pricing', true),
('minimum_plan_price', '100.00', 'number', 'Minimum plan price in TRY', 'pricing', true),
('maximum_plan_price', '5000.00', 'number', 'Maximum plan price in TRY', 'pricing', true),

-- Platform settings
('platform_currency', 'TRY', 'string', 'Platform default currency', 'general', true),
('max_students_free_tier', '5', 'number', 'Maximum students for free tier instructors', 'general', true),
('max_students_premium_tier', '50', 'number', 'Maximum students for premium tier instructors', 'general', true),

-- Feature flags
('enable_instructor_approval', 'true', 'boolean', 'Enable instructor approval workflow', 'features', true),
('enable_payment_processing', 'true', 'boolean', 'Enable payment processing', 'features', true),
('enable_notifications', 'true', 'boolean', 'Enable push notifications', 'features', true),

-- Business rules
('instructor_min_experience_years', '1', 'number', 'Minimum experience years for instructors', 'business', true),
('plan_duration_options', '[1, 3, 6, 12]', 'json', 'Available plan duration options in months', 'business', true),
('supported_languages', '["tr", "en"]', 'json', 'Supported platform languages', 'general', true)

ON CONFLICT (setting_key) DO UPDATE SET
    setting_value = EXCLUDED.setting_value,
    updated_at = NOW();

-- =====================================================
-- 5. CREATE HELPER FUNCTIONS
-- =====================================================

-- Function to get admin setting
CREATE OR REPLACE FUNCTION get_admin_setting(key_name TEXT)
RETURNS TEXT AS $$
DECLARE
    result TEXT;
BEGIN
    SELECT setting_value INTO result
    FROM admin_settings
    WHERE setting_key = key_name;
    
    RETURN result;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Function to calculate all plan prices from desired earnings
CREATE OR REPLACE FUNCTION calculate_plan_prices_from_earnings(
    instructor_uuid UUID,
    desired_earnings DECIMAL(10,2)
) RETURNS VOID AS $$
DECLARE
    commission_rate DECIMAL(5,4);
    basic_monthly DECIMAL(10,2);
    basic_6month DECIMAL(10,2);
    basic_yearly DECIMAL(10,2);
    premium_monthly DECIMAL(10,2);
    premium_6month DECIMAL(10,2);
    premium_yearly DECIMAL(10,2);
BEGIN
    -- Get commission rate from admin settings
    SELECT CAST(setting_value AS DECIMAL(5,4)) INTO commission_rate
    FROM admin_settings
    WHERE setting_key = 'instructor_commission_rate';

    -- If no commission rate found, use default 85%
    IF commission_rate IS NULL THEN
        commission_rate := 0.85;
    END IF;

    -- Calculate basic plan prices (desired earnings / commission rate)
    basic_monthly := ROUND(desired_earnings / commission_rate, 2);
    basic_6month := ROUND((desired_earnings * 6) / commission_rate * 0.95, 2); -- 5% discount for 6 months
    basic_yearly := ROUND((desired_earnings * 12) / commission_rate * 0.90, 2); -- 10% discount for yearly

    -- Calculate premium plan prices (1.5x basic plan)
    premium_monthly := ROUND(basic_monthly * 1.5, 2);
    premium_6month := ROUND(basic_6month * 1.5, 2);
    premium_yearly := ROUND(basic_yearly * 1.5, 2);

    -- Update instructor subscription config
    UPDATE instructor_subscription_configs
    SET
        desired_monthly_earnings = desired_earnings,
        basic_plan_monthly_price = basic_monthly,
        basic_plan_6month_price = basic_6month,
        basic_plan_yearly_price = basic_yearly,
        premium_plan_monthly_price = premium_monthly,
        premium_plan_6month_price = premium_6month,
        premium_plan_yearly_price = premium_yearly,
        updated_at = NOW()
    WHERE instructor_id = instructor_uuid;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- =====================================================
-- 6. LOG MIGRATION
-- =====================================================

INSERT INTO migration_log (migration_version, migration_name, status, notes) VALUES
('002', 'pricing_refactor', 'completed', 'Refactored pricing structure from JSON to columns, removed unused JSON fields, added admin settings');

COMMIT;

-- Final verification
DO $$
BEGIN
    RAISE NOTICE '=== MIGRATION 002 COMPLETED ===';
    RAISE NOTICE 'Pricing structure refactor has been applied successfully';
    RAISE NOTICE 'Admin settings table created with default values';
    RAISE NOTICE 'JSON pricing data migrated to individual columns';
    RAISE NOTICE 'Helper functions created for price calculations';
END;
$$;
