-- =====================================================
-- FITGO PAYMENTS: PAYMENTS TABLE
-- =====================================================
-- Financial transaction records and payment processing
-- Core table for revenue tracking

-- Drop table if exists (for development only)
-- DROP TABLE IF EXISTS payments CASCADE;

-- Create payments table
CREATE TABLE IF NOT EXISTS payments (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    student_id UUID NOT NULL REFERENCES profiles(id) ON DELETE CASCADE,
    instructor_id UUID NOT NULL REFERENCES instructors(profile_id) ON DELETE CASCADE,
    enrollment_id UUID REFERENCES enrollments(id) ON DELETE SET NULL,
    
    -- Payment details
    amount DECIMAL(10,2) NOT NULL CHECK (amount > 0),
    currency TEXT DEFAULT 'TRY' NOT NULL,
    payment_method TEXT CHECK (payment_method IN ('credit_card', 'debit_card', 'bank_transfer', 'paypal', 'stripe', 'iyzico')),
    
    -- Payment status
    status TEXT DEFAULT 'pending' CHECK (status IN ('pending', 'processing', 'completed', 'failed', 'cancelled', 'refunded')),
    payment_date TIMESTAMP WITH TIME ZONE,
    
    -- External payment references
    external_payment_id TEXT, -- Payment gateway transaction ID
    payment_gateway TEXT, -- Which gateway was used
    gateway_response JSONB, -- Full gateway response for debugging
    
    -- Refund information
    refund_amount DECIMAL(10,2) DEFAULT 0.00,
    refund_date TIMESTAMP WITH TIME ZONE,
    refund_reason TEXT,
    
    -- Commission and revenue sharing
    platform_commission_rate DECIMAL(5,4) DEFAULT 0.15, -- 15% default commission
    platform_commission_amount DECIMAL(10,2),
    instructor_amount DECIMAL(10,2),
    
    -- Additional information
    description TEXT,
    metadata JSONB DEFAULT '{}'::jsonb,
    
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Create indexes for better performance
CREATE INDEX IF NOT EXISTS idx_payments_student_id ON payments(student_id);
CREATE INDEX IF NOT EXISTS idx_payments_instructor_id ON payments(instructor_id);
CREATE INDEX IF NOT EXISTS idx_payments_enrollment_id ON payments(enrollment_id);
CREATE INDEX IF NOT EXISTS idx_payments_status ON payments(status);
CREATE INDEX IF NOT EXISTS idx_payments_payment_date ON payments(payment_date);
CREATE INDEX IF NOT EXISTS idx_payments_external_payment_id ON payments(external_payment_id);
CREATE INDEX IF NOT EXISTS idx_payments_payment_gateway ON payments(payment_gateway);
CREATE INDEX IF NOT EXISTS idx_payments_created_at ON payments(created_at);

-- Enable Row Level Security
ALTER TABLE payments ENABLE ROW LEVEL SECURITY;

-- RLS Policies for payments table
-- Students can view their own payments
CREATE POLICY "Students can view own payments" ON payments
    FOR SELECT USING (auth.uid() = student_id);

-- Instructors can view payments for their services
CREATE POLICY "Instructors can view their payments" ON payments
    FOR SELECT USING (auth.uid() = instructor_id);

-- Admin can view and manage all payments
CREATE POLICY "Admin can manage all payments" ON payments
    FOR ALL USING (
        EXISTS (
            SELECT 1 FROM profiles p 
            WHERE p.id = auth.uid() 
            AND p.role = 'admin'
        )
    );

-- Create trigger for updated_at
CREATE TRIGGER update_payments_updated_at 
    BEFORE UPDATE ON payments 
    FOR EACH ROW 
    EXECUTE FUNCTION update_updated_at_column();

-- Create function to calculate commission amounts
CREATE OR REPLACE FUNCTION calculate_payment_commission()
RETURNS TRIGGER AS $$
BEGIN
    -- Calculate platform commission and instructor amount
    NEW.platform_commission_amount := NEW.amount * NEW.platform_commission_rate;
    NEW.instructor_amount := NEW.amount - NEW.platform_commission_amount;
    
    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- Create trigger to auto-calculate commission
CREATE TRIGGER calculate_payment_commission_trigger
    BEFORE INSERT OR UPDATE OF amount, platform_commission_rate ON payments
    FOR EACH ROW
    EXECUTE FUNCTION calculate_payment_commission();

-- Create function to process payment
CREATE OR REPLACE FUNCTION process_payment(
    payment_uuid UUID,
    external_id TEXT,
    gateway TEXT,
    gateway_response_data JSONB DEFAULT NULL
) RETURNS VOID AS $$
BEGIN
    UPDATE payments 
    SET 
        status = 'completed',
        payment_date = NOW(),
        external_payment_id = external_id,
        payment_gateway = gateway,
        gateway_response = gateway_response_data,
        updated_at = NOW()
    WHERE id = payment_uuid
    AND status IN ('pending', 'processing');
    
    -- Update related enrollment payment status
    UPDATE enrollments 
    SET payment_status = 'paid'
    WHERE id = (SELECT enrollment_id FROM payments WHERE id = payment_uuid);
END;
$$ LANGUAGE plpgsql;

-- Create function to refund payment
CREATE OR REPLACE FUNCTION refund_payment(
    payment_uuid UUID,
    refund_amount_param DECIMAL(10,2),
    reason TEXT DEFAULT NULL
) RETURNS VOID AS $$
DECLARE
    payment_record RECORD;
BEGIN
    -- Get payment details
    SELECT * INTO payment_record FROM payments WHERE id = payment_uuid;
    
    IF payment_record.status != 'completed' THEN
        RAISE EXCEPTION 'Can only refund completed payments';
    END IF;
    
    IF refund_amount_param > payment_record.amount THEN
        RAISE EXCEPTION 'Refund amount cannot exceed payment amount';
    END IF;
    
    -- Update payment record
    UPDATE payments 
    SET 
        status = CASE 
            WHEN refund_amount_param = amount THEN 'refunded'
            ELSE 'completed'
        END,
        refund_amount = refund_amount_param,
        refund_date = NOW(),
        refund_reason = reason,
        updated_at = NOW()
    WHERE id = payment_uuid;
    
    -- Update enrollment if fully refunded
    IF refund_amount_param = payment_record.amount THEN
        UPDATE enrollments 
        SET payment_status = 'refunded'
        WHERE id = payment_record.enrollment_id;
    END IF;
END;
$$ LANGUAGE plpgsql;

-- Create function to get payment statistics
CREATE OR REPLACE FUNCTION get_payment_statistics(
    start_date TIMESTAMP WITH TIME ZONE DEFAULT NULL,
    end_date TIMESTAMP WITH TIME ZONE DEFAULT NULL
)
RETURNS TABLE (
    total_payments BIGINT,
    total_amount DECIMAL(10,2),
    total_commission DECIMAL(10,2),
    total_instructor_earnings DECIMAL(10,2),
    completed_payments BIGINT,
    failed_payments BIGINT,
    refunded_payments BIGINT,
    average_payment_amount DECIMAL(10,2)
) AS $$
BEGIN
    RETURN QUERY
    SELECT 
        COUNT(*) as total_payments,
        SUM(amount) as total_amount,
        SUM(platform_commission_amount) as total_commission,
        SUM(instructor_amount) as total_instructor_earnings,
        COUNT(*) FILTER (WHERE status = 'completed') as completed_payments,
        COUNT(*) FILTER (WHERE status = 'failed') as failed_payments,
        COUNT(*) FILTER (WHERE status = 'refunded') as refunded_payments,
        AVG(amount) as average_payment_amount
    FROM payments
    WHERE (start_date IS NULL OR created_at >= start_date)
    AND (end_date IS NULL OR created_at <= end_date);
END;
$$ LANGUAGE plpgsql;

-- Comments for documentation
COMMENT ON TABLE payments IS 'Financial transaction records and payment processing - core table for revenue tracking';
COMMENT ON COLUMN payments.student_id IS 'Foreign key to profiles table (student making payment)';
COMMENT ON COLUMN payments.instructor_id IS 'Foreign key to instructors table (instructor receiving payment)';
COMMENT ON COLUMN payments.currency IS 'Payment currency (default TRY for Turkish Lira)';
COMMENT ON COLUMN payments.platform_commission_rate IS 'Platform commission rate (default 15%)';
COMMENT ON COLUMN payments.external_payment_id IS 'Payment gateway transaction ID';
COMMENT ON COLUMN payments.gateway_response IS 'Full payment gateway response for debugging';
COMMENT ON FUNCTION process_payment IS 'Function to mark payment as completed with gateway details';
COMMENT ON FUNCTION refund_payment IS 'Function to process payment refunds';
COMMENT ON FUNCTION get_payment_statistics IS 'Function to get payment statistics for a date range';
