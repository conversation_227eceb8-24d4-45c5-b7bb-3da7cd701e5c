-- =====================================================
-- FITGO PAYMENTS: USER SUBSCRIPTIONS TABLE
-- =====================================================
-- Subscription lifecycle management and billing cycles
-- Core table for subscription business model

-- Drop table if exists (for development only)
-- DROP TABLE IF EXISTS user_subscriptions CASCADE;

-- Create user_subscriptions table
CREATE TABLE IF NOT EXISTS user_subscriptions (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    user_id UUID NOT NULL REFERENCES profiles(id) ON DELETE CASCADE,
    instructor_id UUID REFERENCES instructors(profile_id) ON DELETE SET NULL,
    
    -- Subscription details
    subscription_type TEXT NOT NULL CHECK (subscription_type IN ('basic', 'premium', 'trial', 'custom')),
    plan_name TEXT NOT NULL,
    
    -- Billing information
    amount DECIMAL(10,2) NOT NULL CHECK (amount >= 0),
    currency TEXT DEFAULT 'TRY' NOT NULL,
    billing_cycle TEXT NOT NULL CHECK (billing_cycle IN ('monthly', 'quarterly', 'semi_annual', 'annual', 'one_time')),
    
    -- Subscription lifecycle
    status TEXT DEFAULT 'active' CHECK (status IN ('active', 'cancelled', 'expired', 'suspended', 'trial')),
    start_date TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    end_date TIMESTAMP WITH TIME ZONE,
    next_billing_date TIMESTAMP WITH TIME ZONE,
    
    -- Trial information
    trial_start_date TIMESTAMP WITH TIME ZONE,
    trial_end_date TIMESTAMP WITH TIME ZONE,
    is_trial BOOLEAN DEFAULT false,
    
    -- Cancellation information
    cancelled_at TIMESTAMP WITH TIME ZONE,
    cancellation_reason TEXT,
    cancelled_by UUID REFERENCES profiles(id),
    
    -- Auto-renewal settings
    auto_renew BOOLEAN DEFAULT true,
    renewal_attempts INTEGER DEFAULT 0,
    max_renewal_attempts INTEGER DEFAULT 3,
    
    -- Payment information
    last_payment_date TIMESTAMP WITH TIME ZONE,
    last_payment_amount DECIMAL(10,2),
    next_payment_amount DECIMAL(10,2),
    
    -- Subscription features (JSON for flexibility)
    features JSONB DEFAULT '{}'::jsonb,
    
    -- External subscription references
    external_subscription_id TEXT, -- Payment gateway subscription ID
    payment_gateway TEXT,
    
    -- Metadata
    metadata JSONB DEFAULT '{}'::jsonb,
    
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Create indexes for better performance
CREATE INDEX IF NOT EXISTS idx_user_subscriptions_user_id ON user_subscriptions(user_id);
CREATE INDEX IF NOT EXISTS idx_user_subscriptions_instructor_id ON user_subscriptions(instructor_id);
CREATE INDEX IF NOT EXISTS idx_user_subscriptions_status ON user_subscriptions(status);
CREATE INDEX IF NOT EXISTS idx_user_subscriptions_subscription_type ON user_subscriptions(subscription_type);
CREATE INDEX IF NOT EXISTS idx_user_subscriptions_end_date ON user_subscriptions(end_date);
CREATE INDEX IF NOT EXISTS idx_user_subscriptions_next_billing_date ON user_subscriptions(next_billing_date);
CREATE INDEX IF NOT EXISTS idx_user_subscriptions_trial_end_date ON user_subscriptions(trial_end_date);
CREATE INDEX IF NOT EXISTS idx_user_subscriptions_auto_renew ON user_subscriptions(auto_renew);

-- Enable Row Level Security
ALTER TABLE user_subscriptions ENABLE ROW LEVEL SECURITY;

-- RLS Policies for user_subscriptions table
-- Users can view their own subscriptions
CREATE POLICY "Users can view own subscriptions" ON user_subscriptions
    FOR SELECT USING (auth.uid() = user_id);

-- Users can update their own subscriptions (limited fields)
CREATE POLICY "Users can update own subscriptions" ON user_subscriptions
    FOR UPDATE USING (auth.uid() = user_id);

-- Instructors can view subscriptions for their services
CREATE POLICY "Instructors can view their subscriptions" ON user_subscriptions
    FOR SELECT USING (auth.uid() = instructor_id);

-- Admin can view and manage all subscriptions
CREATE POLICY "Admin can manage all subscriptions" ON user_subscriptions
    FOR ALL USING (
        EXISTS (
            SELECT 1 FROM profiles p 
            WHERE p.id = auth.uid() 
            AND p.role = 'admin'
        )
    );

-- Create trigger for updated_at
CREATE TRIGGER update_user_subscriptions_updated_at 
    BEFORE UPDATE ON user_subscriptions 
    FOR EACH ROW 
    EXECUTE FUNCTION update_updated_at_column();

-- Create function to calculate next billing date
CREATE OR REPLACE FUNCTION calculate_next_billing_date(
    current_date TIMESTAMP WITH TIME ZONE,
    cycle TEXT
) RETURNS TIMESTAMP WITH TIME ZONE AS $$
BEGIN
    RETURN CASE cycle
        WHEN 'monthly' THEN current_date + INTERVAL '1 month'
        WHEN 'quarterly' THEN current_date + INTERVAL '3 months'
        WHEN 'semi_annual' THEN current_date + INTERVAL '6 months'
        WHEN 'annual' THEN current_date + INTERVAL '1 year'
        ELSE NULL
    END;
END;
$$ LANGUAGE plpgsql;

-- Create function to start subscription
CREATE OR REPLACE FUNCTION start_subscription(
    user_uuid UUID,
    instructor_uuid UUID,
    subscription_type_param TEXT,
    plan_name_param TEXT,
    amount_param DECIMAL(10,2),
    billing_cycle_param TEXT,
    trial_days INTEGER DEFAULT 0
) RETURNS UUID AS $$
DECLARE
    subscription_id UUID;
    start_date_val TIMESTAMP WITH TIME ZONE := NOW();
    trial_end_val TIMESTAMP WITH TIME ZONE;
    next_billing_val TIMESTAMP WITH TIME ZONE;
BEGIN
    -- Calculate trial end date if trial period
    IF trial_days > 0 THEN
        trial_end_val := start_date_val + (trial_days || ' days')::INTERVAL;
        next_billing_val := trial_end_val;
    ELSE
        next_billing_val := calculate_next_billing_date(start_date_val, billing_cycle_param);
    END IF;
    
    -- Create subscription
    INSERT INTO user_subscriptions (
        user_id, instructor_id, subscription_type, plan_name, amount, billing_cycle,
        start_date, trial_end_date, next_billing_date, is_trial,
        next_payment_amount, status
    ) VALUES (
        user_uuid, instructor_uuid, subscription_type_param, plan_name_param, 
        amount_param, billing_cycle_param, start_date_val, trial_end_val, 
        next_billing_val, (trial_days > 0), amount_param,
        CASE WHEN trial_days > 0 THEN 'trial' ELSE 'active' END
    ) RETURNING id INTO subscription_id;
    
    RETURN subscription_id;
END;
$$ LANGUAGE plpgsql;

-- Create function to cancel subscription
CREATE OR REPLACE FUNCTION cancel_subscription(
    subscription_uuid UUID,
    reason TEXT DEFAULT NULL,
    cancelled_by_uuid UUID DEFAULT NULL
) RETURNS VOID AS $$
BEGIN
    UPDATE user_subscriptions 
    SET 
        status = 'cancelled',
        cancelled_at = NOW(),
        cancellation_reason = reason,
        cancelled_by = COALESCE(cancelled_by_uuid, auth.uid()),
        auto_renew = false,
        updated_at = NOW()
    WHERE id = subscription_uuid
    AND status IN ('active', 'trial');
END;
$$ LANGUAGE plpgsql;

-- Create function to renew subscription
CREATE OR REPLACE FUNCTION renew_subscription(
    subscription_uuid UUID,
    payment_amount DECIMAL(10,2)
) RETURNS VOID AS $$
DECLARE
    sub_record RECORD;
BEGIN
    -- Get subscription details
    SELECT * INTO sub_record FROM user_subscriptions WHERE id = subscription_uuid;
    
    -- Update subscription
    UPDATE user_subscriptions 
    SET 
        status = 'active',
        last_payment_date = NOW(),
        last_payment_amount = payment_amount,
        next_billing_date = calculate_next_billing_date(NOW(), sub_record.billing_cycle),
        renewal_attempts = 0,
        is_trial = false,
        updated_at = NOW()
    WHERE id = subscription_uuid;
END;
$$ LANGUAGE plpgsql;

-- Create function to get expiring subscriptions
CREATE OR REPLACE FUNCTION get_expiring_subscriptions(days_ahead INTEGER DEFAULT 7)
RETURNS TABLE (
    id UUID,
    user_id UUID,
    instructor_id UUID,
    plan_name TEXT,
    end_date TIMESTAMP WITH TIME ZONE,
    next_billing_date TIMESTAMP WITH TIME ZONE,
    days_until_expiry INTEGER
) AS $$
BEGIN
    RETURN QUERY
    SELECT 
        us.id,
        us.user_id,
        us.instructor_id,
        us.plan_name,
        us.end_date,
        us.next_billing_date,
        EXTRACT(DAY FROM (COALESCE(us.end_date, us.next_billing_date) - NOW()))::INTEGER as days_until_expiry
    FROM user_subscriptions us
    WHERE us.status IN ('active', 'trial')
    AND (us.end_date IS NOT NULL OR us.next_billing_date IS NOT NULL)
    AND COALESCE(us.end_date, us.next_billing_date) <= NOW() + (days_ahead || ' days')::INTERVAL
    AND COALESCE(us.end_date, us.next_billing_date) >= NOW()
    ORDER BY COALESCE(us.end_date, us.next_billing_date) ASC;
END;
$$ LANGUAGE plpgsql;

-- Comments for documentation
COMMENT ON TABLE user_subscriptions IS 'Subscription lifecycle management and billing cycles - core table for subscription business model';
COMMENT ON COLUMN user_subscriptions.user_id IS 'Foreign key to profiles table (subscriber)';
COMMENT ON COLUMN user_subscriptions.instructor_id IS 'Foreign key to instructors table (service provider)';
COMMENT ON COLUMN user_subscriptions.billing_cycle IS 'Billing frequency: monthly, quarterly, semi_annual, annual, one_time';
COMMENT ON COLUMN user_subscriptions.currency IS 'Subscription currency (default TRY for Turkish Lira)';
COMMENT ON COLUMN user_subscriptions.features IS 'JSON object containing subscription features and limits';
COMMENT ON FUNCTION start_subscription IS 'Function to create a new subscription with optional trial period';
COMMENT ON FUNCTION cancel_subscription IS 'Function to cancel an active subscription';
COMMENT ON FUNCTION renew_subscription IS 'Function to renew a subscription after successful payment';
COMMENT ON FUNCTION get_expiring_subscriptions IS 'Function to get subscriptions expiring within specified days';
