-- =====================================================
-- FITGO FEEDBACK: STUDENT-INSTRUCTOR FEEDBACK SYSTEM
-- =====================================================
-- Student feedback system with photo uploads and instructor responses
-- Based on existing user_profiles photo upload pattern

-- Drop table if exists (for development only)
-- DROP TABLE IF EXISTS feedback CASCADE;
-- DROP TABLE IF EXISTS feedback_photos CASCADE;

-- Create feedback table
CREATE TABLE IF NOT EXISTS feedback (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    student_id UUID NOT NULL REFERENCES profiles(id) ON DELETE CASCADE,
    instructor_id UUID NOT NULL REFERENCES instructors(profile_id) ON DELETE CASCADE,
    enrollment_id UUID REFERENCES enrollments(id) ON DELETE SET NULL,
    
    -- Feedback content
    title TEXT NOT NULL,
    student_notes TEXT NOT NULL,
    
    -- Status management
    status TEXT DEFAULT 'waiting' CHECK (status IN ('waiting', 'reviewed', 'closed')),
    
    -- Instructor response (one-time only)
    instructor_response TEXT,
    instructor_response_date TIMESTAMP WITH TIME ZONE,
    responded_by UUID REFERENCES profiles(id), -- Track which instructor responded
    
    -- Metadata
    feedback_date TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    
    -- Constraints
    UNIQUE(student_id, instructor_id, feedback_date::date), -- One feedback per day per instructor
    CHECK (
        (status = 'reviewed' AND instructor_response IS NOT NULL AND instructor_response_date IS NOT NULL) OR
        (status IN ('waiting', 'closed'))
    )
);

-- Create feedback_photos table (separate table for better organization)
CREATE TABLE IF NOT EXISTS feedback_photos (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    feedback_id UUID NOT NULL REFERENCES feedback(id) ON DELETE CASCADE,
    photo_type TEXT NOT NULL CHECK (photo_type IN ('front', 'side', 'back')),
    photo_url TEXT NOT NULL,
    upload_date TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    file_size_bytes INTEGER,
    
    -- Ensure one photo per type per feedback
    UNIQUE(feedback_id, photo_type)
);

-- Create indexes for better performance
CREATE INDEX IF NOT EXISTS idx_feedback_student_id ON feedback(student_id);
CREATE INDEX IF NOT EXISTS idx_feedback_instructor_id ON feedback(instructor_id);
CREATE INDEX IF NOT EXISTS idx_feedback_enrollment_id ON feedback(enrollment_id);
CREATE INDEX IF NOT EXISTS idx_feedback_status ON feedback(status);
CREATE INDEX IF NOT EXISTS idx_feedback_date ON feedback(feedback_date);
CREATE INDEX IF NOT EXISTS idx_feedback_created_at ON feedback(created_at);

CREATE INDEX IF NOT EXISTS idx_feedback_photos_feedback_id ON feedback_photos(feedback_id);
CREATE INDEX IF NOT EXISTS idx_feedback_photos_type ON feedback_photos(photo_type);

-- Enable Row Level Security
ALTER TABLE feedback ENABLE ROW LEVEL SECURITY;
ALTER TABLE feedback_photos ENABLE ROW LEVEL SECURITY;

-- RLS Policies for feedback table

-- Students can view their own feedback
CREATE POLICY "Students can view their own feedback" ON feedback
    FOR SELECT USING (
        auth.uid() = student_id
    );

-- Students can create feedback for their enrolled instructors
CREATE POLICY "Students can create feedback for enrolled instructors" ON feedback
    FOR INSERT WITH CHECK (
        auth.uid() = student_id
        AND EXISTS (
            SELECT 1 FROM enrollments e
            WHERE e.student_id = auth.uid()
            AND e.instructor_id = feedback.instructor_id
            AND e.status = 'active'
        )
    );

-- Students can update their own feedback (only if not yet reviewed)
CREATE POLICY "Students can update unreviewed feedback" ON feedback
    FOR UPDATE USING (
        auth.uid() = student_id
        AND status = 'waiting'
    );

-- Instructors can view feedback for their students
CREATE POLICY "Instructors can view their students feedback" ON feedback
    FOR SELECT USING (
        EXISTS (
            SELECT 1 FROM instructors i
            WHERE i.profile_id = auth.uid()
            AND i.profile_id = feedback.instructor_id
        )
    );

-- Instructors can update feedback to add response (one-time only)
CREATE POLICY "Instructors can respond to feedback" ON feedback
    FOR UPDATE USING (
        EXISTS (
            SELECT 1 FROM instructors i
            WHERE i.profile_id = auth.uid()
            AND i.profile_id = feedback.instructor_id
        )
        AND status = 'waiting'
        AND instructor_response IS NULL
    );

-- RLS Policies for feedback_photos table

-- Students can view photos of their own feedback
CREATE POLICY "Students can view their feedback photos" ON feedback_photos
    FOR SELECT USING (
        EXISTS (
            SELECT 1 FROM feedback f
            WHERE f.id = feedback_photos.feedback_id
            AND f.student_id = auth.uid()
        )
    );

-- Students can insert photos for their own feedback
CREATE POLICY "Students can upload feedback photos" ON feedback_photos
    FOR INSERT WITH CHECK (
        EXISTS (
            SELECT 1 FROM feedback f
            WHERE f.id = feedback_photos.feedback_id
            AND f.student_id = auth.uid()
            AND f.status = 'waiting'
        )
    );

-- Students can update/delete photos of unreviewed feedback
CREATE POLICY "Students can update unreviewed feedback photos" ON feedback_photos
    FOR UPDATE USING (
        EXISTS (
            SELECT 1 FROM feedback f
            WHERE f.id = feedback_photos.feedback_id
            AND f.student_id = auth.uid()
            AND f.status = 'waiting'
        )
    );

CREATE POLICY "Students can delete unreviewed feedback photos" ON feedback_photos
    FOR DELETE USING (
        EXISTS (
            SELECT 1 FROM feedback f
            WHERE f.id = feedback_photos.feedback_id
            AND f.student_id = auth.uid()
            AND f.status = 'waiting'
        )
    );

-- Instructors can view photos of feedback for their students
CREATE POLICY "Instructors can view student feedback photos" ON feedback_photos
    FOR SELECT USING (
        EXISTS (
            SELECT 1 FROM feedback f
            JOIN instructors i ON i.profile_id = f.instructor_id
            WHERE f.id = feedback_photos.feedback_id
            AND i.profile_id = auth.uid()
        )
    );

-- Create triggers for updated_at
CREATE OR REPLACE FUNCTION update_feedback_updated_at()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = NOW();
    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

CREATE TRIGGER trigger_feedback_updated_at
    BEFORE UPDATE ON feedback
    FOR EACH ROW
    EXECUTE FUNCTION update_feedback_updated_at();

-- Function to automatically close feedback when instructor responds
CREATE OR REPLACE FUNCTION auto_close_feedback_on_response()
RETURNS TRIGGER AS $$
BEGIN
    -- If instructor response is added, automatically set status to 'reviewed'
    IF NEW.instructor_response IS NOT NULL AND OLD.instructor_response IS NULL THEN
        NEW.status = 'reviewed';
        NEW.instructor_response_date = NOW();
        NEW.responded_by = auth.uid();
    END IF;
    
    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

CREATE TRIGGER trigger_auto_close_feedback
    BEFORE UPDATE ON feedback
    FOR EACH ROW
    EXECUTE FUNCTION auto_close_feedback_on_response();

-- Function to get feedback with photos (for application use)
CREATE OR REPLACE FUNCTION get_feedback_with_photos(feedback_uuid UUID)
RETURNS JSON AS $$
DECLARE
    feedback_data JSON;
    photos_data JSON;
BEGIN
    -- Get feedback data
    SELECT to_json(f.*) INTO feedback_data
    FROM feedback f
    WHERE f.id = feedback_uuid;
    
    -- Get photos data
    SELECT json_agg(
        json_build_object(
            'id', fp.id,
            'photo_type', fp.photo_type,
            'photo_url', fp.photo_url,
            'upload_date', fp.upload_date,
            'file_size_bytes', fp.file_size_bytes
        )
    ) INTO photos_data
    FROM feedback_photos fp
    WHERE fp.feedback_id = feedback_uuid;
    
    -- Combine data
    RETURN json_build_object(
        'feedback', feedback_data,
        'photos', COALESCE(photos_data, '[]'::json)
    );
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Function to get student feedback history
CREATE OR REPLACE FUNCTION get_student_feedback_history(student_uuid UUID)
RETURNS JSON AS $$
BEGIN
    RETURN (
        SELECT json_agg(
            json_build_object(
                'id', f.id,
                'title', f.title,
                'status', f.status,
                'feedback_date', f.feedback_date,
                'instructor_name', p.name || ' ' || p.surname,
                'has_response', f.instructor_response IS NOT NULL,
                'photo_count', (
                    SELECT COUNT(*) FROM feedback_photos fp 
                    WHERE fp.feedback_id = f.id
                )
            )
        )
        FROM feedback f
        JOIN instructors i ON i.profile_id = f.instructor_id
        JOIN profiles p ON p.id = i.profile_id
        WHERE f.student_id = student_uuid
        ORDER BY f.feedback_date DESC
    );
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Function to get instructor feedback queue
CREATE OR REPLACE FUNCTION get_instructor_feedback_queue(instructor_uuid UUID)
RETURNS JSON AS $$
BEGIN
    RETURN (
        SELECT json_agg(
            json_build_object(
                'id', f.id,
                'title', f.title,
                'status', f.status,
                'feedback_date', f.feedback_date,
                'student_name', p.name || ' ' || p.surname,
                'days_waiting', EXTRACT(DAY FROM NOW() - f.feedback_date),
                'photo_count', (
                    SELECT COUNT(*) FROM feedback_photos fp 
                    WHERE fp.feedback_id = f.id
                )
            )
        )
        FROM feedback f
        JOIN profiles p ON p.id = f.student_id
        WHERE f.instructor_id = instructor_uuid
        ORDER BY 
            CASE f.status 
                WHEN 'waiting' THEN 1 
                WHEN 'reviewed' THEN 2 
                ELSE 3 
            END,
            f.feedback_date DESC
    );
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Comments for documentation
COMMENT ON TABLE feedback IS 'Student feedback system with instructor responses and photo uploads';
COMMENT ON TABLE feedback_photos IS 'Photos associated with feedback (front, side, back body photos)';
COMMENT ON COLUMN feedback.status IS 'Feedback status: waiting (new), reviewed (instructor responded), closed (archived)';
COMMENT ON COLUMN feedback.instructor_response IS 'One-time instructor response to student feedback';
COMMENT ON COLUMN feedback_photos.photo_type IS 'Type of photo: front, side, or back';
