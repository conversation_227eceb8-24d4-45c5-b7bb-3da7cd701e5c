-- =====================================================
-- FITGO CORE: USER PROFILES TABLE
-- =====================================================
-- Extended user profile data for fitness goals and body photos
-- This table stores additional profile information beyond basic auth data

-- Drop table if exists (for development only)
-- DROP TABLE IF EXISTS user_profiles CASCADE;

-- Create user_profiles table
CREATE TABLE IF NOT EXISTS user_profiles (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    user_id UUID NOT NULL REFERENCES profiles(id) ON DELETE CASCADE,
    fitness_goals TEXT[],
    activity_level TEXT CHECK (activity_level IN ('sedentary', 'lightly_active', 'moderately_active', 'very_active', 'extremely_active')),
    dietary_restrictions TEXT[],
    medical_conditions TEXT[],
    additional_notes TEXT,
    front_photo_url TEXT,
    side_photo_url TEXT,
    back_photo_url TEXT,
    height_cm INTEGER CHECK (height_cm > 0 AND height_cm < 300),
    weight_kg DECIMAL(5,2) CHECK (weight_kg > 0 AND weight_kg < 500),
    target_weight_kg DECIMAL(5,2) CHECK (target_weight_kg > 0 AND target_weight_kg < 500),
    body_fat_percentage DECIMAL(4,2) CHECK (body_fat_percentage >= 0 AND body_fat_percentage <= 100),
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    UNIQUE(user_id)
);

-- Create indexes for better performance
CREATE INDEX IF NOT EXISTS idx_user_profiles_user_id ON user_profiles(user_id);
CREATE INDEX IF NOT EXISTS idx_user_profiles_activity_level ON user_profiles(activity_level);
CREATE INDEX IF NOT EXISTS idx_user_profiles_created_at ON user_profiles(created_at);

-- Enable Row Level Security
ALTER TABLE user_profiles ENABLE ROW LEVEL SECURITY;

-- RLS Policies for user_profiles table
-- Users can view their own profile
CREATE POLICY "Users can view own user profile" ON user_profiles
    FOR SELECT USING (auth.uid() = user_id);

-- Users can update their own profile
CREATE POLICY "Users can update own user profile" ON user_profiles
    FOR UPDATE USING (auth.uid() = user_id);

-- Users can insert their own profile
CREATE POLICY "Users can insert own user profile" ON user_profiles
    FOR INSERT WITH CHECK (auth.uid() = user_id);

-- Users can delete their own profile
CREATE POLICY "Users can delete own user profile" ON user_profiles
    FOR DELETE USING (auth.uid() = user_id);

-- Instructors can view their students' profiles
CREATE POLICY "Instructors can view student user profiles" ON user_profiles
    FOR SELECT USING (
        EXISTS (
            SELECT 1 FROM students s 
            WHERE s.student_id = user_profiles.user_id 
            AND s.instructor_id = auth.uid()
        )
    );

-- Admin can view all user profiles
CREATE POLICY "Admin can view all user profiles" ON user_profiles
    FOR ALL USING (
        EXISTS (
            SELECT 1 FROM profiles p 
            WHERE p.id = auth.uid() 
            AND p.role = 'admin'
        )
    );

-- Create trigger for updated_at
CREATE TRIGGER update_user_profiles_updated_at 
    BEFORE UPDATE ON user_profiles 
    FOR EACH ROW 
    EXECUTE FUNCTION update_updated_at_column();

-- Create function to automatically create user_profile when profile is created
CREATE OR REPLACE FUNCTION create_user_profile_on_signup()
RETURNS TRIGGER AS $$
BEGIN
    INSERT INTO user_profiles (user_id)
    VALUES (NEW.id);
    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- Create trigger to auto-create user_profile
CREATE TRIGGER on_profile_created
    AFTER INSERT ON profiles
    FOR EACH ROW
    EXECUTE FUNCTION create_user_profile_on_signup();

-- Comments for documentation
COMMENT ON TABLE user_profiles IS 'Extended user profile data for fitness goals and body photos';
COMMENT ON COLUMN user_profiles.user_id IS 'Foreign key to profiles table';
COMMENT ON COLUMN user_profiles.fitness_goals IS 'Array of user fitness goals';
COMMENT ON COLUMN user_profiles.activity_level IS 'User activity level for calorie calculations';
COMMENT ON COLUMN user_profiles.dietary_restrictions IS 'Array of dietary restrictions';
COMMENT ON COLUMN user_profiles.medical_conditions IS 'Array of medical conditions to consider';
COMMENT ON COLUMN user_profiles.front_photo_url IS 'URL to front body photo in storage';
COMMENT ON COLUMN user_profiles.side_photo_url IS 'URL to side body photo in storage';
COMMENT ON COLUMN user_profiles.back_photo_url IS 'URL to back body photo in storage';
COMMENT ON COLUMN user_profiles.height_cm IS 'User height in centimeters';
COMMENT ON COLUMN user_profiles.weight_kg IS 'Current weight in kilograms';
COMMENT ON COLUMN user_profiles.target_weight_kg IS 'Target weight in kilograms';
COMMENT ON COLUMN user_profiles.body_fat_percentage IS 'Body fat percentage (0-100)';
