-- =====================================================
-- FITGO CORE: PROFILES TABLE
-- =====================================================
-- Base user profiles table with authentication integration
-- This is the foundation table for all users in the system

-- Drop table if exists (for development only)
-- DROP TABLE IF EXISTS profiles CASCADE;

-- Create profiles table
CREATE TABLE IF NOT EXISTS profiles (
    id UUID PRIMARY KEY DEFAULT auth.uid(),
    email TEXT UNIQUE NOT NULL,
    name TEXT,
    surname TEXT,
    phone TEXT,
    date_of_birth DATE,
    gender VARCHAR(10) CHECK (gender IN ('male', 'female', 'other')),
    role TEXT NOT NULL DEFAULT 'student' CHECK (role IN ('student', 'instructor', 'admin')),
    firebase_token TEXT,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Create indexes for better performance
CREATE INDEX IF NOT EXISTS idx_profiles_email ON profiles(email);
CREATE INDEX IF NOT EXISTS idx_profiles_role ON profiles(role);
CREATE INDEX IF NOT EXISTS idx_profiles_created_at ON profiles(created_at);

-- Enable Row Level Security
ALTER TABLE profiles ENABLE ROW LEVEL SECURITY;

-- RLS Policies for profiles table
-- Users can view their own profile
CREATE POLICY "Users can view own profile" ON profiles
    FOR SELECT USING (auth.uid() = id);

-- Users can update their own profile
CREATE POLICY "Users can update own profile" ON profiles
    FOR UPDATE USING (auth.uid() = id);

-- Users can insert their own profile (during registration)
CREATE POLICY "Users can insert own profile" ON profiles
    FOR INSERT WITH CHECK (auth.uid() = id);

-- Instructors can view student profiles (for their students only)
CREATE POLICY "Instructors can view student profiles" ON profiles
    FOR SELECT USING (
        EXISTS (
            SELECT 1 FROM students s 
            WHERE s.student_id = profiles.id 
            AND s.instructor_id = auth.uid()
        )
        AND profiles.role = 'student'
    );

-- Admin can view all profiles
CREATE POLICY "Admin can view all profiles" ON profiles
    FOR ALL USING (
        EXISTS (
            SELECT 1 FROM profiles p 
            WHERE p.id = auth.uid() 
            AND p.role = 'admin'
        )
    );

-- Create trigger for updated_at
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = NOW();
    RETURN NEW;
END;
$$ language 'plpgsql';

CREATE TRIGGER update_profiles_updated_at 
    BEFORE UPDATE ON profiles 
    FOR EACH ROW 
    EXECUTE FUNCTION update_updated_at_column();

-- Insert sample data (for development)
-- INSERT INTO profiles (id, email, name, surname, role) VALUES
-- ('550e8400-e29b-41d4-a716-************', '<EMAIL>', 'Admin', 'User', 'admin'),
-- ('550e8400-e29b-41d4-a716-************', '<EMAIL>', 'John', 'Doe', 'instructor'),
-- ('550e8400-e29b-41d4-a716-************', '<EMAIL>', 'Jane', 'Smith', 'student');

-- Comments for documentation
COMMENT ON TABLE profiles IS 'Base user profiles table with authentication integration';
COMMENT ON COLUMN profiles.id IS 'UUID primary key, matches auth.uid()';
COMMENT ON COLUMN profiles.email IS 'User email address, unique across system';
COMMENT ON COLUMN profiles.role IS 'User role: student, instructor, or admin';
COMMENT ON COLUMN profiles.gender IS 'User gender: male, female, or other';
COMMENT ON COLUMN profiles.firebase_token IS 'Firebase push notification token';
