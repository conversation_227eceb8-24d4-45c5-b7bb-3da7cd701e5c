-- =====================================================
-- FITGO CONTENT: WORKOUT PLANS TABLES
-- =====================================================
-- Workout plan templates and individual workout plans
-- Structured workout programs created by instructors

-- Drop tables if exist (for development only)
-- DROP TABLE IF EXISTS workout_template_plan_exercises CASCADE;
-- DROP TABLE IF EXISTS workout_template_plans CASCADE;
-- DROP TABLE IF EXISTS workout_plan_templates CASCADE;

-- Create workout_plan_templates table
CREATE TABLE IF NOT EXISTS workout_plan_templates (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    instructor_id UUID NOT NULL REFERENCES instructors(profile_id) ON DELETE CASCADE,
    name TEXT NOT NULL,
    description TEXT,
    
    -- Template metadata
    difficulty_level TEXT CHECK (difficulty_level IN ('beginner', 'intermediate', 'advanced')),
    duration_weeks INTEGER CHECK (duration_weeks > 0),
    sessions_per_week INTEGER CHECK (sessions_per_week > 0 AND sessions_per_week <= 7),
    estimated_duration_minutes INTEGER,
    
    -- Template status
    is_draft BOOLEAN DEFAULT true,
    is_public BOOLEAN DEFAULT false,
    is_featured BOOLEAN DEFAULT false,
    
    -- Content organization
    tags TEXT[] DEFAULT '{}',
    notes TEXT,
    
    -- Analytics
    usage_count INTEGER DEFAULT 0,
    rating DECIMAL(3,2) DEFAULT 0.00 CHECK (rating >= 0 AND rating <= 5),
    rating_count INTEGER DEFAULT 0,
    
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Create workout_template_plans table (individual workout days)
CREATE TABLE IF NOT EXISTS workout_template_plans (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    template_id UUID NOT NULL REFERENCES workout_plan_templates(id) ON DELETE CASCADE,
    name TEXT NOT NULL,
    description TEXT,
    order_index INTEGER NOT NULL DEFAULT 0,
    
    -- Plan details
    target_muscle_groups TEXT[] DEFAULT '{}',
    estimated_duration_minutes INTEGER,
    rest_between_sets_seconds INTEGER DEFAULT 60,
    rest_between_exercises_seconds INTEGER DEFAULT 120,
    
    -- Plan notes
    warm_up_notes TEXT,
    cool_down_notes TEXT,
    general_notes TEXT,
    
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    UNIQUE(template_id, order_index)
);

-- Create workout_template_plan_exercises table (exercises within a workout day)
CREATE TABLE IF NOT EXISTS workout_template_plan_exercises (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    plan_id UUID NOT NULL REFERENCES workout_template_plans(id) ON DELETE CASCADE,
    exercise_id UUID NOT NULL REFERENCES exercises(id) ON DELETE CASCADE,
    order_index INTEGER NOT NULL DEFAULT 0,
    
    -- Exercise parameters
    sets INTEGER NOT NULL DEFAULT 1 CHECK (sets > 0),
    reps INTEGER CHECK (reps > 0),
    duration_seconds INTEGER CHECK (duration_seconds > 0),
    weight_kg DECIMAL(6,2) CHECK (weight_kg > 0),
    distance_meters INTEGER CHECK (distance_meters > 0),
    
    -- Exercise notes
    notes TEXT,
    rest_seconds INTEGER DEFAULT 60,
    
    -- Exercise type (reps, time, distance)
    exercise_type TEXT DEFAULT 'reps' CHECK (exercise_type IN ('reps', 'time', 'distance')),
    
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    UNIQUE(plan_id, order_index)
);

-- Create indexes for better performance
CREATE INDEX IF NOT EXISTS idx_workout_plan_templates_instructor_id ON workout_plan_templates(instructor_id);
CREATE INDEX IF NOT EXISTS idx_workout_plan_templates_is_draft ON workout_plan_templates(is_draft);
CREATE INDEX IF NOT EXISTS idx_workout_plan_templates_is_public ON workout_plan_templates(is_public);
CREATE INDEX IF NOT EXISTS idx_workout_plan_templates_difficulty ON workout_plan_templates(difficulty_level);
CREATE INDEX IF NOT EXISTS idx_workout_plan_templates_tags ON workout_plan_templates USING GIN(tags);

CREATE INDEX IF NOT EXISTS idx_workout_template_plans_template_id ON workout_template_plans(template_id);
CREATE INDEX IF NOT EXISTS idx_workout_template_plans_order_index ON workout_template_plans(order_index);

CREATE INDEX IF NOT EXISTS idx_workout_template_plan_exercises_plan_id ON workout_template_plan_exercises(plan_id);
CREATE INDEX IF NOT EXISTS idx_workout_template_plan_exercises_exercise_id ON workout_template_plan_exercises(exercise_id);
CREATE INDEX IF NOT EXISTS idx_workout_template_plan_exercises_order_index ON workout_template_plan_exercises(order_index);

-- Enable Row Level Security
ALTER TABLE workout_plan_templates ENABLE ROW LEVEL SECURITY;
ALTER TABLE workout_template_plans ENABLE ROW LEVEL SECURITY;
ALTER TABLE workout_template_plan_exercises ENABLE ROW LEVEL SECURITY;

-- RLS Policies for workout_plan_templates
-- Instructors can manage their own templates
CREATE POLICY "Instructors can manage own workout templates" ON workout_plan_templates
    FOR ALL USING (auth.uid() = instructor_id);

-- Students can view public templates
CREATE POLICY "Students can view public workout templates" ON workout_plan_templates
    FOR SELECT USING (
        is_public = true 
        AND is_draft = false
        AND EXISTS (
            SELECT 1 FROM profiles p 
            WHERE p.id = auth.uid() 
            AND p.role = 'student'
        )
    );

-- Public can view public templates
CREATE POLICY "Public can view public workout templates" ON workout_plan_templates
    FOR SELECT USING (is_public = true AND is_draft = false);

-- Admin can manage all templates
CREATE POLICY "Admin can manage all workout templates" ON workout_plan_templates
    FOR ALL USING (
        EXISTS (
            SELECT 1 FROM profiles p 
            WHERE p.id = auth.uid() 
            AND p.role = 'admin'
        )
    );

-- RLS Policies for workout_template_plans
-- Inherit permissions from parent template
CREATE POLICY "Inherit permissions from workout template" ON workout_template_plans
    FOR ALL USING (
        EXISTS (
            SELECT 1 FROM workout_plan_templates wpt
            WHERE wpt.id = workout_template_plans.template_id
            AND (
                wpt.instructor_id = auth.uid() OR
                (wpt.is_public = true AND wpt.is_draft = false) OR
                EXISTS (
                    SELECT 1 FROM profiles p 
                    WHERE p.id = auth.uid() 
                    AND p.role = 'admin'
                )
            )
        )
    );

-- RLS Policies for workout_template_plan_exercises
-- Inherit permissions from parent plan
CREATE POLICY "Inherit permissions from workout plan" ON workout_template_plan_exercises
    FOR ALL USING (
        EXISTS (
            SELECT 1 FROM workout_template_plans wtp
            JOIN workout_plan_templates wpt ON wpt.id = wtp.template_id
            WHERE wtp.id = workout_template_plan_exercises.plan_id
            AND (
                wpt.instructor_id = auth.uid() OR
                (wpt.is_public = true AND wpt.is_draft = false) OR
                EXISTS (
                    SELECT 1 FROM profiles p 
                    WHERE p.id = auth.uid() 
                    AND p.role = 'admin'
                )
            )
        )
    );

-- Create triggers for updated_at
CREATE TRIGGER update_workout_plan_templates_updated_at 
    BEFORE UPDATE ON workout_plan_templates 
    FOR EACH ROW 
    EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_workout_template_plans_updated_at 
    BEFORE UPDATE ON workout_template_plans 
    FOR EACH ROW 
    EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_workout_template_plan_exercises_updated_at 
    BEFORE UPDATE ON workout_template_plan_exercises 
    FOR EACH ROW 
    EXECUTE FUNCTION update_updated_at_column();

-- Comments for documentation
COMMENT ON TABLE workout_plan_templates IS 'Workout plan templates created by instructors';
COMMENT ON TABLE workout_template_plans IS 'Individual workout days within templates';
COMMENT ON TABLE workout_template_plan_exercises IS 'Exercises within a workout day with parameters';
COMMENT ON COLUMN workout_template_plan_exercises.exercise_type IS 'Type of exercise measurement: reps, time, or distance';
