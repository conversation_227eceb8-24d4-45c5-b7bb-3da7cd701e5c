-- =====================================================
-- FITGO CONTENT: EXERCISES TABLE
-- =====================================================
-- Exercise library with metadata and instructions
-- Core content table for workout creation

-- Drop table if exists (for development only)
-- DROP TABLE IF EXISTS exercises CASCADE;

-- Create exercises table
CREATE TABLE IF NOT EXISTS exercises (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    name TEXT NOT NULL,
    description TEXT,
    instructions TEXT,
    category_id UUID, -- Future reference to exercise_categories table
    
    -- Exercise metadata
    muscle_groups TEXT[] NOT NULL DEFAULT '{}',
    equipment TEXT[] DEFAULT '{}',
    difficulty_level TEXT CHECK (difficulty_level IN ('beginner', 'intermediate', 'advanced')),
    
    -- Media and resources
    video_url TEXT,
    image_url TEXT,
    thumbnail_url TEXT,
    
    -- Metrics
    calories_per_minute DECIMAL(5,2),
    estimated_duration_minutes INTEGER,
    
    -- Content management
    is_public BOOLEAN DEFAULT true,
    is_verified BOOLEAN DEFAULT false,
    created_by UUID REFERENCES profiles(id),
    
    -- SEO and search
    tags TEXT[] DEFAULT '{}',
    search_vector tsvector,
    
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Create indexes for better performance
CREATE INDEX IF NOT EXISTS idx_exercises_name ON exercises(name);
CREATE INDEX IF NOT EXISTS idx_exercises_muscle_groups ON exercises USING GIN(muscle_groups);
CREATE INDEX IF NOT EXISTS idx_exercises_equipment ON exercises USING GIN(equipment);
CREATE INDEX IF NOT EXISTS idx_exercises_difficulty_level ON exercises(difficulty_level);
CREATE INDEX IF NOT EXISTS idx_exercises_is_public ON exercises(is_public);
CREATE INDEX IF NOT EXISTS idx_exercises_is_verified ON exercises(is_verified);
CREATE INDEX IF NOT EXISTS idx_exercises_created_by ON exercises(created_by);
CREATE INDEX IF NOT EXISTS idx_exercises_tags ON exercises USING GIN(tags);
CREATE INDEX IF NOT EXISTS idx_exercises_search_vector ON exercises USING GIN(search_vector);

-- Enable Row Level Security
ALTER TABLE exercises ENABLE ROW LEVEL SECURITY;

-- RLS Policies for exercises table
-- Everyone can view public and verified exercises
CREATE POLICY "Public can view public exercises" ON exercises
    FOR SELECT USING (is_public = true AND is_verified = true);

-- Instructors can view all public exercises
CREATE POLICY "Instructors can view public exercises" ON exercises
    FOR SELECT USING (
        is_public = true
        AND EXISTS (
            SELECT 1 FROM profiles p 
            WHERE p.id = auth.uid() 
            AND p.role = 'instructor'
        )
    );

-- Instructors can create exercises
CREATE POLICY "Instructors can create exercises" ON exercises
    FOR INSERT WITH CHECK (
        auth.uid() = created_by
        AND EXISTS (
            SELECT 1 FROM profiles p 
            WHERE p.id = auth.uid() 
            AND p.role = 'instructor'
        )
    );

-- Instructors can update their own exercises
CREATE POLICY "Instructors can update own exercises" ON exercises
    FOR UPDATE USING (
        auth.uid() = created_by
        AND EXISTS (
            SELECT 1 FROM profiles p 
            WHERE p.id = auth.uid() 
            AND p.role = 'instructor'
        )
    );

-- Admin can manage all exercises
CREATE POLICY "Admin can manage all exercises" ON exercises
    FOR ALL USING (
        EXISTS (
            SELECT 1 FROM profiles p 
            WHERE p.id = auth.uid() 
            AND p.role = 'admin'
        )
    );

-- Create trigger for updated_at
CREATE TRIGGER update_exercises_updated_at 
    BEFORE UPDATE ON exercises 
    FOR EACH ROW 
    EXECUTE FUNCTION update_updated_at_column();

-- Create function to update search vector
CREATE OR REPLACE FUNCTION update_exercise_search_vector()
RETURNS TRIGGER AS $$
BEGIN
    NEW.search_vector := 
        setweight(to_tsvector('english', COALESCE(NEW.name, '')), 'A') ||
        setweight(to_tsvector('english', COALESCE(NEW.description, '')), 'B') ||
        setweight(to_tsvector('english', COALESCE(array_to_string(NEW.muscle_groups, ' '), '')), 'C') ||
        setweight(to_tsvector('english', COALESCE(array_to_string(NEW.equipment, ' '), '')), 'D') ||
        setweight(to_tsvector('english', COALESCE(array_to_string(NEW.tags, ' '), '')), 'D');
    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- Create trigger for search vector update
CREATE TRIGGER update_exercise_search_vector_trigger
    BEFORE INSERT OR UPDATE ON exercises
    FOR EACH ROW
    EXECUTE FUNCTION update_exercise_search_vector();

-- Create function to search exercises
CREATE OR REPLACE FUNCTION search_exercises(
    search_query TEXT,
    muscle_group_filter TEXT[] DEFAULT NULL,
    equipment_filter TEXT[] DEFAULT NULL,
    difficulty_filter TEXT DEFAULT NULL,
    limit_count INTEGER DEFAULT 20
)
RETURNS TABLE (
    id UUID,
    name TEXT,
    description TEXT,
    muscle_groups TEXT[],
    equipment TEXT[],
    difficulty_level TEXT,
    image_url TEXT,
    rank REAL
) AS $$
BEGIN
    RETURN QUERY
    SELECT 
        e.id,
        e.name,
        e.description,
        e.muscle_groups,
        e.equipment,
        e.difficulty_level,
        e.image_url,
        ts_rank(e.search_vector, plainto_tsquery('english', search_query)) as rank
    FROM exercises e
    WHERE e.is_public = true 
    AND e.is_verified = true
    AND (search_query = '' OR e.search_vector @@ plainto_tsquery('english', search_query))
    AND (muscle_group_filter IS NULL OR e.muscle_groups && muscle_group_filter)
    AND (equipment_filter IS NULL OR e.equipment && equipment_filter)
    AND (difficulty_filter IS NULL OR e.difficulty_level = difficulty_filter)
    ORDER BY 
        CASE WHEN search_query = '' THEN 0 ELSE ts_rank(e.search_vector, plainto_tsquery('english', search_query)) END DESC,
        e.name ASC
    LIMIT limit_count;
END;
$$ LANGUAGE plpgsql;

-- Create function to get popular exercises
CREATE OR REPLACE FUNCTION get_popular_exercises(limit_count INTEGER DEFAULT 10)
RETURNS TABLE (
    id UUID,
    name TEXT,
    description TEXT,
    muscle_groups TEXT[],
    difficulty_level TEXT,
    image_url TEXT,
    usage_count BIGINT
) AS $$
BEGIN
    RETURN QUERY
    SELECT 
        e.id,
        e.name,
        e.description,
        e.muscle_groups,
        e.difficulty_level,
        e.image_url,
        COUNT(wpe.exercise_id) as usage_count
    FROM exercises e
    LEFT JOIN workout_template_plan_exercises wpe ON wpe.exercise_id = e.id
    WHERE e.is_public = true AND e.is_verified = true
    GROUP BY e.id, e.name, e.description, e.muscle_groups, e.difficulty_level, e.image_url
    ORDER BY usage_count DESC, e.name ASC
    LIMIT limit_count;
END;
$$ LANGUAGE plpgsql;

-- Comments for documentation
COMMENT ON TABLE exercises IS 'Exercise library with metadata and instructions - core content table for workout creation';
COMMENT ON COLUMN exercises.muscle_groups IS 'Array of target muscle groups';
COMMENT ON COLUMN exercises.equipment IS 'Array of required equipment';
COMMENT ON COLUMN exercises.calories_per_minute IS 'Estimated calories burned per minute';
COMMENT ON COLUMN exercises.is_verified IS 'Whether exercise is verified by admin';
COMMENT ON COLUMN exercises.search_vector IS 'Full-text search vector for exercise content';
COMMENT ON FUNCTION search_exercises IS 'Function to search exercises with filters';
COMMENT ON FUNCTION get_popular_exercises IS 'Function to get most used exercises in workout plans';
