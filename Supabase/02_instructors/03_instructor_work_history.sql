-- =====================================================
-- FITGO INSTRUCTORS: INSTRUCTOR WORK HISTORY TABLE
-- =====================================================
-- Instructor work experience and employment history
-- Consolidated from duplicate trainer_work_history

-- Drop table if exists (for development only)
-- DROP TABLE IF EXISTS instructor_work_history CASCADE;

-- Create instructor_work_history table
CREATE TABLE IF NOT EXISTS instructor_work_history (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    instructor_id UUID NOT NULL REFERENCES instructors(profile_id) ON DELETE CASCADE,
    company_name TEXT NOT NULL,
    position TEXT NOT NULL,
    start_date DATE NOT NULL,
    end_date DATE, -- NULL means current position
    description TEXT,
    location TEXT,
    is_current BOOLEAN DEFAULT false,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    CONSTRAINT check_date_order CHECK (end_date IS NULL OR end_date >= start_date),
    CONSTRAINT check_current_position CHECK (
        (is_current = true AND end_date IS NULL) OR 
        (is_current = false)
    )
);

-- Create indexes for better performance
CREATE INDEX IF NOT EXISTS idx_instructor_work_history_instructor_id ON instructor_work_history(instructor_id);
CREATE INDEX IF NOT EXISTS idx_instructor_work_history_start_date ON instructor_work_history(start_date);
CREATE INDEX IF NOT EXISTS idx_instructor_work_history_is_current ON instructor_work_history(is_current);

-- Enable Row Level Security
ALTER TABLE instructor_work_history ENABLE ROW LEVEL SECURITY;

-- RLS Policies for instructor_work_history table
-- Instructors can manage their own work history
CREATE POLICY "Instructors can manage own work history" ON instructor_work_history
    FOR ALL USING (auth.uid() = instructor_id);

-- Students can view work history of public instructors
CREATE POLICY "Students can view public instructor work history" ON instructor_work_history
    FOR SELECT USING (
        EXISTS (
            SELECT 1 FROM instructors i 
            WHERE i.profile_id = instructor_work_history.instructor_id 
            AND i.is_public = true
        )
        AND EXISTS (
            SELECT 1 FROM profiles p 
            WHERE p.id = auth.uid() 
            AND p.role = 'student'
        )
    );

-- Public can view work history of public instructors
CREATE POLICY "Public can view public instructor work history" ON instructor_work_history
    FOR SELECT USING (
        EXISTS (
            SELECT 1 FROM instructors i 
            WHERE i.profile_id = instructor_work_history.instructor_id 
            AND i.is_public = true
        )
    );

-- Admin can view all work history
CREATE POLICY "Admin can view all work history" ON instructor_work_history
    FOR ALL USING (
        EXISTS (
            SELECT 1 FROM profiles p 
            WHERE p.id = auth.uid() 
            AND p.role = 'admin'
        )
    );

-- Create trigger for updated_at
CREATE TRIGGER update_instructor_work_history_updated_at 
    BEFORE UPDATE ON instructor_work_history 
    FOR EACH ROW 
    EXECUTE FUNCTION update_updated_at_column();

-- Create function to calculate total experience years
CREATE OR REPLACE FUNCTION calculate_instructor_experience(instructor_uuid UUID)
RETURNS INTEGER AS $$
DECLARE
    total_months INTEGER := 0;
    work_record RECORD;
BEGIN
    FOR work_record IN 
        SELECT start_date, COALESCE(end_date, CURRENT_DATE) as end_date
        FROM instructor_work_history 
        WHERE instructor_id = instructor_uuid
    LOOP
        total_months := total_months + 
            EXTRACT(YEAR FROM AGE(work_record.end_date, work_record.start_date)) * 12 +
            EXTRACT(MONTH FROM AGE(work_record.end_date, work_record.start_date));
    END LOOP;
    
    RETURN GREATEST(total_months / 12, 0);
END;
$$ LANGUAGE plpgsql;

-- Create function to get current position
CREATE OR REPLACE FUNCTION get_instructor_current_position(instructor_uuid UUID)
RETURNS TEXT AS $$
DECLARE
    current_pos TEXT;
BEGIN
    SELECT CONCAT(position, ' at ', company_name) INTO current_pos
    FROM instructor_work_history
    WHERE instructor_id = instructor_uuid 
    AND is_current = true
    LIMIT 1;
    
    RETURN COALESCE(current_pos, 'Not specified');
END;
$$ LANGUAGE plpgsql;

-- Comments for documentation
COMMENT ON TABLE instructor_work_history IS 'Instructor work experience and employment history (consolidated from duplicate trainer_work_history)';
COMMENT ON COLUMN instructor_work_history.instructor_id IS 'Foreign key to instructors table';
COMMENT ON COLUMN instructor_work_history.company_name IS 'Name of the company/gym/organization';
COMMENT ON COLUMN instructor_work_history.position IS 'Job title/position held';
COMMENT ON COLUMN instructor_work_history.is_current IS 'Whether this is the current position';
COMMENT ON FUNCTION calculate_instructor_experience IS 'Calculate total years of experience for an instructor';
COMMENT ON FUNCTION get_instructor_current_position IS 'Get current position description for an instructor';
