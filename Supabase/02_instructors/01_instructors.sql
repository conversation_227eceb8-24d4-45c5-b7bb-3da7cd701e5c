-- =====================================================
-- FITGO INSTRUCTORS: INSTRUCTORS TABLE
-- =====================================================
-- Unified instructor table with approval system
-- Cleaned up from legacy teachers/trainers tables

-- Drop table if exists (for development only)
-- DROP TABLE IF EXISTS instructors CASCADE;

-- Create instructors table (simplified after major cleanup)
CREATE TABLE IF NOT EXISTS instructors (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    profile_id UUID NOT NULL REFERENCES profiles(id) ON DELETE CASCADE,
    is_public BOOLEAN DEFAULT false,

    -- Course visibility management
    course_status TEXT DEFAULT 'active' CHECK (course_status IN ('active', 'hidden', 'suspended')),
    course_hidden_at TIMESTAMP WITH TIME ZONE,
    course_hidden_reason TEXT,
    can_accept_new_students BOOLEAN DEFAULT true,

    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    UNIQUE(profile_id)
);

-- Create indexes for better performance
CREATE INDEX IF NOT EXISTS idx_instructors_profile_id ON instructors(profile_id);
CREATE INDEX IF NOT EXISTS idx_instructors_is_public ON instructors(is_public);
CREATE INDEX IF NOT EXISTS idx_instructors_course_status ON instructors(course_status);
CREATE INDEX IF NOT EXISTS idx_instructors_can_accept_students ON instructors(can_accept_new_students);
CREATE INDEX IF NOT EXISTS idx_instructors_created_at ON instructors(created_at);

-- Enable Row Level Security
ALTER TABLE instructors ENABLE ROW LEVEL SECURITY;

-- RLS Policies for instructors table
-- Instructors can view their own record
CREATE POLICY "Instructors can view own record" ON instructors
    FOR SELECT USING (auth.uid() = profile_id);

-- Instructors can update their own record
CREATE POLICY "Instructors can update own record" ON instructors
    FOR UPDATE USING (auth.uid() = profile_id);

-- Instructors can insert their own record
CREATE POLICY "Instructors can insert own record" ON instructors
    FOR INSERT WITH CHECK (auth.uid() = profile_id);

-- Students can view public instructor records (active courses or enrolled courses)
CREATE POLICY "Students can view public instructors" ON instructors
    FOR SELECT USING (
        is_public = true
        AND EXISTS (
            SELECT 1 FROM profiles p
            WHERE p.id = auth.uid()
            AND p.role = 'student'
        )
        AND (
            course_status = 'active'
            OR EXISTS (
                SELECT 1 FROM user_enrollments ue
                WHERE ue.instructor_id = instructors.profile_id
                AND ue.user_id = auth.uid()
                AND ue.status = 'active'
            )
        )
    );

-- Public can view public instructor records (for course listings) - only active courses
CREATE POLICY "Public can view public instructors" ON instructors
    FOR SELECT USING (is_public = true AND course_status = 'active');

-- Admin can view all instructor records
CREATE POLICY "Admin can view all instructors" ON instructors
    FOR ALL USING (
        EXISTS (
            SELECT 1 FROM profiles p 
            WHERE p.id = auth.uid() 
            AND p.role = 'admin'
        )
    );

-- Create trigger for updated_at
CREATE TRIGGER update_instructors_updated_at 
    BEFORE UPDATE ON instructors 
    FOR EACH ROW 
    EXECUTE FUNCTION update_updated_at_column();

-- Create function to automatically create instructor record when profile role is set to instructor
CREATE OR REPLACE FUNCTION create_instructor_on_role_change()
RETURNS TRIGGER AS $$
BEGIN
    -- If role changed to instructor and no instructor record exists
    IF NEW.role = 'instructor' AND OLD.role != 'instructor' THEN
        INSERT INTO instructors (profile_id, is_public)
        VALUES (NEW.id, false)
        ON CONFLICT (profile_id) DO NOTHING;
    END IF;
    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- Create trigger to auto-create instructor record
CREATE TRIGGER on_profile_role_changed_to_instructor
    AFTER UPDATE OF role ON profiles
    FOR EACH ROW
    EXECUTE FUNCTION create_instructor_on_role_change();

-- Function to hide instructor course (only if no active students)
CREATE OR REPLACE FUNCTION hide_instructor_course(
    instructor_profile_id UUID,
    hide_reason TEXT DEFAULT NULL
)
RETURNS JSONB AS $$
DECLARE
    active_students_count INTEGER;
    result JSONB;
BEGIN
    -- Check if instructor has active students
    SELECT COUNT(*) INTO active_students_count
    FROM user_enrollments ue
    WHERE ue.instructor_id = instructor_profile_id
    AND ue.status = 'active';

    -- If instructor has active students, don't allow hiding
    IF active_students_count > 0 THEN
        result := jsonb_build_object(
            'success', false,
            'message', 'Cannot hide course while you have active students',
            'active_students_count', active_students_count
        );
        RETURN result;
    END IF;

    -- Hide the course
    UPDATE instructors
    SET
        course_status = 'hidden',
        course_hidden_at = NOW(),
        course_hidden_reason = hide_reason,
        can_accept_new_students = false,
        updated_at = NOW()
    WHERE profile_id = instructor_profile_id;

    -- Update subscription config to require re-approval
    UPDATE instructor_subscription_configs
    SET
        submission_status = 'draft',
        updated_at = NOW()
    WHERE instructor_id = instructor_profile_id;

    result := jsonb_build_object(
        'success', true,
        'message', 'Course hidden successfully. You will need to resubmit for approval to reactivate.',
        'course_status', 'hidden'
    );

    RETURN result;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Function to reactivate instructor course (requires re-approval)
CREATE OR REPLACE FUNCTION reactivate_instructor_course(
    instructor_profile_id UUID
)
RETURNS JSONB AS $$
DECLARE
    result JSONB;
BEGIN
    -- Reactivate the course but keep it non-public until admin approval
    UPDATE instructors
    SET
        course_status = 'active',
        course_hidden_at = NULL,
        course_hidden_reason = NULL,
        can_accept_new_students = true,
        is_public = false, -- Requires admin re-approval
        updated_at = NOW()
    WHERE profile_id = instructor_profile_id;

    -- Reset subscription config to require re-approval
    UPDATE instructor_subscription_configs
    SET
        submission_status = 'draft',
        approval_status = 'pending',
        last_submitted_at = NULL,
        updated_at = NOW()
    WHERE instructor_id = instructor_profile_id;

    result := jsonb_build_object(
        'success', true,
        'message', 'Course reactivated. Please resubmit your subscription plan for admin approval.',
        'course_status', 'active',
        'requires_approval', true
    );

    RETURN result;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Comments for documentation
COMMENT ON TABLE instructors IS 'Unified instructor table with approval system and course visibility management';
COMMENT ON COLUMN instructors.profile_id IS 'Foreign key to profiles table';
COMMENT ON COLUMN instructors.is_public IS 'Whether instructor profile is visible to students';
COMMENT ON COLUMN instructors.course_status IS 'Course visibility status: active (visible to all), hidden (only enrolled students), suspended (admin action)';
COMMENT ON COLUMN instructors.course_hidden_at IS 'Timestamp when course was hidden by instructor';
COMMENT ON COLUMN instructors.course_hidden_reason IS 'Optional reason for hiding course';
COMMENT ON COLUMN instructors.can_accept_new_students IS 'Whether instructor is accepting new student enrollments';

-- Note: The following fields were REMOVED during major cleanup:
-- - bio, specializations, department, gym_affiliation (moved to instructor_subscription_configs or removed)
-- - price_per_session, price_monthly, price_3_months, price_6_months, price_1_year (moved to instructor_subscription_configs)
-- - client_count, total_students, review_count (calculated dynamically or moved to instructor_capacity)
-- - linkedin_url, instagram_url, website_url (removed)
-- - address, city, district, postal_code (removed)
-- - max_course_capacity, available_spots, max_students (moved to instructor_capacity)
-- - approval_status, approved_by, approved_at, application_submitted_at (moved to instructor_subscription_configs)
-- - current_students (moved to instructor_capacity)
-- - is_active (replaced with is_public)
