-- =====================================================
-- FITGO INSTRUCTORS: INSTRUCTORS TABLE
-- =====================================================
-- Unified instructor table with approval system
-- Cleaned up from legacy teachers/trainers tables

-- Drop table if exists (for development only)
-- DROP TABLE IF EXISTS instructors CASCADE;

-- Create instructors table (simplified after major cleanup)
CREATE TABLE IF NOT EXISTS instructors (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    profile_id UUID NOT NULL REFERENCES profiles(id) ON DELETE CASCADE,
    is_public BOOLEAN DEFAULT false,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    UNIQUE(profile_id)
);

-- Create indexes for better performance
CREATE INDEX IF NOT EXISTS idx_instructors_profile_id ON instructors(profile_id);
CREATE INDEX IF NOT EXISTS idx_instructors_is_public ON instructors(is_public);
CREATE INDEX IF NOT EXISTS idx_instructors_created_at ON instructors(created_at);

-- Enable Row Level Security
ALTER TABLE instructors ENABLE ROW LEVEL SECURITY;

-- RLS Policies for instructors table
-- Instructors can view their own record
CREATE POLICY "Instructors can view own record" ON instructors
    FOR SELECT USING (auth.uid() = profile_id);

-- Instructors can update their own record
CREATE POLICY "Instructors can update own record" ON instructors
    FOR UPDATE USING (auth.uid() = profile_id);

-- Instructors can insert their own record
CREATE POLICY "Instructors can insert own record" ON instructors
    FOR INSERT WITH CHECK (auth.uid() = profile_id);

-- Students can view public instructor records
CREATE POLICY "Students can view public instructors" ON instructors
    FOR SELECT USING (
        is_public = true 
        AND EXISTS (
            SELECT 1 FROM profiles p 
            WHERE p.id = auth.uid() 
            AND p.role = 'student'
        )
    );

-- Public can view public instructor records (for course listings)
CREATE POLICY "Public can view public instructors" ON instructors
    FOR SELECT USING (is_public = true);

-- Admin can view all instructor records
CREATE POLICY "Admin can view all instructors" ON instructors
    FOR ALL USING (
        EXISTS (
            SELECT 1 FROM profiles p 
            WHERE p.id = auth.uid() 
            AND p.role = 'admin'
        )
    );

-- Create trigger for updated_at
CREATE TRIGGER update_instructors_updated_at 
    BEFORE UPDATE ON instructors 
    FOR EACH ROW 
    EXECUTE FUNCTION update_updated_at_column();

-- Create function to automatically create instructor record when profile role is set to instructor
CREATE OR REPLACE FUNCTION create_instructor_on_role_change()
RETURNS TRIGGER AS $$
BEGIN
    -- If role changed to instructor and no instructor record exists
    IF NEW.role = 'instructor' AND OLD.role != 'instructor' THEN
        INSERT INTO instructors (profile_id, is_public)
        VALUES (NEW.id, false)
        ON CONFLICT (profile_id) DO NOTHING;
    END IF;
    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- Create trigger to auto-create instructor record
CREATE TRIGGER on_profile_role_changed_to_instructor
    AFTER UPDATE OF role ON profiles
    FOR EACH ROW
    EXECUTE FUNCTION create_instructor_on_role_change();

-- Comments for documentation
COMMENT ON TABLE instructors IS 'Unified instructor table with approval system (cleaned from legacy teachers/trainers)';
COMMENT ON COLUMN instructors.profile_id IS 'Foreign key to profiles table';
COMMENT ON COLUMN instructors.is_public IS 'Whether instructor profile is visible to students';

-- Note: The following fields were REMOVED during major cleanup:
-- - bio, specializations, department, gym_affiliation (moved to instructor_subscription_configs or removed)
-- - price_per_session, price_monthly, price_3_months, price_6_months, price_1_year (moved to instructor_subscription_configs)
-- - client_count, total_students, review_count (calculated dynamically or moved to instructor_capacity)
-- - linkedin_url, instagram_url, website_url (removed)
-- - address, city, district, postal_code (removed)
-- - max_course_capacity, available_spots, max_students (moved to instructor_capacity)
-- - approval_status, approved_by, approved_at, application_submitted_at (moved to instructor_subscription_configs)
-- - current_students (moved to instructor_capacity)
-- - is_active (replaced with is_public)
