-- =====================================================
-- FITGO INSTRUCTORS: INSTRUCTOR SUBSCRIPTION CONFIGS TABLE
-- =====================================================
-- Instructor subscription configurations with approval workflow
-- Refactored from JSON to individual columns for better admin panel updates

-- Drop table if exists (for development only)
-- DROP TABLE IF EXISTS instructor_subscription_configs CASCADE;

-- Create instructor_subscription_configs table
CREATE TABLE IF NOT EXISTS instructor_subscription_configs (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    instructor_id UUID NOT NULL REFERENCES instructors(profile_id) ON DELETE CASCADE,
    
    -- Approval workflow (moved from instructors table)
    approval_status TEXT DEFAULT 'pending' CHECK (approval_status IN ('pending', 'approved', 'rejected', 'under_review')),
    approved_by UUID REFERENCES profiles(id),
    approved_at TIMESTAMP WITH TIME ZONE,
    application_submitted_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    rejection_reason TEXT,
    
    -- Instructor profile information
    bio TEXT,
    specializations TEXT[],
    department TEXT,
    gym_affiliation TEXT,
    
    -- Social links
    linkedin_url TEXT,
    instagram_url TEXT,
    website_url TEXT,
    
    -- Location information
    address TEXT,
    city TEXT,
    district TEXT,
    postal_code TEXT,
    
    -- Instructor desired earnings (what instructor wants to earn monthly)
    desired_monthly_earnings DECIMAL(10,2) NOT NULL DEFAULT 0.00,

    -- Basic Plan Configuration (calculated from desired earnings + commission)
    basic_plan_monthly_price DECIMAL(10,2) DEFAULT 0.00,
    basic_plan_6month_price DECIMAL(10,2) DEFAULT 0.00,
    basic_plan_yearly_price DECIMAL(10,2) DEFAULT 0.00,
    basic_plan_features TEXT[],
    basic_plan_description TEXT,

    -- Premium Plan Configuration (calculated from desired earnings + commission)
    premium_plan_monthly_price DECIMAL(10,2) DEFAULT 0.00,
    premium_plan_6month_price DECIMAL(10,2) DEFAULT 0.00,
    premium_plan_yearly_price DECIMAL(10,2) DEFAULT 0.00,
    premium_plan_features TEXT[],
    premium_plan_description TEXT,

    -- Submission tracking
    submission_status TEXT DEFAULT 'draft' CHECK (submission_status IN ('draft', 'submitted', 'approved', 'rejected', 'needs_revision')),
    last_submitted_at TIMESTAMP WITH TIME ZONE,
    admin_feedback TEXT,
    
    -- Discount codes (keeping as JSON for flexibility)
    discount_codes JSONB DEFAULT '[]'::jsonb,
    
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    UNIQUE(instructor_id)
);

-- Create indexes for better performance
CREATE INDEX IF NOT EXISTS idx_instructor_subscription_configs_instructor_id ON instructor_subscription_configs(instructor_id);
CREATE INDEX IF NOT EXISTS idx_instructor_subscription_configs_approval_status ON instructor_subscription_configs(approval_status);
CREATE INDEX IF NOT EXISTS idx_instructor_subscription_configs_approved_by ON instructor_subscription_configs(approved_by);
CREATE INDEX IF NOT EXISTS idx_instructor_subscription_configs_city ON instructor_subscription_configs(city);

-- Enable Row Level Security
ALTER TABLE instructor_subscription_configs ENABLE ROW LEVEL SECURITY;

-- RLS Policies for instructor_subscription_configs table
-- Instructors can view and update their own config
CREATE POLICY "Instructors can manage own subscription config" ON instructor_subscription_configs
    FOR ALL USING (auth.uid() = instructor_id);

-- Students can view approved configs of public instructors
CREATE POLICY "Students can view approved instructor configs" ON instructor_subscription_configs
    FOR SELECT USING (
        approval_status = 'approved'
        AND EXISTS (
            SELECT 1 FROM instructors i 
            WHERE i.profile_id = instructor_subscription_configs.instructor_id 
            AND i.is_public = true
        )
        AND EXISTS (
            SELECT 1 FROM profiles p 
            WHERE p.id = auth.uid() 
            AND p.role = 'student'
        )
    );

-- Public can view approved configs of public instructors
CREATE POLICY "Public can view approved instructor configs" ON instructor_subscription_configs
    FOR SELECT USING (
        approval_status = 'approved'
        AND EXISTS (
            SELECT 1 FROM instructors i 
            WHERE i.profile_id = instructor_subscription_configs.instructor_id 
            AND i.is_public = true
        )
    );

-- Admin can view and manage all configs
CREATE POLICY "Admin can manage all subscription configs" ON instructor_subscription_configs
    FOR ALL USING (
        EXISTS (
            SELECT 1 FROM profiles p 
            WHERE p.id = auth.uid() 
            AND p.role = 'admin'
        )
    );

-- Create trigger for updated_at
CREATE TRIGGER update_instructor_subscription_configs_updated_at 
    BEFORE UPDATE ON instructor_subscription_configs 
    FOR EACH ROW 
    EXECUTE FUNCTION update_updated_at_column();

-- Create function to calculate plan prices from desired earnings
CREATE OR REPLACE FUNCTION calculate_plan_prices_from_earnings(
    instructor_uuid UUID,
    desired_earnings DECIMAL(10,2)
) RETURNS VOID AS $$
DECLARE
    commission_rate DECIMAL(5,4);
    basic_monthly DECIMAL(10,2);
    basic_6month DECIMAL(10,2);
    basic_yearly DECIMAL(10,2);
    premium_monthly DECIMAL(10,2);
    premium_6month DECIMAL(10,2);
    premium_yearly DECIMAL(10,2);
BEGIN
    -- Get commission rate from admin settings
    SELECT CAST(setting_value AS DECIMAL(5,4)) INTO commission_rate
    FROM admin_settings
    WHERE setting_key = 'instructor_commission_rate';

    -- If no commission rate found, use default 85%
    IF commission_rate IS NULL THEN
        commission_rate := 0.85;
    END IF;

    -- Calculate basic plan prices (desired earnings / commission rate)
    basic_monthly := ROUND(desired_earnings / commission_rate, 2);
    basic_6month := ROUND((desired_earnings * 6) / commission_rate * 0.95, 2); -- 5% discount for 6 months
    basic_yearly := ROUND((desired_earnings * 12) / commission_rate * 0.90, 2); -- 10% discount for yearly

    -- Calculate premium plan prices (1.5x basic plan)
    premium_monthly := ROUND(basic_monthly * 1.5, 2);
    premium_6month := ROUND(basic_6month * 1.5, 2);
    premium_yearly := ROUND(basic_yearly * 1.5, 2);

    -- Update instructor subscription config
    UPDATE instructor_subscription_configs
    SET
        desired_monthly_earnings = desired_earnings,
        basic_plan_monthly_price = basic_monthly,
        basic_plan_6month_price = basic_6month,
        basic_plan_yearly_price = basic_yearly,
        premium_plan_monthly_price = premium_monthly,
        premium_plan_6month_price = premium_6month,
        premium_plan_yearly_price = premium_yearly,
        updated_at = NOW()
    WHERE instructor_id = instructor_uuid;
END;
$$ LANGUAGE plpgsql;

-- Create function to approve instructor
CREATE OR REPLACE FUNCTION approve_instructor_subscription(
    instructor_uuid UUID,
    admin_uuid UUID
) RETURNS VOID AS $$
BEGIN
    UPDATE instructor_subscription_configs
    SET
        approval_status = 'approved',
        approved_by = admin_uuid,
        approved_at = NOW()
    WHERE instructor_id = instructor_uuid;

    -- Make instructor public when approved
    UPDATE instructors
    SET is_public = true
    WHERE profile_id = instructor_uuid;
END;
$$ LANGUAGE plpgsql;

-- Create function to reject instructor
CREATE OR REPLACE FUNCTION reject_instructor_subscription(
    instructor_uuid UUID,
    admin_uuid UUID,
    reason TEXT
) RETURNS VOID AS $$
BEGIN
    UPDATE instructor_subscription_configs 
    SET 
        approval_status = 'rejected',
        approved_by = admin_uuid,
        approved_at = NOW(),
        rejection_reason = reason
    WHERE instructor_id = instructor_uuid;
    
    -- Make instructor private when rejected
    UPDATE instructors 
    SET is_public = false 
    WHERE profile_id = instructor_uuid;
END;
$$ LANGUAGE plpgsql;

-- Comments for documentation
COMMENT ON TABLE instructor_subscription_configs IS 'Instructor subscription configurations with approval workflow (refactored from JSON to columns)';
COMMENT ON COLUMN instructor_subscription_configs.instructor_id IS 'Foreign key to instructors table';
COMMENT ON COLUMN instructor_subscription_configs.approval_status IS 'Approval status: pending, approved, rejected, under_review';
COMMENT ON COLUMN instructor_subscription_configs.desired_monthly_earnings IS 'Amount instructor wants to earn monthly (before platform commission)';
COMMENT ON COLUMN instructor_subscription_configs.basic_plan_monthly_price IS 'Basic plan monthly price (calculated from desired earnings + commission)';
COMMENT ON COLUMN instructor_subscription_configs.basic_plan_6month_price IS 'Basic plan 6-month price (with 5% discount)';
COMMENT ON COLUMN instructor_subscription_configs.basic_plan_yearly_price IS 'Basic plan yearly price (with 10% discount)';
COMMENT ON COLUMN instructor_subscription_configs.premium_plan_monthly_price IS 'Premium plan monthly price (1.5x basic plan)';
COMMENT ON COLUMN instructor_subscription_configs.premium_plan_6month_price IS 'Premium plan 6-month price (1.5x basic plan)';
COMMENT ON COLUMN instructor_subscription_configs.premium_plan_yearly_price IS 'Premium plan yearly price (1.5x basic plan)';
COMMENT ON FUNCTION calculate_plan_prices_from_earnings IS 'Calculate all plan prices from instructor desired earnings using admin commission rate';
COMMENT ON FUNCTION approve_instructor_subscription IS 'Function to approve instructor subscription and make them public';
COMMENT ON FUNCTION reject_instructor_subscription IS 'Function to reject instructor subscription with reason';
