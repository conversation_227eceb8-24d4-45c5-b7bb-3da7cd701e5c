-- =====================================================
-- FITGO INSTRUCTORS: INSTRUCTOR CAPACITY TABLE
-- =====================================================
-- Track current student counts and capacity limits per instructor
-- Moved from instructors table for better data organization

-- Drop table if exists (for development only)
-- DROP TABLE IF EXISTS instructor_capacity CASCADE;

-- Create instructor_capacity table
CREATE TABLE IF NOT EXISTS instructor_capacity (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    instructor_id UUID NOT NULL REFERENCES instructors(profile_id) ON DELETE CASCADE,
    
    -- Capacity settings
    max_students INTEGER DEFAULT 5 CHECK (max_students > 0), -- Free tier limit changed from 50 to 5
    current_students INTEGER DEFAULT 0 CHECK (current_students >= 0),
    max_course_capacity INTEGER DEFAULT 5 CHECK (max_course_capacity > 0),
    
    -- Calculated fields
    available_spots INTEGER GENERATED ALWAYS AS (max_students - current_students) STORED,
    capacity_percentage DECIMAL(5,2) GENERATED ALWAYS AS (
        CASE 
            WHEN max_students > 0 THEN (current_students::DECIMAL / max_students * 100)
            ELSE 0 
        END
    ) STORED,
    
    -- Status tracking
    is_accepting_students BOOLEAN DEFAULT true,
    last_capacity_update TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    UNIQUE(instructor_id),
    CONSTRAINT check_current_not_exceed_max CHECK (current_students <= max_students)
);

-- Create indexes for better performance
CREATE INDEX IF NOT EXISTS idx_instructor_capacity_instructor_id ON instructor_capacity(instructor_id);
CREATE INDEX IF NOT EXISTS idx_instructor_capacity_available_spots ON instructor_capacity(available_spots);
CREATE INDEX IF NOT EXISTS idx_instructor_capacity_is_accepting ON instructor_capacity(is_accepting_students);
CREATE INDEX IF NOT EXISTS idx_instructor_capacity_percentage ON instructor_capacity(capacity_percentage);

-- Enable Row Level Security
ALTER TABLE instructor_capacity ENABLE ROW LEVEL SECURITY;

-- RLS Policies for instructor_capacity table
-- Instructors can view and update their own capacity
CREATE POLICY "Instructors can manage own capacity" ON instructor_capacity
    FOR ALL USING (auth.uid() = instructor_id);

-- Students can view capacity of public instructors
CREATE POLICY "Students can view public instructor capacity" ON instructor_capacity
    FOR SELECT USING (
        EXISTS (
            SELECT 1 FROM instructors i 
            WHERE i.profile_id = instructor_capacity.instructor_id 
            AND i.is_public = true
        )
        AND EXISTS (
            SELECT 1 FROM profiles p 
            WHERE p.id = auth.uid() 
            AND p.role = 'student'
        )
    );

-- Public can view capacity of public instructors
CREATE POLICY "Public can view public instructor capacity" ON instructor_capacity
    FOR SELECT USING (
        EXISTS (
            SELECT 1 FROM instructors i 
            WHERE i.profile_id = instructor_capacity.instructor_id 
            AND i.is_public = true
        )
    );

-- Admin can view and manage all capacity records
CREATE POLICY "Admin can manage all capacity" ON instructor_capacity
    FOR ALL USING (
        EXISTS (
            SELECT 1 FROM profiles p 
            WHERE p.id = auth.uid() 
            AND p.role = 'admin'
        )
    );

-- Create trigger for updated_at
CREATE TRIGGER update_instructor_capacity_updated_at 
    BEFORE UPDATE ON instructor_capacity 
    FOR EACH ROW 
    EXECUTE FUNCTION update_updated_at_column();

-- Create function to automatically create capacity record when instructor is created
CREATE OR REPLACE FUNCTION create_instructor_capacity_on_instructor_creation()
RETURNS TRIGGER AS $$
BEGIN
    INSERT INTO instructor_capacity (instructor_id, max_students, current_students)
    VALUES (NEW.profile_id, 5, 0) -- Default to free tier limit of 5
    ON CONFLICT (instructor_id) DO NOTHING;
    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- Create trigger to auto-create capacity record
CREATE TRIGGER on_instructor_created
    AFTER INSERT ON instructors
    FOR EACH ROW
    EXECUTE FUNCTION create_instructor_capacity_on_instructor_creation();

-- Create function to update current student count
CREATE OR REPLACE FUNCTION update_instructor_student_count(
    instructor_uuid UUID,
    count_change INTEGER
) RETURNS VOID AS $$
BEGIN
    UPDATE instructor_capacity 
    SET 
        current_students = GREATEST(0, current_students + count_change),
        last_capacity_update = NOW()
    WHERE instructor_id = instructor_uuid;
    
    -- Auto-disable accepting students if at capacity
    UPDATE instructor_capacity 
    SET is_accepting_students = (current_students < max_students)
    WHERE instructor_id = instructor_uuid;
END;
$$ LANGUAGE plpgsql;

-- Create function to get instructors with available capacity
CREATE OR REPLACE FUNCTION get_instructors_with_capacity()
RETURNS TABLE (
    instructor_id UUID,
    available_spots INTEGER,
    capacity_percentage DECIMAL(5,2),
    is_accepting_students BOOLEAN
) AS $$
BEGIN
    RETURN QUERY
    SELECT 
        ic.instructor_id,
        ic.available_spots,
        ic.capacity_percentage,
        ic.is_accepting_students
    FROM instructor_capacity ic
    JOIN instructors i ON i.profile_id = ic.instructor_id
    WHERE i.is_public = true
    AND ic.available_spots > 0
    AND ic.is_accepting_students = true
    ORDER BY ic.available_spots DESC, ic.capacity_percentage ASC;
END;
$$ LANGUAGE plpgsql;

-- Comments for documentation
COMMENT ON TABLE instructor_capacity IS 'Track current student counts and capacity limits per instructor (moved from instructors table)';
COMMENT ON COLUMN instructor_capacity.instructor_id IS 'Foreign key to instructors table';
COMMENT ON COLUMN instructor_capacity.max_students IS 'Maximum students instructor can handle (default 5 for free tier)';
COMMENT ON COLUMN instructor_capacity.current_students IS 'Current number of enrolled students';
COMMENT ON COLUMN instructor_capacity.available_spots IS 'Calculated available spots (max - current)';
COMMENT ON COLUMN instructor_capacity.capacity_percentage IS 'Calculated capacity utilization percentage';
COMMENT ON FUNCTION update_instructor_student_count IS 'Function to update student count when enrollments change';
COMMENT ON FUNCTION get_instructors_with_capacity IS 'Function to get instructors with available capacity for new students';
