-- =====================================================
-- FITGO INSTRUCTORS: INSTRUCTOR CERTIFICATIONS TABLE
-- =====================================================
-- Instructor certifications and qualifications
-- Cleaned up from legacy structure

-- Drop table if exists (for development only)
-- DROP TABLE IF EXISTS instructor_certifications CASCADE;

-- Create instructor_certifications table
CREATE TABLE IF NOT EXISTS instructor_certifications (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    instructor_id UUID NOT NULL REFERENCES instructors(profile_id) ON DELETE CASCADE,
    certification_name TEXT NOT NULL,
    issuing_organization TEXT NOT NULL,
    issue_date DATE,
    expiry_date DATE,
    certificate_url TEXT, -- URL to certificate document in storage
    description TEXT,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Create indexes for better performance
CREATE INDEX IF NOT EXISTS idx_instructor_certifications_instructor_id ON instructor_certifications(instructor_id);
CREATE INDEX IF NOT EXISTS idx_instructor_certifications_issue_date ON instructor_certifications(issue_date);
CREATE INDEX IF NOT EXISTS idx_instructor_certifications_expiry_date ON instructor_certifications(expiry_date);

-- Enable Row Level Security
ALTER TABLE instructor_certifications ENABLE ROW LEVEL SECURITY;

-- RLS Policies for instructor_certifications table
-- Instructors can manage their own certifications
CREATE POLICY "Instructors can manage own certifications" ON instructor_certifications
    FOR ALL USING (auth.uid() = instructor_id);

-- Students can view certifications of public instructors
CREATE POLICY "Students can view public instructor certifications" ON instructor_certifications
    FOR SELECT USING (
        EXISTS (
            SELECT 1 FROM instructors i 
            WHERE i.profile_id = instructor_certifications.instructor_id 
            AND i.is_public = true
        )
        AND EXISTS (
            SELECT 1 FROM profiles p 
            WHERE p.id = auth.uid() 
            AND p.role = 'student'
        )
    );

-- Public can view certifications of public instructors
CREATE POLICY "Public can view public instructor certifications" ON instructor_certifications
    FOR SELECT USING (
        EXISTS (
            SELECT 1 FROM instructors i 
            WHERE i.profile_id = instructor_certifications.instructor_id 
            AND i.is_public = true
        )
    );

-- Admin can view all certifications
CREATE POLICY "Admin can view all certifications" ON instructor_certifications
    FOR ALL USING (
        EXISTS (
            SELECT 1 FROM profiles p 
            WHERE p.id = auth.uid() 
            AND p.role = 'admin'
        )
    );

-- Create trigger for updated_at
CREATE TRIGGER update_instructor_certifications_updated_at 
    BEFORE UPDATE ON instructor_certifications 
    FOR EACH ROW 
    EXECUTE FUNCTION update_updated_at_column();

-- Create function to check for expiring certifications
CREATE OR REPLACE FUNCTION get_expiring_certifications(days_ahead INTEGER DEFAULT 30)
RETURNS TABLE (
    instructor_id UUID,
    certification_name TEXT,
    expiry_date DATE,
    days_until_expiry INTEGER
) AS $$
BEGIN
    RETURN QUERY
    SELECT 
        ic.instructor_id,
        ic.certification_name,
        ic.expiry_date,
        (ic.expiry_date - CURRENT_DATE)::INTEGER as days_until_expiry
    FROM instructor_certifications ic
    WHERE ic.expiry_date IS NOT NULL
    AND ic.expiry_date <= CURRENT_DATE + INTERVAL '1 day' * days_ahead
    AND ic.expiry_date >= CURRENT_DATE
    ORDER BY ic.expiry_date ASC;
END;
$$ LANGUAGE plpgsql;

-- Comments for documentation
COMMENT ON TABLE instructor_certifications IS 'Instructor certifications and qualifications (cleaned from legacy structure)';
COMMENT ON COLUMN instructor_certifications.instructor_id IS 'Foreign key to instructors table';
COMMENT ON COLUMN instructor_certifications.certification_name IS 'Name of the certification';
COMMENT ON COLUMN instructor_certifications.issuing_organization IS 'Organization that issued the certification';
COMMENT ON COLUMN instructor_certifications.certificate_url IS 'URL to certificate document in storage';
COMMENT ON FUNCTION get_expiring_certifications IS 'Function to get certifications expiring within specified days';

-- Note: The following fields were REMOVED during cleanup:
-- - is_verified (removed - verification handled through approval process)
-- - external_link (removed - replaced with certificate_url for document storage)
