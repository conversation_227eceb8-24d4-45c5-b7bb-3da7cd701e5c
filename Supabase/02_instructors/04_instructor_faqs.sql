-- =====================================================
-- FITGO INSTRUCTORS: INSTRUCTOR FAQS TABLE
-- =====================================================
-- Frequently Asked Questions created by instructors
-- This table was missing and causing 404 errors

-- Drop table if exists (for development only)
-- DROP TABLE IF EXISTS instructor_faqs CASCADE;

-- Create instructor_faqs table
CREATE TABLE IF NOT EXISTS instructor_faqs (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    instructor_id UUID NOT NULL REFERENCES instructors(profile_id) ON DELETE CASCADE,
    question TEXT NOT NULL,
    answer TEXT NOT NULL,
    order_index INTEGER DEFAULT 0,
    is_active BOOLEAN DEFAULT true,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Create indexes for better performance
CREATE INDEX IF NOT EXISTS idx_instructor_faqs_instructor_id ON instructor_faqs(instructor_id);
CREATE INDEX IF NOT EXISTS idx_instructor_faqs_order_index ON instructor_faqs(order_index);
CREATE INDEX IF NOT EXISTS idx_instructor_faqs_is_active ON instructor_faqs(is_active);

-- Enable Row Level Security
ALTER TABLE instructor_faqs ENABLE ROW LEVEL SECURITY;

-- RLS Policies for instructor_faqs table
-- Instructors can manage their own FAQs
CREATE POLICY "Instructors can manage own faqs" ON instructor_faqs
    FOR ALL USING (auth.uid() = instructor_id);

-- Students can view active FAQs of public instructors
CREATE POLICY "Students can view public instructor faqs" ON instructor_faqs
    FOR SELECT USING (
        is_active = true
        AND EXISTS (
            SELECT 1 FROM instructors i 
            WHERE i.profile_id = instructor_faqs.instructor_id 
            AND i.is_public = true
        )
        AND EXISTS (
            SELECT 1 FROM profiles p 
            WHERE p.id = auth.uid() 
            AND p.role = 'student'
        )
    );

-- Public can view active FAQs of public instructors
CREATE POLICY "Public can view public instructor faqs" ON instructor_faqs
    FOR SELECT USING (
        is_active = true
        AND EXISTS (
            SELECT 1 FROM instructors i 
            WHERE i.profile_id = instructor_faqs.instructor_id 
            AND i.is_public = true
        )
    );

-- Admin can view all FAQs
CREATE POLICY "Admin can view all faqs" ON instructor_faqs
    FOR ALL USING (
        EXISTS (
            SELECT 1 FROM profiles p 
            WHERE p.id = auth.uid() 
            AND p.role = 'admin'
        )
    );

-- Create trigger for updated_at
CREATE TRIGGER update_instructor_faqs_updated_at 
    BEFORE UPDATE ON instructor_faqs 
    FOR EACH ROW 
    EXECUTE FUNCTION update_updated_at_column();

-- Create function to reorder FAQs
CREATE OR REPLACE FUNCTION reorder_instructor_faqs(
    instructor_uuid UUID,
    faq_ids UUID[]
) RETURNS VOID AS $$
DECLARE
    i INTEGER;
BEGIN
    FOR i IN 1..array_length(faq_ids, 1) LOOP
        UPDATE instructor_faqs 
        SET order_index = i - 1
        WHERE id = faq_ids[i] 
        AND instructor_id = instructor_uuid;
    END LOOP;
END;
$$ LANGUAGE plpgsql;

-- Create RPC function for creating instructor_faqs table (for app usage)
CREATE OR REPLACE FUNCTION create_instructor_faqs_table()
RETURNS TEXT AS $$
BEGIN
    -- This function exists to allow the app to check if the table can be created
    -- The actual table creation is handled by this SQL file
    RETURN 'instructor_faqs table already exists or was created successfully';
END;
$$ LANGUAGE plpgsql;

-- Comments for documentation
COMMENT ON TABLE instructor_faqs IS 'Frequently Asked Questions created by instructors (was missing and causing 404 errors)';
COMMENT ON COLUMN instructor_faqs.instructor_id IS 'Foreign key to instructors table';
COMMENT ON COLUMN instructor_faqs.question IS 'FAQ question text';
COMMENT ON COLUMN instructor_faqs.answer IS 'FAQ answer text';
COMMENT ON COLUMN instructor_faqs.order_index IS 'Display order of FAQ (0-based)';
COMMENT ON COLUMN instructor_faqs.is_active IS 'Whether FAQ is visible to students';
COMMENT ON FUNCTION reorder_instructor_faqs IS 'Function to reorder FAQs for an instructor';
COMMENT ON FUNCTION create_instructor_faqs_table IS 'RPC function for app to check table creation capability';
