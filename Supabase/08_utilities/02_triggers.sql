-- =====================================================
-- FITGO UTILITIES: DATABASE TRIGGERS
-- =====================================================
-- Common triggers for audit trails, data validation, and automation

-- Trigger function for email validation
CREATE OR REPLACE FUNCTION validate_email_trigger()
RETURNS TRIGGER AS $$
BEGIN
    IF NEW.email IS NOT NULL AND NOT is_valid_email(NEW.email) THEN
        RAISE EXCEPTION 'Invalid email format: %', NEW.email;
    END IF;
    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- Trigger function for phone validation
CREATE OR REPLACE FUNCTION validate_phone_trigger()
RETURNS TRIGGER AS $$
BEGIN
    IF NEW.phone IS NOT NULL THEN
        -- Format the phone number
        NEW.phone := format_turkish_phone(NEW.phone);
        
        -- Validate the formatted phone number
        IF NOT is_valid_turkish_phone(NEW.phone) THEN
            RAISE EXCEPTION 'Invalid Turkish phone number format: %', NEW.phone;
        END IF;
    END IF;
    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- Trigger function to prevent self-enrollment
CREATE OR REPLACE FUNCTION prevent_self_enrollment_trigger()
RETURNS TRIGGER AS $$
BEGIN
    IF NEW.student_id = NEW.instructor_id THEN
        RAISE EXCEPTION 'Instructors cannot enroll themselves as students';
    END IF;
    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- Trigger function to validate rating range
CREATE OR REPLACE FUNCTION validate_rating_trigger()
RETURNS TRIGGER AS $$
BEGIN
    IF NEW.rating IS NOT NULL AND (NEW.rating < 1 OR NEW.rating > 5) THEN
        RAISE EXCEPTION 'Rating must be between 1 and 5, got: %', NEW.rating;
    END IF;
    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- Trigger function to sanitize text content
CREATE OR REPLACE FUNCTION sanitize_content_trigger()
RETURNS TRIGGER AS $$
BEGIN
    -- Sanitize comment field if it exists
    IF TG_TABLE_NAME = 'instructor_reviews' AND NEW.comment IS NOT NULL THEN
        NEW.comment := sanitize_html(NEW.comment);
    END IF;
    
    -- Sanitize description fields
    IF NEW.description IS NOT NULL THEN
        NEW.description := sanitize_html(NEW.description);
    END IF;
    
    -- Sanitize bio field if it exists
    IF TG_TABLE_NAME = 'instructor_subscription_configs' AND NEW.bio IS NOT NULL THEN
        NEW.bio := sanitize_html(NEW.bio);
    END IF;
    
    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- Trigger function to update instructor rating when review is added/updated
CREATE OR REPLACE FUNCTION update_instructor_rating_trigger()
RETURNS TRIGGER AS $$
DECLARE
    avg_rating DECIMAL(3,2);
    review_count BIGINT;
BEGIN
    -- Calculate new average rating
    SELECT 
        ROUND(AVG(rating)::NUMERIC, 2),
        COUNT(*)
    INTO avg_rating, review_count
    FROM instructor_reviews 
    WHERE instructor_id = COALESCE(NEW.instructor_id, OLD.instructor_id)
    AND is_public = true 
    AND is_approved = true;
    
    -- Update instructor subscription config with new rating
    UPDATE instructor_subscription_configs 
    SET updated_at = NOW()
    WHERE instructor_id = COALESCE(NEW.instructor_id, OLD.instructor_id);
    
    RETURN COALESCE(NEW, OLD);
END;
$$ LANGUAGE plpgsql;

-- Trigger function to log capacity changes
CREATE OR REPLACE FUNCTION log_capacity_changes_trigger()
RETURNS TRIGGER AS $$
BEGIN
    -- Update last_capacity_update when current_students changes
    IF OLD.current_students IS DISTINCT FROM NEW.current_students THEN
        NEW.last_capacity_update := NOW();
        
        -- Auto-disable accepting students if at capacity
        NEW.is_accepting_students := (NEW.current_students < NEW.max_students);
    END IF;
    
    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- Trigger function to validate subscription dates
CREATE OR REPLACE FUNCTION validate_subscription_dates_trigger()
RETURNS TRIGGER AS $$
BEGIN
    -- Validate trial dates
    IF NEW.trial_start_date IS NOT NULL AND NEW.trial_end_date IS NOT NULL THEN
        IF NEW.trial_end_date <= NEW.trial_start_date THEN
            RAISE EXCEPTION 'Trial end date must be after trial start date';
        END IF;
    END IF;
    
    -- Validate subscription dates
    IF NEW.start_date IS NOT NULL AND NEW.end_date IS NOT NULL THEN
        IF NEW.end_date <= NEW.start_date THEN
            RAISE EXCEPTION 'Subscription end date must be after start date';
        END IF;
    END IF;
    
    -- Set next payment amount if not specified
    IF NEW.next_payment_amount IS NULL THEN
        NEW.next_payment_amount := NEW.amount;
    END IF;
    
    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- Trigger function to validate payment amounts
CREATE OR REPLACE FUNCTION validate_payment_amounts_trigger()
RETURNS TRIGGER AS $$
BEGIN
    -- Ensure refund amount doesn't exceed payment amount
    IF NEW.refund_amount > NEW.amount THEN
        RAISE EXCEPTION 'Refund amount (%) cannot exceed payment amount (%)', 
            NEW.refund_amount, NEW.amount;
    END IF;
    
    -- Ensure commission rate is reasonable
    IF NEW.platform_commission_rate < 0 OR NEW.platform_commission_rate > 1 THEN
        RAISE EXCEPTION 'Platform commission rate must be between 0 and 1, got: %', 
            NEW.platform_commission_rate;
    END IF;
    
    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- Apply email validation trigger to profiles
CREATE TRIGGER validate_profile_email
    BEFORE INSERT OR UPDATE OF email ON profiles
    FOR EACH ROW
    EXECUTE FUNCTION validate_email_trigger();

-- Apply phone validation trigger to profiles
CREATE TRIGGER validate_profile_phone
    BEFORE INSERT OR UPDATE OF phone ON profiles
    FOR EACH ROW
    EXECUTE FUNCTION validate_phone_trigger();

-- Apply self-enrollment prevention to students
CREATE TRIGGER prevent_self_enrollment
    BEFORE INSERT OR UPDATE ON students
    FOR EACH ROW
    EXECUTE FUNCTION prevent_self_enrollment_trigger();

-- Apply self-enrollment prevention to enrollments
CREATE TRIGGER prevent_self_enrollment_enrollments
    BEFORE INSERT OR UPDATE ON enrollments
    FOR EACH ROW
    EXECUTE FUNCTION prevent_self_enrollment_trigger();

-- Apply rating validation to instructor_reviews
CREATE TRIGGER validate_review_rating
    BEFORE INSERT OR UPDATE OF rating ON instructor_reviews
    FOR EACH ROW
    EXECUTE FUNCTION validate_rating_trigger();

-- Apply content sanitization to instructor_reviews
CREATE TRIGGER sanitize_review_content
    BEFORE INSERT OR UPDATE ON instructor_reviews
    FOR EACH ROW
    EXECUTE FUNCTION sanitize_content_trigger();

-- Apply content sanitization to instructor_subscription_configs
CREATE TRIGGER sanitize_instructor_config_content
    BEFORE INSERT OR UPDATE ON instructor_subscription_configs
    FOR EACH ROW
    EXECUTE FUNCTION sanitize_content_trigger();

-- Apply instructor rating update trigger
CREATE TRIGGER update_instructor_rating_on_review_change
    AFTER INSERT OR UPDATE OR DELETE ON instructor_reviews
    FOR EACH ROW
    EXECUTE FUNCTION update_instructor_rating_trigger();

-- Apply capacity change logging to instructor_capacity
CREATE TRIGGER log_instructor_capacity_changes
    BEFORE UPDATE ON instructor_capacity
    FOR EACH ROW
    EXECUTE FUNCTION log_capacity_changes_trigger();

-- Apply subscription date validation
CREATE TRIGGER validate_subscription_dates
    BEFORE INSERT OR UPDATE ON user_subscriptions
    FOR EACH ROW
    EXECUTE FUNCTION validate_subscription_dates_trigger();

-- Apply payment amount validation
CREATE TRIGGER validate_payment_amounts
    BEFORE INSERT OR UPDATE ON payments
    FOR EACH ROW
    EXECUTE FUNCTION validate_payment_amounts_trigger();

-- Comments for documentation
COMMENT ON FUNCTION validate_email_trigger IS 'Trigger function to validate email format on insert/update';
COMMENT ON FUNCTION validate_phone_trigger IS 'Trigger function to format and validate Turkish phone numbers';
COMMENT ON FUNCTION prevent_self_enrollment_trigger IS 'Trigger function to prevent instructors from enrolling themselves';
COMMENT ON FUNCTION validate_rating_trigger IS 'Trigger function to validate rating values (1-5)';
COMMENT ON FUNCTION sanitize_content_trigger IS 'Trigger function to sanitize HTML content and prevent XSS';
COMMENT ON FUNCTION update_instructor_rating_trigger IS 'Trigger function to update instructor ratings when reviews change';
COMMENT ON FUNCTION log_capacity_changes_trigger IS 'Trigger function to log capacity changes and auto-disable enrollment';
COMMENT ON FUNCTION validate_subscription_dates_trigger IS 'Trigger function to validate subscription and trial dates';
COMMENT ON FUNCTION validate_payment_amounts_trigger IS 'Trigger function to validate payment and refund amounts';
