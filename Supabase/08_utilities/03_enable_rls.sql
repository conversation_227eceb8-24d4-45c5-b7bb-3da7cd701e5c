-- =====================================================
-- FITGO UTILITIES: ENABLE ROW LEVEL SECURITY
-- =====================================================
-- Enable RLS on all tables and verify security policies

-- Enable RLS on all core tables
ALTER TABLE profiles ENABLE ROW LEVEL SECURITY;
ALTER TABLE user_profiles ENABLE ROW LEVEL SECURITY;

-- Enable RLS on instructor tables
ALTER TABLE instructors ENABLE ROW LEVEL SECURITY;
ALTER TABLE instructor_certifications ENABLE ROW LEVEL SECURITY;
ALTER TABLE instructor_work_history ENABLE ROW LEVEL SECURITY;
ALTER TABLE instructor_faqs ENABLE ROW LEVEL SECURITY;
ALTER TABLE instructor_subscription_configs ENABLE ROW LEVEL SECURITY;
ALTER TABLE instructor_capacity ENABLE ROW LEVEL SECURITY;

-- Enable RLS on admin tables
ALTER TABLE admin_settings ENABLE ROW LEVEL SECURITY;

-- Enable RLS on student tables
ALTER TABLE students ENABLE ROW LEVEL SECURITY;
ALTER TABLE enrollments ENABLE ROW LEVEL SECURITY;

-- Enable RLS on content tables
ALTER TABLE exercises ENABLE ROW LEVEL SECURITY;
ALTER TABLE workout_plan_templates ENABLE ROW LEVEL SECURITY;
ALTER TABLE workout_template_plans ENABLE ROW LEVEL SECURITY;
ALTER TABLE workout_template_plan_exercises ENABLE ROW LEVEL SECURITY;

-- Enable RLS on payment tables
ALTER TABLE payments ENABLE ROW LEVEL SECURITY;
ALTER TABLE user_subscriptions ENABLE ROW LEVEL SECURITY;

-- Enable RLS on social tables
ALTER TABLE instructor_reviews ENABLE ROW LEVEL SECURITY;

-- Function to verify RLS is enabled on all tables
CREATE OR REPLACE FUNCTION verify_rls_enabled()
RETURNS TABLE (
    table_name TEXT,
    rls_enabled BOOLEAN,
    policy_count BIGINT
) AS $$
BEGIN
    RETURN QUERY
    SELECT 
        t.tablename::TEXT,
        t.rowsecurity as rls_enabled,
        COUNT(p.policyname) as policy_count
    FROM pg_tables t
    LEFT JOIN pg_policies p ON p.tablename = t.tablename
    WHERE t.schemaname = 'public'
    AND t.tablename NOT LIKE 'pg_%'
    AND t.tablename NOT LIKE 'information_schema%'
    GROUP BY t.tablename, t.rowsecurity
    ORDER BY t.tablename;
END;
$$ LANGUAGE plpgsql;

-- Function to check if user can access table
CREATE OR REPLACE FUNCTION test_table_access(
    table_name_param TEXT,
    user_role_param TEXT DEFAULT 'student'
)
RETURNS TABLE (
    operation TEXT,
    access_granted BOOLEAN,
    error_message TEXT
) AS $$
DECLARE
    test_query TEXT;
    error_msg TEXT;
BEGIN
    -- Test SELECT access
    BEGIN
        test_query := format('SELECT COUNT(*) FROM %I LIMIT 1', table_name_param);
        EXECUTE test_query;
        
        RETURN QUERY SELECT 'SELECT'::TEXT, true::BOOLEAN, NULL::TEXT;
    EXCEPTION WHEN OTHERS THEN
        GET STACKED DIAGNOSTICS error_msg = MESSAGE_TEXT;
        RETURN QUERY SELECT 'SELECT'::TEXT, false::BOOLEAN, error_msg::TEXT;
    END;
    
    -- Note: INSERT, UPDATE, DELETE tests would require more complex setup
    -- with actual test data and proper user context
END;
$$ LANGUAGE plpgsql;

-- Function to get RLS policy summary
CREATE OR REPLACE FUNCTION get_rls_policy_summary()
RETURNS TABLE (
    table_name TEXT,
    policy_name TEXT,
    policy_command TEXT,
    policy_roles TEXT[]
) AS $$
BEGIN
    RETURN QUERY
    SELECT 
        p.tablename::TEXT,
        p.policyname::TEXT,
        p.cmd::TEXT,
        p.roles::TEXT[]
    FROM pg_policies p
    WHERE p.schemaname = 'public'
    ORDER BY p.tablename, p.policyname;
END;
$$ LANGUAGE plpgsql;

-- Function to create emergency admin access (use with caution)
CREATE OR REPLACE FUNCTION create_emergency_admin_access()
RETURNS TEXT AS $$
DECLARE
    table_record RECORD;
    policy_name TEXT;
BEGIN
    -- This function creates emergency admin policies for all tables
    -- Should only be used in emergency situations
    
    FOR table_record IN 
        SELECT tablename FROM pg_tables 
        WHERE schemaname = 'public' 
        AND rowsecurity = true
    LOOP
        policy_name := 'emergency_admin_access_' || table_record.tablename;
        
        -- Check if policy already exists
        IF NOT EXISTS (
            SELECT 1 FROM pg_policies 
            WHERE tablename = table_record.tablename 
            AND policyname = policy_name
        ) THEN
            EXECUTE format(
                'CREATE POLICY %I ON %I FOR ALL USING (
                    EXISTS (
                        SELECT 1 FROM profiles p 
                        WHERE p.id = auth.uid() 
                        AND p.role = ''admin''
                    )
                )',
                policy_name,
                table_record.tablename
            );
        END IF;
    END LOOP;
    
    RETURN 'Emergency admin access policies created for all tables';
END;
$$ LANGUAGE plpgsql;

-- Function to remove emergency admin access
CREATE OR REPLACE FUNCTION remove_emergency_admin_access()
RETURNS TEXT AS $$
DECLARE
    table_record RECORD;
    policy_name TEXT;
BEGIN
    FOR table_record IN 
        SELECT tablename FROM pg_tables 
        WHERE schemaname = 'public'
    LOOP
        policy_name := 'emergency_admin_access_' || table_record.tablename;
        
        -- Drop policy if it exists
        IF EXISTS (
            SELECT 1 FROM pg_policies 
            WHERE tablename = table_record.tablename 
            AND policyname = policy_name
        ) THEN
            EXECUTE format('DROP POLICY %I ON %I', policy_name, table_record.tablename);
        END IF;
    END LOOP;
    
    RETURN 'Emergency admin access policies removed from all tables';
END;
$$ LANGUAGE plpgsql;

-- Function to check RLS bypass (for debugging)
CREATE OR REPLACE FUNCTION check_rls_bypass()
RETURNS TABLE (
    setting_name TEXT,
    setting_value TEXT,
    description TEXT
) AS $$
BEGIN
    RETURN QUERY
    SELECT 
        'row_security'::TEXT,
        current_setting('row_security')::TEXT,
        'Whether RLS is enforced (on/off)'::TEXT
    UNION ALL
    SELECT 
        'current_user'::TEXT,
        current_user::TEXT,
        'Current database user'::TEXT
    UNION ALL
    SELECT 
        'auth.uid()'::TEXT,
        COALESCE(auth.uid()::TEXT, 'NULL'),
        'Current authenticated user ID'::TEXT
    UNION ALL
    SELECT 
        'session_user'::TEXT,
        session_user::TEXT,
        'Session user (should be authenticator)'::TEXT;
END;
$$ LANGUAGE plpgsql;

-- Verify RLS is working correctly
DO $$
DECLARE
    rls_check RECORD;
    tables_without_rls INTEGER := 0;
BEGIN
    -- Check for tables without RLS
    FOR rls_check IN 
        SELECT tablename, rowsecurity 
        FROM pg_tables 
        WHERE schemaname = 'public' 
        AND rowsecurity = false
        AND tablename NOT LIKE 'pg_%'
    LOOP
        RAISE WARNING 'Table % does not have RLS enabled', rls_check.tablename;
        tables_without_rls := tables_without_rls + 1;
    END LOOP;
    
    IF tables_without_rls = 0 THEN
        RAISE NOTICE 'All tables have RLS enabled successfully';
    ELSE
        RAISE WARNING '% tables found without RLS enabled', tables_without_rls;
    END IF;
END;
$$;

-- Comments for documentation
COMMENT ON FUNCTION verify_rls_enabled IS 'Function to verify RLS is enabled on all tables and count policies';
COMMENT ON FUNCTION test_table_access IS 'Function to test table access for different user roles';
COMMENT ON FUNCTION get_rls_policy_summary IS 'Function to get summary of all RLS policies';
COMMENT ON FUNCTION create_emergency_admin_access IS 'Emergency function to create admin access to all tables (use with caution)';
COMMENT ON FUNCTION remove_emergency_admin_access IS 'Function to remove emergency admin access policies';
COMMENT ON FUNCTION check_rls_bypass IS 'Function to check RLS settings and current user context for debugging';

-- Final verification message
SELECT 'RLS has been enabled on all Fitgo tables. Use verify_rls_enabled() to check status.' as status;
