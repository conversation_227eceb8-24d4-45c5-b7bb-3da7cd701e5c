-- =====================================================
-- FITGO ADMIN: ADMIN SETTINGS TABLE
-- =====================================================
-- Global admin settings for platform configuration
-- Commission rates, platform fees, and other configurable values

-- Drop table if exists (for development only)
-- DROP TABLE IF EXISTS admin_settings CASCADE;

-- Create admin_settings table
CREATE TABLE IF NOT EXISTS admin_settings (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    setting_key TEXT NOT NULL UNIQUE,
    setting_value TEXT NOT NULL,
    setting_type TEXT NOT NULL CHECK (setting_type IN ('string', 'number', 'boolean', 'json')),
    description TEXT,
    category TEXT DEFAULT 'general',
    is_editable BOOLEAN DEFAULT true,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_by UUID REFERENCES profiles(id)
);

-- Create indexes for better performance
CREATE INDEX IF NOT EXISTS idx_admin_settings_key ON admin_settings(setting_key);
CREATE INDEX IF NOT EXISTS idx_admin_settings_category ON admin_settings(category);

-- Enable Row Level Security
ALTER TABLE admin_settings ENABLE ROW LEVEL SECURITY;

-- RLS Policies for admin_settings table
-- Only admins can manage settings
CREATE POLICY "Admin can manage all settings" ON admin_settings
    FOR ALL USING (
        EXISTS (
            SELECT 1 FROM profiles p 
            WHERE p.id = auth.uid() 
            AND p.role = 'admin'
        )
    );

-- Instructors and students can read certain settings
CREATE POLICY "Users can read public settings" ON admin_settings
    FOR SELECT USING (
        category IN ('public', 'pricing') 
        AND is_editable = true
    );

-- Create trigger for updated_at
CREATE TRIGGER update_admin_settings_updated_at 
    BEFORE UPDATE ON admin_settings 
    FOR EACH ROW 
    EXECUTE FUNCTION update_updated_at_column();

-- Insert default admin settings
INSERT INTO admin_settings (setting_key, setting_value, setting_type, description, category, is_editable) VALUES
-- Commission and pricing settings
('platform_commission_rate', '0.15', 'number', 'Platform commission rate (0.15 = 15%)', 'pricing', true),
('instructor_commission_rate', '0.85', 'number', 'Instructor commission rate (0.85 = 85%)', 'pricing', true),
('minimum_plan_price', '100.00', 'number', 'Minimum plan price in TRY', 'pricing', true),
('maximum_plan_price', '5000.00', 'number', 'Maximum plan price in TRY', 'pricing', true),
('basic_plan_6month_discount', '0.05', 'number', 'Discount for 6-month basic plan (0.05 = 5%)', 'pricing', true),
('basic_plan_yearly_discount', '0.10', 'number', 'Discount for yearly basic plan (0.10 = 10%)', 'pricing', true),
('premium_plan_multiplier', '1.5', 'number', 'Premium plan price multiplier (1.5 = 150% of basic plan)', 'pricing', true),

-- Platform settings
('platform_currency', 'TRY', 'string', 'Platform default currency', 'general', true),
('max_students_free_tier', '5', 'number', 'Maximum students for free tier instructors', 'general', true),
('max_students_premium_tier', '50', 'number', 'Maximum students for premium tier instructors', 'general', true),

-- Feature flags
('enable_instructor_approval', 'true', 'boolean', 'Enable instructor approval workflow', 'features', true),
('enable_payment_processing', 'true', 'boolean', 'Enable payment processing', 'features', true),
('enable_notifications', 'true', 'boolean', 'Enable push notifications', 'features', true),

-- Business rules
('instructor_min_experience_years', '1', 'number', 'Minimum experience years for instructors', 'business', true),
('plan_duration_options', '[1, 3, 6, 12]', 'json', 'Available plan duration options in months', 'business', true),
('supported_languages', '["tr", "en"]', 'json', 'Supported platform languages', 'general', true)

ON CONFLICT (setting_key) DO UPDATE SET
    setting_value = EXCLUDED.setting_value,
    updated_at = NOW();

-- Create function to get setting value
CREATE OR REPLACE FUNCTION get_admin_setting(key_name TEXT)
RETURNS TEXT AS $$
DECLARE
    result TEXT;
BEGIN
    SELECT setting_value INTO result
    FROM admin_settings
    WHERE setting_key = key_name;
    
    RETURN result;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Create function to update setting value
CREATE OR REPLACE FUNCTION update_admin_setting(
    key_name TEXT,
    new_value TEXT,
    admin_user_id UUID DEFAULT auth.uid()
)
RETURNS BOOLEAN AS $$
DECLARE
    is_admin BOOLEAN := false;
BEGIN
    -- Check if user is admin
    SELECT EXISTS (
        SELECT 1 FROM profiles 
        WHERE id = admin_user_id AND role = 'admin'
    ) INTO is_admin;
    
    IF NOT is_admin THEN
        RAISE EXCEPTION 'Only admins can update settings';
    END IF;
    
    -- Update the setting
    UPDATE admin_settings 
    SET 
        setting_value = new_value,
        updated_at = NOW(),
        updated_by = admin_user_id
    WHERE setting_key = key_name AND is_editable = true;
    
    RETURN FOUND;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Create function to calculate instructor earnings based on commission
CREATE OR REPLACE FUNCTION calculate_instructor_earnings(
    desired_earnings DECIMAL(10,2)
)
RETURNS DECIMAL(10,2) AS $$
DECLARE
    commission_rate DECIMAL(5,4);
    plan_price DECIMAL(10,2);
BEGIN
    -- Get current commission rate
    SELECT CAST(get_admin_setting('instructor_commission_rate') AS DECIMAL(5,4)) 
    INTO commission_rate;
    
    -- Calculate plan price: desired_earnings / commission_rate
    plan_price := desired_earnings / commission_rate;
    
    RETURN ROUND(plan_price, 2);
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Comments for documentation
COMMENT ON TABLE admin_settings IS 'Global admin settings for platform configuration';
COMMENT ON COLUMN admin_settings.setting_key IS 'Unique setting identifier';
COMMENT ON COLUMN admin_settings.setting_value IS 'Setting value as text (cast to appropriate type)';
COMMENT ON COLUMN admin_settings.setting_type IS 'Data type: string, number, boolean, json';
COMMENT ON COLUMN admin_settings.category IS 'Setting category for organization';
COMMENT ON COLUMN admin_settings.is_editable IS 'Whether setting can be modified';
COMMENT ON FUNCTION get_admin_setting IS 'Get admin setting value by key';
COMMENT ON FUNCTION update_admin_setting IS 'Update admin setting value (admin only)';
COMMENT ON FUNCTION calculate_instructor_earnings IS 'Calculate plan price from desired instructor earnings';
