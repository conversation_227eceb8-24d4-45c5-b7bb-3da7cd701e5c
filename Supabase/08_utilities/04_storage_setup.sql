-- =====================================================
-- FITGO UTILITIES: STORAGE SETUP
-- =====================================================
-- Setup storage buckets and policies for file uploads

-- Create storage buckets
INSERT INTO storage.buckets (id, name, public, file_size_limit, allowed_mime_types)
VALUES
    ('profile_images', 'profile_images', true, 5242880, ARRAY['image/jpeg', 'image/png', 'image/webp']),
    ('exercise_media', 'exercise_media', true, 52428800, ARRAY['image/jpeg', 'image/png', 'image/webp', 'video/mp4', 'video/webm']),
    ('workout_media', 'workout_media', true, 52428800, ARRAY['image/jpeg', 'image/png', 'image/webp', 'video/mp4', 'video/webm']),
    ('certification_documents', 'certification_documents', false, 10485760, ARRAY['application/pdf', 'image/jpeg', 'image/png']),
    ('feedback_photos', 'feedback_photos', true, 5242880, ARRAY['image/jpeg', 'image/png', 'image/webp'])
ON CONFLICT (id) DO UPDATE SET
    public = EXCLUDED.public,
    file_size_limit = EXCLUDED.file_size_limit,
    allowed_mime_types = EXCLUDED.allowed_mime_types;

-- Storage policies for profile_images bucket
CREATE POLICY "Users can upload their own profile images" ON storage.objects
    FOR INSERT WITH CHECK (
        bucket_id = 'profile_images' 
        AND auth.uid()::text = (storage.foldername(name))[1]
    );

CREATE POLICY "Users can view their own profile images" ON storage.objects
    FOR SELECT USING (
        bucket_id = 'profile_images' 
        AND auth.uid()::text = (storage.foldername(name))[1]
    );

CREATE POLICY "Users can update their own profile images" ON storage.objects
    FOR UPDATE USING (
        bucket_id = 'profile_images' 
        AND auth.uid()::text = (storage.foldername(name))[1]
    );

CREATE POLICY "Users can delete their own profile images" ON storage.objects
    FOR DELETE USING (
        bucket_id = 'profile_images' 
        AND auth.uid()::text = (storage.foldername(name))[1]
    );

CREATE POLICY "Public can view public profile images" ON storage.objects
    FOR SELECT USING (bucket_id = 'profile_images');

-- Storage policies for exercise_media bucket
CREATE POLICY "Instructors can upload exercise media" ON storage.objects
    FOR INSERT WITH CHECK (
        bucket_id = 'exercise_media' 
        AND EXISTS (
            SELECT 1 FROM profiles p 
            WHERE p.id = auth.uid() 
            AND p.role IN ('instructor', 'admin')
        )
    );

CREATE POLICY "Public can view exercise media" ON storage.objects
    FOR SELECT USING (bucket_id = 'exercise_media');

-- Storage policies for feedback_photos bucket
CREATE POLICY "Students can upload feedback photos" ON storage.objects
    FOR INSERT WITH CHECK (
        bucket_id = 'feedback_photos'
        AND auth.uid()::text = (storage.foldername(name))[1]
        AND EXISTS (
            SELECT 1 FROM feedback f
            WHERE f.student_id = auth.uid()
            AND f.status = 'waiting'
        )
    );

CREATE POLICY "Students can view their feedback photos" ON storage.objects
    FOR SELECT USING (
        bucket_id = 'feedback_photos'
        AND auth.uid()::text = (storage.foldername(name))[1]
    );

CREATE POLICY "Students can update their feedback photos" ON storage.objects
    FOR UPDATE USING (
        bucket_id = 'feedback_photos'
        AND auth.uid()::text = (storage.foldername(name))[1]
        AND EXISTS (
            SELECT 1 FROM feedback f
            WHERE f.student_id = auth.uid()
            AND f.status = 'waiting'
        )
    );

CREATE POLICY "Students can delete their feedback photos" ON storage.objects
    FOR DELETE USING (
        bucket_id = 'feedback_photos'
        AND auth.uid()::text = (storage.foldername(name))[1]
        AND EXISTS (
            SELECT 1 FROM feedback f
            WHERE f.student_id = auth.uid()
            AND f.status = 'waiting'
        )
    );

CREATE POLICY "Instructors can view student feedback photos" ON storage.objects
    FOR SELECT USING (
        bucket_id = 'feedback_photos'
        AND EXISTS (
            SELECT 1 FROM feedback f
            JOIN instructors i ON i.profile_id = f.instructor_id
            WHERE i.profile_id = auth.uid()
            AND f.student_id::text = (storage.foldername(name))[1]
        )
    );

CREATE POLICY "Instructors can update their exercise media" ON storage.objects
    FOR UPDATE USING (
        bucket_id = 'exercise_media' 
        AND EXISTS (
            SELECT 1 FROM profiles p 
            WHERE p.id = auth.uid() 
            AND p.role IN ('instructor', 'admin')
        )
    );

CREATE POLICY "Instructors can delete their exercise media" ON storage.objects
    FOR DELETE USING (
        bucket_id = 'exercise_media' 
        AND EXISTS (
            SELECT 1 FROM profiles p 
            WHERE p.id = auth.uid() 
            AND p.role IN ('instructor', 'admin')
        )
    );

-- Storage policies for workout_media bucket
CREATE POLICY "Instructors can upload workout media" ON storage.objects
    FOR INSERT WITH CHECK (
        bucket_id = 'workout_media' 
        AND EXISTS (
            SELECT 1 FROM profiles p 
            WHERE p.id = auth.uid() 
            AND p.role IN ('instructor', 'admin')
        )
    );

CREATE POLICY "Public can view workout media" ON storage.objects
    FOR SELECT USING (bucket_id = 'workout_media');

CREATE POLICY "Instructors can update their workout media" ON storage.objects
    FOR UPDATE USING (
        bucket_id = 'workout_media' 
        AND EXISTS (
            SELECT 1 FROM profiles p 
            WHERE p.id = auth.uid() 
            AND p.role IN ('instructor', 'admin')
        )
    );

CREATE POLICY "Instructors can delete their workout media" ON storage.objects
    FOR DELETE USING (
        bucket_id = 'workout_media' 
        AND EXISTS (
            SELECT 1 FROM profiles p 
            WHERE p.id = auth.uid() 
            AND p.role IN ('instructor', 'admin')
        )
    );

-- Storage policies for certification_documents bucket (private)
CREATE POLICY "Instructors can upload their certification documents" ON storage.objects
    FOR INSERT WITH CHECK (
        bucket_id = 'certification_documents' 
        AND auth.uid()::text = (storage.foldername(name))[1]
        AND EXISTS (
            SELECT 1 FROM profiles p 
            WHERE p.id = auth.uid() 
            AND p.role = 'instructor'
        )
    );

CREATE POLICY "Instructors can view their own certification documents" ON storage.objects
    FOR SELECT USING (
        bucket_id = 'certification_documents' 
        AND auth.uid()::text = (storage.foldername(name))[1]
    );

CREATE POLICY "Instructors can update their certification documents" ON storage.objects
    FOR UPDATE USING (
        bucket_id = 'certification_documents' 
        AND auth.uid()::text = (storage.foldername(name))[1]
    );

CREATE POLICY "Instructors can delete their certification documents" ON storage.objects
    FOR DELETE USING (
        bucket_id = 'certification_documents' 
        AND auth.uid()::text = (storage.foldername(name))[1]
    );

CREATE POLICY "Admin can view all certification documents" ON storage.objects
    FOR SELECT USING (
        bucket_id = 'certification_documents' 
        AND EXISTS (
            SELECT 1 FROM profiles p 
            WHERE p.id = auth.uid() 
            AND p.role = 'admin'
        )
    );

-- Function to generate storage path for user files
CREATE OR REPLACE FUNCTION generate_storage_path(
    bucket_name TEXT,
    user_uuid UUID,
    file_name TEXT,
    subfolder TEXT DEFAULT NULL
)
RETURNS TEXT AS $$
BEGIN
    IF subfolder IS NOT NULL THEN
        RETURN format('%s/%s/%s/%s', bucket_name, user_uuid, subfolder, file_name);
    ELSE
        RETURN format('%s/%s/%s', bucket_name, user_uuid, file_name);
    END IF;
END;
$$ LANGUAGE plpgsql;

-- Function to get public URL for storage object
CREATE OR REPLACE FUNCTION get_storage_public_url(
    bucket_name TEXT,
    file_path TEXT
)
RETURNS TEXT AS $$
BEGIN
    RETURN format('https://wrevdlggsevlckprjrwm.supabase.co/storage/v1/object/public/%s/%s', 
                  bucket_name, file_path);
END;
$$ LANGUAGE plpgsql;

-- Function to validate file upload
CREATE OR REPLACE FUNCTION validate_file_upload(
    bucket_name TEXT,
    file_name TEXT,
    file_size BIGINT,
    mime_type TEXT
)
RETURNS BOOLEAN AS $$
DECLARE
    bucket_config RECORD;
BEGIN
    -- Get bucket configuration
    SELECT file_size_limit, allowed_mime_types 
    INTO bucket_config
    FROM storage.buckets 
    WHERE id = bucket_name;
    
    IF NOT FOUND THEN
        RAISE EXCEPTION 'Bucket % not found', bucket_name;
    END IF;
    
    -- Check file size
    IF file_size > bucket_config.file_size_limit THEN
        RAISE EXCEPTION 'File size % exceeds limit % for bucket %', 
                       file_size, bucket_config.file_size_limit, bucket_name;
    END IF;
    
    -- Check MIME type
    IF bucket_config.allowed_mime_types IS NOT NULL 
       AND NOT (mime_type = ANY(bucket_config.allowed_mime_types)) THEN
        RAISE EXCEPTION 'MIME type % not allowed for bucket %. Allowed types: %', 
                       mime_type, bucket_name, bucket_config.allowed_mime_types;
    END IF;
    
    RETURN TRUE;
END;
$$ LANGUAGE plpgsql;

-- Function to clean up orphaned files
CREATE OR REPLACE FUNCTION cleanup_orphaned_storage_files()
RETURNS TABLE (
    bucket_id TEXT,
    file_path TEXT,
    action TEXT
) AS $$
BEGIN
    -- This function would identify and optionally remove orphaned files
    -- Implementation depends on specific business logic
    
    RETURN QUERY
    SELECT 
        'profile_images'::TEXT as bucket_id,
        'example/path'::TEXT as file_path,
        'would_delete'::TEXT as action
    WHERE FALSE; -- Placeholder - implement actual logic
END;
$$ LANGUAGE plpgsql;

-- Function to get storage usage statistics
CREATE OR REPLACE FUNCTION get_storage_usage_stats()
RETURNS TABLE (
    bucket_name TEXT,
    file_count BIGINT,
    total_size_bytes BIGINT,
    total_size_mb DECIMAL(10,2)
) AS $$
BEGIN
    RETURN QUERY
    SELECT 
        o.bucket_id as bucket_name,
        COUNT(*) as file_count,
        SUM(COALESCE((o.metadata->>'size')::BIGINT, 0)) as total_size_bytes,
        ROUND((SUM(COALESCE((o.metadata->>'size')::BIGINT, 0)) / 1048576.0)::NUMERIC, 2) as total_size_mb
    FROM storage.objects o
    GROUP BY o.bucket_id
    ORDER BY total_size_bytes DESC;
END;
$$ LANGUAGE plpgsql;

-- Comments for documentation
COMMENT ON FUNCTION generate_storage_path IS 'Generate standardized storage path for user files';
COMMENT ON FUNCTION get_storage_public_url IS 'Get public URL for storage object';
COMMENT ON FUNCTION validate_file_upload IS 'Validate file upload against bucket constraints';
COMMENT ON FUNCTION cleanup_orphaned_storage_files IS 'Identify and clean up orphaned storage files';
COMMENT ON FUNCTION get_storage_usage_stats IS 'Get storage usage statistics by bucket';

-- Verify storage setup
DO $$
DECLARE
    bucket_count INTEGER;
BEGIN
    SELECT COUNT(*) INTO bucket_count 
    FROM storage.buckets 
    WHERE id IN ('profile_images', 'exercise_media', 'workout_media', 'certification_documents');
    
    IF bucket_count = 4 THEN
        RAISE NOTICE 'All storage buckets created successfully';
    ELSE
        RAISE WARNING 'Expected 4 buckets, found %', bucket_count;
    END IF;
END;
$$;

-- Final status message
SELECT 'Storage buckets and policies have been set up successfully' as status;
