-- =====================================================
-- FITGO UTILITIES: HELPER FUNCTIONS
-- =====================================================
-- Common utility functions used across the application

-- Function to generate short UUID (for display purposes)
CREATE OR REPLACE FUNCTION generate_short_uuid()
RETURNS TEXT AS $$
BEGIN
    RETURN SUBSTRING(gen_random_uuid()::TEXT FROM 1 FOR 8);
END;
$$ LANGUAGE plpgsql;

-- Function to validate email format
CREATE OR REPLACE FUNCTION is_valid_email(email TEXT)
RETURNS BOOLEAN AS $$
BEGIN
    RETURN email ~* '^[A-Za-z0-9._%+-]+@[A-Za-z0-9.-]+\.[A-Za-z]{2,}$';
END;
$$ LANGUAGE plpgsql;

-- Function to validate phone number (Turkish format)
CREATE OR REPLACE FUNCTION is_valid_turkish_phone(phone TEXT)
RETURNS BOOLEAN AS $$
BEGIN
    -- Turkish phone format: +90XXXXXXXXXX or 0XXXXXXXXXX
    RETURN phone ~* '^(\+90|0)[0-9]{10}$';
END;
$$ LANGUAGE plpgsql;

-- Function to format Turkish phone number
CREATE OR REPLACE FUNCTION format_turkish_phone(phone TEXT)
RETURNS TEXT AS $$
BEGIN
    -- Remove all non-digits
    phone := regexp_replace(phone, '[^0-9]', '', 'g');
    
    -- Handle different formats
    IF LENGTH(phone) = 11 AND LEFT(phone, 1) = '0' THEN
        -- 0XXXXXXXXXX -> +90XXXXXXXXXX
        RETURN '+90' || SUBSTRING(phone FROM 2);
    ELSIF LENGTH(phone) = 10 THEN
        -- XXXXXXXXXX -> +90XXXXXXXXXX
        RETURN '+90' || phone;
    ELSIF LENGTH(phone) = 12 AND LEFT(phone, 2) = '90' THEN
        -- 90XXXXXXXXXX -> +90XXXXXXXXXX
        RETURN '+' || phone;
    ELSE
        -- Return as is if format is unclear
        RETURN phone;
    END IF;
END;
$$ LANGUAGE plpgsql;

-- Function to calculate age from birth date
CREATE OR REPLACE FUNCTION calculate_age(birth_date DATE)
RETURNS INTEGER AS $$
BEGIN
    RETURN EXTRACT(YEAR FROM AGE(birth_date));
END;
$$ LANGUAGE plpgsql;

-- Function to calculate BMI
CREATE OR REPLACE FUNCTION calculate_bmi(weight_kg DECIMAL, height_cm INTEGER)
RETURNS DECIMAL(4,1) AS $$
DECLARE
    height_m DECIMAL;
BEGIN
    IF weight_kg IS NULL OR height_cm IS NULL OR height_cm = 0 THEN
        RETURN NULL;
    END IF;
    
    height_m := height_cm / 100.0;
    RETURN ROUND((weight_kg / (height_m * height_m))::NUMERIC, 1);
END;
$$ LANGUAGE plpgsql;

-- Function to get BMI category
CREATE OR REPLACE FUNCTION get_bmi_category(bmi DECIMAL)
RETURNS TEXT AS $$
BEGIN
    IF bmi IS NULL THEN
        RETURN 'Unknown';
    ELSIF bmi < 18.5 THEN
        RETURN 'Underweight';
    ELSIF bmi < 25 THEN
        RETURN 'Normal weight';
    ELSIF bmi < 30 THEN
        RETURN 'Overweight';
    ELSE
        RETURN 'Obese';
    END IF;
END;
$$ LANGUAGE plpgsql;

-- Function to calculate daily calorie needs (Harris-Benedict equation)
CREATE OR REPLACE FUNCTION calculate_daily_calories(
    weight_kg DECIMAL,
    height_cm INTEGER,
    age INTEGER,
    gender TEXT,
    activity_level TEXT
)
RETURNS INTEGER AS $$
DECLARE
    bmr DECIMAL;
    activity_multiplier DECIMAL;
BEGIN
    IF weight_kg IS NULL OR height_cm IS NULL OR age IS NULL OR gender IS NULL THEN
        RETURN NULL;
    END IF;
    
    -- Calculate BMR (Basal Metabolic Rate)
    IF gender = 'male' THEN
        bmr := 88.362 + (13.397 * weight_kg) + (4.799 * height_cm) - (5.677 * age);
    ELSE
        bmr := 447.593 + (9.247 * weight_kg) + (3.098 * height_cm) - (4.330 * age);
    END IF;
    
    -- Apply activity level multiplier
    activity_multiplier := CASE activity_level
        WHEN 'sedentary' THEN 1.2
        WHEN 'lightly_active' THEN 1.375
        WHEN 'moderately_active' THEN 1.55
        WHEN 'very_active' THEN 1.725
        WHEN 'extremely_active' THEN 1.9
        ELSE 1.2
    END;
    
    RETURN ROUND(bmr * activity_multiplier);
END;
$$ LANGUAGE plpgsql;

-- Function to generate slug from text
CREATE OR REPLACE FUNCTION generate_slug(input_text TEXT)
RETURNS TEXT AS $$
BEGIN
    RETURN LOWER(
        TRIM(
            regexp_replace(
                regexp_replace(
                    regexp_replace(input_text, '[^a-zA-Z0-9\s-]', '', 'g'),
                    '\s+', '-', 'g'
                ),
                '-+', '-', 'g'
            ),
            '-'
        )
    );
END;
$$ LANGUAGE plpgsql;

-- Function to get time of day greeting
CREATE OR REPLACE FUNCTION get_time_greeting()
RETURNS TEXT AS $$
DECLARE
    current_hour INTEGER;
BEGIN
    current_hour := EXTRACT(HOUR FROM NOW() AT TIME ZONE 'Europe/Istanbul');
    
    IF current_hour < 6 THEN
        RETURN 'İyi geceler';
    ELSIF current_hour < 12 THEN
        RETURN 'Günaydın';
    ELSIF current_hour < 18 THEN
        RETURN 'İyi günler';
    ELSE
        RETURN 'İyi akşamlar';
    END IF;
END;
$$ LANGUAGE plpgsql;

-- Function to format currency (Turkish Lira)
CREATE OR REPLACE FUNCTION format_currency_tl(amount DECIMAL)
RETURNS TEXT AS $$
BEGIN
    IF amount IS NULL THEN
        RETURN '0,00 TL';
    END IF;
    
    RETURN REPLACE(TO_CHAR(amount, 'FM999,999,999.00'), '.', ',') || ' TL';
END;
$$ LANGUAGE plpgsql;

-- Function to get workout difficulty color
CREATE OR REPLACE FUNCTION get_difficulty_color(difficulty TEXT)
RETURNS TEXT AS $$
BEGIN
    RETURN CASE difficulty
        WHEN 'beginner' THEN '#4CAF50'    -- Green
        WHEN 'intermediate' THEN '#FF9800' -- Orange
        WHEN 'advanced' THEN '#F44336'     -- Red
        ELSE '#9E9E9E'                     -- Grey
    END;
END;
$$ LANGUAGE plpgsql;

-- Function to check if user is instructor
CREATE OR REPLACE FUNCTION is_instructor(user_uuid UUID DEFAULT auth.uid())
RETURNS BOOLEAN AS $$
BEGIN
    RETURN EXISTS (
        SELECT 1 FROM profiles p 
        WHERE p.id = user_uuid 
        AND p.role = 'instructor'
    );
END;
$$ LANGUAGE plpgsql;

-- Function to check if user is admin
CREATE OR REPLACE FUNCTION is_admin(user_uuid UUID DEFAULT auth.uid())
RETURNS BOOLEAN AS $$
BEGIN
    RETURN EXISTS (
        SELECT 1 FROM profiles p 
        WHERE p.id = user_uuid 
        AND p.role = 'admin'
    );
END;
$$ LANGUAGE plpgsql;

-- Function to get user role
CREATE OR REPLACE FUNCTION get_user_role(user_uuid UUID DEFAULT auth.uid())
RETURNS TEXT AS $$
DECLARE
    user_role TEXT;
BEGIN
    SELECT role INTO user_role FROM profiles WHERE id = user_uuid;
    RETURN COALESCE(user_role, 'guest');
END;
$$ LANGUAGE plpgsql;

-- Function to sanitize HTML content
CREATE OR REPLACE FUNCTION sanitize_html(input_text TEXT)
RETURNS TEXT AS $$
BEGIN
    -- Remove script tags and their content
    input_text := regexp_replace(input_text, '<script[^>]*>.*?</script>', '', 'gi');
    
    -- Remove dangerous attributes
    input_text := regexp_replace(input_text, '\s(on\w+|javascript:|data:)[^>]*', '', 'gi');
    
    -- Allow only safe HTML tags
    input_text := regexp_replace(input_text, '<(?!/?(?:p|br|strong|em|u|ol|ul|li|h[1-6])\b)[^>]*>', '', 'gi');
    
    RETURN input_text;
END;
$$ LANGUAGE plpgsql;

-- Comments for documentation
COMMENT ON FUNCTION generate_short_uuid IS 'Generate 8-character UUID for display purposes';
COMMENT ON FUNCTION is_valid_email IS 'Validate email format using regex';
COMMENT ON FUNCTION is_valid_turkish_phone IS 'Validate Turkish phone number format';
COMMENT ON FUNCTION format_turkish_phone IS 'Format phone number to Turkish standard (+90XXXXXXXXXX)';
COMMENT ON FUNCTION calculate_age IS 'Calculate age from birth date';
COMMENT ON FUNCTION calculate_bmi IS 'Calculate BMI from weight and height';
COMMENT ON FUNCTION get_bmi_category IS 'Get BMI category (Underweight, Normal, Overweight, Obese)';
COMMENT ON FUNCTION calculate_daily_calories IS 'Calculate daily calorie needs using Harris-Benedict equation';
COMMENT ON FUNCTION generate_slug IS 'Generate URL-friendly slug from text';
COMMENT ON FUNCTION get_time_greeting IS 'Get Turkish time-based greeting';
COMMENT ON FUNCTION format_currency_tl IS 'Format amount as Turkish Lira currency';
COMMENT ON FUNCTION get_difficulty_color IS 'Get color code for workout difficulty level';
COMMENT ON FUNCTION is_instructor IS 'Check if user has instructor role';
COMMENT ON FUNCTION is_admin IS 'Check if user has admin role';
COMMENT ON FUNCTION get_user_role IS 'Get user role (student, instructor, admin, guest)';
COMMENT ON FUNCTION sanitize_html IS 'Sanitize HTML content to prevent XSS attacks';
